FROM openjdk:17-jdk-slim

WORKDIR /app

# 添加应用JAR包
COPY target/*.jar app.jar

# 创建数据和日志目录
RUN mkdir -p /app/data /app/logs /app/config
VOLUME ["/app/data", "/app/logs", "/app/config"]

# 设置时区
ENV TZ=Asia/Shanghai
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# 设置JVM参数
ENV JAVA_OPTS="-Xms256m -Xmx512m -XX:+UseG1GC -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=/app/logs/heap-dump.hprof"

# 设置健康检查
HEALTHCHECK --interval=30s --timeout=3s --retries=3 CMD wget -q -O- http://localhost:8080/actuator/health | grep UP || exit 1

EXPOSE 8080

ENTRYPOINT ["sh", "-c", "java $JAVA_OPTS -jar app.jar --spring.config.location=file:/app/config/ ${0} ${@}"] 