#sun.net.www MIME content-types table
#
# Property fields:
#
#   <description> ::= 'description' '=' <descriptive string>
#    <extensions> ::= 'file_extensions' '=' <comma-delimited list, include '.'>
#         <image> ::= 'icon' '=' <filename of icon image>
#        <action> ::= 'browser' | 'application' | 'save' | 'unknown'
#   <application> ::= 'application' '=' <command line template>
#

#
# The "we don't know anything about this data" type(s).
# Used internally to mark unrecognized types.
#
content/unknown: description=Unknown Content
unknown/unknown: description=Unknown Data Type

#
# The template we should use for temporary files when launching an application
# to view a document of given type.
#
temp.file.template: c:\\temp\\%s

#
# The "real" types.
#
application/octet-stream: \
	description=Generic Binary Stream;\
	file_extensions=.saveme,.dump,.hqx,.arc,.obj,.lib,.bin,.exe,.zip,.gz

application/oda: \
	description=ODA Document;\
	file_extensions=.oda

application/pdf: \
	description=Adobe PDF Format;\
	file_extensions=.pdf

application/postscript: \
	description=Postscript File;\
	file_extensions=.eps,.ai,.ps;\
	icon=ps

application/rtf: \
	description=Wordpad Document;\
	file_extensions=.rtf;\
	action=application;\
	application=wordpad.exe %s

application/x-dvi: \
	description=TeX DVI File;\
	file_extensions=.dvi

application/x-hdf: \
	description=Hierarchical Data Format;\
	file_extensions=.hdf;\
	action=save

application/x-latex: \
	description=LaTeX Source;\
	file_extensions=.latex

application/x-netcdf: \
	description=Unidata netCDF Data Format;\
	file_extensions=.nc,.cdf;\
	action=save

application/x-tex: \
	description=TeX Source;\
	file_extensions=.tex

application/x-texinfo: \
	description=Gnu Texinfo;\
	file_extensions=.texinfo,.texi

application/x-troff: \
	description=Troff Source;\
	file_extensions=.t,.tr,.roff

application/x-troff-man: \
	description=Troff Manpage Source;\
	file_extensions=.man

application/x-troff-me: \
	description=Troff ME Macros;\
	file_extensions=.me

application/x-troff-ms: \
	description=Troff MS Macros;\
	file_extensions=.ms

application/x-wais-source: \
	description=Wais Source;\
	file_extensions=.src,.wsrc

application/zip: \
	description=Zip File;\
	file_extensions=.zip;\
	icon=zip;\
	action=save

application/x-bcpio: \
	description=Old Binary CPIO Archive;\
	file_extensions=.bcpio;\
	action=save

application/x-cpio: \
	description=Unix CPIO Archive;\
	file_extensions=.cpio;\
	action=save

application/x-gtar: \
	description=Gnu Tar Archive;\
	file_extensions=.gtar;\
	icon=tar;\
	action=save

application/x-shar: \
	description=Shell Archive;\
	file_extensions=.sh,.shar;\
	action=save

application/x-sv4cpio: \
	description=SVR4 CPIO Archive;\
	file_extensions=.sv4cpio;\
	action=save

application/x-sv4crc: \
	description=SVR4 CPIO with CRC;\
	file_extensions=.sv4crc;\
	action=save

application/x-tar: \
	description=Tar Archive;\
	file_extensions=.tar;\
	icon=tar;\
	action=save

application/x-ustar: \
	description=US Tar Archive;\
	file_extensions=.ustar;\
	action=save

audio/basic: \
	description=Basic Audio;\
	file_extensions=.snd,.au;\
	icon=audio

audio/x-aiff: \
	description=Audio Interchange Format File;\
	file_extensions=.aifc,.aif,.aiff;\
	icon=aiff

audio/x-wav: \
	description=Wav Audio;\
	file_extensions=.wav;\
	icon=wav;\
	action=application;\
	application=mplayer.exe %s

image/gif: \
	description=GIF Image;\
	file_extensions=.gif;\
	icon=gif;\
	action=browser

image/ief: \
	description=Image Exchange Format;\
	file_extensions=.ief

image/jpeg: \
	description=JPEG Image;\
	file_extensions=.jfif,.jfif-tbnl,.jpe,.jpg,.jpeg;\
	icon=jpeg;\
	action=browser

image/tiff: \
	description=TIFF Image;\
	file_extensions=.tif,.tiff;\
	icon=tiff

image/vnd.fpx: \
	description=FlashPix Image;\
	file_extensions=.fpx,.fpix

image/x-cmu-rast: \
	description=CMU Raster Image;\
	file_extensions=.ras

image/x-portable-anymap: \
	description=PBM Anymap Image;\
	file_extensions=.pnm

image/x-portable-bitmap: \
	description=PBM Bitmap Image;\
	file_extensions=.pbm

image/x-portable-graymap: \
	description=PBM Graymap Image;\
	file_extensions=.pgm

image/x-portable-pixmap: \
	description=PBM Pixmap Image;\
	file_extensions=.ppm

image/x-rgb: \
	description=RGB Image;\
	file_extensions=.rgb

image/x-xbitmap: \
	description=X Bitmap Image;\
	file_extensions=.xbm,.xpm

image/x-xwindowdump: \
	description=X Window Dump Image;\
	file_extensions=.xwd

image/png: \
	description=PNG Image;\
	file_extensions=.png;\
	icon=png;\
	action=browser

image/bmp: \
	description=Bitmap Image;\
	file_extensions=.bmp;

text/html: \
	description=HTML Document;\
	file_extensions=.htm,.html;\
	icon=html

text/plain: \
	description=Plain Text;\
	file_extensions=.text,.c,.cc,.c++,.h,.pl,.txt,.java,.el;\
	icon=text;\
	action=browser

text/tab-separated-values: \
	description=Tab Separated Values Text;\
	file_extensions=.tsv

text/x-setext: \
	description=Structure Enhanced Text;\
	file_extensions=.etx

video/mpeg: \
	description=MPEG Video Clip;\
	file_extensions=.mpg,.mpe,.mpeg;\
	icon=mpeg

video/quicktime: \
	description=QuickTime Video Clip;\
	file_extensions=.mov,.qt

application/x-troff-msvideo: \
	description=AVI Video;\
	file_extensions=.avi;\
	icon=avi;\
	action=application;\
	application=mplayer.exe %s

video/x-sgi-movie: \
	description=SGI Movie;\
	file_extensions=.movie,.mv

message/rfc822: \
	description=Internet Email Message;\
	file_extensions=.mime

application/xml: \
	description=XML document;\
	file_extensions=.xml


