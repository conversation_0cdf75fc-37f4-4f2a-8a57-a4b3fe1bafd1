#
# Copyright (c) 2004, 2011, Oracle and/or its affiliates. All rights reserved.
# ORACLE PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
#

error.internal.badmsg=erreur interne, message inconnu
error.badinst.nojre=Installation incorrecte. JRE introuvable dans le fichier de configuration
error.launch.execv=Erreur lors de l'appel de Java Web Start (execv)
error.launch.sysexec=Erreur lors de l'appel de Java Web Start (SysExec) 
error.listener.failed=Accueil : \u00E9chec de sysCreateListenerSocket
error.accept.failed=Accueil : \u00E9chec d'accept
error.recv.failed=Accueil : \u00E9chec de recv
error.invalid.port=Accueil : impossible de r\u00E9activer un port valide
error.read=Lecture apr\u00E8s la fin de tampon
error.xmlparsing=Erreur d'analyse XML : type incorrect de jeton
error.splash.exit=Le processus d'affichage de l'\u00E9cran d'accueil de Java Web Start est en cours de fermeture...\n
# "Last WinSock Error" means the error message for the last operation that failed.
error.winsock=\tDerni\u00E8re erreur WinSock : 
error.winsock.load=Impossible de charger winsock.dll
error.winsock.start=Echec de WSAStartup
error.badinst.nohome=Installation incorrecte : JAVAWS_HOME non d\u00E9fini 
error.splash.noimage=Accueil : impossible de charger l'image de l'\u00E9cran d'accueil
error.splash.socket=Accueil : \u00E9chec du socket de serveur
error.splash.cmnd=Accueil : commande inconnue
error.splash.port=Accueil : port non sp\u00E9cifi\u00E9
error.splash.send=Accueil : \u00E9chec de l'envoi
error.splash.timer=Accueil : impossible de cr\u00E9er l'horloge d'arr\u00EAt
error.splash.x11.open=Accueil : impossible d'ouvrir l'affichage X11
error.splash.x11.connect=Accueil : \u00E9chec de la connexion X11
# Javaws usage: '\' is a joining of two sentence,which are connected including
# the invisible character '\n'.
message.javaws.usage=\nSyntaxe :\tjavaws [run-options] <jnlp-file>\t\n\tjavaws [control-options]\t\t\n\no\u00F9 les options d'ex\u00E9cution sont :\t\t\t\n-verbose       \taffichage de texte de sortie suppl\u00E9mentaire\t\n-offline       \tex\u00E9cution de l'application en mode hors ligne\t\n-system        \tex\u00E9cution de l'application \u00E0 partir du cache syst\u00E8me uniquement\n-Xnosplash     \tex\u00E9cution sans affichage de l'\u00E9cran d'accueil\t\n-J<option>     \tsp\u00E9cification d'une option \u00E0 la machine virtuelle\t\n-wait          \tlancement du processus Java et attente de sa fermeture\t\n\nles options de contr\u00F4le sont :\t\n-viewer        \taffichage du visionneur du cache dans le panneau de configuration Java\n-clearcache    \tsuppression de toutes les applications non install\u00E9es du cache\n-uninstall     \tsuppression de toutes les applications du cache\n-uninstall <jnlp-file>              \td\u00E9sinstallation de l'application dans le cache\t\n-import [import-options] <jnlp-file>\timport de l'application dans le cache\t\t\n\nles options d'import sont :\t\t\t\t\t\t\n-silent        \timport silencieux (sans interface utilisateur)\t\n-system        \timport de l'application dans le cache syst\u00E8me\t\n-codebase <url>\textraction des ressources \u00E0 partir d'une base de code sp\u00E9cifique\t\n-shortcut      \tinstallation des raccourcis comme si l'utilisateur avait autoris\u00E9 l'op\u00E9ration\t\n-association   \tinstallation des associations comme si l'utilisateur avait autoris\u00E9 l'op\u00E9ration\t\n\n
