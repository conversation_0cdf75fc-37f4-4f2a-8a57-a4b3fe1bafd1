#
# Copyright (c) 2004, 2013, Oracle and/or its affiliates. All rights reserved.
# ORACLE PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
#

error.internal.badmsg=interner <PERSON>, unbekannte Meldung
error.badinst.nojre=Ung\u00FCltige Installation. Keine JRE in Konfigurationsdatei gefunden
error.launch.execv=<PERSON>hler beim Aufrufen von Java Web Start (execv) aufgetreten
error.launch.sysexec=<PERSON>hler beim Aufrufen von Java Web Start (SysExec) aufgetreten
error.listener.failed=Startbildschirm: sysCreateListenerSocket nicht erfolgreich
error.accept.failed=Startbildschirm: accept nicht erfolgreich
error.recv.failed=Startbildschirm: recv nicht erfolgreich
error.invalid.port=Startbildschirm: Reaktivierung eines g\u00FCltigen Ports nicht m\u00F6glich
error.read=\u00DCber Pufferende hinaus gelesen
error.xmlparsing=XML-Parsefehler: Falscher Tokentyp gefunden
error.splash.exit=Prozess f\u00FCr Startbildschirm von Java Web Start wird beendet.....\n
# "Last WinSock Error" means the error message for the last operation that failed.
error.winsock=\tLetzter WinSock-Fehler: 
error.winsock.load=winsock.dll konnte nicht geladen werden
error.winsock.start=WSAStartup nicht erfolgreich
error.badinst.nohome=Ung\u00FCltige Installation: JAVAWS_HOME nicht festgelegt 
error.splash.noimage=Startbildschirm: Startbildschirmbild konnte nicht geladen werden
error.splash.socket=Startbildschirm: Server-Socket nicht erfolgreich
error.splash.cmnd=Startbildschirm: Unbekannter Befehl
error.splash.port=Startbildschirm: Port nicht angegeben
error.splash.send=Startbildschirm: send nicht erfolgreich
error.splash.timer=Startbildschirm: Timer f\u00FCr das Herunterfahren konnte nicht erstellt werden
error.splash.x11.open=Startbildschirm: X11-Anzeige kann nicht ge\u00F6ffnet werden
error.splash.x11.connect=Startbildschirm: X11-Verbindung nicht erfolgreich
# Javaws usage: '\' is a joining of two sentence,which are connected including
# the invisible character '\n'.
message.javaws.usage=\nVerwendung:\tjavaws [run-options] <jnlp-file>\t\n\tjavaws [control-options]\t\t\n\nwobei run-options Folgendes umfasst:\t\t\t\n-verbose       \tZus\u00E4tzliche Ausgabe anzeigen\t\n-offline       \tAnwendung im Offlinemodus ausf\u00FChren\t\n-system        \tAnwendung nur aus Systemcache ausf\u00FChren\n-Xnosplash     \tOhne Anzeige eines Startbildschirms ausf\u00FChren\t\n-J<option>     \tOption f\u00FCr VM angeben\t\n-wait          \tJava-Prozess starten und auf dessen Beendigung warten\t\n\ncontrol-options umfassen:\t\n-viewer        \tCache-Viewer in Java-Systemsteuerung anzeigen\n-clearcache    \tAlle nicht installierten Anwendungen aus dem Cache entfernen\n-uninstall     \tAlle Anwendungen aus dem Cache entfernen\n-uninstall <jnlp-file>              \tAnwendung aus dem Cache entfernen\t\n-import [import-options] <jnlp-file>\tAnwendung in Cache importieren\t\t\n\nimport-options umfassen:\t\t\t\t\t\t\n-silent        \tVollautomatisch importieren (ohne Benutzeroberfl\u00E4che)\t\n-system        \tAnwendung in Systemcache importieren\t\n-codebase <url>\tRessourcen aus angegebener Codebase abrufen\t\n-shortcut      \tShortcuts so installieren, als w\u00FCrde der Benutzer einen Prompt zulassen\t\n-association   \tVerkn\u00FCpfungen so installieren, als w\u00FCrde der Benutzer einen Prompt zulassen\n\n
