#
# Copyright (c) 2004, 2013, Oracle and/or its affiliates. All rights reserved.
# ORACLE PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
#

error.internal.badmsg=\u5167\u90E8\u932F\u8AA4\uFF0C\u4E0D\u660E\u7684\u8A0A\u606F
error.badinst.nojre=\u5B89\u88DD\u932F\u8AA4\u3002\u5728\u7D44\u614B\u6A94\u4E2D\u627E\u4E0D\u5230 JRE
error.launch.execv=\u547C\u53EB Java Web Start (execv) \u6642\u9047\u5230\u932F\u8AA4
error.launch.sysexec=\u547C\u53EB Java Web Start (SysExec) \u6642\u9047\u5230\u932F\u8AA4
error.listener.failed=Splash: sysCreateListenerSocket \u5931\u6557
error.accept.failed=Splash: \u63A5\u53D7\u5931\u6557
error.recv.failed=Splash: recv \u5931\u6557
error.invalid.port=Splash: \u6709\u6548\u7684\u9023\u63A5\u57E0\u5C1A\u672A\u56DE\u5FA9
error.read=\u8B80\u53D6\u8D85\u51FA\u7DE9\u885D\u5340\u7D50\u5C3E
error.xmlparsing=XML \u5256\u6790\u932F\u8AA4: \u627E\u5230\u932F\u8AA4\u7684\u8A18\u865F\u7A2E\u985E
error.splash.exit=Java Web Start \u9583\u73FE\u87A2\u5E55\u8655\u7406\u7D50\u675F\u4E2D.....\n
# "Last WinSock Error" means the error message for the last operation that failed.
error.winsock=\t\u4E0A\u4E00\u6B21 WinSock \u932F\u8AA4: 
error.winsock.load=\u7121\u6CD5\u8F09\u5165 winsock.dll
error.winsock.start=WSAStartup \u5931\u6557
error.badinst.nohome=\u5B89\u88DD\u932F\u8AA4: \u672A\u8A2D\u5B9A JAVAWS_HOME 
error.splash.noimage=Splash: \u7121\u6CD5\u8F09\u5165\u9583\u73FE\u87A2\u5E55\u5F71\u50CF
error.splash.socket=Splash: \u4F3A\u670D\u5668 socket \u5931\u6557
error.splash.cmnd=Splash: \u7121\u6CD5\u8FA8\u8B58\u547D\u4EE4
error.splash.port=Splash: \u672A\u6307\u5B9A\u9023\u63A5\u57E0
error.splash.send=Splash: \u50B3\u9001\u5931\u6557
error.splash.timer=Splash: \u7121\u6CD5\u5EFA\u7ACB\u95DC\u6A5F\u8A08\u6642\u5668
error.splash.x11.open=Splash: \u7121\u6CD5\u958B\u555F X11 \u986F\u793A\u756B\u9762
error.splash.x11.connect=Splash: X11 \u9023\u7DDA\u5931\u6557
# Javaws usage: '\' is a joining of two sentence,which are connected including
# the invisible character '\n'.
message.javaws.usage=\n\u7528\u6CD5:\tjavaws [run-options] <jnlp-file>\t\n\tjavaws [control-options]\t\t\n\n\u5176\u4E2D\uFF0Crun-options \u5305\u62EC:\t\t\t\n-verbose       \t\u986F\u793A\u66F4\u8A73\u7D30\u7684\u8F38\u51FA\t\n-offline       \t\u5728\u96E2\u7DDA\u6A21\u5F0F\u4E0B\u57F7\u884C\u61C9\u7528\u7A0B\u5F0F\t\n-system        \t\u50C5\u5F9E\u7CFB\u7D71\u5FEB\u53D6\u57F7\u884C\u61C9\u7528\u7A0B\u5F0F\n-Xnosplash     \t\u57F7\u884C\u6642\u4E0D\u986F\u793A\u8EDF\u9AD4\u8CC7\u8A0A\u756B\u9762\t\n-J<option>     \t\u70BA vm \u63D0\u4F9B\u9078\u9805\t\n-wait          \t\u555F\u52D5 Java \u8655\u7406\u4E26\u7B49\u5F85\u5176\u7D50\u675F\t\n\ncontrol-options \u5305\u62EC:\t\n-viewer        \t\u5728 Java \u63A7\u5236\u9762\u677F\u4E2D\u986F\u793A\u5FEB\u53D6\u6AA2\u8996\u5668\n-clearcache    \t\u5F9E\u5FEB\u53D6\u4E2D\u79FB\u9664\u6240\u6709\u975E\u5B89\u88DD\u61C9\u7528\u7A0B\u5F0F\n-uninstall     \t\u5F9E\u5FEB\u53D6\u4E2D\u79FB\u9664\u6240\u6709\u61C9\u7528\u7A0B\u5F0F\n-uninstall <jnlp-file>              \t\u5F9E\u5FEB\u53D6\u4E2D\u79FB\u9664\u61C9\u7528\u7A0B\u5F0F\t\n-import [import-options] <jnlp-file>\t\u5C07\u61C9\u7528\u7A0B\u5F0F\u532F\u5165\u5FEB\u53D6\t\t\n\nimport-options \u5305\u62EC:\t\t\t\t\t\t\n-silent        \t\u4EE5\u7121\u63D0\u793A\u6A21\u5F0F\u532F\u5165 (\u7121\u4F7F\u7528\u8005\u4ECB\u9762)\t\n-system        \t\u5C07\u61C9\u7528\u7A0B\u5F0F\u532F\u5165\u7CFB\u7D71\u5FEB\u53D6\t\n-codebase <url>\t\u5F9E\u6307\u5B9A\u7684\u4EE3\u78BC\u5EAB\u64F7\u53D6\u8CC7\u6E90\t\n-shortcut      \t\u5B89\u88DD\u6377\u5F91 (\u7336\u5982\u4F7F\u7528\u8005\u5DF2\u5141\u8A31\u63D0\u793A)\t\n-association   \t\u5B89\u88DD\u95DC\u806F (\u7336\u5982\u4F7F\u7528\u8005\u5DF2\u5141\u8A31\u63D0\u793A)\t\n\n
