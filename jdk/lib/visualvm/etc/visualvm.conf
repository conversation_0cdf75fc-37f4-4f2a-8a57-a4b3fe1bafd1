#
# Copyright (c) 2007, 2014, Oracle and/or its affiliates. All rights reserved.
# ORACLE PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.

# Default locations of userdir and cachedir:
#
# On Windows ${DEFAULT_USERDIR_ROOT} will be replaced by the launcher
# with "<AppData>\VisualVM" where <AppData> is user's
# value of "AppData" key in Windows Registry under
# "HKCU\Software\Microsoft\Windows\CurrentVersion\Explorer\Shell Folders"
# and ${DEFAULT_CACHEDIR_ROOT} will be replaced by the launcher
# with "<Local AppData>\VisualVM\Cache" where <Local AppData> is user's
# value of "Local AppData" key in Windows Registry under
# "HKCU\Software\Microsoft\Windows\CurrentVersion\Explorer\Shell Folders"
#
# On Mac ${DEFAULT_USERDIR_ROOT} will be replaced by the launcher
# with "~/Library/Application Support/VisualVM" and
# ${DEFAULT_CACHEDIR_ROOT} with "~/Library/Caches/VisualVM"
#
# On other systems ${DEFAULT_USERDIR_ROOT} will be replaced by the launcher
# with "~/.visualvm" and ${DEFAULT_CACHEDIR_ROOT} with "~/.cache/visualvm"
#
# You can also use ${HOME} variable which will be replaced with
# user.home JVM system property value. This variable is valid only in
# visualvm_default_userdir and visualvm_default_cachedir properties.
#
# NOTE: If you specify a non-default userdir path on command line
# (--userdir option) and don't specify a cachedir path (--cachedir option),
# cachedir will be in "<userdir>/var/cache".
#
# Cachedir must be different from userdir. The same cachedir and userdir
# would cause problems.
#
visualvm_default_userdir="${DEFAULT_USERDIR_ROOT}/8u131"
visualvm_default_cachedir="${DEFAULT_CACHEDIR_ROOT}/8u131"

# Options used by VisualVM launcher by default:
# (can be overridden by explicit command line switches)
#

visualvm_default_options="-J-client -J-Xms24m -J-Xmx256m -J-Dsun.jvmstat.perdata.syncWaitMs=10000 -J-Dsun.java2d.noddraw=true -J-Dsun.java2d.d3d=false -J-Dnetbeans.keyring.no.master=true -J-Dplugin.manager.install.global=false -J--add-exports=java.desktop/sun.awt=ALL-UNNAMED -J--add-exports=jdk.jvmstat/sun.jvmstat.monitor.event=ALL-UNNAMED -J--add-exports=jdk.jvmstat/sun.jvmstat.monitor=ALL-UNNAMED -J--add-exports=java.desktop/sun.swing=ALL-UNNAMED -J--add-exports=jdk.attach/sun.tools.attach=ALL-UNNAMED -J--add-modules=java.activation -J-XX:+IgnoreUnrecognizedVMOptions"
# for development purposes you may wish to append: -J-Dnetbeans.logger.console=true -J-ea

# Default location of JDK:
#
# It can be overridden on command line by using --jdkhome <dir>
# Be careful when changing jdkhome.
# There are two VisualVM launchers for Windows (32-bit and 64-bit) and
# installer points to one of those in the VisualVM application shortcut
# based on the Java version selected at installation time.
#
#visualvm_jdkhome="/path/to/jdk"

# Additional module clusters:
# using ${path.separator} (';' on Windows or ':' on Unix):
#
#visualvm_extraclusters="/absolute/path/to/cluster1:/absolute/path/to/cluster2"
