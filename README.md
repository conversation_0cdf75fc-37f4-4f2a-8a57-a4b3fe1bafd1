# 佰税科技 API 集成说明

本模块实现了与佰税科技提供的第三方 API 的集成，主要用于查询关联企业信息。

## 配置说明

在 `application-dev.properties` 中配置以下参数：

```properties
# 佰税科技API接口相关配置
baishui.appKey=your_app_key
baishui.appSecret=your_app_secret
baishui.tokenUrl=https://api.example.com/oauth/token
baishui.apiUrl=https://api.example.com/apiService/oauth/public/api
```

## 使用方法

### 1. 注入 BaiShuiApiService

```java
@Autowired
private BaiShuiApiService baiShuiApiService;
```

### 2. 查询关联企业

```java
// 创建请求参数
RelatedEnterpriseRequest request = new RelatedEnterpriseRequest();
request.setUid("用户ID");
request.setSid("会话ID");
// 设置其他参数...

// 调用API
try {
    BaiShuiApiResponse response = baiShuiApiService.getRelatedEnterprise(request);
    if ("00".equals(response.getCode())) {
        // 处理成功响应
        List<BaiShuiApiDataItem> dataList = response.getDataList();
        // ...
    } else {
        // 处理失败响应
        String errorMessage = response.getMess();
        // ...
    }
} catch (Exception e) {
    // 处理异常
    // ...
}
```

## API 说明

### 1. 用户授权

- 接口说明：获取访问令牌
- 请求参数：appKey, appSecret
- 响应参数：code, mess, token, expiresIn

### 2. 查询关联企业

- 业务ID：nsfw.nsrGlqyCx2
- 请求参数：uid, sid, bid, ver, kz, CZRY_DM, SFSWJG_DM, SFZJHM, SFBDW
- 响应参数：code, mess, dataList(包含 djxh, nsrsbh, nsrmc, zgswjDm, zgswksfjDm, nsrztMc, sfzj)

## 注意事项

1. 令牌有效期为1年，但为安全起见，本实现中默认1小时后重新获取
2. 所有业务接口调用时均需带上 appKey 和 token 参数
3. 若业务接口返回令牌无效，会自动重新获取令牌
4. 日期格式：YYYY-MM-DD
5. 时间格式：YYYY-MM-DD HH24:MI:SS
6. 特殊字符处理：字段内容中含有 "<"、">"、"," 等特殊字符，均需要转成中文
