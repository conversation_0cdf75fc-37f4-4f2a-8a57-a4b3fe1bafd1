@echo off
setlocal

:: 设置 JAVA_HOME 为当前目录下的 jdk
echo Setting JAVA_HOME to %~dp0jdk
set JAVA_HOME=%~dp0jdk

:: 如果 dist 目录已存在，删除它以确保干净的打包
if exist dist (
    echo Removing existing dist directory...
    rmdir /s /q dist
)

:: 创建 dist 目录
echo Creating dist directory...
mkdir dist

:: 使用 Maven 打包项目
echo Running Maven to build the project...
mvn clean package

:: 检查是否成功生成 JAR 文件
echo Checking for built JAR file...
set found=
for %%f in (target\queue-api-*.jar) do set found=1
if not defined found (
    echo Error: No JAR file found in target. Build may have failed.
    pause
    exit /b 1
)

:: 复制 JAR 文件到 dist 目录
echo Copying JAR file to dist...
for %%f in (target\queue-api-*.jar) do copy "%%f" dist\

:: 复制 jdk 文件夹到 dist\jdk
echo Copying JDK to dist\jdk...
xcopy jdk dist\jdk /E /H /K /Y

:: 复制 run.bat 到 dist
echo Copying run.bat to dist...
copy run.bat dist\

:: 将 dist 目录中的内容打包成 ZIP 文件
echo Zipping dist contents to dist.zip...
powershell Compress-Archive -Path dist\* -DestinationPath dist.zip

:: 完成提示
echo Packaging complete. dist.zip has been created.
pause

endlocal