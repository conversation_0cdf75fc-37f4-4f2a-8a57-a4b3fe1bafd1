package com.ziyun.screen.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ziyun.common.annotation.DataColumn;
import com.ziyun.common.annotation.DataPermission;
import com.ziyun.screen.domain.QscTaxCloudDeal;
import com.ziyun.screen.domain.QscTaxDealRateRank;
import com.ziyun.screen.domain.vo.QscTaxCloudDealVo;
import com.ziyun.common.core.mapper.BaseMapperPlus;
import com.ziyun.screen.domain.vo.QscTaxDealRateRankVo;
import org.apache.ibatis.annotations.Param;

/**
 * 云厅办Mapper接口
 *
 * <AUTHOR>
 * @date 2022-12-04
 */
public interface QscTaxCloudDealMapper extends BaseMapperPlus<QscTaxCloudDealMapper, QscTaxCloudDeal, QscTaxCloudDealVo> {
    @DataPermission({
        @DataColumn(key = "deptName", value = "dept_id"),
        @DataColumn(key = "userName", value = "user_id")
    })
    Page<QscTaxCloudDealVo> customPageList(@Param("page") Page<QscTaxCloudDeal> page, @Param("ew") Wrapper<QscTaxCloudDeal> wrapper);
}
