package com.ziyun.screen.domain.vo;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.ziyun.common.annotation.ExcelDictFormat;
import com.ziyun.common.convert.ExcelDictConvert;
import lombok.Data;
import java.util.Date;



/**
 * 月电话接听量和接听率视图对象 qsc_phone_vol_rate_analysis
 *
 * <AUTHOR>
 * @date 2022-12-04
 */
@Data
@ExcelIgnoreUnannotated
public class QscPhoneVolRateAnalysisVo {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @ExcelProperty(value = "ID")
    private Long id;

    /**
     * 时间维度(单位:月)
     */
    @ExcelProperty(value = "时间维度(单位:月)")
    private Date axisX;

    /**
     * 电话接听量
     */
    @ExcelProperty(value = "电话接听量")
    private Long barY;

    /**
     * 电话接听率(单位:%)
     */
    @ExcelProperty(value = "电话接听率(单位:%)")
    private Long lineY;

    /**
     * 用户ID
     */
    @ExcelProperty(value = "用户ID")
    private Long userId;

    /**
     * 部门ID
     */
    @ExcelProperty(value = "部门ID")
    private Long deptId;


}
