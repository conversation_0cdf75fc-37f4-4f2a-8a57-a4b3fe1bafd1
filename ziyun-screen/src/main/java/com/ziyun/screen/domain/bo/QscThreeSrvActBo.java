package com.ziyun.screen.domain.bo;

import com.ziyun.common.core.validate.AddGroup;
import com.ziyun.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.*;

import java.util.Date;

import com.ziyun.common.core.domain.BaseEntity;

/**
 * 三服务活动集锦业务对象 qsc_three_srv_act
 *
 * <AUTHOR>
 * @date 2022-12-04
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class QscThreeSrvActBo extends BaseEntity {

    /**
     * ID
     */
    @NotNull(message = "ID不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 活动集锦内容
     */
    @NotBlank(message = "活动集锦内容不能为空", groups = { AddGroup.class, EditGroup.class })
    private String actContent;

    /**
     * 活动图片精选
     */
    @NotBlank(message = "活动图片精选不能为空", groups = { AddGroup.class, EditGroup.class })
    private String actImage;

    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long userId;

    /**
     * 部门ID
     */
    @NotNull(message = "部门ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long deptId;


}
