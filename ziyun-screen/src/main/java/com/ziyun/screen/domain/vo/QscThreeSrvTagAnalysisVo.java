package com.ziyun.screen.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.ziyun.common.annotation.ExcelDictFormat;
import com.ziyun.common.convert.ExcelDictConvert;
import lombok.Data;
import java.util.Date;



/**
 * 三服务标签视图对象 qsc_three_srv_tag_analysis
 *
 * <AUTHOR>
 * @date 2022-12-04
 */
@Data
@ExcelIgnoreUnannotated
public class QscThreeSrvTagAnalysisVo {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @ExcelProperty(value = "ID")
    private Long id;

    /**
     * 服务标签
     */
    @ExcelProperty(value = "服务标签")
    private String srvTag;

    /**
     * 服务量
     */
    @ExcelProperty(value = "服务量")
    private Long srvTagVol;

    /**
     * 服务量占比
     */
    @ExcelProperty(value = "服务量占比")
    private String srvTagRate;

    /**
     * 
     */
    @ExcelProperty(value = "")
    private String pieTag;

    /**
     * 用户ID
     */
    @ExcelProperty(value = "用户ID")
    private Long userId;

    /**
     * 部门ID
     */
    @ExcelProperty(value = "部门ID")
    private Long deptId;


}
