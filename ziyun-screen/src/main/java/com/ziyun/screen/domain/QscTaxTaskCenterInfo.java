package com.ziyun.screen.domain;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ziyun.common.core.domain.BaseEntity;

/**
 * 斗门任务中心信息对象 qsc_tax_task_center_info
 *
 * <AUTHOR>
 * @date 2022-12-04
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("qsc_tax_task_center_info")
public class QscTaxTaskCenterInfo extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * id
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 用户id
     */
    private Long userId;
    /**
     * 部门id
     */
    private Long deptId;
    /**
     * 任务中心分类
     */
    private String taskCenterType;
    /**
     * excel序号
     */
//    private Long serialNumber;
    /**
     * 任务日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date taskCreateTime;
    /**
     * 名称
     */
    private String taskName;
    /**
     * 姓名
     */
    private String name;
    /**
     * 联系电话
     */
    private String phoneNumber;
    /**
     * 任务来源
     */
    private String taskSource;
    /**
     * 业务编号
     */
    private String taskBizNum;
    /**
     * 类别
     */
    private String taskType;
    /**
     * 反映内容
     */
    private String taskReactionContent;
    /**
     * 任务详情
     */
    private String taskDetails;
    /**
     * 答复内容
     */
    private String taskReplyContent;
    /**
     * 处理(答复)意见及结论
     */
    private String taskProcessResults;
    /**
     * 辅导情况
     */
    private String taskCounselingSituation;
    /**
     * 受理人
     */
    private String acceptor;
    /**
     * 完结日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date taskFinishTime;
    /**
     * 任务状态
     */
    private String taskStatus;

}
