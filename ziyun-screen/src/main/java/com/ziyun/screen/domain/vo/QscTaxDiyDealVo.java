package com.ziyun.screen.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.ziyun.common.annotation.ExcelDictFormat;
import com.ziyun.common.convert.ExcelDictConvert;
import lombok.Data;
import java.util.Date;



/**
 * 自助办视图对象 qsc_tax_diy_deal
 *
 * <AUTHOR>
 * @date 2022-12-04
 */
@Data
@ExcelIgnoreUnannotated
public class QscTaxDiyDealVo {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @ExcelProperty(value = "ID")
    private Long id;

    /**
     * 自助办理事项
     */
    @ExcelProperty(value = "自助办理事项")
    private String name;

    /**
     * 业务量(单位:笔)
     */
    @ExcelProperty(value = "业务量(单位:笔)")
    private Long value;

//    /**
//     * 用户ID
//     */
//    @ExcelProperty(value = "用户ID")
//    private Long userId;
//
//    /**
//     * 部门ID
//     */
//    @ExcelProperty(value = "部门ID")
//    private Long deptId;


}
