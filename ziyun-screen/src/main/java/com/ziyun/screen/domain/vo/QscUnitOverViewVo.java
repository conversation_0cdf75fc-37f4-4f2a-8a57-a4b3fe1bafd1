package com.ziyun.screen.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.ziyun.common.annotation.ExcelDictFormat;
import com.ziyun.common.convert.ExcelDictConvert;
import lombok.Data;
import java.util.Date;



/**
 * 大厅信息总览视图对象 qsc_unit_over_view
 *
 * <AUTHOR>
 * @date 2022-12-04
 */
@Data
@ExcelIgnoreUnannotated
public class QscUnitOverViewVo {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @ExcelProperty(value = "id")
    private Long id;

    /**
     * 大厅登记序号
     */
    @ExcelProperty(value = "大厅登记序号")
    private Long unitCode;

    /**
     * 窗口数量
     */
    @ExcelProperty(value = "窗口数量")
    private Long winNum;

    /**
     * 人员数量
     */
    @ExcelProperty(value = "人员数量")
    private Long empNum;

    /**
     * 同比增长
     */
    @ExcelProperty(value = "同比增长")
    private Long rate;

    /**
     * 自助机数量
     */
    @ExcelProperty(value = "自助机数量")
    private Long diyNum;

    /**
     * 用户ID
     */
    @ExcelProperty(value = "用户ID")
    private Long userId;

    /**
     * 部门ID
     */
    @ExcelProperty(value = "部门ID")
    private Long deptId;


}
