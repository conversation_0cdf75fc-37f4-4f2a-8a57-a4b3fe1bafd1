package com.ziyun.screen.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ziyun.common.core.domain.BaseEntity;

/**
 * 入库出库对象 qsc_tax_stock_in_out
 *
 * <AUTHOR>
 * @date 2022-12-08
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("qsc_tax_stock_in_out")
public class QscTaxStockInOut extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * stock_id
     */
    @TableId(value = "stock_id")
    private Long stockId;
    /**
     * 用户ID
     */
    private Long userId;
    /**
     * 部门ID
     */
    private Long deptId;
    /**
     * 街道id
     */
    private Long regionId;
    /**
     * 街道名称
     */
    private String regionName;
    /**
     * 纳税人名称
     */
    private String taxpayerName;
    /**
     * 信用等级
     */
    private String evaluationResult;
    /**
     * 入库（万元）
     */
    private BigDecimal stockIn;
    /**
     * 退税（万元）
     */
    private BigDecimal stockOut;
    /**
     * 同比
     */
    private String rate;
    /**
     * 导入日期
     */
    private Date importDate;

}
