package com.ziyun.screen.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.ziyun.common.annotation.ExcelDictFormat;
import com.ziyun.common.convert.ExcelDictConvert;
import lombok.Data;
import java.util.Date;



/**
 * 办税率排名视图对象 qsc_tax_deal_rate_rank
 *
 * <AUTHOR>
 * @date 2022-12-04
 */
@Data
@ExcelIgnoreUnannotated
public class QscTaxDealRateRankVo {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @ExcelProperty(value = "id")
    private Long id;

    /**
     * 办理方式名称
     */
    @ExcelProperty(value = "办理方式名称")
    private String dealName;

    /**
     * 办税率
     */
    @ExcelProperty(value = "办税率")
    private String dealRate;

    /**
     * 区办税率
     */
    @ExcelProperty(value = "区办税率")
    private String sectionRate;

    /**
     * 全市平均水平
     */
    @ExcelProperty(value = "全市平均水平")
    private String cityAvg;

    /**
     * 全市排名
     */
    @ExcelProperty(value = "全市排名")
    private Long listRank;

    /**
     * 用户ID
     */
    @ExcelProperty(value = "用户ID")
    private Long userId;

    /**
     * 部门ID
     */
    @ExcelProperty(value = "部门ID")
    private Long deptId;


}
