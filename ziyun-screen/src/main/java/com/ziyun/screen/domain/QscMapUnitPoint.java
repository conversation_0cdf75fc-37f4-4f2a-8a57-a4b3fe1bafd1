package com.ziyun.screen.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;

import com.ziyun.common.core.domain.BaseEntity;

/**
 * 地图点位信息对象 qsc_map_unit_point
 *
 * <AUTHOR>
 * @date 2022-12-04
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("qsc_map_unit_point")
public class QscMapUnitPoint extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * 
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 序号
     */
    private String unitCode;
    /**
     * 办税点名称
     */
    private String name;
    /**
     * 左侧偏移
     */
    private String panlLeft;
    /**
     * 顶部偏移
     */
    private String panlTop;
    /**
     * 背景颜色
     */
    private String bgColor;
    /**
     * 地址
     */
    private String address;
    /**
     * 联系方式
     */
    private String phoneNum;
    /**
     * 税务机关代码
     */
    private String swjgdm;
    /**
     * 删除标志（0代表存在 1代表删除）
     */
    @TableLogic
    private String delFlag;
    /**
     * 用户ID
     */
    private Long userId;
    /**
     * 部门ID
     */
    private Long deptId;

}
