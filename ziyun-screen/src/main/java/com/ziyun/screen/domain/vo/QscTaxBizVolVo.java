package com.ziyun.screen.domain.vo;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.ziyun.common.annotation.ExcelDictFormat;
import com.ziyun.common.convert.ExcelDictConvert;
import lombok.Data;
import java.util.Date;



/**
 * 业务量总览视图对象 qsc_tax_biz_vol
 *
 * <AUTHOR>
 * @date 2022-12-04
 */
@Data
@ExcelIgnoreUnannotated
public class QscTaxBizVolVo {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @ExcelProperty(value = "ID")
    private Long id;

    /**
     * 时间维度(单位:月)
     */
    @ExcelProperty(value = "时间维度(单位:月)")
    private Date bizMonthly;

    /**
     * 业务总量
     */
    @ExcelProperty(value = "业务总量")
    private Long bizMonthlyVol;

    /**
     * 同比率(单位:%)
     */
    @ExcelProperty(value = "同比率(单位:%)")
    private String bizMonthlyRatio;

    /**
     * 用户ID
     */
    @ExcelProperty(value = "用户ID")
    private Long userId;

    /**
     * 部门ID
     */
    @ExcelProperty(value = "部门ID")
    private Long deptId;


}
