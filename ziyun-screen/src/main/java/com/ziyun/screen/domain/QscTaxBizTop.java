package com.ziyun.screen.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ziyun.common.core.domain.BaseEntity;

/**
 * 月度业务量排名对象 qsc_tax_biz_top
 *
 * <AUTHOR>
 * @date 2022-12-04
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("qsc_tax_biz_top")
public class QscTaxBizTop extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * ID
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 时间维度
     */
    private Date month;
    /**
     * 业务名称
     */
    private String bizName;
    /**
     * 业务量
     */
    private Long bizVol;
    /**
     * 业务量占比
     */
    private Long bizRatio;
    /**
     * 用户ID
     */
    private Long userId;
    /**
     * 部门ID
     */
    private Long deptId;

}
