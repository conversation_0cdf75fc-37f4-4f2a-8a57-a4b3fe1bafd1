package com.ziyun.screen.service;

import com.ziyun.screen.domain.QscThreeSrvTagAnalysis;
import com.ziyun.screen.domain.vo.QscThreeSrvTagAnalysisVo;
import com.ziyun.screen.domain.bo.QscThreeSrvTagAnalysisBo;
import com.ziyun.common.core.page.TableDataInfo;
import com.ziyun.common.core.domain.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 三服务标签Service接口
 *
 * <AUTHOR>
 * @date 2022-12-04
 */
public interface IQscThreeSrvTagAnalysisService {

    /**
     * 查询三服务标签
     */
    QscThreeSrvTagAnalysisVo queryById(Long id);

    /**
     * 查询三服务标签列表
     */
    TableDataInfo<QscThreeSrvTagAnalysisVo> queryPageList(QscThreeSrvTagAnalysisBo bo, PageQuery pageQuery);

    /**
     * 查询三服务标签列表
     */
    List<QscThreeSrvTagAnalysisVo> queryList(QscThreeSrvTagAnalysisBo bo);

    /**
     * 新增三服务标签
     */
    Boolean insertByBo(QscThreeSrvTagAnalysisBo bo);

    /**
     * 修改三服务标签
     */
    Boolean updateByBo(QscThreeSrvTagAnalysisBo bo);

    /**
     * 校验并批量删除三服务标签信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
