package com.ziyun.screen.service;

import com.ziyun.screen.domain.QscTaxCostSysInfo;
import com.ziyun.screen.domain.vo.QscTaxCostSysInfoVo;
import com.ziyun.screen.domain.bo.QscTaxCostSysInfoBo;
import com.ziyun.common.core.page.TableDataInfo;
import com.ziyun.common.core.domain.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 税费协同信息Service接口
 *
 * <AUTHOR>
 * @date 2022-12-04
 */
public interface IQscTaxCostSysInfoService {

    /**
     * 查询税费协同信息
     */
    QscTaxCostSysInfoVo queryById(Long id);

    /**
     * 查询税费协同信息列表
     */
    TableDataInfo<QscTaxCostSysInfoVo> queryPageList(QscTaxCostSysInfoBo bo, PageQuery pageQuery);

    /**
     * 查询税费协同信息列表
     */
    List<QscTaxCostSysInfoVo> queryList(QscTaxCostSysInfoBo bo);

    /**
     * 新增税费协同信息
     */
    Boolean insertByBo(QscTaxCostSysInfoBo bo);

    /**
     * 修改税费协同信息
     */
    Boolean updateByBo(QscTaxCostSysInfoBo bo);

    /**
     * 校验并批量删除税费协同信息信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
