package com.ziyun.screen.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.ziyun.common.utils.StringUtils;
import com.ziyun.common.core.page.TableDataInfo;
import com.ziyun.common.core.domain.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import com.ziyun.screen.domain.bo.QscTaxDealRateRankBo;
import com.ziyun.screen.domain.vo.QscTaxDealRateRankVo;
import com.ziyun.screen.domain.QscTaxDealRateRank;
import com.ziyun.screen.mapper.QscTaxDealRateRankMapper;
import com.ziyun.screen.service.IQscTaxDealRateRankService;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 办税率排名Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-12-04
 */
@RequiredArgsConstructor
@Service
@DS("master")
public class QscTaxDealRateRankServiceImpl implements IQscTaxDealRateRankService {

    private final QscTaxDealRateRankMapper baseMapper;

    /**
     * 查询办税率排名
     */
    @Override
    public QscTaxDealRateRankVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询办税率排名列表
     */
    @Override
    public TableDataInfo<QscTaxDealRateRankVo> queryPageList(QscTaxDealRateRankBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<QscTaxDealRateRank> lqw = buildQueryWrapper(bo);
        Page<QscTaxDealRateRankVo> result = baseMapper.customPageList(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询办税率排名列表
     */
    @Override
    public List<QscTaxDealRateRankVo> queryList(QscTaxDealRateRankBo bo) {
        LambdaQueryWrapper<QscTaxDealRateRank> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<QscTaxDealRateRank> buildQueryWrapper(QscTaxDealRateRankBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<QscTaxDealRateRank> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(bo.getDealName()), QscTaxDealRateRank::getDealName, bo.getDealName());
        lqw.eq(StringUtils.isNotBlank(bo.getDealRate()), QscTaxDealRateRank::getDealRate, bo.getDealRate());
        lqw.eq(StringUtils.isNotBlank(bo.getSectionRate()), QscTaxDealRateRank::getSectionRate, bo.getSectionRate());
        lqw.eq(StringUtils.isNotBlank(bo.getCityAvg()), QscTaxDealRateRank::getCityAvg, bo.getCityAvg());
        lqw.eq(bo.getListRank() != null, QscTaxDealRateRank::getListRank, bo.getListRank());
        lqw.eq(bo.getUserId() != null, QscTaxDealRateRank::getUserId, bo.getUserId());
        lqw.eq(bo.getDeptId() != null, QscTaxDealRateRank::getDeptId, bo.getDeptId());
        return lqw;
    }

    /**
     * 新增办税率排名
     */
    @Override
    public Boolean insertByBo(QscTaxDealRateRankBo bo) {
        QscTaxDealRateRank add = BeanUtil.toBean(bo, QscTaxDealRateRank.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改办税率排名
     */
    @Override
    public Boolean updateByBo(QscTaxDealRateRankBo bo) {
        QscTaxDealRateRank update = BeanUtil.toBean(bo, QscTaxDealRateRank.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(QscTaxDealRateRank entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除办税率排名
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }
}
