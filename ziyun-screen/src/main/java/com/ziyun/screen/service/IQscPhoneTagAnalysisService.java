package com.ziyun.screen.service;

import com.ziyun.screen.domain.QscPhoneTagAnalysis;
import com.ziyun.screen.domain.vo.QscPhoneTagAnalysisVo;
import com.ziyun.screen.domain.bo.QscPhoneTagAnalysisBo;
import com.ziyun.common.core.page.TableDataInfo;
import com.ziyun.common.core.domain.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 电话热点咨询Service接口
 *
 * <AUTHOR>
 * @date 2022-12-04
 */
public interface IQscPhoneTagAnalysisService {

    /**
     * 查询电话热点咨询
     */
    QscPhoneTagAnalysisVo queryById(Long id);

    /**
     * 查询电话热点咨询列表
     */
    TableDataInfo<QscPhoneTagAnalysisVo> queryPageList(QscPhoneTagAnalysisBo bo, PageQuery pageQuery);

    /**
     * 查询电话热点咨询列表
     */
    List<QscPhoneTagAnalysisVo> queryList(QscPhoneTagAnalysisBo bo);

    /**
     * 新增电话热点咨询
     */
    Boolean insertByBo(QscPhoneTagAnalysisBo bo);

    /**
     * 修改电话热点咨询
     */
    Boolean updateByBo(QscPhoneTagAnalysisBo bo);

    /**
     * 校验并批量删除电话热点咨询信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
