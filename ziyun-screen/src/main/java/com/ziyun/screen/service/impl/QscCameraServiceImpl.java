package com.ziyun.screen.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.ziyun.common.utils.StringUtils;
import com.ziyun.common.core.page.TableDataInfo;
import com.ziyun.common.core.domain.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import com.ziyun.screen.domain.bo.QscCameraBo;
import com.ziyun.screen.domain.vo.QscCameraVo;
import com.ziyun.screen.domain.QscCamera;
import com.ziyun.screen.mapper.QscCameraMapper;
import com.ziyun.screen.service.IQscCameraService;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 【请填写功能名称】Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-12-04
 */
@RequiredArgsConstructor
@Service
@DS("master")
public class QscCameraServiceImpl implements IQscCameraService {

    private final QscCameraMapper baseMapper;

    /**
     * 查询【请填写功能名称】
     */
    @Override
    public QscCameraVo queryById(Long cameraId){
        return baseMapper.selectVoById(cameraId);
    }

    /**
     * 查询【请填写功能名称】列表
     */
    @Override
    public TableDataInfo<QscCameraVo> queryPageList(QscCameraBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<QscCamera> lqw = buildQueryWrapper(bo);
        Page<QscCameraVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询【请填写功能名称】列表
     */
    @Override
    public List<QscCameraVo> queryList(QscCameraBo bo) {
        LambdaQueryWrapper<QscCamera> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<QscCamera> buildQueryWrapper(QscCameraBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<QscCamera> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getIp()), QscCamera::getIp, bo.getIp());
        lqw.eq(bo.getPort() != null, QscCamera::getPort, bo.getPort());
        lqw.like(StringUtils.isNotBlank(bo.getUsername()), QscCamera::getUsername, bo.getUsername());
        lqw.eq(StringUtils.isNotBlank(bo.getPassword()), QscCamera::getPassword, bo.getPassword());
        lqw.eq(StringUtils.isNotBlank(bo.getUrl()), QscCamera::getUrl, bo.getUrl());
        lqw.eq(StringUtils.isNotBlank(bo.getCameraProduce()), QscCamera::getCameraProduce, bo.getCameraProduce());
        lqw.like(StringUtils.isNotBlank(bo.getCameraName()), QscCamera::getCameraName, bo.getCameraName());
        lqw.eq(StringUtils.isNotBlank(bo.getDeviceType()), QscCamera::getDeviceType, bo.getDeviceType());
        lqw.eq(StringUtils.isNotBlank(bo.getUnitCode()), QscCamera::getUnitCode, bo.getUnitCode());
        lqw.eq(StringUtils.isNotBlank(bo.getNvrProduce()), QscCamera::getNvrProduce, bo.getNvrProduce());
        lqw.eq(StringUtils.isNotBlank(bo.getNvrPath()), QscCamera::getNvrPath, bo.getNvrPath());
        lqw.eq(StringUtils.isNotBlank(bo.getPlayBack()), QscCamera::getPlayBack, bo.getPlayBack());
        lqw.eq(bo.getUserId() != null, QscCamera::getUserId, bo.getUserId());
        lqw.eq(bo.getDeptId() != null, QscCamera::getDeptId, bo.getDeptId());
        return lqw;
    }

    /**
     * 新增【请填写功能名称】
     */
    @Override
    public Boolean insertByBo(QscCameraBo bo) {
        QscCamera add = BeanUtil.toBean(bo, QscCamera.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setCameraId(add.getCameraId());
        }
        return flag;
    }

    /**
     * 修改【请填写功能名称】
     */
    @Override
    public Boolean updateByBo(QscCameraBo bo) {
        QscCamera update = BeanUtil.toBean(bo, QscCamera.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(QscCamera entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除【请填写功能名称】
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }
}
