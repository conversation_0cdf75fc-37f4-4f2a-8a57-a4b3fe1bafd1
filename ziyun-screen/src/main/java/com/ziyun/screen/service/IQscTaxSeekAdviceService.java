package com.ziyun.screen.service;

import com.ziyun.screen.domain.QscTaxSeekAdvice;
import com.ziyun.screen.domain.vo.QscTaxSeekAdviceVo;
import com.ziyun.screen.domain.bo.QscTaxSeekAdviceBo;
import com.ziyun.common.core.page.TableDataInfo;
import com.ziyun.common.core.domain.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 咨询热点Service接口
 *
 * <AUTHOR>
 * @date 2022-12-04
 */
public interface IQscTaxSeekAdviceService {

    /**
     * 查询咨询热点
     */
    QscTaxSeekAdviceVo queryById(Long id);

    /**
     * 查询咨询热点列表
     */
    TableDataInfo<QscTaxSeekAdviceVo> queryPageList(QscTaxSeekAdviceBo bo, PageQuery pageQuery);

    /**
     * 查询咨询热点列表
     */
    List<QscTaxSeekAdviceVo> queryList(QscTaxSeekAdviceBo bo);

    /**
     * 新增咨询热点
     */
    Boolean insertByBo(QscTaxSeekAdviceBo bo);

    /**
     * 修改咨询热点
     */
    Boolean updateByBo(QscTaxSeekAdviceBo bo);

    /**
     * 校验并批量删除咨询热点信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
