package com.ziyun.screen.service;

import com.ziyun.screen.domain.QscMapUnitPoint;
import com.ziyun.screen.domain.vo.QscMapUnitPointVo;
import com.ziyun.screen.domain.bo.QscMapUnitPointBo;
import com.ziyun.common.core.page.TableDataInfo;
import com.ziyun.common.core.domain.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 地图点位信息Service接口
 *
 * <AUTHOR>
 * @date 2022-12-04
 */
public interface IQscMapUnitPointService {

    /**
     * 查询地图点位信息
     */
    QscMapUnitPointVo queryById(Long id);

    /**
     * 查询地图点位信息列表
     */
    TableDataInfo<QscMapUnitPointVo> queryPageList(QscMapUnitPointBo bo, PageQuery pageQuery);

    /**
     * 查询地图点位信息列表
     */
    List<QscMapUnitPointVo> queryList(QscMapUnitPointBo bo);

    /**
     * 新增地图点位信息
     */
    Boolean insertByBo(QscMapUnitPointBo bo);

    /**
     * 修改地图点位信息
     */
    Boolean updateByBo(QscMapUnitPointBo bo);

    /**
     * 校验并批量删除地图点位信息信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
