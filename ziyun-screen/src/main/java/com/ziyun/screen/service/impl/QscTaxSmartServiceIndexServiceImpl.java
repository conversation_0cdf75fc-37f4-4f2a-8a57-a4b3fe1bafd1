package com.ziyun.screen.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.ziyun.common.utils.StringUtils;
import com.ziyun.common.core.page.TableDataInfo;
import com.ziyun.common.core.domain.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import com.ziyun.screen.domain.bo.QscTaxSmartServiceIndexBo;
import com.ziyun.screen.domain.vo.QscTaxSmartServiceIndexVo;
import com.ziyun.screen.domain.QscTaxSmartServiceIndex;
import com.ziyun.screen.mapper.QscTaxSmartServiceIndexMapper;
import com.ziyun.screen.service.IQscTaxSmartServiceIndexService;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 智慧办税服务指数Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-11-08
 */
@RequiredArgsConstructor
@Service
@DS("master")
public class QscTaxSmartServiceIndexServiceImpl implements IQscTaxSmartServiceIndexService {

    private final QscTaxSmartServiceIndexMapper baseMapper;

    /**
     * 查询智慧办税服务指数
     */
    @Override
    public QscTaxSmartServiceIndexVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询智慧办税服务指数列表
     */
    @Override
    public TableDataInfo<QscTaxSmartServiceIndexVo> queryPageList(QscTaxSmartServiceIndexBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<QscTaxSmartServiceIndex> lqw = buildQueryWrapper(bo);
        Page<QscTaxSmartServiceIndexVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询智慧办税服务指数列表
     */
    @Override
    public List<QscTaxSmartServiceIndexVo> queryList(QscTaxSmartServiceIndexBo bo) {
        LambdaQueryWrapper<QscTaxSmartServiceIndex> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<QscTaxSmartServiceIndex> buildQueryWrapper(QscTaxSmartServiceIndexBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<QscTaxSmartServiceIndex> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(bo.getIndexName()), QscTaxSmartServiceIndex::getIndexName, bo.getIndexName());
        lqw.eq(StringUtils.isNotBlank(bo.getIndexValue()), QscTaxSmartServiceIndex::getIndexValue, bo.getIndexValue());
        lqw.eq(StringUtils.isNotBlank(bo.getIndexLeft()), QscTaxSmartServiceIndex::getIndexLeft, bo.getIndexLeft());
        lqw.eq(StringUtils.isNotBlank(bo.getIdnexTop()), QscTaxSmartServiceIndex::getIdnexTop, bo.getIdnexTop());
        return lqw;
    }

    /**
     * 新增智慧办税服务指数
     */
    @Override
    public Boolean insertByBo(QscTaxSmartServiceIndexBo bo) {
        QscTaxSmartServiceIndex add = BeanUtil.toBean(bo, QscTaxSmartServiceIndex.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改智慧办税服务指数
     */
    @Override
    public Boolean updateByBo(QscTaxSmartServiceIndexBo bo) {
        QscTaxSmartServiceIndex update = BeanUtil.toBean(bo, QscTaxSmartServiceIndex.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(QscTaxSmartServiceIndex entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除智慧办税服务指数
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }
}
