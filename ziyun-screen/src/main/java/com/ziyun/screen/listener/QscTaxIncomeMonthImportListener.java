package com.ziyun.screen.listener;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.ziyun.common.excel.ExcelListener;
import com.ziyun.common.excel.ExcelResult;
import com.ziyun.common.exception.ServiceException;
import com.ziyun.common.helper.LoginHelper;
import com.ziyun.common.utils.ValidatorUtils;
import com.ziyun.common.utils.spring.SpringUtils;
import com.ziyun.screen.domain.bo.QscTaxIncomeMonthBo;
import com.ziyun.screen.domain.vo.QscTaxIncomeMonthImportVo;
import com.ziyun.screen.service.IQscTaxIncomeMonthService;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

@Slf4j
public class QscTaxIncomeMonthImportListener extends AnalysisEventListener<QscTaxIncomeMonthImportVo> implements ExcelListener<QscTaxIncomeMonthImportVo> {

    private final IQscTaxIncomeMonthService incomeMonthService;

    private final Boolean isUpdateSupport;


    private int successNum = 0;
    private int failureNum = 0;

    private final StringBuilder successMsg = new StringBuilder();
    private final StringBuilder failureMsg = new StringBuilder();

    public QscTaxIncomeMonthImportListener(Boolean isUpdateSupport) {
        this.incomeMonthService = SpringUtils.getBean(IQscTaxIncomeMonthService.class);
        this.isUpdateSupport = isUpdateSupport;
    }

    @Override
    public void invoke(QscTaxIncomeMonthImportVo inOutImportVo, AnalysisContext analysisContext) {

        QscTaxIncomeMonthBo inOut = this.incomeMonthService.queryByDate(inOutImportVo.getIncomeMonth());
        try {
            // 验证是否存
            if (ObjectUtil.isNull(inOut)) {
                inOut = BeanUtil.toBean(inOutImportVo, QscTaxIncomeMonthBo.class);
                ValidatorUtils.validate(inOut);
                inOut.setUserId(LoginHelper.getLoginUser().getUserId());
                inOut.setDeptId(LoginHelper.getLoginUser().getDeptId());
                incomeMonthService.insertByBo(inOut);
                successNum++;
                successMsg.append("<br/>").append(successNum).append("、月份数据 ").append(inOut.getIncomeMonth()).append(" 导入成功");
            } else if (isUpdateSupport) {
                inOut = BeanUtil.toBean(inOutImportVo, QscTaxIncomeMonthBo.class);
                inOut.setUserId(LoginHelper.getUserId());
                inOut.setDeptId(LoginHelper.getDeptId());
                ValidatorUtils.validate(inOut);
                incomeMonthService.updateByBo(inOut);
                successNum++;
                successMsg.append("<br/>").append(successNum).append("、月份数据 ").append(inOut.getIncomeMonth()).append(" 更新成功");
            } else {
                failureNum++;
                failureMsg.append("<br/>").append(failureNum).append("、月份数据 ").append(inOut.getIncomeMonth()).append(" 已存在");
            }
        } catch (Exception e) {
            failureNum++;
            String msg = "<br/>" + failureNum + "、月份数据 " + inOut.getIncomeMonth() + " 导入失败：";
            failureMsg.append(msg).append(e.getMessage());
            log.error(msg, e);
        }
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {

    }

    @Override
    public ExcelResult<QscTaxIncomeMonthImportVo> getExcelResult() {
        return new ExcelResult<QscTaxIncomeMonthImportVo>() {
            @Override
            public List<QscTaxIncomeMonthImportVo> getList() {
                return null;
            }

            @Override
            public List<String> getErrorList() {
                return null;
            }

            @Override
            public String getAnalysis() {
                if (failureNum > 0) {
                    failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
                    throw new ServiceException(failureMsg.toString());
                } else {
                    successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
                }
                return successMsg.toString();
            }
        };
    }
}
