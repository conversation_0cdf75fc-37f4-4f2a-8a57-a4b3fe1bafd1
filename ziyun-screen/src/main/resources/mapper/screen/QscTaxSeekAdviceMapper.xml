<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ziyun.screen.mapper.QscTaxSeekAdviceMapper">

    <resultMap type="com.ziyun.screen.domain.QscTaxSeekAdvice" id="QscTaxSeekAdviceResult">
        <result property="id" column="id"/>
        <result property="dayCount" column="day_count"/>
        <result property="olDayCount" column="ol_day_count"/>
        <result property="monthCount" column="month_count"/>
        <result property="olMonthCount" column="ol_month_count"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="userId" column="user_id"/>
        <result property="deptId" column="dept_id"/>
    </resultMap>


</mapper>
