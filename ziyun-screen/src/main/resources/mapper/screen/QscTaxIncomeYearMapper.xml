<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ziyun.screen.mapper.QscTaxIncomeYearMapper">

    <resultMap type="com.ziyun.screen.domain.QscTaxIncomeYear" id="QscTaxIncomeYearResult">
        <result property="id" column="id"/>
        <result property="incomeYear" column="income_year"/>
        <result property="income" column="income"/>
        <result property="rate" column="rate"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="userId" column="user_id"/>
        <result property="deptId" column="dept_id"/>
    </resultMap>

    <select id="customPageList" resultType="com.ziyun.screen.domain.vo.QscTaxIncomeYearVo">
        SELECT * FROM qsc_tax_income_year ${ew.customSqlSegment}
    </select>
</mapper>
