# 服务端口
PORT=8080

# 运行环境 (development/production)
ENVIRONMENT=development

# 数据库连接配置
# 格式: username:password@tcp(host:port)/database?charset=utf8mb4&parseTime=True&loc=Local
DATABASE_URL=root:password@tcp(localhost:3306)/calling_system?charset=utf8mb4&parseTime=True&loc=Local

# TTS引擎配置 (espeak/festival/say/mock)
TTS_ENGINE=mock

# 音频文件目录
AUDIO_DIR=./audio

# 示例配置说明:
# 1. 请根据实际情况修改数据库连接信息
# 2. TTS_ENGINE设置为mock可以在没有TTS引擎的环境下运行
# 3. 复制此文件为.env并修改相应配置