package service

import (
	"fmt"
	"log"
	"time"

	"go-calling-service/internal/model"
)

// CallingService 呼叫服务
type CallingService struct {
	ledService   *LedService
	voiceService *VoiceService
}

// NewCallingService 创建呼叫服务实例
func NewCallingService(ledService *LedService, voiceService *VoiceService) *CallingService {
	return &CallingService{
		ledService:   ledService,
		voiceService: voiceService,
	}
}

// ProcessCall 处理呼叫请求
func (s *CallingService) ProcessCall(windowNo, ticketNo, businessType string) error {
	log.Printf("处理呼叫请求: 窗口=%s, 票号=%s, 业务类型=%s", windowNo, ticketNo, businessType)

	// 发送到LED显示屏
	if err := s.ledService.SendCallInfoToLed(windowNo, ticketNo, businessType); err != nil {
		log.Printf("LED显示失败: %v", err)
		// LED失败不影响语音播报
	}

	// 添加语音播报任务
	if err := s.voiceService.AddVoiceTask(windowNo, ticketNo, businessType); err != nil {
		log.Printf("语音播报失败: %v", err)
		return fmt.Errorf("语音播报失败: %v", err)
	}

	log.Printf("呼叫处理完成: 窗口=%s, 票号=%s", windowNo, ticketNo)
	return nil
}

// ProcessComplete 处理完成服务
func (s *CallingService) ProcessComplete(windowNo, ticketNo, businessType string) error {
	log.Printf("处理完成服务: 窗口=%s, 票号=%s, 业务类型=%s", windowNo, ticketNo, businessType)

	// 可以在这里添加完成服务的特定逻辑
	// 比如清除LED显示、记录服务完成时间等

	return nil
}

// ProcessRecall 处理重新呼叫
func (s *CallingService) ProcessRecall(windowNo, ticketNo, businessType string) error {
	log.Printf("处理重新呼叫: 窗口=%s, 票号=%s, 业务类型=%s", windowNo, ticketNo, businessType)

	// 重新呼叫与普通呼叫处理相同
	return s.ProcessCall(windowNo, ticketNo, businessType)
}

// CreateCallingMessage 创建呼叫消息
func (s *CallingService) CreateCallingMessage(msgType, windowNo, ticketNo, businessType string) *model.CallingMessage {
	return &model.CallingMessage{
		Type:         msgType,
		WindowNo:     windowNo,
		TicketNo:     ticketNo,
		BusinessType: businessType,
		Timestamp:    time.Now().UnixMilli(),
		CreatedAt:    time.Now(),
	}
}

// GetServiceStatus 获取服务状态
func (s *CallingService) GetServiceStatus() map[string]interface{} {
	status := make(map[string]interface{})

	// 语音服务状态
	status["voice_service"] = map[string]interface{}{
		"running":     s.voiceService.IsRunning(),
		"queue_size":  s.voiceService.GetQueueSize(),
		"tts_engine":  s.voiceService.GetTTSEngine(),
	}

	// LED设备状态
	devices, err := s.ledService.GetEnabledDevices()
	if err != nil {
		status["led_service"] = map[string]interface{}{
			"error": err.Error(),
		}
	} else {
		status["led_service"] = map[string]interface{}{
			"enabled_devices": len(devices),
			"devices":         devices,
		}
	}

	status["timestamp"] = time.Now().Unix()
	return status
}

// TestServices 测试服务功能
func (s *CallingService) TestServices() error {
	// 测试语音服务
	if !s.voiceService.IsRunning() {
		return fmt.Errorf("语音服务未运行")
	}

	// 测试LED服务
	devices, err := s.ledService.GetEnabledDevices()
	if err != nil {
		return fmt.Errorf("获取LED设备失败: %v", err)
	}

	// 测试设备连接
	for _, device := range devices {
		if err := s.ledService.TestDeviceConnection(device); err != nil {
			log.Printf("LED设备连接测试失败 %s:%d - %v", device.IPAddress, device.Port, err)
		}
	}

	return nil
}

// BatchCall 批量呼叫
func (s *CallingService) BatchCall(calls []model.CallRequest) []error {
	var errors []error

	for _, call := range calls {
		if err := s.ProcessCall(call.WindowNo, call.TicketNo, call.BusinessType); err != nil {
			errors = append(errors, fmt.Errorf("窗口%s票号%s呼叫失败: %v", call.WindowNo, call.TicketNo, err))
		} else {
			errors = append(errors, nil)
		}
		// 批量呼叫间隔
		time.Sleep(1 * time.Second)
	}

	return errors
}