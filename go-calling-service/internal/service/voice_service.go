package service

import (
	"fmt"
	"log"
	"os"
	"os/exec"
	"path/filepath"
	"sync"
	"time"

	"go-calling-service/internal/config"
	"go-calling-service/internal/model"
)

// VoiceService 语音播报服务
type VoiceService struct {
	voiceQueue chan model.VoiceMessage
	audioDir   string
	ttsEngine  string
	isRunning  bool
	stopChan   chan struct{}
	wg         sync.WaitGroup
	mu         sync.RWMutex
}

// NewVoiceService 创建语音服务实例
func NewVoiceService(config *config.Config) *VoiceService {
	return &VoiceService{
		voiceQueue: make(chan model.VoiceMessage, 100),
		audioDir:   config.AudioDir,
		ttsEngine:  "hybrid", // 直接设置为hybrid模式
		stopChan:   make(chan struct{}),
	}
}

// Start 启动语音服务
func (s *VoiceService) Start() {
	s.mu.Lock()
	defer s.mu.Unlock()

	if s.isRunning {
		log.Println("语音服务已经在运行中")
		return
	}

	// 确保音频目录存在
	if err := os.MkdirAll(s.audioDir, 0755); err != nil {
		log.Printf("创建音频目录失败: %v", err)
	}

	// 检测可用的TTS引擎
	s.detectTTSEngine()

	s.isRunning = true
	s.wg.Add(1)

	// 启动语音处理协程
	go s.processVoiceQueue()

	log.Println("语音播报服务已启动")
}

// Stop 停止语音服务
func (s *VoiceService) Stop() {
	s.mu.Lock()
	defer s.mu.Unlock()

	if !s.isRunning {
		return
	}

	s.isRunning = false
	close(s.stopChan)
	s.wg.Wait()

	log.Println("语音播报服务已停止")
}

// AddVoiceTask 添加语音播报任务
func (s *VoiceService) AddVoiceTask(windowNo, ticketNo, businessType string) error {
	s.mu.RLock()
	defer s.mu.RUnlock()

	if !s.isRunning {
		return fmt.Errorf("语音服务未启动")
	}

	message := model.VoiceMessage{
		WindowNo:     windowNo,
		TicketNo:     ticketNo,
		BusinessType: businessType,
		CreatedAt:    time.Now(),
	}

	select {
	case s.voiceQueue <- message:
		log.Printf("成功添加语音播报任务: 窗口%s, 票号%s", windowNo, ticketNo)
		return nil
	default:
		return fmt.Errorf("语音队列已满，无法添加任务")
	}
}

// processVoiceQueue 处理语音播报队列
func (s *VoiceService) processVoiceQueue() {
	defer s.wg.Done()

	for {
		select {
		case <-s.stopChan:
			return
		case message := <-s.voiceQueue:
			s.playVoice(message)
			// 播报间隔
			time.Sleep(3 * time.Second)
		}
	}
}

// playVoice 播放语音
func (s *VoiceService) playVoice(message model.VoiceMessage) {
	// 构建语音内容
	voiceText := fmt.Sprintf("%s号窗口请%s号客户", message.WindowNo, message.TicketNo)

	log.Printf("准备播放语音: %s", voiceText)

	switch s.ttsEngine {
	case "espeak":
		s.playWithEspeak(voiceText)
	case "festival":
		s.playWithFestival(voiceText)
	case "say": // macOS
		s.playWithSay(voiceText)
	default:
		// 模拟播放
		s.simulatePlay(voiceText)
	}
}

// detectTTSEngine 检测可用的TTS引擎
func (s *VoiceService) detectTTSEngine() {
	// 检测espeak
	if s.isCommandAvailable("espeak") {
		s.ttsEngine = "espeak"
		log.Println("检测到espeak TTS引擎")
		return
	}

	// 检测festival
	if s.isCommandAvailable("festival") {
		s.ttsEngine = "festival"
		log.Println("检测到festival TTS引擎")
		return
	}

	// 检测say (macOS)
	if s.isCommandAvailable("say") {
		s.ttsEngine = "say"
		log.Println("检测到say TTS引擎 (macOS)")
		return
	}

	// 没有检测到TTS引擎，使用模拟模式
	s.ttsEngine = "simulate"
	log.Println("未检测到TTS引擎，使用模拟模式")
}

// isCommandAvailable 检查命令是否可用
func (s *VoiceService) isCommandAvailable(command string) bool {
	_, err := exec.LookPath(command)
	return err == nil
}

// playWithEspeak 使用espeak播放语音
func (s *VoiceService) playWithEspeak(text string) {
	cmd := exec.Command("espeak", "-s", "150", "-v", "zh", text)
	if err := cmd.Run(); err != nil {
		log.Printf("espeak播放失败: %v", err)
		// 降级到模拟播放
		s.simulatePlay(text)
	} else {
		log.Printf("espeak播放成功: %s", text)
	}
}

// playWithFestival 使用festival播放语音
func (s *VoiceService) playWithFestival(text string) {
	// 创建临时文件
	tempFile := filepath.Join(s.audioDir, fmt.Sprintf("temp_%d.txt", time.Now().UnixNano()))
	if err := os.WriteFile(tempFile, []byte(text), 0644); err != nil {
		log.Printf("创建临时文件失败: %v", err)
		s.simulatePlay(text)
		return
	}
	defer os.Remove(tempFile)

	cmd := exec.Command("festival", "--tts", tempFile)
	if err := cmd.Run(); err != nil {
		log.Printf("festival播放失败: %v", err)
		// 降级到模拟播放
		s.simulatePlay(text)
	} else {
		log.Printf("festival播放成功: %s", text)
	}
}

// playWithSay 使用say播放语音 (macOS)
func (s *VoiceService) playWithSay(text string) {
	cmd := exec.Command("say", "-v", "Ting-Ting", text)
	if err := cmd.Run(); err != nil {
		log.Printf("say播放失败: %v", err)
		// 降级到模拟播放
		s.simulatePlay(text)
	} else {
		log.Printf("say播放成功: %s", text)
	}
}

// simulatePlay 模拟播放语音
func (s *VoiceService) simulatePlay(text string) {
	log.Printf("模拟播放语音: %s", text)
	// 模拟播放时间
	time.Sleep(2 * time.Second)
	log.Printf("模拟播放完成: %s", text)
}

// GetQueueSize 获取队列大小
func (s *VoiceService) GetQueueSize() int {
	return len(s.voiceQueue)
}

// IsRunning 检查服务是否运行中
func (s *VoiceService) IsRunning() bool {
	s.mu.RLock()
	defer s.mu.RUnlock()
	return s.isRunning
}

// SetTTSEngine 设置TTS引擎
func (s *VoiceService) SetTTSEngine(engine string) {
	s.mu.Lock()
	defer s.mu.Unlock()
	s.ttsEngine = engine
	log.Printf("TTS引擎已设置为: %s", engine)
}

// GetTTSEngine 获取当前TTS引擎
func (s *VoiceService) GetTTSEngine() string {
	s.mu.RLock()
	defer s.mu.RUnlock()
	return s.ttsEngine
}