package handler

import (
	"log"
	"net/http"
	"strconv"
	"time"

	"go-calling-service/internal/model"
	"go-calling-service/internal/service"

	"github.com/gin-gonic/gin"
	"github.com/gorilla/websocket"
)

// Handlers HTTP处理器集合
type Handlers struct {
	callingService *service.CallingService
	ledService     *service.LedService
	voiceService   *service.VoiceService
	upgrader       websocket.Upgrader
	clients        map[*websocket.Conn]bool
}

// NewHandlers 创建处理器实例
func NewHandlers(callingService *service.CallingService, ledService *service.LedService, voiceService *service.VoiceService) *Handlers {
	return &Handlers{
		callingService: callingService,
		ledService:     ledService,
		voiceService:   voiceService,
		upgrader: websocket.Upgrader{
			CheckOrigin: func(r *http.Request) bool {
				return true // 允许所有来源，生产环境应该限制
			},
		},
		clients: make(map[*websocket.Conn]bool),
	}
}

// HandleCall 处理呼叫请求
func (h *Handlers) HandleCall(c *gin.Context) {
	var req model.CallRequest
	log.Printf("收到呼叫请求: %v", req)
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, model.NewErrorResponse(400, "请求参数错误: "+err.Error()))
		return
	}

	// 处理呼叫
	if err := h.callingService.ProcessCall(req.WindowNo, req.TicketNo, req.BusinessType); err != nil {
		c.JSON(http.StatusInternalServerError, model.NewErrorResponse(500, "呼叫处理失败: "+err.Error()))
		return
	}

	// 创建呼叫消息
	message := h.callingService.CreateCallingMessage("CALL", req.WindowNo, req.TicketNo, req.BusinessType)

	// 广播WebSocket消息
	h.broadcastMessage(message)

	c.JSON(http.StatusOK, model.NewSuccessResponse(map[string]string{
		"message": "呼叫成功",
		"window":  req.WindowNo,
		"ticket":  req.TicketNo,
	}))
}

// HandleComplete 处理完成服务请求
func (h *Handlers) HandleComplete(c *gin.Context) {
	var req model.CallRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, model.NewErrorResponse(400, "请求参数错误: "+err.Error()))
		return
	}

	// 处理完成服务
	if err := h.callingService.ProcessComplete(req.WindowNo, req.TicketNo, req.BusinessType); err != nil {
		c.JSON(http.StatusInternalServerError, model.NewErrorResponse(500, "完成服务处理失败: "+err.Error()))
		return
	}

	// 创建完成消息
	message := h.callingService.CreateCallingMessage("COMPLETE", req.WindowNo, req.TicketNo, req.BusinessType)

	// 广播WebSocket消息
	h.broadcastMessage(message)

	c.JSON(http.StatusOK, model.NewSuccessResponse(map[string]string{
		"message": "服务完成",
		"window":  req.WindowNo,
		"ticket":  req.TicketNo,
	}))
}

// HandleRecall 处理重新呼叫请求
func (h *Handlers) HandleRecall(c *gin.Context) {
	var req model.CallRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, model.NewErrorResponse(400, "请求参数错误: "+err.Error()))
		return
	}

	// 处理重新呼叫
	if err := h.callingService.ProcessRecall(req.WindowNo, req.TicketNo, req.BusinessType); err != nil {
		c.JSON(http.StatusInternalServerError, model.NewErrorResponse(500, "重新呼叫处理失败: "+err.Error()))
		return
	}

	// 创建重新呼叫消息
	message := h.callingService.CreateCallingMessage("RECALL", req.WindowNo, req.TicketNo, req.BusinessType)

	// 广播WebSocket消息
	h.broadcastMessage(message)

	c.JSON(http.StatusOK, model.NewSuccessResponse(map[string]string{
		"message": "重新呼叫成功",
		"window":  req.WindowNo,
		"ticket":  req.TicketNo,
	}))
}

// HandleTTSTest 处理TTS测试请求
func (h *Handlers) HandleTTSTest(c *gin.Context) {
	windowNo := c.DefaultQuery("windowNo", "1")
	ticketNo := c.DefaultQuery("ticketNo", "A001")

	log.Printf("收到TTS测试请求: 窗口=%s, 票号=%s", windowNo, ticketNo)

	// 添加语音播报任务
	if err := h.voiceService.AddVoiceTask(windowNo, ticketNo, "测试"); err != nil {
		c.JSON(http.StatusInternalServerError, model.NewErrorResponse(500, "添加语音播报任务失败: "+err.Error()))
		return
	}

	c.JSON(http.StatusOK, model.NewSuccessResponse(map[string]string{
		"message": "语音播报任务已添加: " + windowNo + "号窗口请" + ticketNo + "号客户",
		"window":  windowNo,
		"ticket":  ticketNo,
	}))
}

// HandleGetLedDevices 获取LED设备列表
func (h *Handlers) HandleGetLedDevices(c *gin.Context) {
	devices, err := h.ledService.GetAllDevices()
	if err != nil {
		c.JSON(http.StatusInternalServerError, model.NewErrorResponse(500, "获取LED设备列表失败: "+err.Error()))
		return
	}

	c.JSON(http.StatusOK, model.NewSuccessResponse(devices))
}

// HandleUpdateLedDeviceStatus 更新LED设备状态
func (h *Handlers) HandleUpdateLedDeviceStatus(c *gin.Context) {
	// 获取设备ID
	deviceIDStr := c.Param("id")
	deviceID, err := strconv.ParseInt(deviceIDStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, model.NewErrorResponse(400, "设备ID格式错误"))
		return
	}

	// 解析请求体
	var req model.UpdateDeviceStatusRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, model.NewErrorResponse(400, "请求参数错误: "+err.Error()))
		return
	}

	// 更新设备状态
	if err := h.ledService.UpdateDeviceStatus(deviceID, req.Enabled); err != nil {
		c.JSON(http.StatusInternalServerError, model.NewErrorResponse(500, "更新设备状态失败: "+err.Error()))
		return
	}

	c.JSON(http.StatusOK, model.NewSuccessResponse(map[string]interface{}{
		"message":   "设备状态更新成功",
		"device_id": deviceID,
		"enabled":   req.Enabled,
	}))
}

// HandleWebSocket 处理WebSocket连接
func (h *Handlers) HandleWebSocket(c *gin.Context) {
	// 升级HTTP连接为WebSocket
	conn, err := h.upgrader.Upgrade(c.Writer, c.Request, nil)
	if err != nil {
		log.Printf("WebSocket升级失败: %v", err)
		return
	}
	defer conn.Close()

	// 注册客户端
	h.clients[conn] = true
	log.Printf("新的WebSocket连接建立: %s", conn.RemoteAddr())

	// 发送欢迎消息
	welcomeMsg := model.WebSocketMessage{
		Type:      "WELCOME",
		Payload:   "连接成功",
		Timestamp: time.Now().UnixMilli(),
	}
	conn.WriteJSON(welcomeMsg)

	// 处理消息
	for {
		var message model.WebSocketMessage
		err := conn.ReadJSON(&message)
		if err != nil {
			log.Printf("读取WebSocket消息失败: %v", err)
			break
		}

		// 处理心跳
		if message.Type == "HEARTBEAT" {
			heartbeatResp := model.WebSocketMessage{
				Type:      "HEARTBEAT_ACK",
				Timestamp: time.Now().UnixMilli(),
			}
			conn.WriteJSON(heartbeatResp)
			continue
		}

		// 处理其他消息类型
		h.processWebSocketMessage(message, conn)
	}

	// 移除客户端
	delete(h.clients, conn)
	log.Printf("WebSocket连接关闭: %s", conn.RemoteAddr())
}

// processWebSocketMessage 处理WebSocket消息
func (h *Handlers) processWebSocketMessage(message model.WebSocketMessage, conn *websocket.Conn) {
	log.Printf("收到WebSocket消息: 类型=%s, 时间戳=%d", message.Type, message.Timestamp)

	// 根据消息类型处理
	switch message.Type {
	case "DATA":
		// 处理数据消息
		if payload, ok := message.Payload.(map[string]interface{}); ok {
			windowNo, _ := payload["windowNo"].(string)
			ticketNo, _ := payload["ticketNo"].(string)
			businessType, _ := payload["businessType"].(string)

			if windowNo != "" && ticketNo != "" {
				// 处理呼叫
				if err := h.callingService.ProcessCall(windowNo, ticketNo, businessType); err != nil {
					log.Printf("WebSocket呼叫处理失败: %v", err)
				}
			} else {
				log.Println("WebSocket消息内容不完整")
			}
		}
	default:
		log.Printf("未知的WebSocket消息类型: %s", message.Type)
	}
}

// broadcastMessage 广播消息到所有WebSocket客户端
func (h *Handlers) broadcastMessage(message interface{}) {
	for conn := range h.clients {
		if err := conn.WriteJSON(message); err != nil {
			log.Printf("广播消息失败: %v", err)
			// 移除失效的连接
			conn.Close()
			delete(h.clients, conn)
		}
	}
}

// GetServiceStatus 获取服务状态
func (h *Handlers) GetServiceStatus(c *gin.Context) {
	status := h.callingService.GetServiceStatus()
	status["websocket_clients"] = len(h.clients)
	c.JSON(http.StatusOK, model.NewSuccessResponse(status))
}
