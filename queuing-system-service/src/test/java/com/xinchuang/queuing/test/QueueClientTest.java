//package com.xinchuang.queuing.test;
//
//import org.junit.jupiter.api.BeforeEach;
//import org.junit.jupiter.api.Test;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.boot.test.web.client.TestRestTemplate;
//import org.springframework.http.ResponseEntity;
//import org.springframework.web.socket.client.standard.StandardWebSocketClient;
//import org.springframework.web.socket.messaging.WebSocketStompClient;
//
//import java.util.concurrent.CountDownLatch;
//import java.util.concurrent.TimeUnit;
//
//import static org.junit.jupiter.api.Assertions.*;
//
//@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
//public class QueueClientTest {
//
//    private TestRestTemplate restTemplate;
//    private WebSocketStompClient stompClient;
//    private String serverUrl = "http://localhost:8080";
//    private String wsUrl = "ws://localhost:8080/queue-ws";
//
//    @BeforeEach
//    void setUp() {
//        restTemplate = new TestRestTemplate();
//        stompClient = new WebSocketStompClient(new StandardWebSocketClient());
//    }
//
//    @Test
//    void testGetBusinessTypes() {
//        // 测试获取业务类型列表
//        ResponseEntity<BusinessType[]> response = restTemplate.getForEntity(
//            serverUrl + "/api/business/types",
//            BusinessType[].class
//        );
//
//        assertTrue(response.getStatusCode().is2xxSuccessful());
//        assertNotNull(response.getBody());
//        assertTrue(response.getBody().length > 0);
//    }
//
//    @Test
//    void testQueueTicketGeneration() {
//        // 测试取号功能
//        QueueRequest request = new QueueRequest();
//        request.setBusinessType("TYPE_A");
//        request.setCustomerName("测试用户");
//        request.setCustomerPhone("***********");
//
//        ResponseEntity<QueueTicket> response = restTemplate.postForEntity(
//            serverUrl + "/api/queue/ticket",
//            request,
//            QueueTicket.class
//        );
//
//        assertTrue(response.getStatusCode().is2xxSuccessful());
//        assertNotNull(response.getBody());
//        assertNotNull(response.getBody().getTicketNo());
//    }
//
//    @Test
//    void testWebSocketConnection() throws InterruptedException {
//        // 测试WebSocket连接
//        CountDownLatch latch = new CountDownLatch(1);
//        final boolean[] connected = {false};
//
//        stompClient.connect(wsUrl, new StompSessionHandlerAdapter() {
//            @Override
//            public void afterConnected(StompSession session, StompHeaders connectedHeaders) {
//                connected[0] = true;
//                latch.countDown();
//            }
//        });
//
//        assertTrue(latch.await(5, TimeUnit.SECONDS));
//        assertTrue(connected[0]);
//    }
//
//    @Test
//    void testQueueStatus() {
//        // 测试查询队列状态
//        String ticketNo = "A001";
//        ResponseEntity<QueueStatus> response = restTemplate.getForEntity(
//            serverUrl + "/api/queue/status/{ticketNo}",
//            QueueStatus.class,
//            ticketNo
//        );
//
//        assertTrue(response.getStatusCode().is2xxSuccessful());
//        assertNotNull(response.getBody());
//        assertNotNull(response.getBody().getStatus());
//    }
//
//    // 内部测试用数据类
//    private static class BusinessType {
//        private String code;
//        private String name;
//        // getters and setters
//    }
//
//    private static class QueueRequest {
//        private String businessType;
//        private String customerName;
//        private String customerPhone;
//        // getters and setters
//    }
//
//    private static class QueueTicket {
//        private String ticketNo;
//        private String businessType;
//        private String status;
//        // getters and setters
//    }
//
//    private static class QueueStatus {
//        private String ticketNo;
//        private String status;
//        private Integer waitingCount;
//        // getters and setters
//    }
//}