-- 初始化数据库结构（基于原有creat.sql适配）
CREATE TABLE IF NOT EXISTS business (
    uid INTEGER PRIMARY KEY AUTOINCREMENT,
    prefix VARCHAR(20),
    name VARCHAR(20),
    enabled TINYINT,
    type INTEGER,
    handle_count INTEGER,
    is_special INTEGER DEFAULT 0
);

CREATE TABLE IF NOT EXISTS employee (
    uid INTEGER PRIMARY KEY AUTOINCREMENT,
    name <PERSON><PERSON><PERSON><PERSON>(20),
    id VARCHAR(20),
    username VARCHAR(20),
    password VARCHAR(32),
    image VARCHAR(255),
    status INTEGER,
    enabled TINYINT,
    access INTEGER,
    nsfw_id VARCHAR(50)
);

-- 添加版本控制表
CREATE TABLE IF NOT EXISTS data_version (
    entity_name VARCHAR(50) PRIMARY KEY,
    version INTEGER NOT NULL DEFAULT 1,
    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 添加同步记录表
CREATE TABLE IF NOT EXISTS sync_record (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    entity_type VARCHAR(50) NOT NULL,
    entity_id VARCHAR(50) NOT NULL,
    operation VARCHAR(10) NOT NULL,
    status VARCHAR(20) NOT NULL,
    retry_count INTEGER DEFAULT 0,
    error_message TEXT,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP,
    next_retry_time TIMESTAMP
);

-- 索引优化
CREATE INDEX IF NOT EXISTS idx_sync_record_status ON sync_record(status);
CREATE INDEX IF NOT EXISTS idx_sync_record_retry ON sync_record(next_retry_time) WHERE status = 'RETRY'; 