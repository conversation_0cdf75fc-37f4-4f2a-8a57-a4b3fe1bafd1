-- 创建设备与大厅映射表
CREATE TABLE IF NOT EXISTS device_hall_mapping (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    device_id VARCHAR(64) NOT NULL COMMENT '设备唯一标识',
    device_name VARCHAR(100) NOT NULL COMMENT '设备名称',
    device_type VARCHAR(20) NOT NULL COMMENT '设备类型（TICKET:取号端, CALL:呼叫端, DISPLAY:显示屏）',
    hall_id INT NOT NULL COMMENT '关联的大厅ID',
    ip_address VARCHAR(50) COMMENT '设备IP地址',
    mac_address VARCHAR(50) COMMENT '设备MAC地址',
    status VARCHAR(20) DEFAULT 'ACTIVE' COMMENT '设备状态（ACTIVE:活跃, INACTIVE:非活跃）',
    register_time DATETIME NOT NULL COMMENT '注册时间',
    last_active_time DATETIME NOT NULL COMMENT '最后活跃时间',
    remarks VARCHAR(255) COMMENT '备注信息',
    INDEX idx_device_id (device_id),
    INDEX idx_hall_id (hall_id),
    INDEX idx_ip_address (ip_address),
    INDEX idx_mac_address (mac_address)
) COMMENT '设备与大厅映射表';

-- 创建存储过程：根据设备标识获取大厅ID
DELIMITER //
CREATE PROCEDURE sp_get_hall_by_device(
    IN p_device_id VARCHAR(64),
    IN p_ip_address VARCHAR(50),
    IN p_mac_address VARCHAR(50)
)
BEGIN
    DECLARE v_hall_id INT;
    
    -- 1. 通过设备ID查找
    IF p_device_id IS NOT NULL THEN
        SELECT hall_id INTO v_hall_id FROM device_hall_mapping 
        WHERE device_id = p_device_id LIMIT 1;
    END IF;
    
    -- 2. 通过MAC地址查找
    IF v_hall_id IS NULL AND p_mac_address IS NOT NULL THEN
        SELECT hall_id INTO v_hall_id FROM device_hall_mapping 
        WHERE mac_address = p_mac_address LIMIT 1;
    END IF;
    
    -- 3. 通过IP地址查找
    IF v_hall_id IS NULL AND p_ip_address IS NOT NULL THEN
        SELECT hall_id INTO v_hall_id FROM device_hall_mapping 
        WHERE ip_address = p_ip_address LIMIT 1;
    END IF;
    
    -- 返回结果
    SELECT v_hall_id AS hall_id;
    
    -- 更新设备活跃状态
    IF v_hall_id IS NOT NULL THEN
        IF p_device_id IS NOT NULL THEN
            UPDATE device_hall_mapping SET 
                last_active_time = NOW(),
                status = 'ACTIVE'
            WHERE device_id = p_device_id;
        ELSEIF p_mac_address IS NOT NULL THEN
            UPDATE device_hall_mapping SET 
                last_active_time = NOW(),
                status = 'ACTIVE'
            WHERE mac_address = p_mac_address AND device_id IS NULL;
        ELSEIF p_ip_address IS NOT NULL THEN
            UPDATE device_hall_mapping SET 
                last_active_time = NOW(),
                status = 'ACTIVE'
            WHERE ip_address = p_ip_address AND device_id IS NULL AND mac_address IS NULL;
        END IF;
    END IF;
END //
DELIMITER ;

-- 创建存储过程：注册设备并关联到大厅
DELIMITER //
CREATE PROCEDURE sp_register_device(
    IN p_device_id VARCHAR(64),
    IN p_device_name VARCHAR(100),
    IN p_device_type VARCHAR(20),
    IN p_hall_id INT,
    IN p_ip_address VARCHAR(50),
    IN p_mac_address VARCHAR(50),
    IN p_remarks VARCHAR(255)
)
BEGIN
    INSERT INTO device_hall_mapping (
        device_id, device_name, device_type, hall_id, 
        ip_address, mac_address, status, 
        register_time, last_active_time, remarks
    ) VALUES (
        p_device_id, p_device_name, p_device_type, p_hall_id, 
        p_ip_address, p_mac_address, 'ACTIVE', 
        NOW(), NOW(), p_remarks
    ) ON DUPLICATE KEY UPDATE
        device_name = p_device_name,
        device_type = p_device_type,
        hall_id = p_hall_id,
        ip_address = p_ip_address,
        mac_address = p_mac_address,
        status = 'ACTIVE',
        last_active_time = NOW(),
        remarks = p_remarks;
        
    -- 返回设备ID
    SELECT p_device_id AS device_id;
END //
DELIMITER ;