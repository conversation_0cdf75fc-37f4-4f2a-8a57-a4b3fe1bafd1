package com.xinchuang.queuing.api;

import com.xinchuang.queuing.service.DeviceIdentificationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Map;
import java.util.HashMap;

/**
 * 设备标识控制器
 * 提供设备注册和大厅识别的API接口
 */
@RestController
@RequestMapping("/api/device")
public class DeviceController {

    @Autowired
    private DeviceIdentificationService deviceIdentificationService;

    /**
     * 注册设备并关联到大厅
     */
    @PostMapping("/register")
    public ResponseEntity<?> registerDevice(@RequestBody Map<String, Object> request) {
        String deviceName = (String) request.get("deviceName");
        String deviceType = (String) request.get("deviceType");
        Integer hallId = (Integer) request.get("hallId");
        
        if (deviceName == null || deviceType == null || hallId == null) {
            return ResponseEntity.badRequest().body("Missing required parameters");
        }
        
        String deviceId = deviceIdentificationService.registerDevice(deviceName, deviceType, hallId);
        
        Map<String, Object> response = new HashMap<>();
        response.put("deviceId", deviceId);
        response.put("message", "Device registered successfully");
        
        return ResponseEntity.ok(response);
    }
    
    /**
     * 识别当前设备所属的大厅
     */
    @GetMapping("/identify")
    public ResponseEntity<?> identifyHall(@RequestParam(required = false) String deviceId) {
        Integer hallId = deviceIdentificationService.identifyHallId(deviceId);
        
        if (hallId == null) {
            return ResponseEntity.notFound().build();
        }
        
        Map<String, Object> response = new HashMap<>();
        response.put("hallId", hallId);
        
        return ResponseEntity.ok(response);
    }
    
    /**
     * 获取设备信息
     */
    @GetMapping("/info")
    public ResponseEntity<?> getDeviceInfo() {
        Map<String, Object> deviceInfo = new HashMap<>();
        deviceInfo.put("ipAddress", deviceIdentificationService.getLocalIpAddress());
        deviceInfo.put("macAddress", deviceIdentificationService.getLocalMacAddress());
        
        return ResponseEntity.ok(deviceInfo);
    }
}