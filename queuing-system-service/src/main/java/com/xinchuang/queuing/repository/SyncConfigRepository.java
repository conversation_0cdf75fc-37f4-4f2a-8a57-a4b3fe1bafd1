package com.xinchuang.queuing.repository;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.xinchuang.queuing.entity.SyncConfig;
import org.apache.ibatis.annotations.Mapper;

/**
 * 同步配置数据访问接口
 * 用于处理SyncConfig实体的数据库操作
 */
@Mapper
public interface SyncConfigRepository extends BaseMapper<SyncConfig> {
    /**
     * 查询最新版本的配置记录
     * @return 最新版本的配置记录
     */
    default SyncConfig findTopByOrderByVersionDesc() {
        return selectOne(
            new LambdaQueryWrapper<SyncConfig>()
                .orderByDesc(SyncConfig::getVersion)
                .last("LIMIT 1")
        );
    }
}