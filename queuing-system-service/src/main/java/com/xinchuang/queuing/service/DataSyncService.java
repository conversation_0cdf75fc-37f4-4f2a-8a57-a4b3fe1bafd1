package com.xinchuang.queuing.service;

import com.xinchuang.queuing.entity.QueueRecord;
import com.xinchuang.queuing.entity.SyncConfig;
import com.xinchuang.queuing.repository.QueueRecordRepository;
import com.xinchuang.queuing.repository.SyncConfigRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.time.LocalDateTime;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 数据同步服务
 * 负责将本地数据库中的记录同步到云端服务器
 */
@Service
public class DataSyncService {

    @Autowired
    private QueueRecordRepository queueRecordRepository;

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private SyncConfigRepository syncConfigRepository;

    @Value("${central.api.url}")
    private String centralApiUrl;

    @Value("${cloud.api.url}")
    private String CLOUD_API_URL;

    private AtomicInteger syncBatchSize = new AtomicInteger(50);
    private volatile boolean isSyncing = false;

    /**
     * 定时同步任务
     * 每5分钟执行一次，将未同步的数据推送到云端
     */
    @Scheduled(fixedRate = 300000)
    public void syncData() {
        if (isSyncing) {
            return;
        }
        isSyncing = true;
        try {
            // 获取未同步的记录
            List<QueueRecord> unsyncedRecords = queueRecordRepository.findUnsyncedRecords(syncBatchSize.get());
            // 获取最新配置
            syncConfig();
        
        for (QueueRecord record : unsyncedRecords) {
            try {
                // 尝试将数据同步到云端
                boolean success = syncToCloud(record);
                if (success) {
                    // 更新同步状态
                    record.setSyncStatus(1);
                    record.setLastSyncTime(LocalDateTime.now());
                    queueRecordRepository.insert(record);
                }
            } catch (Exception e) {
                // 同步失败，记录错误信息
                record.setRemarks("Sync failed: " + e.getMessage());
                record.setRetryCount(record.getRetryCount() + 1);
                queueRecordRepository.insert(record);
            }
        }
        } finally {
            isSyncing = false;
        }
    }

    /**
     * 同步配置信息
     */
    private void syncConfig() {
        try {
            SyncConfig latestConfig = restTemplate.getForObject(centralApiUrl + "/api/config/latest", SyncConfig.class);
            if (latestConfig != null && latestConfig.getVersion() > getCurrentConfigVersion()) {
                syncConfigRepository.insert(latestConfig);
                updateSyncParameters(latestConfig);
            }
        } catch (Exception e) {
            // 配置同步失败，使用本地配置
        }
    }

    /**
     * 获取当前配置版本
     */
    private long getCurrentConfigVersion() {
        SyncConfig currentConfig = syncConfigRepository.selectOne(
            new com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper<SyncConfig>()
                .orderByDesc(SyncConfig::getVersion)
                .last("LIMIT 1")
        );
        return currentConfig != null ? currentConfig.getVersion() : 0;
    }

    /**
     * 更新同步参数
     */
    private void updateSyncParameters(SyncConfig config) {
        if (config.getBatchSize() > 0) {
            syncBatchSize.set(config.getBatchSize());
        }
    }

    /**
     * 将单条记录同步到云端
     * @param record 需要同步的记录
     * @return 同步是否成功
     */
    private boolean syncToCloud(QueueRecord record) {
        try {
            // 发送数据到云端API
            restTemplate.postForEntity(CLOUD_API_URL, record, Void.class);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 手动触发数据同步
     * @return 同步的记录数量
     */
    public int manualSync() {
        List<QueueRecord> unsyncedRecords = queueRecordRepository.findUnsyncedRecords(syncBatchSize.get());
        int syncCount = 0;

        for (QueueRecord record : unsyncedRecords) {
            if (syncToCloud(record)) {
                record.setSyncStatus(1);
                record.setLastSyncTime(LocalDateTime.now());
                queueRecordRepository.insert(record);
                syncCount++;
            }
        }

        return syncCount;
    }
}