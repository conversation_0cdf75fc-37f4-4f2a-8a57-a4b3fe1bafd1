-- AGENT_INFO 表
CREATE TABLE [dbo].[AGENT_INFO] (
    [ID] BIGINT IDENTITY(1,1) PRIMARY KEY,
    [AGENT_NAME] NVARCHAR(50) NOT NULL,
    [AGENT_ID_CARD] NVARCHAR(32) NOT NULL,
    [AGENT_PHONE] NVARCHAR(20) NOT NULL,
    [TKT_DATE_TIME] DATETIME NULL,
    [TKT_UID] NVARCHAR(32) NULL,
    [TKT_ID] NVARCHAR(32) NULL,
    [AGENT_ORG] NVARCHAR(100) NULL
);

-- AGENT_INFO_ENTERPRISE 表
CREATE TABLE [dbo].[AGENT_INFO_ENTERPRISE] (
    [ID] BIGINT IDENTITY(1,1) PRIMARY KEY,
    [AGENT_INFO_ID] BIGINT NOT NULL,
    [DJXH] NVARCHAR(50) NULL,
    [NSRSBH] NVARCHAR(50) NULL,
    [NSRMC] NVARCHAR(200) NULL,
    [ZGSWJ_DM] NVARCHAR(50) NULL,
    [ZGSWKSFJ_DM] NVARCHAR(50) NULL,
    [NSRZT_MC] NVARCHAR(50) NULL,
    [SFZJ] NVARCHAR(10) NULL,
    [CREATE_TIME] DATETIME DEFAULT GETDATE(),
    [UPDATE_TIME] DATETIME DEFAULT GETDATE(),
    CONSTRAINT [FK_AGENT_INFO_ENTERPRISE_AGENT_INFO_ID] FOREIGN KEY ([AGENT_INFO_ID])
        REFERENCES [dbo].[AGENT_INFO]([ID])
);

-- 可选索引
CREATE INDEX IX_AGENT_INFO_ENTERPRISE_NSRSBH ON [dbo].[AGENT_INFO_ENTERPRISE]([NSRSBH]); 