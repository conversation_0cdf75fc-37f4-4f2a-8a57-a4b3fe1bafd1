# 数据库脚本说明

本目录包含用于创建和更新数据库表结构的 SQL Server 脚本。

## 脚本列表

1. `update_agent_info_table.sql` - 更新 agent_info 表结构
   - 添加 agent_org 列（代理人所属机构）
   - 添加 tkt_id 列（票据号）
   - 将 take_number_time 列的数据类型修改为 DATETIME
   - 将 number_code 列的数据类型修改为 INT

2. `create_agent_info_enterprise_table.sql` - 创建 agent_info_enterprise 表
   - 创建表结构
   - 添加外键约束
   - 添加索引

## 使用方法

1. 在 SQL Server Management Studio 中打开脚本
2. 连接到目标数据库
3. 执行脚本

或者使用命令行：

```
sqlcmd -S <服务器名> -d <数据库名> -U <用户名> -P <密码> -i update_agent_info_table.sql
sqlcmd -S <服务器名> -d <数据库名> -U <用户名> -P <密码> -i create_agent_info_enterprise_table.sql
```

## 注意事项

- 脚本包含安全检查，只有在表或列不存在时才会创建或修改
- 在修改列数据类型时，会尝试将现有数据转换为新类型
- 如果数据转换失败，原始数据可能会丢失
- 建议在执行脚本前备份数据库
