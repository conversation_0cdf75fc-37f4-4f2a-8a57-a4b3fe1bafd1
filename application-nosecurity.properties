# ç¦ç¨Spring Security
spring.autoconfigure.exclude=org.springframework.boot.autoconfigure.security.servlet.SecurityAutoConfiguration

# éæèµæºéç½®
spring.web.resources.static-locations=classpath:/static/,classpath:/public/,file:./Website/Upload/
spring.mvc.static-path-pattern=/**

# æå¡å¨éç½®
server.port=8823

# æ°æ®åºéç½®ï¼å¦æéè¦ï¼
spring.datasource.url=************************************************************************************************************************************************
spring.datasource.username=root
spring.datasource.password=123456
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver

# JPAéç½®
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=false
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.MySQL8Dialect