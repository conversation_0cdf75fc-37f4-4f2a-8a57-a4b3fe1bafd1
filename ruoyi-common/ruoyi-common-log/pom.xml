<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.xinchuang</groupId>
        <artifactId>xinchuang-service</artifactId>
        <version>${revision}</version>
        <relativePath>../../pom.xml</relativePath>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>ruoyi-common-log</artifactId>

    <description>
        ruoyi-common-log 日志记录
    </description>

    <dependencies>

        <!-- RuoYi Common Core-->
        <dependency>
            <groupId>com.xinchuang</groupId>
            <artifactId>ruoyi-common-core</artifactId>
        </dependency>

        <!-- RuoYi Common Security-->
        <dependency>
            <groupId>com.xinchuang</groupId>
            <artifactId>ruoyi-common-security</artifactId>
        </dependency>

        <!-- tLog -->
        <dependency>
            <groupId>com.yomahub</groupId>
            <artifactId>tlog-web-spring-boot-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
        </dependency>

    </dependencies>

</project>
