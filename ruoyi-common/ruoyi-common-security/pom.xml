<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.xinchuang</groupId>
        <artifactId>ruoyi-common</artifactId>
        <version>1.0.0</version>
        <relativePath>../pom.xml</relativePath>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>ruoyi-common-security</artifactId>

    <description>
        ruoyi-common-security 安全模块
    </description>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.xinchuang</groupId>
                <artifactId>ruoyi-common-bom</artifactId>
                <version>${revision}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>

        <!-- RuoYi Common Core -->
        <dependency>
            <groupId>com.xinchuang</groupId>
            <artifactId>ruoyi-common-core</artifactId>
            <version>${revision}</version>
        </dependency>

        <!-- RuoYi Common Redis -->
        <dependency>
            <groupId>com.xinchuang</groupId>
            <artifactId>ruoyi-common-redis</artifactId>
            <version>${revision}</version>
        </dependency>

        <!-- Sa-Token 权限认证 -->
        <dependency>
            <groupId>cn.dev33</groupId>
            <artifactId>sa-token-spring-boot3-starter</artifactId>
        </dependency>

        <!-- Sa-Token 整合 Redis （使用 jackson 序列化方式） -->
        <dependency>
            <groupId>cn.dev33</groupId>
            <artifactId>sa-token-dao-redis-jackson</artifactId>
        </dependency>

    </dependencies>

</project>
