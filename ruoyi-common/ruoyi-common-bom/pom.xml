<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.xinchuang</groupId>
        <artifactId>ruoyi-common</artifactId>
        <version>${revision}</version>
        <relativePath>../pom.xml</relativePath>
    </parent>
    <artifactId>ruoyi-common-bom</artifactId>
    <!-- version会从parent继承 -->
    <packaging>pom</packaging>

    <description>
        ruoyi-common-bom common依赖项
    </description>

    <!-- revision 属性将从父POM (ruoyi-common -> xinchuang-service) 继承 -->

    <dependencyManagement>
        <dependencies>
            <!-- 核心模块 -->
            <dependency>
                <groupId>com.xinchuang</groupId>
                <artifactId>ruoyi-common-core</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 接口模块 -->
            <dependency>
                <groupId>com.xinchuang</groupId>
                <artifactId>ruoyi-common-doc</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 安全模块 -->
            <dependency>
                <groupId>com.xinchuang</groupId>
                <artifactId>ruoyi-common-security</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.xinchuang</groupId>
                <artifactId>ruoyi-common-satoken</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 日志记录 -->
            <dependency>
                <groupId>com.xinchuang</groupId>
                <artifactId>ruoyi-common-log</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 字典 -->
            <dependency>
                <groupId>com.xinchuang</groupId>
                <artifactId>ruoyi-common-dict</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- excel -->
            <dependency>
                <groupId>com.xinchuang</groupId>
                <artifactId>ruoyi-common-excel</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 缓存服务 -->
            <dependency>
                <groupId>com.xinchuang</groupId>
                <artifactId>ruoyi-common-redis</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- web服务 -->
            <dependency>
                <groupId>com.xinchuang</groupId>
                <artifactId>ruoyi-common-web</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 数据库服务 -->
            <dependency>
                <groupId>com.xinchuang</groupId>
                <artifactId>ruoyi-common-mybatis</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.xinchuang</groupId>
                <artifactId>ruoyi-common-job</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.xinchuang</groupId>
                <artifactId>ruoyi-common-dubbo</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.xinchuang</groupId>
                <artifactId>ruoyi-common-seata</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.xinchuang</groupId>
                <artifactId>ruoyi-common-loadbalancer</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.xinchuang</groupId>
                <artifactId>ruoyi-common-oss</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 限流 -->
            <dependency>
                <groupId>com.xinchuang</groupId>
                <artifactId>ruoyi-common-ratelimiter</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.xinchuang</groupId>
                <artifactId>ruoyi-common-idempotent</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.xinchuang</groupId>
                <artifactId>ruoyi-common-mail</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.xinchuang</groupId>
                <artifactId>ruoyi-common-sms</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.xinchuang</groupId>
                <artifactId>ruoyi-common-logstash</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.xinchuang</groupId>
                <artifactId>ruoyi-common-elasticsearch</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.xinchuang</groupId>
                <artifactId>ruoyi-common-sentinel</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.xinchuang</groupId>
                <artifactId>ruoyi-common-skylog</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.xinchuang</groupId>
                <artifactId>ruoyi-common-prometheus</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.xinchuang</groupId>
                <artifactId>ruoyi-common-translation</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 脱敏模块 -->
            <dependency>
                <groupId>com.xinchuang</groupId>
                <artifactId>ruoyi-common-sensitive</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 序列化模块 -->
            <dependency>
                <groupId>com.xinchuang</groupId>
                <artifactId>ruoyi-common-json</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.xinchuang</groupId>
                <artifactId>ruoyi-common-encrypt</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 租户模块 -->
            <dependency>
                <groupId>com.xinchuang</groupId>
                <artifactId>ruoyi-common-tenant</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.xinchuang</groupId>
                <artifactId>ruoyi-common-websocket</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.xinchuang</groupId>
                <artifactId>ruoyi-common-social</artifactId>
                <version>${revision}</version>
            </dependency>

        </dependencies>
    </dependencyManagement>
</project>
