# 排队系统整合优化计划 (Consolidated Optimization Plan)

## 概述

本文档旨在整合现有项目文档（包括架构设计、优化建议、服务设计、部署说明等），提出一个统一、分阶段的系统优化和演进计划。目标是将系统从当前以大厅为单位的独立部署模式，逐步过渡到以省局中心服务为核心、大厅服务专注于本地业务处理的统一、高效、可扩展的架构。

**本文档整合了以下关键信息来源：**

*   `两级架构设计方案.md`
*   `云端数据库优化设计说明.md`
*   `Java系统设计文档.md` / `项目分析文档.md`
*   `calling-system-service/设计文档.md`
*   `data-sync-service/设计文档.md`
*   `系统优化方案_20240321.md`
*   `部署说明.md`
*   `central-management-service/src/main/resources/docs/数字证书身份认证方案.md`
*   (以及其他相关服务设计和架构文档)

## 核心原则

*   **分阶段实施**：将复杂的优化过程分解为可管理的阶段，降低风险，确保平稳过渡。
*   **向下兼容**：在过渡阶段，确保新旧系统或模块能够协同工作。
*   **稳定优先**：在引入新功能或架构变更时，优先保证现有核心业务的稳定性。
*   **数据驱动**：基于监控数据和业务反馈来指导优化方向和优先级。
*   **集中管理**：逐步将认证、配置、核心数据管理向中心服务集中。
*   **本地优先**：大厅服务优先保障本地业务处理，具备离线运行能力。

## 整体架构 (基于两级架构设计)

### 1. 两级架构设计

```
[省局中心服务器集群 (Central Server Cluster)] ←─────→ [大厅本地服务器 (Hall Server)]
       ↑                                         ↑
       │ (Management, Aggregation)                 │ (Local Business Logic)
       ↓                                         ↓
  [管理端Web应用 (Admin Web App)]         [取号/呼号/显示终端 (Terminals)]
```

#### 1.1 省局层 (Central Layer) - `central-management-service` & OceanBase

*   **职责**:
    *   中央数据管理和存储 (OceanBase)。
    *   全省业务数据聚合和分析。
    *   系统配置管理和下发 (用户、业务类型、窗口等)。
    *   提供管理端Web应用接口。
    *   统一身份认证和授权。
    *   处理大厅上报的数据。
    *   (逻辑上)支持市/区/县级管理功能。
*   **核心组件**:
    *   `central-management-service`: Spring Boot 应用，提供 API。
    *   OceanBase 数据库集群。
    *   (未来可能) API 网关、负载均衡器。

#### 1.2 大厅层 (Hall Layer) - `queuing-system-service`, `calling-system-service`, `data-sync-service` & SQLite

*   **职责**:
    *   本地取号、叫号核心业务处理。
    *   本地数据存储 (SQLite) 和管理，支持离线运行。
    *   与本地终端设备 (取号机, 叫号屏/终端, LED, 语音设备) 通信。
    *   与省局中心进行数据同步 (通过 `data-sync-service`)。
*   **核心组件**:
    *   `queuing-system-service`: 处理取号逻辑。
    *   `calling-system-service`: 处理呼叫、窗口交互、LED/语音控制。
    *   `data-sync-service`: 负责与中心的数据双向同步。
    *   本地 SQLite 数据库。
    *   (可选) 本地消息队列 (如 RabbitMQ)。

### 2. 数据流设计

#### 2.1 上行数据流 (大厅 → 省局)

```
[大厅 SQLite] → [data-sync-service] → [central-management-service API] → [OceanBase]
```

*   **同步内容**: 业务数据（取号记录 `queue_record`、呼叫记录、窗口状态变更、(未来)服务评价等）。
*   **同步策略**:
    *   `data-sync-service` 定期轮询本地 SQLite 数据库查找待同步数据 (基于状态标记或版本号)。
    *   通过调用 `central-management-service` 提供的 API 上传数据。
    *   增量同步为主。
    *   考虑关键数据 (如取号成功) 近实时同步。

#### 2.2 下行数据流 (省局 → 大厅)

```
[OceanBase] → [central-management-service API] → [data-sync-service] → [大厅 SQLite]
```

*   **同步内容**: 配置数据（员工信息 `employees`、业务类型 `business_types`、窗口信息 `counters`/`windows`、大厅配置 `hall_config` 等）。
*   **同步策略**:
    *   `data-sync-service` 定期调用 `central-management-service` API 检查配置更新 (基于版本号或时间戳)。
    *   或由中央服务通过某种机制 (如消息队列) 通知 `data-sync-service` 有配置更新。
    *   大厅服务启动时主动拉取一次最新配置。

### 3. 离线运行机制

*   **核心**: 大厅服务依赖本地 SQLite 数据库完成所有核心业务流程。
*   **网络中断**:
    *   本地业务正常运行。
    *   `data-sync-service` 记录待同步数据（上行），并标记状态。
    *   无法获取最新的中心配置（下行）。
*   **网络恢复**:
    *   `data-sync-service` 自动检测网络恢复。
    *   优先同步积压的上行数据。
    *   拉取最新的下行配置数据。
*   **数据一致性**:
    *   使用版本号、时间戳或唯一业务 ID 解决潜在冲突。
    *   以省局数据为权威来源处理配置冲突。
    *   业务数据冲突需要定义明确的解决策略 (如时间优先、特定状态优先)。

## 优化阶段

---

### Phase 1: Stabilize Existing Services (稳定现有服务)

**目标:**

*   确保当前各大厅独立部署的排队系统、呼号系统、数据同步服务的稳定运行。
*   提升系统的可观测性（Logging, Monitoring）。
*   解决已知的基础性能瓶颈和Bug。
*   为后续的集中化改造打下坚实基础。

**主要行动步骤:**

1.  **增强日志记录:**
    *   **操作:** 在 `queuing-system-service`, `calling-system-service`, `data-sync-service` 中标准化日志格式 (如使用 JSON 格式)，增加关键业务流程 (取号、呼叫、完成、同步)、错误处理、外部调用 (LED, 语音, 中心API)、数据库操作等环节的详细日志。包含请求ID、用户ID (如果可用)、时间戳等关键信息。
    *   **参考:** `项目分析文档.md`, `Java系统设计文档.md` (日志框架 log4net / Logback)
    *   **配置示例 (Logback):**
        ```xml
        <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
            <file>/app/logs/service.log</file>
            <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
                <fileNamePattern>/app/logs/service.%d{yyyy-MM-dd}.log</fileNamePattern>
                <maxHistory>30</maxHistory>
            </rollingPolicy>
            <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
                 <!-- 可改为 JSON Encoder -->
                <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n</pattern>
            </encoder>
        </appender>
        ```

2.  **完善基础监控:**
    *   **操作:**
        *   确认并优化 Spring Boot Actuator 的 `health` (显示详细信息, `management.endpoint.health.show-details=always`) 和 `prometheus` 端点配置。
        *   确保暴露关键指标：JVM 指标 (内存、线程、GC), Tomcat 指标 (连接数、线程池), 数据库连接池指标 (HikariCP), 本地队列长度 (如果使用内存队列), 数据同步状态 (待同步数、上次同步时间、失败次数)。
        *   集成外部监控系统 (如 Prometheus + Grafana) 进行指标收集和可视化。
    *   **参考:** `config/hall*/application.yml` (`management.endpoints.web.exposure.include=*`)
    *   **示例指标 (代码中自定义):**
        ```java
        // 在 DataSyncService 中
        import io.micrometer.core.instrument.Gauge;
        import io.micrometer.core.instrument.MeterRegistry;

        // ...
        private final MeterRegistry meterRegistry;
        private AtomicInteger pendingSyncCount = new AtomicInteger(0);

        public DataSyncService(MeterRegistry meterRegistry) {
            this.meterRegistry = meterRegistry;
            Gauge.builder("data.sync.pending.count", pendingSyncCount, AtomicInteger::get)
                 .description("Number of records pending synchronization")
                 .register(meterRegistry);
        }

        // 在更新待同步计数时: pendingSyncCount.set(newCount);
        ```

3.  **本地数据库 (SQLite) 优化:**
    *   **操作:**
        *   为各大厅本地 SQLite 数据库实施定期优化脚本或在 `data-sync-service` 空闲时触发。
        *   启用 WAL (Write-Ahead Logging) 模式提高并发性 (`PRAGMA journal_mode=WAL;`)。
        *   调整缓存大小 (`PRAGMA cache_size = <pages>;`)。
        *   执行 `VACUUM` 清理碎片, `REINDEX` 重建索引, `ANALYZE` 更新统计信息。
    *   **参考:** `系统优化方案_20240321.md` (2.1.1 本地数据库优化)
    *   **代码示例:**
        ```java
        // 在 LocalDatabaseOptimizer 或 DataSyncService 中
        @Autowired
        private JdbcTemplate jdbcTemplate; // Or use native SQLite connection

        public void optimizeDatabase() {
            try {
                jdbcTemplate.execute("PRAGMA journal_mode=WAL;"); // Set once usually
                jdbcTemplate.execute("VACUUM;");
                // Reindex specific tables/indexes if needed
                // jdbcTemplate.execute("REINDEX index_name;");
                jdbcTemplate.execute("ANALYZE;");
                log.info("Local SQLite database optimization completed.");
            } catch (DataAccessException e) {
                log.error("Error optimizing local SQLite database", e);
            }
        }
        ```
    *   **SQLite Schema Snippets (示例):**
        <details>
        <summary>本地排队记录表示例 (local_queue_record)</summary>

        ```sql
        CREATE TABLE IF NOT EXISTS local_queue_record (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            ticket_no VARCHAR(30) UNIQUE NOT NULL,      -- 票号
            business_type_code VARCHAR(50) NOT NULL, -- 业务类型代码
            status INTEGER DEFAULT 0,                -- 状态：0-等待, 1-处理中, 2-完成, 3-取消, 4-过号
            priority INTEGER DEFAULT 5,            -- 优先级
            customer_info TEXT,                      -- 客户信息 (JSON or specific fields)
            create_time DATETIME DEFAULT CURRENT_TIMESTAMP, -- 创建时间
            update_time DATETIME,                      -- 更新时间
            assigned_window VARCHAR(20),             -- 分配窗口
            process_start_time DATETIME,             -- 处理开始时间
            process_end_time DATETIME,               -- 处理结束时间
            sync_status INTEGER DEFAULT 0,           -- 同步状态: 0-未同步, 1-同步中, 2-已同步, -1-同步失败
            sync_retry_count INTEGER DEFAULT 0,      -- 同步重试次数
            local_version INTEGER DEFAULT 1          -- 本地版本号 (用于增量同步)
        );
        CREATE INDEX IF NOT EXISTS idx_lqr_status ON local_queue_record(status);
        CREATE INDEX IF NOT EXISTS idx_lqr_sync_status ON local_queue_record(sync_status);
        CREATE INDEX IF NOT EXISTS idx_lqr_create_time ON local_queue_record(create_time);
        ```
        </details>
        <details>
        <summary>本地配置缓存表示例 (local_config)</summary>

        ```sql
        CREATE TABLE IF NOT EXISTS local_config (
            config_key VARCHAR(100) PRIMARY KEY NOT NULL, -- 配置项 Key (e.g., 'business_type_list', 'window_info_W01')
            config_value TEXT,                           -- 配置值 (JSON)
            version INTEGER DEFAULT 1,                   -- 配置版本号 (与中心同步)
            last_update_time DATETIME DEFAULT CURRENT_TIMESTAMP -- 最后更新时间
        );
        ```
        </details>

4.  **基础同步逻辑加固 (`data-sync-service`):**
    *   **操作:**
        *   优化基本重试逻辑：增加指数退避策略，设置最大重试次数。
        *   清晰记录同步状态 (`sync_status`) 和重试次数 (`sync_retry_count`) 到本地 SQLite 表。
        *   实现更可靠的待同步数据检测机制 (基于 `sync_status` 或 `local_version`)。
        *   处理中心 API 调用失败 (网络错误、业务逻辑错误) 的情况。
        *   增加同步过程的详细日志。
    *   **参考:** `data-sync-service/设计文档.md`, `系统优化方案_20240321.md` (1.4 数据同步策略优化)
    *   **设计要点:**
        *   **原子性**: 更新本地记录状态和调用远程 API 尽量在一个事务或补偿流程中处理。
        *   **幂等性**: 中心 API 设计需要幂等性，防止重试导致数据重复。
        *   **错误处理**: 区分可重试错误（如网络超时）和不可重试错误（如数据校验失败）。

5.  **已知Bug修复:**
    *   **操作:** 根据测试和运行反馈，梳理并修复当前版本中影响稳定性的关键Bug。

6.  **文档完善:**
    *   **操作:** 更新 `部署说明.md` (包含监控设置、数据库优化脚本等)。更新各服务的设计文档，反映当前的稳定状态和运维要点。
    *   **整合部署说明 (`部署说明.md` 核心内容):**
        <details>
        <summary>部署说明核心</summary>

        *   **环境要求**:
            *   **中心服务器**: Linux (CentOS/Ubuntu recommended), Docker, Docker Compose, 4GB+ RAM, Ports (OceanBase, Nginx, Service ports).
            *   **大厅服务器**: Linux/Windows, Docker, Docker Compose, 2GB+ RAM per hall set, Ports (Service ports, WebSocket).
            *   **数据库**: OceanBase (Central), SQLite (Local).
            *   **网络**: 中心与大厅网络互通。
        *   **部署步骤 (中心)**:
            1.  准备主机环境 (Docker, Docker Compose)。
            2.  创建目录结构 (`/opt/xinchuang/central`, `/opt/xinchuang/central/config`, `/opt/xinchuang/central/data/oceanbase` etc.)。
            3.  放置 `central-server-docker-compose.yml`, `config/central/application.yml`, `config/nginx/nginx.conf` 等。
            4.  修改配置 (数据库密码, IP地址等)。
            5.  `docker-compose -f central-server-docker-compose.yml up -d`.
            6.  验证服务状态 (`docker ps`, `docker logs`, 访问 Nginx/服务 URL)。
        *   **部署步骤 (大厅)**:
            1.  准备主机环境 (Docker, Docker Compose)。
            2.  创建目录结构 (`/opt/xinchuang/hall`, `/opt/xinchuang/hall/config`, `/opt/xinchuang/hall/data/hallX` etc.)。
            3.  放置 `docker-compose.yml`, 各 hall 配置 (`config/hall1/application.yml`, `config/hall1-calling/application.yml` ...)。使用 `copy-hall-configs.bat` (或 .sh) 脚本生成 hall2, hall3 配置。
            4.  修改配置 (中心服务地址, 大厅 ID, 本地 DB 路径 `${LOCAL_DB_PATH:/app/data/hallX.db}` 等)。
            5.  `docker-compose up -d` (启动所有) 或 `docker-compose up -d hall1-queuing hall1-calling hall1-sync` (启动指定大厅)。
            6.  验证服务状态 (`docker ps`, `docker logs`, 访问 Web 界面)。
        *   **维护命令**:
            *   查看日志: `docker logs <container_name>`
            *   重启服务: `docker-compose restart <service_name>`
            *   停止服务: `docker-compose down`
            *   数据库备份 (OceanBase): (参考 OceanBase 文档)
            *   数据库备份 (SQLite): `sqlite3 /app/data/hallX.db ".backup /path/to/backup/hallX_backup.db"`
        *   **常见问题**: 服务启动失败 (检查日志、端口占用、配置错误), 数据同步失败 (检查网络、中心服务状态、同步日志), 数据库连接问题。
        </details>

---

### Phase 2: Centralized User Authentication (集中化用户认证)

**目标:**

*   将用户（主要是窗口工作人员 Staff/Employee）的管理和认证功能集中到 `central-management-service`。
*   大厅的 `calling-system-service`（窗口端）和可能的管理操作依赖中心服务进行登录认证。
*   为后续实现统一的权限管理（RBAC）奠定基础。

**主要行动步骤:**

1.  **中心数据库设计 (用户/员工 - OceanBase):**
    *   **操作:** 在 OceanBase 中设计并创建 `employees` 表及相关表 (`roles`, `permissions`, `employee_roles`)。
    *   **参考:** `云端数据库优化设计说明.md`, `central-management-service` 实体设计
    *   **`employees`表示例:**
        ```sql
        CREATE TABLE employees (
            id BIGINT AUTO_INCREMENT PRIMARY KEY,
            employee_id VARCHAR(50) UNIQUE NOT NULL COMMENT '员工工号',
            employee_name VARCHAR(100) NOT NULL COMMENT '员工姓名',
            password_hash VARCHAR(255) NOT NULL COMMENT '加密后的密码 (e.g., bcrypt)',
            hall_id BIGINT NOT NULL COMMENT '所属大厅ID',
            status VARCHAR(20) DEFAULT 'ACTIVE' COMMENT '状态 (ACTIVE, INACTIVE, LOCKED)',
            email VARCHAR(100),
            phone VARCHAR(20),
            create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
            update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            last_login_time DATETIME,
            failed_login_attempts INT DEFAULT 0,
            INDEX idx_emp_hall_id (hall_id),
            FOREIGN KEY (hall_id) REFERENCES halls(id) -- Assuming 'halls' table exists
        );
        ```
    *   **`roles`表示例:** `(id, role_name, description)`
    *   **`permissions`表示例:** `(id, permission_code, description)`
    *   **`employee_roles`表示例:** `(employee_id, role_id)` (Many-to-Many)
    *   **`role_permissions`表示例:** `(role_id, permission_id)` (Many-to-Many)

2.  **开发中央认证接口 (`central-management-service`):**
    *   **操作:**
        *   开发 RESTful API:
            *   `POST /api/auth/login`: 接收工号/密码，验证成功返回 JWT。
            *   `POST /api/auth/refresh`: 接收 refresh token，返回新的 access token。
            *   `GET /api/auth/me`: (需要认证) 返回当前用户信息。
            *   (Admin APIs) `POST /api/admin/employees`, `GET /api/admin/employees`, `PUT /api/admin/employees/{id}` 等 CRUD 操作。
        *   使用 Spring Security 处理认证和授权。
        *   集成 JWT 库 (e.g., `jjwt`) 生成和校验 Token。
        *   使用 `BCryptPasswordEncoder` 进行密码存储和校验。
    *   **参考:** `central-management-service` Controller 和 Service 设计, Spring Security Docs, JWT Specs.
    *   **JWT Payload 建议:** 包含 `sub` (employee_id), `roles`, `hall_id`, `exp` (expiration).

3.  **改造大厅呼号服务 (`calling-system-service`):**
    *   **操作:**
        *   移除本地用户认证逻辑。
        *   修改登录界面/逻辑，将用户输入的凭据发送到中央 `/api/auth/login` 接口。
        *   存储获取到的 JWT (Access Token 和 Refresh Token) - 安全存储，如内存或 HttpOnly Cookie。
        *   在后续需要认证的请求（如呼叫、完成操作）的 Header 中添加 `Authorization: Bearer <access_token>`。
        *   实现 Token 过期处理和刷新逻辑。
    *   **参考:** `calling-system-service/设计文档.md`

4.  **员工信息下行同步 (可选/按需):**
    *   **操作:** 如果大厅服务需要离线获取某些员工信息（如当前窗口可登录员工列表），可通过 `data-sync-service` 从中心同步 `employees` 表的部分信息到本地 SQLite。或者 `calling-system-service` 在启动和登录成功后，调用中心 API 拉取必要信息并缓存。
    *   **参考:** `两级架构设计方案.md` (2.2 下行数据流)

5.  **安全加固:**
    *   **操作:**
        *   强制所有认证和管理 API 使用 HTTPS。
        *   使用强密码策略和加密存储 (BCrypt)。
        *   防止暴力破解 (如登录失败次数限制、验证码)。
        *   保护 JWT 密钥。
    *   **整合数字证书认证 (可选):**
        *   **场景:** 用于服务间认证 (如 `data-sync-service` 到 `central-management-service`) 或特定终端认证。
        *   **操作:**
            *   建立 CA 并为每个大厅/服务颁发证书。
            *   `central-management-service` 配置为信任该 CA，并能验证客户端证书。
            *   客户端 (如 `data-sync-service`) 配置为在 TLS 握手时提供其证书。
            *   在 `central-management-service` 中可以从证书中提取信息 (如大厅编码 CN/OU) 进行身份识别。
        *   **参考:** `central-management-service/src/main/resources/docs/数字证书身份认证方案.md`

---

### Phase 3: Centralized Configuration Management (集中化配置管理)

**目标:**

*   将核心业务配置（如业务类型、窗口信息、大厅参数）的管理集中到 `central-management-service`。
*   大厅服务（`queuing-system-service`, `calling-system-service`, `data-sync-service`）能够从中央服务获取配置，减少对本地配置文件的依赖。
*   实现配置的动态更新和下发。

**主要行动步骤:**

1.  **中心数据库设计 (配置 - OceanBase):**
    *   **操作:** 在 OceanBase 中设计/扩展相关配置表，确保包含 `hall_id` 实现多租户隔离。
    *   **参考:** `云端数据库优化设计说明.md`, `central-management-service` 实体类 (`BusinessType`, `Counter`, `Hall`, `HallBusinessTypeMapping`)
    *   **`business_types` 扩展:** 添加 Phase 4 中提到的字段 (`start_time`, `end_time`, `required_materials`等)。
    *   **`counters` (或 `windows`) 表:** `(id, counter_number, name, hall_id, status, type, description, create_time, update_time)`
    *   **`halls` 表:** `(id, hall_code, name, address, region_code, status, config_json, create_time, update_time)`
    *   **`hall_business_type_mappings` 表:** `(id, hall_id, business_type_id, enabled, priority, daily_limit, create_time, update_time)`
    *   **`hall_config` 表 (通用配置):** `(id, hall_id, config_key, config_value, description, create_time, update_time)`

2.  **开发中央配置接口 (`central-management-service`):**
    *   **操作:**
        *   开发 RESTful API 用于按 `hall_id` 查询各类配置:
            *   `GET /api/config/halls/{hallId}/business-types`
            *   `GET /api/config/halls/{hallId}/counters`
            *   `GET /api/config/halls/{hallId}/settings` (获取通用配置)
        *   考虑实现配置版本管理，API 可返回配置版本号或 ETag。
        *   (Admin APIs) 提供 CRUD 操作管理这些配置。
    *   **参考:** `central-management-service` Controller 和 Service 设计

3.  **改造大厅服务 (Queuing, Calling, Sync):**
    *   **操作:**
        *   **启动时拉取:** 服务启动时调用中央配置 API，获取本大厅所需的全量配置。
        *   **本地缓存:** 将获取到的配置缓存在本地 SQLite (`local_config` 表) 或内存中。
        *   **定期刷新/更新:**
            *   **轮询 (Pull):** 定期调用配置 API，检查版本号或 ETag 是否有变化，如有变化则重新拉取。
            *   **推送通知 (Push - 更优):** `central-management-service` 在配置变更后，通过消息队列 (需要引入 MQ) 或 WebSocket (如果已有连接) 通知对应大厅的 `data-sync-service` 或其他服务去拉取最新配置。
        *   修改业务逻辑，使其读取缓存/本地数据库中的配置，而不是硬编码或本地文件。
    *   **参考:** 各服务的 `application.yml` 和启动逻辑, `系统优化方案_20240321.md` (2.2 缓存优化)

4.  **配置同步机制选择:**
    *   **操作:** 明确采用 Pull 还是 Push 模式。
        *   **Pull 优点:** 实现简单，对中心服务压力小。**缺点:** 延迟高，轮询消耗资源。
        *   **Push 优点:** 实时性好。**缺点:** 需要引入 MQ 或依赖 WebSocket，增加系统复杂度。
    *   **建议:** 初期可采用 Pull 模式，后续根据实时性要求考虑升级到 Push 模式。
    *   **参考:** `两级架构设计方案.md`, `架构优化设计.md`

5.  **本地配置文件 (`application.yml`) 调整:**
    *   **操作:** 逐步移除本地 `application.yml` 中可被中央管理的动态配置项 (如业务类型列表、窗口列表)。保留：
        *   服务端口 (`server.port`)
        *   中心服务地址 (`central.server.url`)
        *   本地数据库路径 (`spring.datasource.url`, 使用 `${LOCAL_DB_PATH:...}`)
        *   日志配置 (`logging.file.path`)
        *   Actuator 配置
        *   大厅 ID (`app.hall-id`, 可通过环境变量注入)
        *   MQ/Redis 等中间件连接信息 (如果引入)

---

### Phase 4: Advanced Optimization and Deepening (高级优化与深化)

**目标:**

*   基于稳定的基础架构和集中的管理能力，实施更高级的业务功能和系统优化。
*   全面提升系统性能、用户体验、安全性和智能化水平。

**主要行动步骤:** (整合 `系统优化方案_20240321.md` 的所有内容)

1.  **实施高级业务功能:**
    *   **操作:**
        *   **业务类型扩展实现:** 在 `central-management-service` 和 `queuing-system-service` 中实现对 `BusinessType` 扩展字段 (办理时段, 所需材料, 前置条件, 身份验证, 预约) 的处理和展示。
            ```java
            // BusinessType 实体扩展 (已在 Phase 3 定义)
            // 在 queuing-system-service 中增加逻辑判断是否在服务时间、是否需要验证等
            ```
        *   **高级排队优先级规则:** 实现 `PriorityCalculationService`，在 `queuing-system-service` 取号时调用，计算并存储优先级到 `local_queue_record`。
            ```java
            // PriorityCalculationService 实现 (参考 系统优化方案_20240321.md 1.3.1)
            // 需要 QueueRecord 包含 customerType, isAppointment, isUrgent 等字段
            ```
        *   **更精准的等待时间预估:** 实现 `WaitTimeCalculationService`，供 `queuing-system-service` 调用，在取号成功后或界面查询时显示。
            ```java
            // WaitTimeCalculationService 实现 (参考 系统优化方案_20240321.md 4.1.1)
            // 需要依赖中心或本地缓存获取平均处理时间、当前等待人数、开放窗口数
            ```
        *   **多渠道通知:** 实现 `NotificationService`，集成 SMS、(可选)WeChat 服务。在关键事件 (如即将叫号、处理完成、等待超时) 时触发通知。
            ```java
            // NotificationService 实现 (参考 系统优化方案_20240321.md 4.2.1)
            // 需要配置 SMS/WeChat 服务商的 API Key/Secret
            // 需要获取客户手机号/WeChat ID
            ```
        *   **服务评价与反馈:** 在 `calling-system-service` 窗口端或独立评价器上增加评价功能，将评价数据同步到中心。

2.  **深化数据同步优化 (`data-sync-service`):**
    *   **操作:**
        *   **冲突解决机制:** 实现 `ConflictResolver` 接口及其策略 (如时间戳优先、中心优先)。
        *   **批量处理:** 修改 `data-sync-service` 从本地读取和向中心发送数据时，采用批量方式，减少 I/O 和网络请求次数。
        *   **断点续传 (针对大文件或长时间同步):** 如果有大文件同步需求，需要记录同步进度，失败后从断点恢复。对于数据库记录同步，主要靠状态和版本号。
        *   **(可选) 引入消息队列:** 使用 RabbitMQ 或 Kafka 替代直接 API 调用进行数据上报，实现解耦、削峰填谷、保证最终一致性。`data-sync-service` 将数据发送到 MQ，中心服务消费 MQ 消息。
    *   **参考:** `系统优化方案_20240321.md` (1.4 同步服务优化), `架构优化设计.md` (消息队列)
    *   **代码示例 (冲突解决):**
        ```java
        public interface ConflictResolver {
            boolean hasConflict(SyncData localData, SyncData centralData);
            SyncData resolveConflict(SyncData localData, SyncData centralData);
        }
        // 实现 TimestampBasedConflictResolver 等策略
        ```

3.  **加强安全性:**
    *   **操作:**
        *   **审计日志:** 实现 `AuditLogService`，使用 AOP 或拦截器在关键操作 (登录、配置修改、敏感数据访问、重要业务操作) 前后记录日志到数据库或专用日志系统。
            ```java
            // AuditLogService 实现 (参考 系统优化方案_20240321.md 3.1.1)
            // AuditLog 实体类/表需要包含: 操作类型, 操作员ID, 时间戳, IP地址, 请求参数, 结果等
            ```
        *   **敏感数据加密:** 实现 `EncryptionService` (如 AES)，在存储用户密码 (虽然已用 Bcrypt)、客户联系方式、身份证号 (如果涉及) 等敏感信息前进行加密。
            ```java
            // EncryptionService 实现 (参考 系统优化方案_20240321.md 3.2.1)
            // 需要安全地管理加密密钥 (如配置中心、环境变量)
            ```
        *   **RBAC 权限细化:** 在 `central-management-service` 中基于 Phase 2 建立的用户角色体系，使用 Spring Security 的注解 (`@PreAuthorize`) 或过滤器，对 API 和服务方法进行细粒度的权限控制。

4.  **提升用户体验:**
    *   **操作:**
        *   优化取号机界面 (UI/UX)。
        *   优化窗口工作人员操作界面 (`calling-system-service`)。
        *   提供大屏幕或 Web 界面实时展示各业务类型的排队状态和预计等待时间。
        *   提供业务办理进度查询功能。

5.  **引入统计分析:**
    *   **操作:**
        *   **数据聚合:** 定期将大厅上报的业务数据在中心库 (OceanBase) 进行聚合处理。
        *   **开发分析服务:** 实现 `BusinessAnalysisService`，提供 API 用于查询统计报表。
        *   **报表内容:** 平均等待/处理时长、业务量趋势、高峰时段分析、窗口利用率、员工绩效、客户满意度等。
        *   **可视化:** 对接 BI 工具 (如 Grafana, Superset) 或开发专门的报表界面展示分析结果。
            ```java
            // BusinessAnalysisService 实现 (参考 系统优化方案_20240321.md 5.1.1)
            // 需要设计聚合表或复杂的 SQL 查询/存储过程
            ```

6.  **高级监控与告警:**
    *   **操作:**
        *   **业务指标监控:** 除了系统指标，重点监控核心业务指标 (如：平均等待时间、排队长度、同步延迟、API 调用成功率/耗时、错误率)。
        *   **分布式追踪:** 引入 SkyWalking, Zipkin 等工具实现跨服务的请求链路追踪，方便排查问题。
        *   **日志聚合与分析:** 使用 ELK (Elasticsearch, Logstash, Kibana) 或 EFK (Elasticsearch, Fluentd, Kibana) 栈集中管理和分析所有服务的日志。
        *   **配置告警规则:** 在监控系统 (Prometheus Alertmanager, Grafana Alerting) 中配置告警规则，针对关键指标异常 (如 CPU/内存过高、队列积压、同步失败、错误率飙升、服务宕机) 发送通知 (邮件、短信、钉钉、微信)。
            ```java
            // SystemMonitorService 示例 (参考 系统优化方案_20240321.md 6.1.1)
            // 实际需要与监控系统集成
            ```

7.  **性能调优:**
    *   **操作:**
        *   **缓存策略优化:** 引入分布式缓存 (Redis) 缓存热点数据（如常用配置、用户信息），减轻数据库压力。
            ```java
            // CacheService 示例 (参考 系统优化方案_20240321.md 2.2.1)
            // 使用 Spring Cache 注解简化缓存应用
            ```
        *   **数据库查询优化:** 分析慢查询日志，优化 SQL 语句，合理创建索引。
        *   **JVM 调优:** 根据监控数据调整 JVM 堆大小、GC 策略等参数。
        *   **并发控制:** 对高并发接口（如取号）进行限流 (如 Resilience4j, Sentinel) 或使用乐观锁/悲观锁处理并发写。
        *   **异步处理:** 对于非核心、耗时的操作（如发送通知、记录日志）采用异步处理 (如 `@Async` 注解、消息队列)。

---

## 总结

本整合优化计划提供了一个从当前状态向目标架构演进的清晰路径。每个阶段都有明确的目标和可行的步骤，并整合了项目各个设计文档的核心内容。建议团队定期回顾此计划，根据实际进展、业务需求变化和技术发展进行调整，将此文档作为核心的设计与演进蓝图。