<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xinchuang.hall.mapper.TicketMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.xinchuang.hall.domain.Ticket">
        <result column="UID" property="uid" />
        <result column="TKT_ID" property="tktId" />
        <result column="TKT_INTID" property="tktIntid" />
        <result column="TKT_DATE" property="tktDate" />
        <result column="TKT_TIME" property="tktTime" />
        <result column="WIN_ID" property="winId" />
        <result column="BIZ_UID" property="bizUid" />
        <result column="EMP_UID" property="empUid" />
        <result column="START_TIME" property="startTime" />
        <result column="END_TIME" property="endTime" />
        <result column="RANK" property="rank" />
        <result column="STATUS" property="status" />
        <result column="FIRST_TIME" property="firstTime" />
        <result column="SEC_START_TIME" property="secStartTime" />
        <result column="IS_LINK" property="isLink"/>
        <result column="IS_AUTO" property="isAuto"/>
        <result column="IS_ENTERPRISE" property="isEnterPrise"/>
        <result column="ENTERPRISE_ID" property="enterPriseId"/>
        <result column="NSFW_ID" property="nsfwId"/>
        <result column="RANK_USER_NAME" property="rankUserName"/>
        <result column="RANK_USER_PHONE" property="rankUserPhone"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        UID, TKT_ID, TKT_INTID, TKT_DATE, TKT_TIME, WIN_ID, BIZ_UID, EMP_UID, START_TIME, END_TIME, RANK, STATUS, FIRST_TIME, SEC_START_TIME
    </sql>

</mapper>
