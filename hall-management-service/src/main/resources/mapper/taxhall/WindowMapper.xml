<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xinchuang.hall.mapper.WindowMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.xinchuang.hall.domain.Window">
        <result column="UID" property="uid"/>
        <result column="NAME" property="name"/>
        <result column="LED_ADDRESS" property="ledAddress"/>
        <result column="LED_TEXT" property="ledText"/>
        <result column="ENABLED" property="enabled"/>
        <result column="SID" property="sid"/>
        <result column="RANK_MODE" property="rankMode" />
        <result column="RANK_ADDRESS" property="rankAddress"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        UID, NAME, LED_ADDRESS, LED_TEXT, ENABLED, SID, RANK_MODE, RANK_ADDRESS
    </sql>

</mapper>
