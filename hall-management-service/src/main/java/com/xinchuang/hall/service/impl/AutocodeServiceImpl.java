package com.xinchuang.hall.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xinchuang.hall.mapper.AutocodeMapper;
import com.xinchuang.hall.domain.Autocode;
import com.xinchuang.hall.service.AutocodeService;
import org.springframework.stereotype.Service;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-03
 */
@Service
@DS("taxhall")
public class AutocodeServiceImpl extends ServiceImpl<AutocodeMapper, Autocode> implements AutocodeService {

}
