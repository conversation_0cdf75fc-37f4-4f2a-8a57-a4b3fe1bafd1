package com.xinchuang.central.entity;

import lombok.Data;
import lombok.NoArgsConstructor;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import java.time.LocalDateTime;

/**
 * 窗口业务类型映射实体类
 * 用于配置每个窗口可以办理的业务类型
 */
@TableName("counter_business_type_mappings")
@Data
@NoArgsConstructor
public class CounterBusinessTypeMapping {
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 窗口ID
     */
    @TableField(value = "counter_id")
    private Long counterId;

    /**
     * 窗口编号
     */
    @TableField(value = "counter_number")
    private String counterNumber;

    /**
     * 业务类型ID
     */
    @TableField(value = "business_type_id")
    private Long businessTypeId;

    /**
     * 业务类型编码
     */
    @TableField(value = "business_type_code")
    private String businessTypeCode;

    /**
     * 所属大厅ID
     */
    @TableField(value = "hall_id")
    private Long hallId;

    /**
     * 是否启用
     */
    @TableField(value = "enabled")
    private Boolean enabled = true;

    /**
     * 优先级（数字越小优先级越高）
     */
    @TableField(value = "priority")
    private Integer priority = 5;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private LocalDateTime updateTime;

    /**
     * 备注信息
     */
    @TableField(value = "remarks")
    private String remarks;
}