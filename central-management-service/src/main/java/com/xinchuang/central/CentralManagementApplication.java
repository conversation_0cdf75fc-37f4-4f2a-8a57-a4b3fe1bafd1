package com.xinchuang.central;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.config.server.EnableConfigServer;
import de.codecentric.boot.admin.server.config.EnableAdminServer;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * 中央管理服务应用
 * 作为省局层的核心服务，负责：
 * 1. 中央数据管理和存储
 * 2. 全省业务数据聚合和分析
 * 3. 系统配置管理和下发
 * 4. 提供管理端Web应用
 * 5. 负载均衡和高可用保障
 */
@SpringBootApplication
@EnableConfigServer
@EnableAdminServer
@EnableScheduling
public class CentralManagementApplication {
    public static void main(String[] args) {
        SpringApplication.run(CentralManagementApplication.class, args);
    }
}