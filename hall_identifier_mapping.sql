-- 大厅标识映射表设计
-- 用于存储不同系统的标识对应关系

-- 创建大厅标识映射表
CREATE TABLE IF NOT EXISTS testdemo.hall_identifier_mapping (
    id INT(10) AUTO_INCREMENT PRIMARY KEY,
    hall_id INT(10) NOT NULL COMMENT '关联的大厅ID',
    identifier_type VARCHAR(50) NOT NULL COMMENT '标识类型，如tax_bureau_code、tax_authority_id等',
    identifier_value VARCHAR(100) NOT NULL COMMENT '标识值',
    description VARCHAR(255) COMMENT '描述信息',
    is_primary TINYINT(1) DEFAULT 0 COMMENT '是否为主要标识 1-是 0-否',
    create_time DATETIME,
    update_time DATETIME,
    INDEX idx_hall_id (hall_id),
    INDEX idx_identifier (identifier_type, identifier_value),
    UNIQUE KEY uk_hall_identifier (hall_id, identifier_type)
) COMMENT '大厅标识映射表';

-- 创建根据外部标识查询大厅ID的存储过程
DELIMITER //
CREATE PROCEDURE testdemo.sp_get_hall_by_identifier(
    IN p_identifier_type VARCHAR(50),
    IN p_identifier_value VARCHAR(100)
)
BEGIN
    SELECT h.*
    FROM testdemo.hall_info h
    JOIN testdemo.hall_identifier_mapping m ON h.hall_id = m.hall_id
    WHERE m.identifier_type = p_identifier_type
    AND m.identifier_value = p_identifier_value;
END //
DELIMITER ;

-- 创建根据多个标识类型查询大厅的存储过程
DELIMITER //
CREATE PROCEDURE testdemo.sp_get_hall_by_multiple_identifiers(
    IN p_identifier_json JSON
)
BEGIN
    -- 使用JSON格式接收多个标识条件
    -- 示例: '{"tax_bureau_code":"12345","tax_authority_id":"T789"}'    
    SELECT DISTINCT h.*
    FROM testdemo.hall_info h
    JOIN testdemo.hall_identifier_mapping m ON h.hall_id = m.hall_id
    WHERE (m.identifier_type, m.identifier_value) IN (
        SELECT 
            JSON_UNQUOTE(JSON_KEYS(p_identifier_json)) AS type,
            JSON_UNQUOTE(JSON_EXTRACT(p_identifier_json, CONCAT('$.', JSON_UNQUOTE(JSON_KEYS(p_identifier_json))))) AS value
    );
END //
DELIMITER ;

-- 创建添加大厅标识的存储过程
DELIMITER //
CREATE PROCEDURE testdemo.sp_add_hall_identifier(
    IN p_hall_id INT(10),
    IN p_identifier_type VARCHAR(50),
    IN p_identifier_value VARCHAR(100),
    IN p_description VARCHAR(255),
    IN p_is_primary TINYINT(1)
)
BEGIN
    INSERT INTO testdemo.hall_identifier_mapping
    (hall_id, identifier_type, identifier_value, description, is_primary, create_time, update_time)
    VALUES
    (p_hall_id, p_identifier_type, p_identifier_value, p_description, p_is_primary, NOW(), NOW())
    ON DUPLICATE KEY UPDATE
    identifier_value = p_identifier_value,
    description = p_description,
    is_primary = p_is_primary,
    update_time = NOW();
END //
DELIMITER ;

-- 修改大厅服务层查询接口，支持多种标识查询
DELIMITER //
CREATE PROCEDURE testdemo.sp_get_business_by_identifier(
    IN p_identifier_type VARCHAR(50),
    IN p_identifier_value VARCHAR(100)
)
BEGIN
    SELECT b.*
    FROM testdemo.business b
    JOIN testdemo.hall_identifier_mapping m ON b.hall_id = m.hall_id
    WHERE m.identifier_type = p_identifier_type
    AND m.identifier_value = p_identifier_value
    AND b.ENABLED = 1
    ORDER BY b.TYPE, b.UID;
END //
DELIMITER ;

-- 示例数据
/*
-- 插入示例大厅标识
INSERT INTO testdemo.hall_identifier_mapping
(hall_id, identifier_type, identifier_value, description, is_primary, create_time, update_time)
VALUES
(1, 'tax_bureau_code', 'TB12345', '税务局大厅编号', 1, NOW(), NOW()),
(1, 'tax_authority_id', 'TA7890', '税务机关ID', 0, NOW(), NOW()),
(1, 'region_org_code', 'R123', '区域组织代码', 0, NOW(), NOW()),
(2, 'tax_bureau_code', 'TB67890', '税务局大厅编号', 1, NOW(), NOW()),
(2, 'tax_authority_id', 'TA1234', '税务机关ID', 0, NOW(), NOW());
*/