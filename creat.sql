create table if not exists testdemo.business
(
    UID          INT(10) auto_increment
        primary key,
    PREFIX       VARCHAR(20)       null,
    NAME         VARCHAR(20)       null,
    ENABLED      TINYINT(3)        null,
    TYPE         INT(10)           null,
    HANDLE_COUNT INT(10)           null,
    IS_SPECIAL   INT(10) default 0 null
);

create table if not exists testdemo.employee
(
    UID      INT(10) auto_increment
        primary key,
    NAME     VARCHAR(20)  null,
    ID       VARCHAR(20)  null,
    USERNAME VARCHAR(20)  null,
    PASSWORD VARCHAR(32)  null,
    IMAGE    VARCHAR(255) null,
    STATUS   INT(10)      null,
    ENABLED  TINYINT(3)   null,
    ACCESS   INT(10)      null,
    NSFW_ID  VARCHAR(50)  null
);

create table if not exists testdemo.notice
(
    UID     INT(10) auto_increment
        primary key,
    CONTENT TEXT(65535) null,
    ENABLED TINYINT(3)  null
);

create table if not exists testdemo.resources
(
    ID      INT(10) auto_increment
        primary key,
    `<PERSON>EY`   VARCHAR(20)       null,
    TYPE    INT(10) default 0 null,
    URL     VARCHAR(255)      null,
    `INDEX` INT(10) default 1 null,
    ENABLED INT(10) default 1 null
);

create table if not exists testdemo.special_taxers
(
    uid          INT(10) auto_increment
        primary key,
    taxer_name   VARCHAR(50) null,
    taxer_phone  VARCHAR(50) null,
    special_text VARCHAR(50) null,
    taxer_sfz    VARCHAR(50) null
);

create table if not exists testdemo.`system`
(
    `KEY` VARCHAR(20)  not null
        primary key,
    VALUE VARCHAR(255) null,
    MEMO  VARCHAR(255) null
);

create table if not exists testdemo.test_center
(
    id         INT(10) auto_increment
        primary key,
    name       VARCHAR(100) not null,
    age        INT(10)      null,
    department VARCHAR(100) null,
    hire_date  DATE(10) null
);

create table if not exists testdemo.ticket
(
    UID             INT(10) auto_increment
        primary key,
    TKT_ID          VARCHAR(20)       null,
    TKT_INTID       INT(10)           null,
    TKT_DATE        DATE(10) null,
    TKT_TIME        TIME(8)           null,
    WIN_ID          INT(10)           null,
    BIZ_UID         INT(10)           null,
    EMP_UID         INT(10)           null,
    START_TIME      DATETIME(19)      null,
    END_TIME        DATETIME(19)      null,
    `RANK`          INT(10)           null,
    STATUS          INT(10)           null,
    FIRST_TIME      DATETIME(19)      null,
    SEC_START_TIME  DATETIME(19)      null,
    IS_LINK         INT(10) default 0 null,
    IS_AUTO         INT(10) default 0 null,
    IS_ENTERPRISE   INT(10) default 0 null,
    ENTERPRISE_ID   VARCHAR(50)       null,
    NSFW_ID         VARCHAR(50)       null,
    RANK_USER_NAME  VARCHAR(50)       null,
    RANK_USER_PHONE VARCHAR(50)       null
);

create table if not exists testdemo.ticket_exchange
(
    UID         INT(10) auto_increment
        primary key,
    DATE        DATE(10) null,
    BIZ_UID     INT(10)      null,
    TYPE        INT(10)      null,
    TAX_CODE    VARCHAR(30)  null,
    ID_CODE     VARCHAR(30)  null,
    VERIFY_CODE VARCHAR(10)  null,
    EXPIRE_TIME DATETIME(19) null,
    NJ          VARCHAR(10)  null,
    TKT_UID     INT(10)      null
);

create table if not exists testdemo.ticket_log
(
    UID             BIGINT(19)        not null,
    TKT_ID          VARCHAR(20)       null,
    TKT_DATE        DATE(10) null,
    TKT_TIME        TIME(8)           null,
    WIN_ID          VARCHAR(20)       null,
    BIZ_UID         INT(10)           null,
    BIZ_PREFIX      VARCHAR(20)       null,
    BIZ_NAME        VARCHAR(20)       null,
    EMP_UID         INT(10)           null,
    EMP_ID          VARCHAR(20)       null,
    EMP_NAME        VARCHAR(20)       null,
    START_TIME      DATETIME(19)      null,
    END_TIME        DATETIME(19)      null,
    `RANK`          INT(10)           null,
    STATUS          INT(10)           null,
    SYNC_STATUS     INT(10)           null,
    SYNC_DATE       DATETIME(19)      null,
    SEC_START_TIME  DATETIME(19)      null,
    IS_LINK         INT(10) default 0 null,
    IS_AUTO         INT(10) default 0 null,
    IS_ENTERPRISE   INT(10) default 0 null,
    ENTERPRISE_ID   VARCHAR(50)       null,
    NSFW_ID         VARCHAR(50)       null,
    RANK_USER_NAME  VARCHAR(50)       null,
    RANK_USER_PHONE VARCHAR(50)       null
);

create table if not exists testdemo.ticket_phone
(
    uid              INT(10) auto_increment
        primary key,
    tkt_uid          INT(10)      null,
    phone            VARCHAR(50)  null,
    frist_start_time DATETIME(19) null
);

create table if not exists testdemo.ticket_verify
(
    UID          INT(10) auto_increment
        primary key,
    NAME         VARCHAR(50)  null,
    CODE         VARCHAR(50)  null,
    COMPARECODE  VARCHAR(50)  null,
    COMPAREVALUE VARCHAR(20)  null,
    TKT_UID      INT(10)      null,
    DATE         DATETIME(19) null,
    PHONE        VARCHAR(50)  null
);

create table if not exists testdemo.tv_client
(
    IP           VARCHAR(50)  null,
    MACHINE_CODE VARCHAR(50)  not null,
    SVR_NAME     VARCHAR(50)  not null,
    SHOW_BIZ     VARCHAR(255) null,
    SHOW_STATUS  VARCHAR(255) null,
    SHOW_VIDEO   VARCHAR(255) null
);

create table if not exists testdemo.`window`
(
    UID          INT(10) auto_increment
        primary key,
    NAME         VARCHAR(20) null,
    LED_ADDRESS  TEXT(65535) null,
    LED_TEXT     VARCHAR(50) null,
    ENABLED      TINYINT(3)  null,
    SID          VARCHAR(50) null,
    RANK_MODE    VARCHAR(10) null,
    RANK_ADDRESS VARCHAR(50) null
);

create table if not exists testdemo.window_business
(
    BIZ_UID  INT(10) not null,
    WIN_UID  INT(10) not null,
    PRIORITY INT(10) null,
    primary key (BIZ_UID, WIN_UID)
);

create table if not exists testdemo.window_status
(
    WIN_UID     INT(10)      not null
        primary key,
    EMP_UID     INT(10)      null,
    STATUS      INT(10)      null,
    PC_NAME     VARCHAR(100) null,
    CLIENT_VER  VARCHAR(20)  null,
    UPDATE_TIME DATETIME(19) null,
    CLIENT_IP   VARCHAR(64)  null
);

