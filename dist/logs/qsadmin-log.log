2025-04-29 02:11:47.525 [main] WARN  c.b.m.core.metadata.TableInfoHelper - This "prefix" is the table primary key by @TableId annotation in Class: "com.qs.admin.taxhall.model.Autocode",So @TableField annotation will not work!
2025-04-29 02:11:47.615 [main] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.qs.admin.taxhall.model.vo.WindowBusinessVO".
2025-04-29 02:11:47.615 [main] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.qs.admin.taxhall.model.vo.WindowBusinessVO ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-04-29 02:11:48.830 [main] WARN  c.b.m.core.metadata.TableInfoHelper - This "unitCode" is the table primary key by @TableId annotation in Class: "com.qs.admin.taxhall.model.ClientInfo",So @TableField annotation will not work!
