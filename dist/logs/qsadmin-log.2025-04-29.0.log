2025-04-29 02:44:02.763 [main] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.qs.admin.taxhall.model.vo.WindowBusinessVO".
2025-04-29 02:44:02.764 [main] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.qs.admin.taxhall.model.vo.WindowBusinessVO ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-04-29 02:44:02.948 [main] WARN  c.b.m.core.metadata.TableInfoHelper - This "unitCode" is the table primary key by @TableId annotation in Class: "com.qs.admin.taxhall.model.ClientInfo",So @TableField annotation will not work!
2025-04-29 02:44:03.063 [main] WARN  c.b.m.core.metadata.TableInfoHelper - This "prefix" is the table primary key by @TableId annotation in Class: "com.qs.admin.taxhall.model.Autocode",So @TableField annotation will not work!
2025-04-29 02:55:09.753 [main] INFO  com.qs.admin.QscAdminApplication - Starting QscAdminApplication v1.0.0 on MSI with PID 4720 (D:\project\Java Projectes\qsadmin\dist\queue-api-1.0.0.jar started by KarlKyo in D:\project\Java Projectes\qsadmin\dist)
2025-04-29 02:55:09.755 [main] DEBUG com.qs.admin.QscAdminApplication - Running with Spring Boot v2.2.1.RELEASE, Spring v5.2.23.RELEASE
2025-04-29 02:55:09.755 [main] INFO  com.qs.admin.QscAdminApplication - The following profiles are active: prod
2025-04-29 02:55:16.420 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8823"]
2025-04-29 02:55:16.420 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-04-29 02:55:16.420 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.27]
2025-04-29 02:55:16.472 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-04-29 02:55:17.487 [main] INFO  c.q.a.t.s.impl.BaiShuiApiServiceImpl - 获取佰税API访问令牌
2025-04-29 02:55:17.848 [main] INFO  c.q.a.t.s.impl.BaiShuiApiServiceImpl - 成功获取佰税API访问令牌
2025-04-29 02:55:17.918 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-04-29 02:55:18.121 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-04-29 02:55:18.126 [main] DEBUG c.q.a.t.m.SystemMapper.selectList - ==>  Preparing: SELECT [KEY],[VALUE],[MEMO] FROM system WHERE ([KEY] = ?)
2025-04-29 02:55:18.147 [main] DEBUG c.q.a.t.m.SystemMapper.selectList - ==> Parameters: BS_API_APPKEY(String)
2025-04-29 02:55:18.174 [main] DEBUG c.q.a.t.m.SystemMapper.selectList - <==      Total: 1
2025-04-29 02:55:18.180 [main] DEBUG c.q.a.t.m.SystemMapper.selectList - ==>  Preparing: SELECT [KEY],[VALUE],[MEMO] FROM system WHERE ([KEY] = ?)
2025-04-29 02:55:18.180 [main] DEBUG c.q.a.t.m.SystemMapper.selectList - ==> Parameters: BS_API_TOKEN(String)
2025-04-29 02:55:18.197 [main] DEBUG c.q.a.t.m.SystemMapper.selectList - <==      Total: 1
2025-04-29 02:55:18.205 [main] DEBUG c.q.a.t.mapper.SystemMapper.update - ==>  Preparing: UPDATE system SET [VALUE]=?, [MEMO]=? WHERE [KEY]=? AND [VALUE]=? AND [MEMO]=?
2025-04-29 02:55:18.206 [main] DEBUG c.q.a.t.mapper.SystemMapper.update - ==> Parameters: QmVhcmVyIGV5SmhiR2NpT2lKSVV6STFOaUo5LmV5SmhjSEJyWlhraU9pSklXa0pUU2toWVZDSXNJbVY0Y0NJNk1UYzNOelF3TWpVeU0zMC5LMXhZZ2RjRHl2VHRDS196dEktajg3SFhfZ0Vzc3A5NzlfckNHMGJWR1lZ(String), 佰税科技API授权令牌，自动同步于Tue Apr 29 02:55:18 CST 2025(String), BS_API_TOKEN(String), QmVhcmVyIGV5SmhiR2NpT2lKSVV6STFOaUo5LmV5SmhjSEJyWlhraU9pSklXa0pUU2toWVZDSXNJbVY0Y0NJNk1UYzNOelF3TWpVeU0zMC5LMXhZZ2RjRHl2VHRDS196dEktajg3SFhfZ0Vzc3A5NzlfckNHMGJWR1lZ(String), 佰税科技API授权令牌，自动同步于Tue Apr 29 02:55:18 CST 2025(String)
2025-04-29 02:55:18.225 [main] DEBUG c.q.a.t.mapper.SystemMapper.update - <==    Updates: 0
2025-04-29 02:55:18.225 [main] INFO  c.q.a.t.task.BaiShuiTokenSyncTask - TOKEN已同步更新
2025-04-29 02:55:18.515 [main] WARN  c.b.m.core.metadata.TableInfoHelper - This "unitCode" is the table primary key by @TableId annotation in Class: "com.qs.admin.taxhall.model.ClientInfo",So @TableField annotation will not work!
2025-04-29 02:55:18.566 [main] WARN  c.b.m.core.metadata.TableInfoHelper - This "prefix" is the table primary key by @TableId annotation in Class: "com.qs.admin.taxhall.model.Autocode",So @TableField annotation will not work!
2025-04-29 02:55:18.729 [main] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.qs.admin.taxhall.model.vo.WindowBusinessVO".
2025-04-29 02:55:18.729 [main] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.qs.admin.taxhall.model.vo.WindowBusinessVO ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-04-29 02:55:19.117 [main] INFO  s.d.s.w.PropertySourcedRequestMappingHandlerMapping - Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2Controller#getDocumentation(String, HttpServletRequest)]
2025-04-29 02:55:19.558 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Context refreshed
2025-04-29 02:55:19.572 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
2025-04-29 02:55:19.623 [main] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
2025-04-29 02:55:19.740 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: addUsingPOST_1
2025-04-29 02:55:19.744 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: detailUsingGET_1
2025-04-29 02:55:19.759 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPUT_1
2025-04-29 02:55:19.764 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: addUsingPOST_2
2025-04-29 02:55:19.766 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deleteUsingDELETE_1
2025-04-29 02:55:19.767 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: detailUsingGET_2
2025-04-29 02:55:19.769 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: listUsingGET_1
2025-04-29 02:55:19.771 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPUT_2
2025-04-29 02:55:19.775 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: addUsingPOST_3
2025-04-29 02:55:19.778 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deleteUsingDELETE_2
2025-04-29 02:55:19.779 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: detailUsingGET_3
2025-04-29 02:55:19.781 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: listUsingGET_2
2025-04-29 02:55:19.782 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPUT_3
2025-04-29 02:55:19.785 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: addUsingPOST_4
2025-04-29 02:55:19.787 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deleteUsingDELETE_3
2025-04-29 02:55:19.788 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: detailUsingGET_4
2025-04-29 02:55:19.789 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: listUsingGET_3
2025-04-29 02:55:19.791 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPUT_4
2025-04-29 02:55:19.792 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: listUsingGET_4
2025-04-29 02:55:19.795 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPUT_5
2025-04-29 02:55:19.797 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: addUsingPOST_5
2025-04-29 02:55:19.798 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deleteUsingDELETE_4
2025-04-29 02:55:19.799 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: detailUsingGET_5
2025-04-29 02:55:19.800 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: listUsingGET_5
2025-04-29 02:55:19.801 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPUT_6
2025-04-29 02:55:19.804 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: addUsingPOST_6
2025-04-29 02:55:19.805 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: batchDeleteUsingDELETE_1
2025-04-29 02:55:19.807 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: detailUsingGET_6
2025-04-29 02:55:19.808 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: listUsingGET_6
2025-04-29 02:55:19.809 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPUT_7
2025-04-29 02:55:19.813 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: addUsingPOST_7
2025-04-29 02:55:19.814 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deleteUsingDELETE_5
2025-04-29 02:55:19.816 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: detailUsingGET_7
2025-04-29 02:55:19.817 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: listUsingGET_7
2025-04-29 02:55:19.818 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPUT_8
2025-04-29 02:55:19.821 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: addUsingPOST_8
2025-04-29 02:55:19.827 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: listUsingGET_8
2025-04-29 02:55:19.829 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPUT_9
2025-04-29 02:55:19.832 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: addUsingPOST_9
2025-04-29 02:55:19.836 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deleteUsingDELETE_6
2025-04-29 02:55:19.838 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: detailUsingGET_8
2025-04-29 02:55:19.839 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: listUsingGET_9
2025-04-29 02:55:19.840 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPUT_10
2025-04-29 02:55:19.842 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: addUsingPOST_10
2025-04-29 02:55:19.843 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: detailUsingGET_9
2025-04-29 02:55:19.844 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: listUsingGET_10
2025-04-29 02:55:19.845 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPUT_11
2025-04-29 02:55:19.847 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: addUsingPOST_11
2025-04-29 02:55:19.849 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deleteUsingDELETE_7
2025-04-29 02:55:19.849 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: detailUsingGET_10
2025-04-29 02:55:19.851 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: listUsingGET_11
2025-04-29 02:55:19.852 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPUT_12
2025-04-29 02:55:19.855 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: addUsingPOST_12
2025-04-29 02:55:19.856 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deleteUsingDELETE_8
2025-04-29 02:55:19.857 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: detailUsingGET_11
2025-04-29 02:55:19.858 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: listUsingGET_12
2025-04-29 02:55:19.859 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPUT_13
2025-04-29 02:55:19.861 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: addUsingPOST_13
2025-04-29 02:55:19.863 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: batchDeleteUsingDELETE_2
2025-04-29 02:55:19.864 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deleteUsingDELETE_9
2025-04-29 02:55:19.867 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: listUsingGET_13
2025-04-29 02:55:19.868 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPUT_14
2025-04-29 02:55:19.871 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: addUsingPOST_14
2025-04-29 02:55:19.872 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deleteUsingDELETE_10
2025-04-29 02:55:19.873 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: detailUsingGET_12
2025-04-29 02:55:19.874 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPUT_15
2025-04-29 02:55:19.887 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8823"]
2025-04-29 02:55:19.909 [main] INFO  com.qs.admin.QscAdminApplication - Started QscAdminApplication in 10.546 seconds (JVM running for 10.774)
2025-04-29 02:57:44.444 [main] INFO  com.qs.admin.QscAdminApplication - Starting QscAdminApplication v1.0.0 on MSI with PID 37372 (D:\project\Java Projectes\qsadmin\dist\queue-api-1.0.0.jar started by KarlKyo in D:\project\Java Projectes\qsadmin\dist)
2025-04-29 02:57:44.445 [main] DEBUG com.qs.admin.QscAdminApplication - Running with Spring Boot v2.2.1.RELEASE, Spring v5.2.23.RELEASE
2025-04-29 02:57:44.446 [main] INFO  com.qs.admin.QscAdminApplication - The following profiles are active: prod
2025-04-29 02:57:51.151 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8823"]
2025-04-29 02:57:51.152 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-04-29 02:57:51.152 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.27]
2025-04-29 02:57:51.205 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-04-29 02:57:52.170 [main] WARN  c.b.m.core.metadata.TableInfoHelper - This "prefix" is the table primary key by @TableId annotation in Class: "com.qs.admin.taxhall.model.Autocode",So @TableField annotation will not work!
2025-04-29 02:57:52.288 [main] WARN  c.b.m.core.metadata.TableInfoHelper - This "unitCode" is the table primary key by @TableId annotation in Class: "com.qs.admin.taxhall.model.ClientInfo",So @TableField annotation will not work!
2025-04-29 02:57:52.447 [main] INFO  c.q.a.t.s.impl.BaiShuiApiServiceImpl - 获取佰税API访问令牌
2025-04-29 02:57:52.652 [main] INFO  c.q.a.t.s.impl.BaiShuiApiServiceImpl - 成功获取佰税API访问令牌
2025-04-29 02:57:52.719 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-04-29 02:57:52.910 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-04-29 02:57:52.918 [main] DEBUG c.q.a.t.m.SystemMapper.selectList - ==>  Preparing: SELECT [KEY],[VALUE],[MEMO] FROM system WHERE ([KEY] = ?)
2025-04-29 02:57:52.933 [main] DEBUG c.q.a.t.m.SystemMapper.selectList - ==> Parameters: BS_API_APPKEY(String)
2025-04-29 02:57:52.959 [main] DEBUG c.q.a.t.m.SystemMapper.selectList - <==      Total: 1
2025-04-29 02:57:52.966 [main] DEBUG c.q.a.t.m.SystemMapper.selectList - ==>  Preparing: SELECT [KEY],[VALUE],[MEMO] FROM system WHERE ([KEY] = ?)
2025-04-29 02:57:52.967 [main] DEBUG c.q.a.t.m.SystemMapper.selectList - ==> Parameters: BS_API_TOKEN(String)
2025-04-29 02:57:52.981 [main] DEBUG c.q.a.t.m.SystemMapper.selectList - <==      Total: 1
2025-04-29 02:57:52.989 [main] DEBUG c.q.a.t.mapper.SystemMapper.update - ==>  Preparing: UPDATE system SET [VALUE]=?, [MEMO]=? WHERE [KEY]=? AND [VALUE]=? AND [MEMO]=?
2025-04-29 02:57:52.990 [main] DEBUG c.q.a.t.mapper.SystemMapper.update - ==> Parameters: QmVhcmVyIGV5SmhiR2NpT2lKSVV6STFOaUo5LmV5SmhjSEJyWlhraU9pSklXa0pUU2toWVZDSXNJbVY0Y0NJNk1UYzNOelF3TWpZM04zMC5XMEE4eFFmbWpNZ0lkNGlvdldYQ0tpSWVEb19KWG1jZHkzUnU4Z3pSUmdF(String), 佰税科技API授权令牌，自动同步于Tue Apr 29 02:57:52 CST 2025(String), BS_API_TOKEN(String), QmVhcmVyIGV5SmhiR2NpT2lKSVV6STFOaUo5LmV5SmhjSEJyWlhraU9pSklXa0pUU2toWVZDSXNJbVY0Y0NJNk1UYzNOelF3TWpZM04zMC5XMEE4eFFmbWpNZ0lkNGlvdldYQ0tpSWVEb19KWG1jZHkzUnU4Z3pSUmdF(String), 佰税科技API授权令牌，自动同步于Tue Apr 29 02:57:52 CST 2025(String)
2025-04-29 02:57:53.005 [main] DEBUG c.q.a.t.mapper.SystemMapper.update - <==    Updates: 0
2025-04-29 02:57:53.005 [main] INFO  c.q.a.t.task.BaiShuiTokenSyncTask - TOKEN已同步更新
2025-04-29 02:57:53.173 [main] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.qs.admin.taxhall.model.vo.WindowBusinessVO".
2025-04-29 02:57:53.176 [main] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.qs.admin.taxhall.model.vo.WindowBusinessVO ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-04-29 02:57:54.406 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8823"]
2025-04-29 02:57:54.422 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Pausing ProtocolHandler ["http-nio-8823"]
2025-04-29 02:57:54.423 [main] INFO  o.a.catalina.core.StandardService - Stopping service [Tomcat]
2025-04-29 02:57:54.425 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Stopping ProtocolHandler ["http-nio-8823"]
2025-04-29 02:57:54.425 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Destroying ProtocolHandler ["http-nio-8823"]
2025-04-29 02:57:54.430 [main] ERROR o.s.b.d.LoggingFailureAnalysisReporter - 

***************************
APPLICATION FAILED TO START
***************************

Description:

Web server failed to start. Port 8823 was already in use.

Action:

Identify and stop the process that's listening on port 8823 or configure this application to listen on another port.

2025-04-29 02:57:54.435 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-04-29 02:57:54.437 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-04-29 02:58:22.494 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-04-29 02:58:22.496 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-04-29 03:02:24.004 [main] INFO  com.qs.admin.QscAdminApplication - Starting QscAdminApplication v1.0.0 on MSI with PID 8956 (D:\project\Java Projectes\qsadmin\dist\queue-api-1.0.0.jar started by KarlKyo in D:\project\Java Projectes\qsadmin\dist)
2025-04-29 03:02:24.005 [main] DEBUG com.qs.admin.QscAdminApplication - Running with Spring Boot v2.2.1.RELEASE, Spring v5.2.23.RELEASE
2025-04-29 03:02:24.005 [main] INFO  com.qs.admin.QscAdminApplication - The following profiles are active: prod
2025-04-29 03:02:30.441 [main] WARN  io.undertow.websockets.jsr - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
2025-04-29 03:02:30.460 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-04-29 03:02:31.252 [main] INFO  c.q.a.t.s.impl.BaiShuiApiServiceImpl - 获取佰税API访问令牌
2025-04-29 03:02:31.599 [main] INFO  c.q.a.t.s.impl.BaiShuiApiServiceImpl - 成功获取佰税API访问令牌
2025-04-29 03:02:31.664 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-04-29 03:02:31.935 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-04-29 03:02:31.940 [main] DEBUG c.q.a.t.m.SystemMapper.selectList - ==>  Preparing: SELECT [KEY],[VALUE],[MEMO] FROM system WHERE ([KEY] = ?)
2025-04-29 03:02:31.955 [main] DEBUG c.q.a.t.m.SystemMapper.selectList - ==> Parameters: BS_API_APPKEY(String)
2025-04-29 03:02:31.986 [main] DEBUG c.q.a.t.m.SystemMapper.selectList - <==      Total: 1
2025-04-29 03:02:31.992 [main] DEBUG c.q.a.t.m.SystemMapper.selectList - ==>  Preparing: SELECT [KEY],[VALUE],[MEMO] FROM system WHERE ([KEY] = ?)
2025-04-29 03:02:31.992 [main] DEBUG c.q.a.t.m.SystemMapper.selectList - ==> Parameters: BS_API_TOKEN(String)
2025-04-29 03:02:32.008 [main] DEBUG c.q.a.t.m.SystemMapper.selectList - <==      Total: 1
2025-04-29 03:02:32.013 [main] DEBUG c.q.a.t.mapper.SystemMapper.update - ==>  Preparing: UPDATE system SET [VALUE]=?, [MEMO]=? WHERE [KEY]=? AND [VALUE]=? AND [MEMO]=?
2025-04-29 03:02:32.013 [main] DEBUG c.q.a.t.mapper.SystemMapper.update - ==> Parameters: QmVhcmVyIGV5SmhiR2NpT2lKSVV6STFOaUo5LmV5SmhjSEJyWlhraU9pSklXa0pUU2toWVZDSXNJbVY0Y0NJNk1UYzNOelF3TWprMU5uMC5MUXdCd3VrRW1HT19Nbk5ocWVKLURNWGJtMWIyWUZsUkZiVDNwYlA2b0lZ(String), 佰税科技API授权令牌，自动同步于Tue Apr 29 03:02:32 CST 2025(String), BS_API_TOKEN(String), QmVhcmVyIGV5SmhiR2NpT2lKSVV6STFOaUo5LmV5SmhjSEJyWlhraU9pSklXa0pUU2toWVZDSXNJbVY0Y0NJNk1UYzNOelF3TWprMU5uMC5MUXdCd3VrRW1HT19Nbk5ocWVKLURNWGJtMWIyWUZsUkZiVDNwYlA2b0lZ(String), 佰税科技API授权令牌，自动同步于Tue Apr 29 03:02:32 CST 2025(String)
2025-04-29 03:02:32.031 [main] DEBUG c.q.a.t.mapper.SystemMapper.update - <==    Updates: 0
2025-04-29 03:02:32.031 [main] INFO  c.q.a.t.task.BaiShuiTokenSyncTask - TOKEN已同步更新
2025-04-29 03:02:32.185 [main] WARN  c.b.m.core.metadata.TableInfoHelper - This "prefix" is the table primary key by @TableId annotation in Class: "com.qs.admin.taxhall.model.Autocode",So @TableField annotation will not work!
2025-04-29 03:02:32.353 [main] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.qs.admin.taxhall.model.vo.WindowBusinessVO".
2025-04-29 03:02:32.353 [main] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.qs.admin.taxhall.model.vo.WindowBusinessVO ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-04-29 03:02:32.393 [main] WARN  c.b.m.core.metadata.TableInfoHelper - This "unitCode" is the table primary key by @TableId annotation in Class: "com.qs.admin.taxhall.model.ClientInfo",So @TableField annotation will not work!
2025-04-29 03:02:33.576 [main] INFO  io.undertow - starting server: Undertow - 2.0.27.Final
2025-04-29 03:02:33.582 [main] INFO  org.xnio - XNIO version 3.3.8.Final
2025-04-29 03:02:33.588 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.3.8.Final
2025-04-29 03:02:33.624 [main] INFO  com.qs.admin.QscAdminApplication - Started QscAdminApplication in 9.989 seconds (JVM running for 10.197)
2025-04-29 03:07:04.694 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-04-29 03:07:04.696 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-04-29 03:07:04.698 [SpringContextShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.0.27.Final
2025-04-29 03:07:34.944 [main] INFO  com.qs.admin.QscAdminApplication - Starting QscAdminApplication v1.0.0 on MSI with PID 37884 (D:\project\Java Projectes\qsadmin\dist\queue-api-1.0.0.jar started by KarlKyo in D:\project\Java Projectes\qsadmin\dist)
2025-04-29 03:07:34.946 [main] DEBUG com.qs.admin.QscAdminApplication - Running with Spring Boot v2.2.1.RELEASE, Spring v5.2.23.RELEASE
2025-04-29 03:07:34.946 [main] INFO  com.qs.admin.QscAdminApplication - The following profiles are active: prod
2025-04-29 03:07:41.338 [main] WARN  io.undertow.websockets.jsr - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
2025-04-29 03:07:41.357 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-04-29 03:07:42.108 [main] INFO  c.q.a.t.s.impl.BaiShuiApiServiceImpl - 获取佰税API访问令牌
2025-04-29 03:07:42.286 [main] INFO  c.q.a.t.s.impl.BaiShuiApiServiceImpl - 成功获取佰税API访问令牌
2025-04-29 03:07:42.346 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-04-29 03:07:42.523 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-04-29 03:07:42.527 [main] DEBUG c.q.a.t.m.SystemMapper.selectList - ==>  Preparing: SELECT [KEY],[VALUE],[MEMO] FROM system WHERE ([KEY] = ?)
2025-04-29 03:07:42.545 [main] DEBUG c.q.a.t.m.SystemMapper.selectList - ==> Parameters: BS_API_APPKEY(String)
2025-04-29 03:07:42.573 [main] DEBUG c.q.a.t.m.SystemMapper.selectList - <==      Total: 1
2025-04-29 03:07:42.578 [main] DEBUG c.q.a.t.m.SystemMapper.selectList - ==>  Preparing: SELECT [KEY],[VALUE],[MEMO] FROM system WHERE ([KEY] = ?)
2025-04-29 03:07:42.579 [main] DEBUG c.q.a.t.m.SystemMapper.selectList - ==> Parameters: BS_API_TOKEN(String)
2025-04-29 03:07:42.598 [main] DEBUG c.q.a.t.m.SystemMapper.selectList - <==      Total: 1
2025-04-29 03:07:42.606 [main] DEBUG c.q.a.t.mapper.SystemMapper.update - ==>  Preparing: UPDATE system SET [VALUE]=?, [MEMO]=? WHERE [KEY]=? AND [VALUE]=? AND [MEMO]=?
2025-04-29 03:07:42.606 [main] DEBUG c.q.a.t.mapper.SystemMapper.update - ==> Parameters: QmVhcmVyIGV5SmhiR2NpT2lKSVV6STFOaUo5LmV5SmhjSEJyWlhraU9pSklXa0pUU2toWVZDSXNJbVY0Y0NJNk1UYzNOelF3TXpJMk4zMC5pTHl2VERaMUpSUmxBQ0ZhbkhIZnNhT2RkaG53R2lIMlZQNkJSdi1NclpZ(String), 佰税科技API授权令牌，自动同步于Tue Apr 29 03:07:42 CST 2025(String), BS_API_TOKEN(String), QmVhcmVyIGV5SmhiR2NpT2lKSVV6STFOaUo5LmV5SmhjSEJyWlhraU9pSklXa0pUU2toWVZDSXNJbVY0Y0NJNk1UYzNOelF3TXpJMk4zMC5pTHl2VERaMUpSUmxBQ0ZhbkhIZnNhT2RkaG53R2lIMlZQNkJSdi1NclpZ(String), 佰税科技API授权令牌，自动同步于Tue Apr 29 03:07:42 CST 2025(String)
2025-04-29 03:07:42.626 [main] DEBUG c.q.a.t.mapper.SystemMapper.update - <==    Updates: 0
2025-04-29 03:07:42.627 [main] INFO  c.q.a.t.task.BaiShuiTokenSyncTask - TOKEN已同步更新
2025-04-29 03:07:42.967 [main] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.qs.admin.taxhall.model.vo.WindowBusinessVO".
2025-04-29 03:07:42.967 [main] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.qs.admin.taxhall.model.vo.WindowBusinessVO ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-04-29 03:07:44.153 [main] INFO  io.undertow - starting server: Undertow - 2.0.27.Final
2025-04-29 03:07:44.159 [main] INFO  org.xnio - XNIO version 3.3.8.Final
2025-04-29 03:07:44.164 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.3.8.Final
2025-04-29 03:07:44.200 [main] INFO  com.qs.admin.QscAdminApplication - Started QscAdminApplication in 9.617 seconds (JVM running for 9.844)
2025-04-29 03:08:27.844 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-04-29 03:08:27.846 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-04-29 03:08:27.848 [SpringContextShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.0.27.Final
2025-04-29 16:27:42.797 [main] INFO  com.qs.admin.QscAdminApplication - Starting QscAdminApplication v1.0.0 on MSI with PID 11872 (D:\project\Java Projectes\qsadmin\dist\queue-api-1.0.0.jar started by KarlKyo in D:\project\Java Projectes\qsadmin\dist)
2025-04-29 16:27:42.799 [main] DEBUG com.qs.admin.QscAdminApplication - Running with Spring Boot v2.2.1.RELEASE, Spring v5.2.23.RELEASE
2025-04-29 16:27:42.799 [main] INFO  com.qs.admin.QscAdminApplication - The following profiles are active: prod
2025-04-29 16:27:49.412 [main] WARN  io.undertow.websockets.jsr - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
2025-04-29 16:27:49.433 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-04-29 16:27:50.640 [main] INFO  c.q.a.t.s.impl.BaiShuiApiServiceImpl - 获取佰税API访问令牌
2025-04-29 16:27:50.646 [main] INFO  c.q.a.t.s.impl.BaiShuiApiServiceImpl - tokenUrl=http://80.12.140.31:8722/oauth/token
2025-04-29 16:28:11.732 [main] ERROR c.q.a.t.s.impl.BaiShuiApiServiceImpl - 获取佰税API访问令牌异常
org.springframework.web.client.ResourceAccessException: I/O error on POST request for "http://80.12.140.31:8722/oauth/token": Connection timed out: connect; nested exception is java.net.ConnectException: Connection timed out: connect
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:751)
	at org.springframework.web.client.RestTemplate.execute(RestTemplate.java:677)
	at org.springframework.web.client.RestTemplate.postForObject(RestTemplate.java:421)
	at com.qs.admin.taxhall.service.impl.BaiShuiApiServiceImpl.getToken(BaiShuiApiServiceImpl.java:73)
	at com.qs.admin.taxhall.task.BaiShuiTokenSyncTask.syncAppKeyAndToken(BaiShuiTokenSyncTask.java:49)
	at com.qs.admin.taxhall.task.BaiShuiTokenSyncTask.initSync(BaiShuiTokenSyncTask.java:29)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleElement.invoke(InitDestroyAnnotationBeanPostProcessor.java:389)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeInitMethods(InitDestroyAnnotationBeanPostProcessor.java:333)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeInitialization(InitDestroyAnnotationBeanPostProcessor.java:157)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsBeforeInitialization(AbstractAutowireCapableBeanFactory.java:416)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1795)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:595)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:517)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:323)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:222)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:321)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1287)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1207)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:636)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:116)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:397)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1429)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:594)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:517)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:323)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:222)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:321)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:879)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:879)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:551)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:141)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:747)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:397)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:315)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1226)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1215)
	at com.qs.admin.QscAdminApplication.main(QscAdminApplication.java:19)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.boot.loader.MainMethodRunner.run(MainMethodRunner.java:48)
	at org.springframework.boot.loader.Launcher.launch(Launcher.java:87)
	at org.springframework.boot.loader.Launcher.launch(Launcher.java:51)
	at org.springframework.boot.loader.JarLauncher.main(JarLauncher.java:52)
Caused by: java.net.ConnectException: Connection timed out: connect
	at java.net.DualStackPlainSocketImpl.connect0(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:75)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:476)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:218)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:200)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:162)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:394)
	at java.net.Socket.connect(Socket.java:606)
	at java.net.Socket.connect(Socket.java:555)
	at sun.net.NetworkClient.doConnect(NetworkClient.java:180)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:463)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:558)
	at sun.net.www.http.HttpClient.<init>(HttpClient.java:242)
	at sun.net.www.http.HttpClient.New(HttpClient.java:339)
	at sun.net.www.http.HttpClient.New(HttpClient.java:357)
	at sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1240)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1167)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1061)
	at sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:995)
	at org.springframework.http.client.SimpleBufferingClientHttpRequest.executeInternal(SimpleBufferingClientHttpRequest.java:76)
	at org.springframework.http.client.AbstractBufferingClientHttpRequest.executeInternal(AbstractBufferingClientHttpRequest.java:48)
	at org.springframework.http.client.AbstractClientHttpRequest.execute(AbstractClientHttpRequest.java:53)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:742)
	... 51 common frames omitted
2025-04-29 16:28:11.733 [main] ERROR c.q.a.t.task.BaiShuiTokenSyncTask - 同步佰税API APPKEY和TOKEN异常（不影响项目运行）
java.lang.RuntimeException: 授权请求异常: I/O error on POST request for "http://80.12.140.31:8722/oauth/token": Connection timed out: connect; nested exception is java.net.ConnectException: Connection timed out: connect
	at com.qs.admin.taxhall.service.impl.BaiShuiApiServiceImpl.getToken(BaiShuiApiServiceImpl.java:85)
	at com.qs.admin.taxhall.task.BaiShuiTokenSyncTask.syncAppKeyAndToken(BaiShuiTokenSyncTask.java:49)
	at com.qs.admin.taxhall.task.BaiShuiTokenSyncTask.initSync(BaiShuiTokenSyncTask.java:29)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleElement.invoke(InitDestroyAnnotationBeanPostProcessor.java:389)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeInitMethods(InitDestroyAnnotationBeanPostProcessor.java:333)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeInitialization(InitDestroyAnnotationBeanPostProcessor.java:157)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsBeforeInitialization(AbstractAutowireCapableBeanFactory.java:416)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1795)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:595)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:517)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:323)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:222)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:321)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1287)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1207)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:636)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:116)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:397)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1429)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:594)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:517)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:323)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:222)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:321)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:879)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:879)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:551)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:141)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:747)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:397)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:315)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1226)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1215)
	at com.qs.admin.QscAdminApplication.main(QscAdminApplication.java:19)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.boot.loader.MainMethodRunner.run(MainMethodRunner.java:48)
	at org.springframework.boot.loader.Launcher.launch(Launcher.java:87)
	at org.springframework.boot.loader.Launcher.launch(Launcher.java:51)
	at org.springframework.boot.loader.JarLauncher.main(JarLauncher.java:52)
Caused by: org.springframework.web.client.ResourceAccessException: I/O error on POST request for "http://80.12.140.31:8722/oauth/token": Connection timed out: connect; nested exception is java.net.ConnectException: Connection timed out: connect
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:751)
	at org.springframework.web.client.RestTemplate.execute(RestTemplate.java:677)
	at org.springframework.web.client.RestTemplate.postForObject(RestTemplate.java:421)
	at com.qs.admin.taxhall.service.impl.BaiShuiApiServiceImpl.getToken(BaiShuiApiServiceImpl.java:73)
	... 48 common frames omitted
Caused by: java.net.ConnectException: Connection timed out: connect
	at java.net.DualStackPlainSocketImpl.connect0(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:75)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:476)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:218)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:200)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:162)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:394)
	at java.net.Socket.connect(Socket.java:606)
	at java.net.Socket.connect(Socket.java:555)
	at sun.net.NetworkClient.doConnect(NetworkClient.java:180)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:463)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:558)
	at sun.net.www.http.HttpClient.<init>(HttpClient.java:242)
	at sun.net.www.http.HttpClient.New(HttpClient.java:339)
	at sun.net.www.http.HttpClient.New(HttpClient.java:357)
	at sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1240)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1167)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1061)
	at sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:995)
	at org.springframework.http.client.SimpleBufferingClientHttpRequest.executeInternal(SimpleBufferingClientHttpRequest.java:76)
	at org.springframework.http.client.AbstractBufferingClientHttpRequest.executeInternal(AbstractBufferingClientHttpRequest.java:48)
	at org.springframework.http.client.AbstractClientHttpRequest.execute(AbstractClientHttpRequest.java:53)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:742)
	... 51 common frames omitted
2025-04-29 16:28:11.807 [main] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.qs.admin.taxhall.model.vo.WindowBusinessVO".
2025-04-29 16:28:11.807 [main] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.qs.admin.taxhall.model.vo.WindowBusinessVO ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-04-29 16:28:13.160 [main] INFO  io.undertow - starting server: Undertow - 2.0.27.Final
2025-04-29 16:28:13.167 [main] INFO  org.xnio - XNIO version 3.3.8.Final
2025-04-29 16:28:13.175 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.3.8.Final
2025-04-29 16:28:13.221 [main] INFO  com.qs.admin.QscAdminApplication - Started QscAdminApplication in 30.862 seconds (JVM running for 31.227)
2025-04-29 16:38:53.733 [SpringContextShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.0.27.Final
2025-04-29 16:39:04.582 [main] INFO  com.qs.admin.QscAdminApplication - Starting QscAdminApplication v1.0.0 on MSI with PID 32316 (D:\project\Java Projectes\qsadmin\dist\queue-api-1.0.0.jar started by KarlKyo in D:\project\Java Projectes\qsadmin\dist)
2025-04-29 16:39:04.584 [main] DEBUG com.qs.admin.QscAdminApplication - Running with Spring Boot v2.2.1.RELEASE, Spring v5.2.23.RELEASE
2025-04-29 16:39:04.584 [main] INFO  com.qs.admin.QscAdminApplication - The following profiles are active: prod
2025-04-29 16:39:11.135 [main] WARN  io.undertow.websockets.jsr - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
2025-04-29 16:39:11.155 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-04-29 16:39:12.231 [main] INFO  c.q.a.t.s.impl.BaiShuiApiServiceImpl - 获取佰税API访问令牌
2025-04-29 16:39:12.234 [main] INFO  c.q.a.t.s.impl.BaiShuiApiServiceImpl - tokenUrl=http://80.12.140.31:8722/oauth/token
2025-04-29 16:39:33.312 [main] ERROR c.q.a.t.s.impl.BaiShuiApiServiceImpl - 获取佰税API访问令牌异常
org.springframework.web.client.ResourceAccessException: I/O error on POST request for "http://80.12.140.31:8722/oauth/token": Connection timed out: connect; nested exception is java.net.ConnectException: Connection timed out: connect
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:751)
	at org.springframework.web.client.RestTemplate.execute(RestTemplate.java:677)
	at org.springframework.web.client.RestTemplate.postForObject(RestTemplate.java:421)
	at com.qs.admin.taxhall.service.impl.BaiShuiApiServiceImpl.getToken(BaiShuiApiServiceImpl.java:73)
	at com.qs.admin.taxhall.task.BaiShuiTokenSyncTask.syncAppKeyAndToken(BaiShuiTokenSyncTask.java:49)
	at com.qs.admin.taxhall.task.BaiShuiTokenSyncTask.initSync(BaiShuiTokenSyncTask.java:29)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleElement.invoke(InitDestroyAnnotationBeanPostProcessor.java:389)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeInitMethods(InitDestroyAnnotationBeanPostProcessor.java:333)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeInitialization(InitDestroyAnnotationBeanPostProcessor.java:157)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsBeforeInitialization(AbstractAutowireCapableBeanFactory.java:416)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1795)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:595)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:517)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:323)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:222)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:321)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1287)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1207)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:636)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:116)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:397)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1429)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:594)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:517)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:323)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:222)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:321)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:879)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:879)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:551)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:141)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:747)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:397)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:315)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1226)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1215)
	at com.qs.admin.QscAdminApplication.main(QscAdminApplication.java:19)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.boot.loader.MainMethodRunner.run(MainMethodRunner.java:48)
	at org.springframework.boot.loader.Launcher.launch(Launcher.java:87)
	at org.springframework.boot.loader.Launcher.launch(Launcher.java:51)
	at org.springframework.boot.loader.JarLauncher.main(JarLauncher.java:52)
Caused by: java.net.ConnectException: Connection timed out: connect
	at java.net.DualStackPlainSocketImpl.connect0(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:75)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:476)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:218)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:200)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:162)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:394)
	at java.net.Socket.connect(Socket.java:606)
	at java.net.Socket.connect(Socket.java:555)
	at sun.net.NetworkClient.doConnect(NetworkClient.java:180)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:463)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:558)
	at sun.net.www.http.HttpClient.<init>(HttpClient.java:242)
	at sun.net.www.http.HttpClient.New(HttpClient.java:339)
	at sun.net.www.http.HttpClient.New(HttpClient.java:357)
	at sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1240)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1167)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1061)
	at sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:995)
	at org.springframework.http.client.SimpleBufferingClientHttpRequest.executeInternal(SimpleBufferingClientHttpRequest.java:76)
	at org.springframework.http.client.AbstractBufferingClientHttpRequest.executeInternal(AbstractBufferingClientHttpRequest.java:48)
	at org.springframework.http.client.AbstractClientHttpRequest.execute(AbstractClientHttpRequest.java:53)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:742)
	... 51 common frames omitted
2025-04-29 16:39:33.313 [main] ERROR c.q.a.t.task.BaiShuiTokenSyncTask - 同步佰税API APPKEY和TOKEN异常（不影响项目运行）
java.lang.RuntimeException: 授权请求异常: I/O error on POST request for "http://80.12.140.31:8722/oauth/token": Connection timed out: connect; nested exception is java.net.ConnectException: Connection timed out: connect
	at com.qs.admin.taxhall.service.impl.BaiShuiApiServiceImpl.getToken(BaiShuiApiServiceImpl.java:85)
	at com.qs.admin.taxhall.task.BaiShuiTokenSyncTask.syncAppKeyAndToken(BaiShuiTokenSyncTask.java:49)
	at com.qs.admin.taxhall.task.BaiShuiTokenSyncTask.initSync(BaiShuiTokenSyncTask.java:29)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleElement.invoke(InitDestroyAnnotationBeanPostProcessor.java:389)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeInitMethods(InitDestroyAnnotationBeanPostProcessor.java:333)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeInitialization(InitDestroyAnnotationBeanPostProcessor.java:157)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsBeforeInitialization(AbstractAutowireCapableBeanFactory.java:416)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1795)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:595)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:517)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:323)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:222)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:321)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1287)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1207)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:636)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:116)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:397)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1429)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:594)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:517)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:323)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:222)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:321)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:879)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:879)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:551)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:141)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:747)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:397)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:315)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1226)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1215)
	at com.qs.admin.QscAdminApplication.main(QscAdminApplication.java:19)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.boot.loader.MainMethodRunner.run(MainMethodRunner.java:48)
	at org.springframework.boot.loader.Launcher.launch(Launcher.java:87)
	at org.springframework.boot.loader.Launcher.launch(Launcher.java:51)
	at org.springframework.boot.loader.JarLauncher.main(JarLauncher.java:52)
Caused by: org.springframework.web.client.ResourceAccessException: I/O error on POST request for "http://80.12.140.31:8722/oauth/token": Connection timed out: connect; nested exception is java.net.ConnectException: Connection timed out: connect
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:751)
	at org.springframework.web.client.RestTemplate.execute(RestTemplate.java:677)
	at org.springframework.web.client.RestTemplate.postForObject(RestTemplate.java:421)
	at com.qs.admin.taxhall.service.impl.BaiShuiApiServiceImpl.getToken(BaiShuiApiServiceImpl.java:73)
	... 48 common frames omitted
Caused by: java.net.ConnectException: Connection timed out: connect
	at java.net.DualStackPlainSocketImpl.connect0(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:75)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:476)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:218)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:200)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:162)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:394)
	at java.net.Socket.connect(Socket.java:606)
	at java.net.Socket.connect(Socket.java:555)
	at sun.net.NetworkClient.doConnect(NetworkClient.java:180)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:463)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:558)
	at sun.net.www.http.HttpClient.<init>(HttpClient.java:242)
	at sun.net.www.http.HttpClient.New(HttpClient.java:339)
	at sun.net.www.http.HttpClient.New(HttpClient.java:357)
	at sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1240)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1167)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1061)
	at sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:995)
	at org.springframework.http.client.SimpleBufferingClientHttpRequest.executeInternal(SimpleBufferingClientHttpRequest.java:76)
	at org.springframework.http.client.AbstractBufferingClientHttpRequest.executeInternal(AbstractBufferingClientHttpRequest.java:48)
	at org.springframework.http.client.AbstractClientHttpRequest.execute(AbstractClientHttpRequest.java:53)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:742)
	... 51 common frames omitted
2025-04-29 16:39:33.380 [main] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.qs.admin.taxhall.model.vo.WindowBusinessVO".
2025-04-29 16:39:33.381 [main] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.qs.admin.taxhall.model.vo.WindowBusinessVO ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-04-29 16:39:34.633 [main] INFO  io.undertow - starting server: Undertow - 2.0.27.Final
2025-04-29 16:39:34.639 [main] INFO  org.xnio - XNIO version 3.3.8.Final
2025-04-29 16:39:34.648 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.3.8.Final
2025-04-29 16:39:34.683 [main] INFO  com.qs.admin.QscAdminApplication - Started QscAdminApplication in 30.472 seconds (JVM running for 30.69)
