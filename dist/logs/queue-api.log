2025-06-23 16:16:23.940 [main] INFO  com.qs.admin.QscAdminApplication - Starting QscAdminApplication v1.0.0 on MSI with PID 36648 (D:\project\Java Projectes\qsadmin\dist\queue-api-1.0.0.jar started by <PERSON><PERSON><PERSON> in D:\project\Java Projectes\qsadmin\dist)
2025-06-23 16:16:23.947 [main] INFO  com.qs.admin.QscAdminApplication - The following profiles are active: prod
2025-06-23 16:16:30.497 [main] WARN  io.undertow.websockets.jsr - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
2025-06-23 16:16:30.520 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-06-23 16:16:31.069 [main] WARN  c.b.m.core.injector.AbstractMethod - [com.qs.admin.taxhall.mapper.AgentInfoEnterpriseMapper.insert] Has been loaded by XML or SqlProvider or Mybat<PERSON>'s Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-06-23 16:16:31.077 [main] WARN  c.b.m.core.injector.AbstractMethod - [com.qs.admin.taxhall.mapper.AgentInfoEnterpriseMapper.selectById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectById]
2025-06-23 16:16:31.087 [main] WARN  c.b.m.core.injector.AbstractMethod - [com.qs.admin.taxhall.mapper.AgentInfoEnterpriseMapper.selectList] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectList]
2025-06-23 16:16:31.235 [main] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.qs.admin.taxhall.model.vo.WindowBusinessVO".
2025-06-23 16:16:31.385 [main] WARN  c.b.m.core.injector.AbstractMethod - [com.qs.admin.taxhall.mapper.AgentInfoMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-06-23 16:16:31.392 [main] WARN  c.b.m.core.injector.AbstractMethod - [com.qs.admin.taxhall.mapper.AgentInfoMapper.selectById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectById]
2025-06-23 16:16:31.401 [main] WARN  c.b.m.core.injector.AbstractMethod - [com.qs.admin.taxhall.mapper.AgentInfoMapper.selectList] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectList]
2025-06-23 16:16:31.422 [main] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.qs.admin.taxhall.model.WindowBusiness".
2025-06-23 16:16:32.147 [main] INFO  c.q.a.t.s.impl.BaiShuiApiServiceImpl - 获取佰税API访问令牌
2025-06-23 16:16:32.153 [main] INFO  c.q.a.t.s.impl.BaiShuiApiServiceImpl - tokenUrl=http://80.12.140.31:8722/oauth/token
2025-06-23 16:16:53.233 [main] ERROR c.q.a.t.s.impl.BaiShuiApiServiceImpl - 获取佰税API访问令牌异常
org.springframework.web.client.ResourceAccessException: I/O error on POST request for "http://80.12.140.31:8722/oauth/token": Connection timed out: connect; nested exception is java.net.ConnectException: Connection timed out: connect
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:751)
	at org.springframework.web.client.RestTemplate.execute(RestTemplate.java:677)
	at org.springframework.web.client.RestTemplate.postForObject(RestTemplate.java:421)
	at com.qs.admin.taxhall.service.impl.BaiShuiApiServiceImpl.getToken(BaiShuiApiServiceImpl.java:73)
	at com.qs.admin.taxhall.task.BaiShuiTokenSyncTask.syncAppKeyAndToken(BaiShuiTokenSyncTask.java:49)
	at com.qs.admin.taxhall.task.BaiShuiTokenSyncTask.initSync(BaiShuiTokenSyncTask.java:29)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleElement.invoke(InitDestroyAnnotationBeanPostProcessor.java:389)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeInitMethods(InitDestroyAnnotationBeanPostProcessor.java:333)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeInitialization(InitDestroyAnnotationBeanPostProcessor.java:157)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsBeforeInitialization(AbstractAutowireCapableBeanFactory.java:416)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1795)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:595)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:517)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:323)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:222)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:321)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1287)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1207)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:636)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:116)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:397)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1429)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:594)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:517)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:323)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:222)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:321)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:879)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:879)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:551)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:141)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:747)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:397)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:315)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1226)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1215)
	at com.qs.admin.QscAdminApplication.main(QscAdminApplication.java:17)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.boot.loader.MainMethodRunner.run(MainMethodRunner.java:48)
	at org.springframework.boot.loader.Launcher.launch(Launcher.java:87)
	at org.springframework.boot.loader.Launcher.launch(Launcher.java:51)
	at org.springframework.boot.loader.JarLauncher.main(JarLauncher.java:52)
Caused by: java.net.ConnectException: Connection timed out: connect
	at java.net.DualStackPlainSocketImpl.connect0(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:75)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:476)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:218)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:200)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:162)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:394)
	at java.net.Socket.connect(Socket.java:606)
	at java.net.Socket.connect(Socket.java:555)
	at sun.net.NetworkClient.doConnect(NetworkClient.java:180)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:463)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:558)
	at sun.net.www.http.HttpClient.<init>(HttpClient.java:242)
	at sun.net.www.http.HttpClient.New(HttpClient.java:339)
	at sun.net.www.http.HttpClient.New(HttpClient.java:357)
	at sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1240)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1167)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1061)
	at sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:995)
	at org.springframework.http.client.SimpleBufferingClientHttpRequest.executeInternal(SimpleBufferingClientHttpRequest.java:76)
	at org.springframework.http.client.AbstractBufferingClientHttpRequest.executeInternal(AbstractBufferingClientHttpRequest.java:48)
	at org.springframework.http.client.AbstractClientHttpRequest.execute(AbstractClientHttpRequest.java:53)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:742)
	... 51 common frames omitted
2025-06-23 16:16:53.236 [main] ERROR c.q.a.t.task.BaiShuiTokenSyncTask - 同步佰税API APPKEY和TOKEN异常（不影响项目运行）
java.lang.RuntimeException: 授权请求异常: I/O error on POST request for "http://80.12.140.31:8722/oauth/token": Connection timed out: connect; nested exception is java.net.ConnectException: Connection timed out: connect
	at com.qs.admin.taxhall.service.impl.BaiShuiApiServiceImpl.getToken(BaiShuiApiServiceImpl.java:85)
	at com.qs.admin.taxhall.task.BaiShuiTokenSyncTask.syncAppKeyAndToken(BaiShuiTokenSyncTask.java:49)
	at com.qs.admin.taxhall.task.BaiShuiTokenSyncTask.initSync(BaiShuiTokenSyncTask.java:29)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleElement.invoke(InitDestroyAnnotationBeanPostProcessor.java:389)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeInitMethods(InitDestroyAnnotationBeanPostProcessor.java:333)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeInitialization(InitDestroyAnnotationBeanPostProcessor.java:157)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsBeforeInitialization(AbstractAutowireCapableBeanFactory.java:416)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1795)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:595)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:517)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:323)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:222)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:321)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1287)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1207)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:636)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:116)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:397)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1429)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:594)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:517)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:323)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:222)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:321)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:879)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:879)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:551)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:141)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:747)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:397)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:315)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1226)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1215)
	at com.qs.admin.QscAdminApplication.main(QscAdminApplication.java:17)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.boot.loader.MainMethodRunner.run(MainMethodRunner.java:48)
	at org.springframework.boot.loader.Launcher.launch(Launcher.java:87)
	at org.springframework.boot.loader.Launcher.launch(Launcher.java:51)
	at org.springframework.boot.loader.JarLauncher.main(JarLauncher.java:52)
Caused by: org.springframework.web.client.ResourceAccessException: I/O error on POST request for "http://80.12.140.31:8722/oauth/token": Connection timed out: connect; nested exception is java.net.ConnectException: Connection timed out: connect
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:751)
	at org.springframework.web.client.RestTemplate.execute(RestTemplate.java:677)
	at org.springframework.web.client.RestTemplate.postForObject(RestTemplate.java:421)
	at com.qs.admin.taxhall.service.impl.BaiShuiApiServiceImpl.getToken(BaiShuiApiServiceImpl.java:73)
	... 48 common frames omitted
Caused by: java.net.ConnectException: Connection timed out: connect
	at java.net.DualStackPlainSocketImpl.connect0(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:75)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:476)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:218)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:200)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:162)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:394)
	at java.net.Socket.connect(Socket.java:606)
	at java.net.Socket.connect(Socket.java:555)
	at sun.net.NetworkClient.doConnect(NetworkClient.java:180)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:463)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:558)
	at sun.net.www.http.HttpClient.<init>(HttpClient.java:242)
	at sun.net.www.http.HttpClient.New(HttpClient.java:339)
	at sun.net.www.http.HttpClient.New(HttpClient.java:357)
	at sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1240)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1167)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1061)
	at sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:995)
	at org.springframework.http.client.SimpleBufferingClientHttpRequest.executeInternal(SimpleBufferingClientHttpRequest.java:76)
	at org.springframework.http.client.AbstractBufferingClientHttpRequest.executeInternal(AbstractBufferingClientHttpRequest.java:48)
	at org.springframework.http.client.AbstractClientHttpRequest.execute(AbstractClientHttpRequest.java:53)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:742)
	... 51 common frames omitted
2025-06-23 16:16:54.581 [main] INFO  io.undertow - starting server: Undertow - 2.0.27.Final
2025-06-23 16:16:54.588 [main] INFO  org.xnio - XNIO version 3.3.8.Final
2025-06-23 16:16:54.597 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.3.8.Final
2025-06-23 16:16:54.651 [main] INFO  com.qs.admin.QscAdminApplication - Started QscAdminApplication in 31.167 seconds (JVM running for 31.507)
2025-06-23 16:18:36.825 [XNIO-1 task-1] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-23 16:18:36.956 [XNIO-1 task-2] INFO  c.q.a.t.c.SimpleLoginController - 用户登录请求: username=admin, ip=*************
2025-06-23 16:18:37.045 [XNIO-1 task-2] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-06-23 16:18:37.363 [XNIO-1 task-2] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-06-23 16:18:37.453 [XNIO-1 task-2] INFO  c.q.a.t.c.SimpleLoginController - 用户登录成功: username=admin, uid=1, ip=*************, 菜单数量=5
2025-06-23 16:28:09.247 [XNIO-1 task-12] WARN  c.b.m.e.p.i.PaginationInnerInterceptor - optimize this sql to a count sql has exception, sql:"SELECT  [KEY],[VALUE],[MEMO]  FROM system", exception:
net.sf.jsqlparser.parser.ParseException: Encountered unexpected token: "SELECT" <K_SELECT>
    at line 1, column 1.

Was expecting one of:

    "CALL"
    "COMMENT"
    "COMMIT"
    "DECLARE"
    "DELETE"
    "DESCRIBE"
    "DROP"
    "EXEC"
    "EXECUTE"
    "EXPLAIN"
    "GRANT"
    "INSERT"
    "MERGE"
    "SET"
    "SHOW"
    "TRUNCATE"
    "UPDATE"
    "UPSERT"
    "USE"
    "VALUES"
    "WITH"

2025-06-23 16:28:14.128 [XNIO-1 task-14] WARN  c.b.m.e.p.i.PaginationInnerInterceptor - optimize this sql to a count sql has exception, sql:"SELECT  uid,[NAME],LED_ADDRESS,LED_TEXT,ENABLED,SID,RANK_MODE,RANK_ADDRESS  FROM dbo.WINDOW", exception:
net.sf.jsqlparser.parser.ParseException: Encountered unexpected token: "[" "["
    at line 1, column 13.

Was expecting one of:

    "!"
    "("
    "*"
    "+"
    "-"
    ":"
    "?"
    "@"
    "@@"
    "ACTION"
    "ALL"
    "ANY"
    "BYTE"
    "CASCADE"
    "CASE"
    "CAST"
    "CHANGE"
    "CHAR"
    "CHARACTER"
    "COLUMN"
    "COLUMNS"
    "COMMENT"
    "COMMIT"
    "CREATE"
    "CURRENT"
    "CYCLE"
    "DESC"
    "DESCRIBE"
    "DISABLE"
    "DIV"
    "DO"
    "DOUBLE"
    "DUPLICATE"
    "ENABLE"
    "END"
    "EXCLUDE"
    "EXISTS"
    "EXTRACT"
    "FALSE"
    "FIRST"
    "FN"
    "FOLLOWING"
    "FORMAT"
    "GROUP"
    "GROUP_CONCAT"
    "IF"
    "IIF"
    "INDEX"
    "INSERT"
    "INTERVAL"
    "ISNULL"
    "KEY"
    "LAST"
    "LEFT"
    "LIMIT"
    "MATCH"
    "MATERIALIZED"
    "NEXTVAL"
    "NO"
    "NOLOCK"
    "NOT"
    "NULL"
    "NULLS"
    "OF"
    "OFFSET"
    "ON"
    "OPEN"
    "OPTIMIZE"
    "ORDER"
    "OVER"
    "PARTITION"
    "PATH"
    "PERCENT"
    "PRECISION"
    "PRIMARY"
    "PRIOR"
    "PROCEDURE"
    "PUBLIC"
    "RANGE"
    "READ"
    "REPLACE"
    "RIGHT"
    "ROW"
    "ROWS"
    "SCHEMA"
    "SEPARATOR"
    "SEQUENCE"
    "SESSION"
    "SET"
    "SIBLINGS"
    "SIZE"
    "SOME"
    "START"
    "TABLE"
    "TABLES"
    "TEMP"
    "TEMPORARY"
    "TO"
    "TOP"
    "TRUE"
    "TRUNCATE"
    "TYPE"
    "UNSIGNED"
    "VALIDATE"
    "VALUE"
    "VALUES"
    "VIEW"
    "XML"
    "XMLSERIALIZE"
    "ZONE"
    "{"
    "{d"
    "{t"
    "{ts"
    "~"
    <K_DATETIMELITERAL>
    <K_DATE_LITERAL>
    <K_TIME_KEY_EXPR>
    <S_CHAR_LITERAL>
    <S_DOUBLE>
    <S_HEX>
    <S_IDENTIFIER>
    <S_LONG>
    <S_QUOTED_IDENTIFIER>

2025-06-23 16:28:21.723 [XNIO-1 task-16] WARN  c.b.m.e.p.i.PaginationInnerInterceptor - optimize this sql to a count sql has exception, sql:"SELECT  uid,[NAME],LED_ADDRESS,LED_TEXT,ENABLED,SID,RANK_MODE,RANK_ADDRESS  FROM dbo.WINDOW", exception:
net.sf.jsqlparser.parser.ParseException: Encountered unexpected token: "[" "["
    at line 1, column 13.

Was expecting one of:

    "!"
    "("
    "*"
    "+"
    "-"
    ":"
    "?"
    "@"
    "@@"
    "ACTION"
    "ALL"
    "ANY"
    "BYTE"
    "CASCADE"
    "CASE"
    "CAST"
    "CHANGE"
    "CHAR"
    "CHARACTER"
    "COLUMN"
    "COLUMNS"
    "COMMENT"
    "COMMIT"
    "CREATE"
    "CURRENT"
    "CYCLE"
    "DESC"
    "DESCRIBE"
    "DISABLE"
    "DIV"
    "DO"
    "DOUBLE"
    "DUPLICATE"
    "ENABLE"
    "END"
    "EXCLUDE"
    "EXISTS"
    "EXTRACT"
    "FALSE"
    "FIRST"
    "FN"
    "FOLLOWING"
    "FORMAT"
    "GROUP"
    "GROUP_CONCAT"
    "IF"
    "IIF"
    "INDEX"
    "INSERT"
    "INTERVAL"
    "ISNULL"
    "KEY"
    "LAST"
    "LEFT"
    "LIMIT"
    "MATCH"
    "MATERIALIZED"
    "NEXTVAL"
    "NO"
    "NOLOCK"
    "NOT"
    "NULL"
    "NULLS"
    "OF"
    "OFFSET"
    "ON"
    "OPEN"
    "OPTIMIZE"
    "ORDER"
    "OVER"
    "PARTITION"
    "PATH"
    "PERCENT"
    "PRECISION"
    "PRIMARY"
    "PRIOR"
    "PROCEDURE"
    "PUBLIC"
    "RANGE"
    "READ"
    "REPLACE"
    "RIGHT"
    "ROW"
    "ROWS"
    "SCHEMA"
    "SEPARATOR"
    "SEQUENCE"
    "SESSION"
    "SET"
    "SIBLINGS"
    "SIZE"
    "SOME"
    "START"
    "TABLE"
    "TABLES"
    "TEMP"
    "TEMPORARY"
    "TO"
    "TOP"
    "TRUE"
    "TRUNCATE"
    "TYPE"
    "UNSIGNED"
    "VALIDATE"
    "VALUE"
    "VALUES"
    "VIEW"
    "XML"
    "XMLSERIALIZE"
    "ZONE"
    "{"
    "{d"
    "{t"
    "{ts"
    "~"
    <K_DATETIMELITERAL>
    <K_DATE_LITERAL>
    <K_TIME_KEY_EXPR>
    <S_CHAR_LITERAL>
    <S_DOUBLE>
    <S_HEX>
    <S_IDENTIFIER>
    <S_LONG>
    <S_QUOTED_IDENTIFIER>

2025-06-23 16:28:22.540 [XNIO-1 task-18] WARN  c.b.m.e.p.i.PaginationInnerInterceptor - optimize this sql to a count sql has exception, sql:"SELECT  uid,[NAME],LED_ADDRESS,LED_TEXT,ENABLED,SID,RANK_MODE,RANK_ADDRESS  FROM dbo.WINDOW", exception:
net.sf.jsqlparser.parser.ParseException: Encountered unexpected token: "[" "["
    at line 1, column 13.

Was expecting one of:

    "!"
    "("
    "*"
    "+"
    "-"
    ":"
    "?"
    "@"
    "@@"
    "ACTION"
    "ALL"
    "ANY"
    "BYTE"
    "CASCADE"
    "CASE"
    "CAST"
    "CHANGE"
    "CHAR"
    "CHARACTER"
    "COLUMN"
    "COLUMNS"
    "COMMENT"
    "COMMIT"
    "CREATE"
    "CURRENT"
    "CYCLE"
    "DESC"
    "DESCRIBE"
    "DISABLE"
    "DIV"
    "DO"
    "DOUBLE"
    "DUPLICATE"
    "ENABLE"
    "END"
    "EXCLUDE"
    "EXISTS"
    "EXTRACT"
    "FALSE"
    "FIRST"
    "FN"
    "FOLLOWING"
    "FORMAT"
    "GROUP"
    "GROUP_CONCAT"
    "IF"
    "IIF"
    "INDEX"
    "INSERT"
    "INTERVAL"
    "ISNULL"
    "KEY"
    "LAST"
    "LEFT"
    "LIMIT"
    "MATCH"
    "MATERIALIZED"
    "NEXTVAL"
    "NO"
    "NOLOCK"
    "NOT"
    "NULL"
    "NULLS"
    "OF"
    "OFFSET"
    "ON"
    "OPEN"
    "OPTIMIZE"
    "ORDER"
    "OVER"
    "PARTITION"
    "PATH"
    "PERCENT"
    "PRECISION"
    "PRIMARY"
    "PRIOR"
    "PROCEDURE"
    "PUBLIC"
    "RANGE"
    "READ"
    "REPLACE"
    "RIGHT"
    "ROW"
    "ROWS"
    "SCHEMA"
    "SEPARATOR"
    "SEQUENCE"
    "SESSION"
    "SET"
    "SIBLINGS"
    "SIZE"
    "SOME"
    "START"
    "TABLE"
    "TABLES"
    "TEMP"
    "TEMPORARY"
    "TO"
    "TOP"
    "TRUE"
    "TRUNCATE"
    "TYPE"
    "UNSIGNED"
    "VALIDATE"
    "VALUE"
    "VALUES"
    "VIEW"
    "XML"
    "XMLSERIALIZE"
    "ZONE"
    "{"
    "{d"
    "{t"
    "{ts"
    "~"
    <K_DATETIMELITERAL>
    <K_DATE_LITERAL>
    <K_TIME_KEY_EXPR>
    <S_CHAR_LITERAL>
    <S_DOUBLE>
    <S_HEX>
    <S_IDENTIFIER>
    <S_LONG>
    <S_QUOTED_IDENTIFIER>

2025-06-23 16:28:31.245 [XNIO-1 task-21] WARN  c.b.m.e.p.i.PaginationInnerInterceptor - optimize this sql to a count sql has exception, sql:"SELECT  uid,[NAME],LED_ADDRESS,LED_TEXT,ENABLED,SID,RANK_MODE,RANK_ADDRESS  FROM dbo.WINDOW", exception:
net.sf.jsqlparser.parser.ParseException: Encountered unexpected token: "[" "["
    at line 1, column 13.

Was expecting one of:

    "!"
    "("
    "*"
    "+"
    "-"
    ":"
    "?"
    "@"
    "@@"
    "ACTION"
    "ALL"
    "ANY"
    "BYTE"
    "CASCADE"
    "CASE"
    "CAST"
    "CHANGE"
    "CHAR"
    "CHARACTER"
    "COLUMN"
    "COLUMNS"
    "COMMENT"
    "COMMIT"
    "CREATE"
    "CURRENT"
    "CYCLE"
    "DESC"
    "DESCRIBE"
    "DISABLE"
    "DIV"
    "DO"
    "DOUBLE"
    "DUPLICATE"
    "ENABLE"
    "END"
    "EXCLUDE"
    "EXISTS"
    "EXTRACT"
    "FALSE"
    "FIRST"
    "FN"
    "FOLLOWING"
    "FORMAT"
    "GROUP"
    "GROUP_CONCAT"
    "IF"
    "IIF"
    "INDEX"
    "INSERT"
    "INTERVAL"
    "ISNULL"
    "KEY"
    "LAST"
    "LEFT"
    "LIMIT"
    "MATCH"
    "MATERIALIZED"
    "NEXTVAL"
    "NO"
    "NOLOCK"
    "NOT"
    "NULL"
    "NULLS"
    "OF"
    "OFFSET"
    "ON"
    "OPEN"
    "OPTIMIZE"
    "ORDER"
    "OVER"
    "PARTITION"
    "PATH"
    "PERCENT"
    "PRECISION"
    "PRIMARY"
    "PRIOR"
    "PROCEDURE"
    "PUBLIC"
    "RANGE"
    "READ"
    "REPLACE"
    "RIGHT"
    "ROW"
    "ROWS"
    "SCHEMA"
    "SEPARATOR"
    "SEQUENCE"
    "SESSION"
    "SET"
    "SIBLINGS"
    "SIZE"
    "SOME"
    "START"
    "TABLE"
    "TABLES"
    "TEMP"
    "TEMPORARY"
    "TO"
    "TOP"
    "TRUE"
    "TRUNCATE"
    "TYPE"
    "UNSIGNED"
    "VALIDATE"
    "VALUE"
    "VALUES"
    "VIEW"
    "XML"
    "XMLSERIALIZE"
    "ZONE"
    "{"
    "{d"
    "{t"
    "{ts"
    "~"
    <K_DATETIMELITERAL>
    <K_DATE_LITERAL>
    <K_TIME_KEY_EXPR>
    <S_CHAR_LITERAL>
    <S_DOUBLE>
    <S_HEX>
    <S_IDENTIFIER>
    <S_LONG>
    <S_QUOTED_IDENTIFIER>

2025-06-23 16:28:45.503 [XNIO-1 task-23] ERROR c.q.a.c.e.GlobalExceptionHandler - 系统异常
org.springframework.http.converter.HttpMessageNotReadableException: Required request body is missing: public com.qs.admin.common.domain.R<?> com.qs.admin.taxhall.controller.WindowController.update(com.qs.admin.taxhall.model.dto.WindowDTO)
	at org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor.readWithMessageConverters(RequestResponseBodyMethodProcessor.java:161)
	at org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor.resolveArgument(RequestResponseBodyMethodProcessor.java:131)
	at org.springframework.web.method.support.HandlerMethodArgumentResolverComposite.resolveArgument(HandlerMethodArgumentResolverComposite.java:121)
	at org.springframework.web.method.support.InvocableHandlerMethod.getMethodArgumentValues(InvocableHandlerMethod.java:167)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:134)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:106)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:888)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:793)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPut(FrameworkServlet.java:920)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:526)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at io.undertow.servlet.handlers.ServletHandler.handleRequest(ServletHandler.java:74)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:129)
	at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:61)
	at org.apache.shiro.web.servlet.AdviceFilter.executeChain(AdviceFilter.java:108)
	at org.apache.shiro.web.servlet.AdviceFilter.doFilterInternal(AdviceFilter.java:137)
	at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:154)
	at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:66)
	at org.apache.shiro.web.servlet.AdviceFilter.executeChain(AdviceFilter.java:108)
	at org.apache.shiro.web.servlet.AdviceFilter.doFilterInternal(AdviceFilter.java:137)
	at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:154)
	at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:66)
	at org.apache.shiro.web.servlet.AbstractShiroFilter.executeChain(AbstractShiroFilter.java:458)
	at org.apache.shiro.web.servlet.AbstractShiroFilter$1.call(AbstractShiroFilter.java:373)
	at org.apache.shiro.subject.support.SubjectCallable.doCall(SubjectCallable.java:90)
	at org.apache.shiro.subject.support.SubjectCallable.call(SubjectCallable.java:83)
	at org.apache.shiro.subject.support.DelegatingSubject.execute(DelegatingSubject.java:387)
	at org.apache.shiro.web.servlet.AbstractShiroFilter.doFilterInternal(AbstractShiroFilter.java:370)
	at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:154)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:61)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at com.alibaba.druid.support.http.WebStatFilter.doFilter(WebStatFilter.java:123)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:61)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:61)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:61)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:61)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at io.undertow.servlet.handlers.FilterHandler.handleRequest(FilterHandler.java:84)
	at io.undertow.servlet.handlers.security.ServletSecurityRoleHandler.handleRequest(ServletSecurityRoleHandler.java:62)
	at io.undertow.servlet.handlers.ServletChain$1.handleRequest(ServletChain.java:68)
	at io.undertow.servlet.handlers.ServletDispatchingHandler.handleRequest(ServletDispatchingHandler.java:36)
	at io.undertow.servlet.handlers.RedirectDirHandler.handleRequest(RedirectDirHandler.java:68)
	at io.undertow.servlet.handlers.security.SSLInformationAssociationHandler.handleRequest(SSLInformationAssociationHandler.java:132)
	at io.undertow.servlet.handlers.security.ServletAuthenticationCallHandler.handleRequest(ServletAuthenticationCallHandler.java:57)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.security.handlers.AbstractConfidentialityHandler.handleRequest(AbstractConfidentialityHandler.java:46)
	at io.undertow.servlet.handlers.security.ServletConfidentialityConstraintHandler.handleRequest(ServletConfidentialityConstraintHandler.java:64)
	at io.undertow.security.handlers.AuthenticationMechanismsHandler.handleRequest(AuthenticationMechanismsHandler.java:60)
	at io.undertow.servlet.handlers.security.CachedAuthenticatedSessionHandler.handleRequest(CachedAuthenticatedSessionHandler.java:77)
	at io.undertow.security.handlers.AbstractSecurityContextAssociationHandler.handleRequest(AbstractSecurityContextAssociationHandler.java:43)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.servlet.handlers.ServletInitialHandler.handleFirstRequest(ServletInitialHandler.java:269)
	at io.undertow.servlet.handlers.ServletInitialHandler.access$100(ServletInitialHandler.java:78)
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:133)
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:130)
	at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:48)
	at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43)
	at io.undertow.servlet.handlers.ServletInitialHandler.dispatchRequest(ServletInitialHandler.java:249)
	at io.undertow.servlet.handlers.ServletInitialHandler.access$000(ServletInitialHandler.java:78)
	at io.undertow.servlet.handlers.ServletInitialHandler$1.handleRequest(ServletInitialHandler.java:99)
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:376)
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:830)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
2025-06-23 16:28:45.513 [XNIO-1 task-24] WARN  c.b.m.e.p.i.PaginationInnerInterceptor - optimize this sql to a count sql has exception, sql:"SELECT  uid,[NAME],LED_ADDRESS,LED_TEXT,ENABLED,SID,RANK_MODE,RANK_ADDRESS  FROM dbo.WINDOW", exception:
net.sf.jsqlparser.parser.ParseException: Encountered unexpected token: "[" "["
    at line 1, column 13.

Was expecting one of:

    "!"
    "("
    "*"
    "+"
    "-"
    ":"
    "?"
    "@"
    "@@"
    "ACTION"
    "ALL"
    "ANY"
    "BYTE"
    "CASCADE"
    "CASE"
    "CAST"
    "CHANGE"
    "CHAR"
    "CHARACTER"
    "COLUMN"
    "COLUMNS"
    "COMMENT"
    "COMMIT"
    "CREATE"
    "CURRENT"
    "CYCLE"
    "DESC"
    "DESCRIBE"
    "DISABLE"
    "DIV"
    "DO"
    "DOUBLE"
    "DUPLICATE"
    "ENABLE"
    "END"
    "EXCLUDE"
    "EXISTS"
    "EXTRACT"
    "FALSE"
    "FIRST"
    "FN"
    "FOLLOWING"
    "FORMAT"
    "GROUP"
    "GROUP_CONCAT"
    "IF"
    "IIF"
    "INDEX"
    "INSERT"
    "INTERVAL"
    "ISNULL"
    "KEY"
    "LAST"
    "LEFT"
    "LIMIT"
    "MATCH"
    "MATERIALIZED"
    "NEXTVAL"
    "NO"
    "NOLOCK"
    "NOT"
    "NULL"
    "NULLS"
    "OF"
    "OFFSET"
    "ON"
    "OPEN"
    "OPTIMIZE"
    "ORDER"
    "OVER"
    "PARTITION"
    "PATH"
    "PERCENT"
    "PRECISION"
    "PRIMARY"
    "PRIOR"
    "PROCEDURE"
    "PUBLIC"
    "RANGE"
    "READ"
    "REPLACE"
    "RIGHT"
    "ROW"
    "ROWS"
    "SCHEMA"
    "SEPARATOR"
    "SEQUENCE"
    "SESSION"
    "SET"
    "SIBLINGS"
    "SIZE"
    "SOME"
    "START"
    "TABLE"
    "TABLES"
    "TEMP"
    "TEMPORARY"
    "TO"
    "TOP"
    "TRUE"
    "TRUNCATE"
    "TYPE"
    "UNSIGNED"
    "VALIDATE"
    "VALUE"
    "VALUES"
    "VIEW"
    "XML"
    "XMLSERIALIZE"
    "ZONE"
    "{"
    "{d"
    "{t"
    "{ts"
    "~"
    <K_DATETIMELITERAL>
    <K_DATE_LITERAL>
    <K_TIME_KEY_EXPR>
    <S_CHAR_LITERAL>
    <S_DOUBLE>
    <S_HEX>
    <S_IDENTIFIER>
    <S_LONG>
    <S_QUOTED_IDENTIFIER>

2025-06-23 16:32:54.817 [XNIO-1 task-26] INFO  c.q.a.t.s.impl.WindowServiceImpl - 接收到添加窗口请求
2025-06-23 16:32:54.843 [XNIO-1 task-26] INFO  c.q.a.t.s.impl.WindowServiceImpl - 处理后的WindowDTO: WindowDTO(uid=0, name=1001, ledAddress=1, ledText=1, enabled=1, sid=0, rankMode=0, rankAddress=null, bizList=[WindowBusinessDTO(uid=1, name=综合业务, priority=0, enabled=true), WindowBusinessDTO(uid=2, name=股权转让, priority=1, enabled=true), WindowBusinessDTO(uid=3, name=注销业务, priority=0, enabled=false), WindowBusinessDTO(uid=4, name=城乡两费, priority=0, enabled=false), WindowBusinessDTO(uid=5, name=金税, priority=0, enabled=false), WindowBusinessDTO(uid=7, name=行政审批, priority=0, enabled=false), WindowBusinessDTO(uid=10, name=综合业务, priority=0, enabled=false), WindowBusinessDTO(uid=15, name=股权转让, priority=0, enabled=false), WindowBusinessDTO(uid=19, name=金税业务, priority=0, enabled=false), WindowBusinessDTO(uid=23, name=进出口备案, priority=0, enabled=false), WindowBusinessDTO(uid=25, name=跨区域涉税事项, priority=0, enabled=false)])
2025-06-23 16:32:54.881 [XNIO-1 task-26] INFO  c.q.a.t.s.impl.WindowServiceImpl - 保存WindowBusiness记录: winUid=42, bizUid=1, priority=0
2025-06-23 16:32:54.893 [XNIO-1 task-26] INFO  c.q.a.t.s.impl.WindowServiceImpl - 保存WindowBusiness记录: winUid=42, bizUid=2, priority=1
2025-06-23 16:32:54.902 [XNIO-1 task-27] WARN  c.b.m.e.p.i.PaginationInnerInterceptor - optimize this sql to a count sql has exception, sql:"SELECT  uid,[NAME],LED_ADDRESS,LED_TEXT,ENABLED,SID,RANK_MODE,RANK_ADDRESS  FROM dbo.WINDOW", exception:
net.sf.jsqlparser.parser.ParseException: Encountered unexpected token: "[" "["
    at line 1, column 13.

Was expecting one of:

    "!"
    "("
    "*"
    "+"
    "-"
    ":"
    "?"
    "@"
    "@@"
    "ACTION"
    "ALL"
    "ANY"
    "BYTE"
    "CASCADE"
    "CASE"
    "CAST"
    "CHANGE"
    "CHAR"
    "CHARACTER"
    "COLUMN"
    "COLUMNS"
    "COMMENT"
    "COMMIT"
    "CREATE"
    "CURRENT"
    "CYCLE"
    "DESC"
    "DESCRIBE"
    "DISABLE"
    "DIV"
    "DO"
    "DOUBLE"
    "DUPLICATE"
    "ENABLE"
    "END"
    "EXCLUDE"
    "EXISTS"
    "EXTRACT"
    "FALSE"
    "FIRST"
    "FN"
    "FOLLOWING"
    "FORMAT"
    "GROUP"
    "GROUP_CONCAT"
    "IF"
    "IIF"
    "INDEX"
    "INSERT"
    "INTERVAL"
    "ISNULL"
    "KEY"
    "LAST"
    "LEFT"
    "LIMIT"
    "MATCH"
    "MATERIALIZED"
    "NEXTVAL"
    "NO"
    "NOLOCK"
    "NOT"
    "NULL"
    "NULLS"
    "OF"
    "OFFSET"
    "ON"
    "OPEN"
    "OPTIMIZE"
    "ORDER"
    "OVER"
    "PARTITION"
    "PATH"
    "PERCENT"
    "PRECISION"
    "PRIMARY"
    "PRIOR"
    "PROCEDURE"
    "PUBLIC"
    "RANGE"
    "READ"
    "REPLACE"
    "RIGHT"
    "ROW"
    "ROWS"
    "SCHEMA"
    "SEPARATOR"
    "SEQUENCE"
    "SESSION"
    "SET"
    "SIBLINGS"
    "SIZE"
    "SOME"
    "START"
    "TABLE"
    "TABLES"
    "TEMP"
    "TEMPORARY"
    "TO"
    "TOP"
    "TRUE"
    "TRUNCATE"
    "TYPE"
    "UNSIGNED"
    "VALIDATE"
    "VALUE"
    "VALUES"
    "VIEW"
    "XML"
    "XMLSERIALIZE"
    "ZONE"
    "{"
    "{d"
    "{t"
    "{ts"
    "~"
    <K_DATETIMELITERAL>
    <K_DATE_LITERAL>
    <K_TIME_KEY_EXPR>
    <S_CHAR_LITERAL>
    <S_DOUBLE>
    <S_HEX>
    <S_IDENTIFIER>
    <S_LONG>
    <S_QUOTED_IDENTIFIER>

2025-06-23 16:33:43.261 [XNIO-1 task-28] WARN  c.b.m.e.p.i.PaginationInnerInterceptor - optimize this sql to a count sql has exception, sql:"SELECT  uid,[NAME],LED_ADDRESS,LED_TEXT,ENABLED,SID,RANK_MODE,RANK_ADDRESS  FROM dbo.WINDOW", exception:
net.sf.jsqlparser.parser.ParseException: Encountered unexpected token: "[" "["
    at line 1, column 13.

Was expecting one of:

    "!"
    "("
    "*"
    "+"
    "-"
    ":"
    "?"
    "@"
    "@@"
    "ACTION"
    "ALL"
    "ANY"
    "BYTE"
    "CASCADE"
    "CASE"
    "CAST"
    "CHANGE"
    "CHAR"
    "CHARACTER"
    "COLUMN"
    "COLUMNS"
    "COMMENT"
    "COMMIT"
    "CREATE"
    "CURRENT"
    "CYCLE"
    "DESC"
    "DESCRIBE"
    "DISABLE"
    "DIV"
    "DO"
    "DOUBLE"
    "DUPLICATE"
    "ENABLE"
    "END"
    "EXCLUDE"
    "EXISTS"
    "EXTRACT"
    "FALSE"
    "FIRST"
    "FN"
    "FOLLOWING"
    "FORMAT"
    "GROUP"
    "GROUP_CONCAT"
    "IF"
    "IIF"
    "INDEX"
    "INSERT"
    "INTERVAL"
    "ISNULL"
    "KEY"
    "LAST"
    "LEFT"
    "LIMIT"
    "MATCH"
    "MATERIALIZED"
    "NEXTVAL"
    "NO"
    "NOLOCK"
    "NOT"
    "NULL"
    "NULLS"
    "OF"
    "OFFSET"
    "ON"
    "OPEN"
    "OPTIMIZE"
    "ORDER"
    "OVER"
    "PARTITION"
    "PATH"
    "PERCENT"
    "PRECISION"
    "PRIMARY"
    "PRIOR"
    "PROCEDURE"
    "PUBLIC"
    "RANGE"
    "READ"
    "REPLACE"
    "RIGHT"
    "ROW"
    "ROWS"
    "SCHEMA"
    "SEPARATOR"
    "SEQUENCE"
    "SESSION"
    "SET"
    "SIBLINGS"
    "SIZE"
    "SOME"
    "START"
    "TABLE"
    "TABLES"
    "TEMP"
    "TEMPORARY"
    "TO"
    "TOP"
    "TRUE"
    "TRUNCATE"
    "TYPE"
    "UNSIGNED"
    "VALIDATE"
    "VALUE"
    "VALUES"
    "VIEW"
    "XML"
    "XMLSERIALIZE"
    "ZONE"
    "{"
    "{d"
    "{t"
    "{ts"
    "~"
    <K_DATETIMELITERAL>
    <K_DATE_LITERAL>
    <K_TIME_KEY_EXPR>
    <S_CHAR_LITERAL>
    <S_DOUBLE>
    <S_HEX>
    <S_IDENTIFIER>
    <S_LONG>
    <S_QUOTED_IDENTIFIER>

2025-06-23 16:33:49.773 [XNIO-1 task-29] WARN  c.b.m.e.p.i.PaginationInnerInterceptor - optimize this sql to a count sql has exception, sql:"SELECT  uid,[NAME],LED_ADDRESS,LED_TEXT,ENABLED,SID,RANK_MODE,RANK_ADDRESS  FROM dbo.WINDOW", exception:
net.sf.jsqlparser.parser.ParseException: Encountered unexpected token: "[" "["
    at line 1, column 13.

Was expecting one of:

    "!"
    "("
    "*"
    "+"
    "-"
    ":"
    "?"
    "@"
    "@@"
    "ACTION"
    "ALL"
    "ANY"
    "BYTE"
    "CASCADE"
    "CASE"
    "CAST"
    "CHANGE"
    "CHAR"
    "CHARACTER"
    "COLUMN"
    "COLUMNS"
    "COMMENT"
    "COMMIT"
    "CREATE"
    "CURRENT"
    "CYCLE"
    "DESC"
    "DESCRIBE"
    "DISABLE"
    "DIV"
    "DO"
    "DOUBLE"
    "DUPLICATE"
    "ENABLE"
    "END"
    "EXCLUDE"
    "EXISTS"
    "EXTRACT"
    "FALSE"
    "FIRST"
    "FN"
    "FOLLOWING"
    "FORMAT"
    "GROUP"
    "GROUP_CONCAT"
    "IF"
    "IIF"
    "INDEX"
    "INSERT"
    "INTERVAL"
    "ISNULL"
    "KEY"
    "LAST"
    "LEFT"
    "LIMIT"
    "MATCH"
    "MATERIALIZED"
    "NEXTVAL"
    "NO"
    "NOLOCK"
    "NOT"
    "NULL"
    "NULLS"
    "OF"
    "OFFSET"
    "ON"
    "OPEN"
    "OPTIMIZE"
    "ORDER"
    "OVER"
    "PARTITION"
    "PATH"
    "PERCENT"
    "PRECISION"
    "PRIMARY"
    "PRIOR"
    "PROCEDURE"
    "PUBLIC"
    "RANGE"
    "READ"
    "REPLACE"
    "RIGHT"
    "ROW"
    "ROWS"
    "SCHEMA"
    "SEPARATOR"
    "SEQUENCE"
    "SESSION"
    "SET"
    "SIBLINGS"
    "SIZE"
    "SOME"
    "START"
    "TABLE"
    "TABLES"
    "TEMP"
    "TEMPORARY"
    "TO"
    "TOP"
    "TRUE"
    "TRUNCATE"
    "TYPE"
    "UNSIGNED"
    "VALIDATE"
    "VALUE"
    "VALUES"
    "VIEW"
    "XML"
    "XMLSERIALIZE"
    "ZONE"
    "{"
    "{d"
    "{t"
    "{ts"
    "~"
    <K_DATETIMELITERAL>
    <K_DATE_LITERAL>
    <K_TIME_KEY_EXPR>
    <S_CHAR_LITERAL>
    <S_DOUBLE>
    <S_HEX>
    <S_IDENTIFIER>
    <S_LONG>
    <S_QUOTED_IDENTIFIER>

2025-06-23 16:42:58.403 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-06-23 16:42:58.405 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-06-23 16:42:58.407 [SpringContextShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-06-23 16:42:58.408 [SpringContextShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.0.27.Final
