2025-04-29 01:27:16.597  WARN 8576 --- [main] c.b.m.core.metadata.TableInfoHelper      : This "prefix" is the table primary key by @TableId annotation in Class: "com.qs.admin.taxhall.model.Autocode",So @TableField annotation will not work!
2025-04-29 01:27:16.856  WARN 8576 --- [main] c.b.m.core.metadata.TableInfoHelper      : This "unitCode" is the table primary key by @TableId annotation in Class: "com.qs.admin.taxhall.model.ClientInfo",So @TableField annotation will not work!
2025-04-29 01:27:16.936  WARN 8576 --- [main] c.b.m.core.metadata.TableInfoHelper      : This "id" is the table primary key by @TableId annotation in Class: "com.qs.admin.taxhall.model.Things",So @TableField annotation will not work!
2025-04-29 01:27:17.283  WARN 8576 --- [main] c.b.m.core.metadata.TableInfoHelper      : Can not find table primary key in Class: "com.qs.admin.taxhall.model.vo.WindowBusinessVO".
2025-04-29 01:27:17.284  WARN 8576 --- [main] c.b.m.core.injector.DefaultSqlInjector   : class com.qs.admin.taxhall.model.vo.WindowBusinessVO ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-04-29 01:35:25.717  WARN 8576 --- [http-nio-8823-exec-4] c.q.a.t.s.impl.AgentInfoServiceImpl      : 票据 UID 为 null，使用默认值 0
