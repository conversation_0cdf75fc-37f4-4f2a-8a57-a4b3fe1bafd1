<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qs.admin.taxhall.mapper.TicketExchangeMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.qs.admin.taxhall.model.TicketExchange">
        <result column="UID" property="uid" />
        <result column="DATE" property="date" />
        <result column="BIZ_UID" property="bizUid" />
        <result column="TYPE" property="type" />
        <result column="TAX_CODE" property="taxCode" />
        <result column="ID_CODE" property="idCode" />
        <result column="VERIFY_CODE" property="verifyCode" />
        <result column="EXPIRE_TIME" property="expireTime" />
        <result column="NJ" property="nj" />
        <result column="TKT_UID" property="tktUid" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        UID, DATE, BIZ_UID, TYPE, TAX_CODE, ID_CODE, VERIFY_CODE, EXPIRE_TIME, NJ, TKT_UID
    </sql>

</mapper>
