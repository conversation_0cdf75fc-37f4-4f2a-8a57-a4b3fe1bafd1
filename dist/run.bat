@echo off
:: 设置控制台编码为UTF-8
chcp 65001

set JAVA_HOME=%~dp0jdk
set CONFIG_DIR=%~dp0config
set CONFIG_FILE=%CONFIG_DIR%\application-prod.yml
set PORT=9999

:: 检查9999端口是否被占用，如被占用则杀掉进程
for /f "tokens=5" %%a in ('netstat -ano ^| findstr :%PORT% ^| findstr LISTENING') do (
    echo 端口 %PORT% 已被进程ID %%a 占用，尝试结束该进程...
    taskkill /F /PID %%a
    echo 已结束进程 %%a
    :: 添加短暂延迟，确保端口完全释放
    timeout /t 2 >nul
)

:: 检查 JAVA_HOME 目录是否存在
if not exist "%JAVA_HOME%" (
    echo Error: JAVA_HOME directory not found at %JAVA_HOME%
    pause
    exit /b 1
)

:: 检查 java.exe 是否存在
if not exist "%JAVA_HOME%\bin\java.exe" (
    echo Error: java.exe not found in %JAVA_HOME%\bin
    pause
    exit /b 1
)

:: 检查 JAR 文件是否存在
if not exist "%~dp0queue-api-1.0.0.jar" (
    echo Error: JAR file not found at %~dp0queue-api-1.0.0.jar
    pause
    exit /b 1
)

:: 检查 config 目录是否存在
if not exist "%CONFIG_DIR%" (
    echo Error: Config directory not found at %CONFIG_DIR%
    pause
    exit /b 1
)

:: 检查外部配置文件是否存在
if not exist "%CONFIG_FILE%" (
    echo Error: Config file not found at %CONFIG_FILE%
    pause
    exit /b 1
)

echo Starting queue-api-1.0.0.jar using JDK from %JAVA_HOME%
echo Using external configuration from %CONFIG_FILE% alongside internal application.yml and application-prod.yml
"%JAVA_HOME%\bin\java.exe" -Dfile.encoding=UTF-8 -jar "%~dp0queue-api-1.0.0.jar" --spring.profiles.active=prod --spring.config.location=classpath:/application.yml,classpath:/application-prod.yml,file:"%CONFIG_FILE%"
pause