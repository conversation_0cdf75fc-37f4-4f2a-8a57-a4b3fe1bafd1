#
# Copyright (c) 2004, 2011, Oracle and/or its affiliates. All rights reserved.
# ORACLE PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
#

error.internal.badmsg=internal error, unknown message
error.badinst.nojre=Bad installation. No JRE found in configuration file
error.launch.execv=Error encountered while invoking Java Web Start (execv)
error.launch.sysexec=Error encountered while invoking Java Web Start (SysExec) 
error.listener.failed=Splash: sysCreateListenerSocket failed
error.accept.failed=Splash: accept failed
error.recv.failed=Splash: recv failed
error.invalid.port=Splash: didn't revive a valid port
error.read=Read past end of buffer
error.xmlparsing=XML Parsing error: wrong kind of token found
error.splash.exit=Java Web Start splash screen process exiting .....\n
# "Last WinSock Error" means the error message for the last operation that failed.
error.winsock=\tLast WinSock Error: 
error.winsock.load=Couldn't load winsock.dll
error.winsock.start=WSAStartup failed
error.badinst.nohome=Bad installation: JAVAWS_HOME not set 
error.splash.noimage=Splash: couldn't load splash screen image
error.splash.socket=Splash: server socket failed
error.splash.cmnd=Splash: unrecognized command
error.splash.port=Splash: port not specified
error.splash.send=Splash: send failed
error.splash.timer=Splash: couldn't create shutdown timer
error.splash.x11.open=Splash: Can't open X11 display
error.splash.x11.connect=Splash: X11 connection failed
# Javaws usage: '\' is a joining of two sentence,which are connected including
# the invisible character '\n'.
message.javaws.usage=\n\
Usage:\tjavaws [run-options] <jnlp-file>	\n\
      \tjavaws [control-options]		\n\
						\n\
where run-options include:			\n\
  -verbose       \tdisplay additional output	\n\
  -offline       \trun the application in offline mode	\n\
  -system        \trun the application from the system cache only\n\
  -Xnosplash     \trun without showing a splash screen	\n\
  -J<option>     \tsupply option to the vm	\n\
  -wait          \tstart java process and wait for its exit	\n\
	\n\
control-options include:	\n\
  -viewer        \tshow the cache viewer in the java control panel\n\
  -clearcache    \tremove all non-installed applications from the cache\n\
  -uninstall     \tremove all applications from the cache\n\
  -uninstall <jnlp-file>              \tremove the application from the cache	\n\
  -import [import-options] <jnlp-file>\timport the application to the cache		\n\
									\n\
import-options include:						\n\
  -silent        \timport silently (with no user interface)	\n\
  -system        \timport application into the system cache	\n\
  -codebase <url>\tretrieve resources from the given codebase	\n\
  -shortcut      \tinstall shortcuts as if user allowed prompt	\n\
  -association   \tinstall associations as if user allowed prompt	\n\
\n
