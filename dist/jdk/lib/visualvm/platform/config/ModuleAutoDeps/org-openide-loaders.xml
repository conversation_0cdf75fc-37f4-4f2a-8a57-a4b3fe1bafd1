<?xml version="1.0" encoding="UTF-8"?>
<!--
Copyright (c) 1992, 2014, Oracle and/or its affiliates. All rights reserved.
ORACLE PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
-->

<!DOCTYPE transformations PUBLIC "-//NetBeans//DTD Module Automatic Dependencies 1.0//EN" "http://www.netbeans.org/dtds/module-auto-deps-1_0.dtd">

<transformations version="1.0">
    <transformationgroup>
        <description>No need for separate templates API. Merged into org.openide.loaders</description>
        <transformation>
            <trigger-dependency type="cancel">
                <module-dependency codenamebase="org.netbeans.modules.templates"/>
            </trigger-dependency>
            <implies>
                <result>
                    <module-dependency codenamebase="org.openide.loaders"  spec="7.12"/>
                </result>
            </implies>
        </transformation>
    </transformationgroup>

</transformations>
