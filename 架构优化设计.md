# 排队系统架构优化设计

## 1. 系统整体架构

### 1.1 微服务架构设计
- **取号服务(queuing-system-service)**
  - 部署在大厅服务器
  - 负责票号生成和业务处理
  - 维护本地SQLite数据库
  - 提供离线操作支持

- **呼号服务(calling-system-service)**
  - 独立部署
  - 处理窗口呼叫业务
  - 管理LED显示和语音播报
  - WebSocket实时通信

- **数据同步服务(data-sync-service)**
  - 负责本地与云端数据同步
  - 实现断点续传
  - 处理数据冲突

### 1.2 数据流设计
```
[取号终端] → [本地SQLite] ↔ [数据同步服务] ↔ [省级云数据库]
                ↓
        [消息队列(RabbitMQ)]
                ↓
    [呼号终端] → [LED/语音服务]
```

## 2. 关键功能实现

### 2.1 离线操作支持
1. **本地数据库设计**
   - 使用SQLite作为本地存储
   - 复制必要的业务表结构
   - 添加同步状态标记字段

2. **数据同步机制**
   - 定期检查网络状态
   - 增量同步策略
   - 冲突解决机制

### 2.2 实时通信实现
1. **WebSocket服务**
   - 维护LED显示屏连接
   - 处理呼号消息推送
   - 实现心跳检测

2. **消息队列集成**
   - 使用RabbitMQ作为消息中间件
   - 实现消息持久化
   - 保证消息可靠投递

## 3. 数据库优化

### 3.1 本地数据库表设计
```sql
-- 本地票号表
CREATE TABLE local_ticket (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    tkt_id VARCHAR(20),
    biz_uid INTEGER,
    status INTEGER,
    sync_status INTEGER,  -- 同步状态：0-未同步，1-已同步，2-同步失败
    create_time DATETIME,
    update_time DATETIME,
    FOREIGN KEY (biz_uid) REFERENCES business(uid)
);

-- 同步记录表
CREATE TABLE sync_record (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    table_name VARCHAR(50),
    record_id INTEGER,
    sync_type INTEGER,  -- 1-插入，2-更新，3-删除
    sync_status INTEGER,
    retry_count INTEGER,
    last_sync_time DATETIME
);
```

### 3.2 索引优化
```sql
-- 票号查询优化
CREATE INDEX idx_ticket_status ON local_ticket(status, sync_status);
CREATE INDEX idx_ticket_date ON local_ticket(create_time);

-- 同步性能优化
CREATE INDEX idx_sync_status ON sync_record(sync_status, table_name);
```

## 4. 部署架构

### 4.1 服务器部署
- **大厅服务器**
  - queuing-system-service
  - SQLite本地数据库
  - RabbitMQ服务

- **窗口终端**
  - 呼号客户端
  - LED控制服务

- **省级云服务器**
  - 云端数据库
  - 数据同步服务

### 4.2 高可用设计
1. **服务容错**
   - 服务健康检查
   - 自动重启机制
   - 日志监控告警

2. **数据安全**
   - 本地数据定期备份
   - 同步操作日志记录
   - 数据一致性校验

## 5. 性能优化

### 5.1 缓存策略
- 使用Redis缓存热点数据
- 实现多级缓存机制
- 合理设置缓存过期时间

### 5.2 并发处理
- 使用线程池处理并发请求
- 实现请求队列削峰
- 添加服务限流保护

## 6. 监控告警

### 6.1 系统监控
- 服务器资源监控
- 网络连接状态监控
- 服务运行状态监控

### 6.2 业务监控
- 排队等待时间监控
- 业务处理效率分析
- 异常情况实时告警