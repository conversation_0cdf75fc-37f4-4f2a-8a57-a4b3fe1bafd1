package com.ziyun.repairsystem.taxhall.controller;


import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.wuwenze.poi.ExcelKit;
import com.ziyun.repairsystem.common.annotation.Log;
import com.ziyun.repairsystem.common.controller.BaseController;
import com.ziyun.repairsystem.common.domain.QueryRequest;
import com.ziyun.repairsystem.common.domain.ResponseBo;
import com.ziyun.repairsystem.common.exception.ZiYunException;
import com.ziyun.repairsystem.taxhall.domain.SwjgInfo;
import com.ziyun.repairsystem.taxhall.domain.SwjgRo;
import com.ziyun.repairsystem.taxhall.service.SwjgInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("swjg")
public class SwjgInfoController extends BaseController {
    private String message;

    @Autowired
    private SwjgInfoService swjgInfoService;

    @GetMapping
//    @RequiresPermissions("swjg:view")
    public Map<String, Object> swjgList(QueryRequest request, SwjgInfo swjgInfo) {
        return getDataTable(this.swjgInfoService.findSwjgInfoes(request, swjgInfo));
    }

    @GetMapping("/querySwjgDeviceInfo")
    public ResponseBo querySwjgRo(@RequestParam(value = "swjgdm", required = false) String swjgdm) throws ZiYunException {
        try{
        SwjgRo swjg = swjgInfoService.querySwjgRo(swjgdm);
        return ResponseBo.ok(swjg);
        }catch (Exception e){
            message = "查询失败";
            log.error(message, e);
            throw new ZiYunException(message);
        }
    }

    @PutMapping("/updateSwjgDeviceInfo")
    public void updateSwjgRo(@Valid SwjgRo swjgRo) throws ZiYunException {
        try {
            this.swjgInfoService.updateSwjgRo(swjgRo);
        } catch (Exception e) {
            message = "修改大厅成功";
            log.error(message, e);
            throw new ZiYunException(message);
        }
    }


    @Log("新增大厅")
    @PostMapping
//    @RequiresPermissions("swjg:add")
    public void addSwjg(@Valid SwjgInfo swjgInfo) throws ZiYunException {
        try {
            this.swjgInfoService.createSwjgInfo(swjgInfo);
        } catch (Exception e) {
            message = "新增大厅成功";
            log.error(message, e);
            throw new ZiYunException(message);
        }
    }

    @Log("删除大厅")
    @DeleteMapping("/{swjgIds}")
//    @RequiresPermissions("swjg:delete")
    public void deleteSwjg(@NotBlank(message = "{required}") @PathVariable String swjgIds) throws ZiYunException {
        try {
            String[] ids = swjgIds.split(StringPool.COMMA);
            this.swjgInfoService.deleteSwjgInfoes(ids);
        } catch (Exception e) {
            message = "删除大厅成功";
            log.error(message, e);
            throw new ZiYunException(message);
        }
    }

    @Log("修改大厅")
    @PutMapping
//    @RequiresPermissions("swjg:update")
    public void updateSwjg(@Valid SwjgInfo swjgInfo) throws ZiYunException {
        try {
            log.info(swjgInfo.toString());
            this.swjgInfoService.updateSwjgInfo(swjgInfo);
        } catch (Exception e) {
            message = "修改大厅成功";
            log.error(message, e);
            throw new ZiYunException(message);
        }
    }

    @PostMapping("excel")
//    @RequiresPermissions("swjg:export")
    public void export(QueryRequest request, SwjgInfo swjgInfo, HttpServletResponse response) throws ZiYunException {
        try {
            List<SwjgInfo> swjgInfos = this.swjgInfoService.findSwjgInfoes(request, swjgInfo).getRecords();
            ExcelKit.$Export(SwjgInfo.class, response).downXlsx(swjgInfos, true);
        } catch (Exception e) {
            message = "导出Excel失败";
            log.error(message, e);
            throw new ZiYunException(message);
        }
    }

    @GetMapping("/infoBySWDM")
    public ResponseBo getInfoBySWJGDM(@NotBlank(message = "{required}") @RequestParam String swjgdm) {
        List<Map<String, Object>> reList = new ArrayList<>();
        SwjgInfo swjgInfo = new SwjgInfo();
//        swjgInfo.setSwjgdm(swjgdm);
        log.info(swjgdm+"---");
        swjgInfo.setDistrictId(swjgdm);
        List<SwjgInfo> swList = this.swjgInfoService.findSwjgInfoes(swjgInfo);
        if (null != swList) {
            for (SwjgInfo swjg : swList) {
                Map<String, Object> swMap = new HashMap<>();
                swMap.put("value", swjg.getBsdtdjxh());
                swMap.put("label", swjg.getBsdtmc());
                reList.add(swMap);
            }
            return ResponseBo.ok(reList);
        }
        return ResponseBo.error("获取失败");
    }
}
