package com.ziyunnotify.forward.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpEntity;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.util.EntityUtils;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.util.Map;

/**
 * HTTP客户端服务
 * 统一管理HTTP请求，提供连接池和超时配置
 * 
 * @Author: ytx
 * @Date: 2024/01/01
 */
@Slf4j
@Service
public class HttpClientService {
    
    private CloseableHttpClient httpClient;
    private PoolingHttpClientConnectionManager connectionManager;
    
    @PostConstruct
    public void init() {
        // 创建连接池管理器
        connectionManager = new PoolingHttpClientConnectionManager();
        connectionManager.setMaxTotal(200); // 最大连接数
        connectionManager.setDefaultMaxPerRoute(50); // 每个路由的最大连接数
        
        // 创建请求配置
        RequestConfig requestConfig = RequestConfig.custom()
                .setConnectTimeout(5000) // 连接超时5秒
                .setSocketTimeout(30000) // 读取超时30秒
                .setConnectionRequestTimeout(3000) // 从连接池获取连接超时3秒
                .build();
        
        // 创建HTTP客户端
        httpClient = HttpClients.custom()
                .setConnectionManager(connectionManager)
                .setDefaultRequestConfig(requestConfig)
                .build();
        
        log.info("HTTP客户端初始化完成");
    }
    
    @PreDestroy
    public void destroy() {
        try {
            if (httpClient != null) {
                httpClient.close();
            }
            if (connectionManager != null) {
                connectionManager.close();
            }
            log.info("HTTP客户端资源已释放");
        } catch (Exception e) {
            log.error("释放HTTP客户端资源时发生异常", e);
        }
    }
    
    /**
     * 发送POST请求
     * 
     * @param url 请求URL
     * @param requestData 请求数据
     * @return 响应结果
     * @throws HttpClientException HTTP请求异常
     */
    public JSONObject postJson(String url, Map<String, Object> requestData) throws HttpClientException {
        if (url == null || url.trim().isEmpty()) {
            throw new HttpClientException("请求URL不能为空");
        }
        
        if (requestData == null || requestData.isEmpty()) {
            throw new HttpClientException("请求参数不能为空");
        }
        
        HttpPost httpPost = new HttpPost(url);
        CloseableHttpResponse response = null;
        
        try {
            // 设置请求体
            String jsonString = JSON.toJSONString(requestData);
            StringEntity entity = new StringEntity(jsonString, ContentType.APPLICATION_JSON);
            httpPost.setEntity(entity);
            
            log.info("发送HTTP请求到: {}, 请求数据: {}", url, jsonString);
            
            // 发送请求
            response = httpClient.execute(httpPost);
            
            // 检查响应状态
            int statusCode = response.getStatusLine().getStatusCode();
            if (statusCode < 200 || statusCode >= 300) {
                throw new HttpClientException("HTTP请求失败，状态码: " + statusCode);
            }
            
            // 获取响应内容
            HttpEntity responseEntity = response.getEntity();
            String responseStr = EntityUtils.toString(responseEntity, "UTF-8");
            
            log.info("收到HTTP响应: {}", responseStr);
            
            // 解析JSON响应
            JSONObject result = JSON.parseObject(responseStr);
            if (result == null) {
                throw new HttpClientException("响应内容不是有效的JSON格式");
            }
            
            return result;
            
        } catch (HttpClientException e) {
            throw e;
        } catch (Exception e) {
            log.error("HTTP请求发生异常", e);
            throw new HttpClientException("HTTP请求执行失败: " + e.getMessage(), e);
        } finally {
            // 释放资源
            if (response != null) {
                try {
                    response.close();
                } catch (Exception e) {
                    log.warn("关闭HTTP响应时发生异常", e);
                }
            }
            httpPost.releaseConnection();
        }
    }
}
