package com.ziyunnotify.forward.common;

import com.ziyunnotify.forward.service.HttpClientException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

/**
 * 全局异常处理器
 * 
 * @Author: ytx
 * @Date: 2024/01/01
 */
@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandler {
    
    /**
     * 处理HTTP客户端异常
     */
    @ExceptionHandler(HttpClientException.class)
    public ApiResponse<Object> handleHttpClientException(HttpClientException e) {
        log.error("HTTP客户端异常: {}", e.getMessage(), e);
        return ApiResponse.error(502, "第三方服务异常: " + e.getMessage());
    }
    
    /**
     * 处理参数异常
     */
    @ExceptionHandler(IllegalArgumentException.class)
    public ApiResponse<Object> handleIllegalArgumentException(IllegalArgumentException e) {
        log.warn("参数异常: {}", e.getMessage());
        return ApiResponse.badRequest(e.getMessage());
    }
    
    /**
     * 处理通用异常
     */
    @ExceptionHandler(Exception.class)
    public ApiResponse<Object> handleException(Exception e) {
        log.error("系统异常", e);
        return ApiResponse.error("系统内部错误");
    }
}
