package com.qs.admin.taxhall.service;

import com.qs.admin.taxhall.model.dto.MenuDTO;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 菜单服务类
 * <AUTHOR> Admin Team
 */
@Service
public class MenuService {

    /**
     * 根据用户角色获取菜单列表
     * @param userRole 用户角色
     * @return 菜单列表
     */
    public List<MenuDTO> getMenusByRole(String userRole) {
        // 根据角色返回不同的菜单，这里先返回完整菜单
        return getAllMenus();
    }

    /**
     * 获取所有菜单
     * @return 完整菜单列表
     */
    public List<MenuDTO> getAllMenus() {
        List<MenuDTO> menus = new ArrayList<>();

        // 1. 人员统计/预警
        menus.add(new MenuDTO("el-icon-lx-home", "dashboard", "人员统计/预警", 0));

        // 2. 票据
        menus.add(new MenuDTO("el-icon-s-ticket", "ticket", "票据", 0));

        // 3. 历史票号明细
        menus.add(new MenuDTO("el-icon-s-order", "ticket-log", "历史票号明细", 0));

        // 4. 大厅基本设置 (有子菜单)
        List<MenuDTO> hallSettingSubs = Arrays.asList(
            new MenuDTO(null, "employee", "人员管理", 0),
            new MenuDTO(null, "window", "窗口管理", 0),
            new MenuDTO(null, "business", "业务管理", 0)
        );
        menus.add(new MenuDTO("el-icon-s-home", "3", "大厅基本设置", 1, hallSettingSubs));

        // 5. 系统管理设置
        menus.add(new MenuDTO("el-icon-setting", "system", "系统管理设置", 0));

        return menus;
    }

    /**
     * 根据用户权限过滤菜单
     * @param menus 原始菜单列表
     * @param userAccess 用户权限级别
     * @return 过滤后的菜单列表
     */
    public List<MenuDTO> filterMenusByAccess(List<MenuDTO> menus, Integer userAccess) {
        List<MenuDTO> filteredMenus = new ArrayList<>();
        
        for (MenuDTO menu : menus) {
            // 如果用户权限级别大于等于菜单要求的权限级别，则可以访问
            // 处理access为null的情况，默认为0
            Integer menuAccess = menu.getAccess() != null ? menu.getAccess() : 0;
            if (userAccess >= menuAccess) {
                MenuDTO filteredMenu = new MenuDTO();
                filteredMenu.setIcon(menu.getIcon());
                filteredMenu.setIndex(menu.getIndex());
                filteredMenu.setTitle(menu.getTitle());
                filteredMenu.setAccess(menu.getAccess());
                
                // 递归过滤子菜单
                if (menu.getSubs() != null && !menu.getSubs().isEmpty()) {
                    List<MenuDTO> filteredSubs = filterMenusByAccess(menu.getSubs(), userAccess);
                    if (!filteredSubs.isEmpty()) {
                        filteredMenu.setSubs(filteredSubs);
                    }
                }
                
                filteredMenus.add(filteredMenu);
            }
        }
        
        return filteredMenus;
    }

    /**
     * 根据用户ID获取用户菜单（预留接口，后期可从数据库获取）
     * @param userId 用户ID
     * @return 用户菜单列表
     */
    public List<MenuDTO> getMenusByUserId(Long userId) {
        // 这里可以根据用户ID从数据库查询用户角色和权限
        // 目前先返回默认菜单
        List<MenuDTO> allMenus = getAllMenus();
        
        // 假设普通用户权限级别为0，管理员为1
        Integer userAccess = (userId == 1L) ? 1 : 0;
        
        return filterMenusByAccess(allMenus, userAccess);
    }
}
