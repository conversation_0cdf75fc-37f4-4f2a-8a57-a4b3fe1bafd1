package com.qs.admin.taxhall.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.qs.admin.common.constant.Constants;
import com.qs.admin.common.core.Query;
//import com.qs.admin.common.datasource.DBIdentifier;
import com.qs.admin.taxhall.mapper.TicketMapper;
import com.qs.admin.taxhall.model.Ticket;
import com.qs.admin.taxhall.model.TicketLog;
import com.qs.admin.taxhall.model.vo.TicketVO;
import com.qs.admin.taxhall.service.TicketService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-03
 */
@Slf4j
@Service
@DS("taxhall")
public class TicketServiceImpl extends ServiceImpl<TicketMapper, Ticket> implements TicketService {

    @Override
    public IPage<Ticket> page(IPage<Ticket> page, Ticket ticket) {
//        //DBIdentifier.setUnitCode(Constants.UNIT_CODE);
        QueryWrapper<Ticket> queryWrapper = new QueryWrapper<>();

        // 检查ticket是否为null
        if (ticket != null) {
            //票号查询
            queryWrapper.eq(StringUtils.isNotBlank(ticket.getTktId()),"tktId", ticket.getTktId());
            //窗口查询
            queryWrapper.in(null != ticket.getWinId(),"win_id", ticket.getWinId());
            //业务查询
            queryWrapper.eq(null != ticket.getBizUid(),"biz_uid", ticket.getBizUid());
            //工作人员查询
            queryWrapper.eq(null != ticket.getEmpUid(),"emp_uid", ticket.getEmpUid());
            //状态查询
            queryWrapper.eq(null != ticket.getStatus(),"status", ticket.getStatus());
            //维护人查询
            if(null!=ticket.getRank()){
                if(ticket.getRank()>20&&ticket.getRank()<30){
                    queryWrapper.ge("rank", 2200)
                            .le("rank",2210);
                } else if (ticket.getRank()>30) {
                    queryWrapper.ge("rank", 3100)
                            .le("rank",3115);
                }else{
                    queryWrapper.eq(null != ticket.getRank(),"rank", ticket.getRank());
                }
            }

            // 修复空指针异常：检查startDateTime和endDateTime是否为null
            if (ticket.getStartDateTime() != null && ticket.getEndDateTime() != null &&
                !ticket.getStartDateTime().equals(ticket.getEndDateTime())) {
                if (StringUtils.isNotBlank(ticket.getStartDateTime()) && StringUtils.isNotBlank(ticket.getEndDateTime())) {
                    //报障时段查询
                    queryWrapper
                            .ge("tktTime", ticket.getStartDateTime())
                            .le("tktTime", ticket.getEndDateTime());
                }
            }
        }
        IPage<Ticket> ticketIPage = this.baseMapper.selectPage(page, queryWrapper);
        if (null != ticketIPage.getRecords()) {
            List<Ticket> ticketList = ticketIPage.getRecords();
            for (Ticket ticket1 : ticketList) {
                // 安全调用setTktDateTime
                try {
                    ticket1.setTktDateTime();
                } catch (Exception e) {
                    log.warn("设置票据日期时间失败: {}", e.getMessage());
                }
            }
            ticketIPage.setRecords(ticketList);
            return ticketIPage;
        }
        return ticketIPage;
    }

    @Override
    public IPage<TicketVO> pageVO(Page<TicketVO> page, Ticket ticket) {
        if (ticket == null) {
            // 无条件查询
            return this.baseMapper.selectTicketVOPage(page);
        } else {
            // 有条件查询
            return this.baseMapper.selectTicketVOPageWithCondition(page, ticket);
        }
    }
}
