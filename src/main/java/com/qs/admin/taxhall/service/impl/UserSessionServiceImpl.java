package com.qs.admin.taxhall.service.impl;

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.qs.admin.common.shiro.security.ShiroJwtUtil;
import com.qs.admin.common.constant.SecurityConsts;
import com.qs.admin.taxhall.model.Employee;
import com.qs.admin.taxhall.service.EmployeeService;
import com.qs.admin.taxhall.service.UserSessionService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.concurrent.TimeUnit;

/**
 * 用户会话管理服务实现类
 */
@Service
public class UserSessionServiceImpl implements UserSessionService {

    private static final Logger logger = LoggerFactory.getLogger(UserSessionServiceImpl.class);

    @Autowired
    private ShiroJwtUtil jwtUtil;

    @Autowired
    private EmployeeService employeeService;

    /**
     * 用户会话缓存（用于存储活跃的会话）
     * key: token, value: Employee
     */
    private final Cache<String, Employee> sessionCache = Caffeine.newBuilder()
            .maximumSize(1000)
            .expireAfterWrite(24, TimeUnit.HOURS)
            .build();

    /**
     * 黑名单缓存（用于存储已注销的token）
     * key: token, value: 注销时间
     */
    private final Cache<String, Long> blacklistCache = Caffeine.newBuilder()
            .maximumSize(10000)
            .expireAfterWrite(24, TimeUnit.HOURS)
            .build();

    @Override
    public String createSession(Employee employee) {
        try {
            // 生成JWT token
            String token = jwtUtil.sign(employee.getUsername(), String.valueOf(System.currentTimeMillis()));

            // 将用户信息存入会话缓存
            sessionCache.put(token, employee);

            logger.info("创建用户会话成功: userId={}, username={}", employee.getUid(), employee.getUsername());
            return token;
        } catch (Exception e) {
            logger.error("创建用户会话失败", e);
            throw new RuntimeException("创建会话失败", e);
        }
    }

    @Override
    public boolean validateSession(String token) {
        if (token == null || token.trim().isEmpty()) {
            return false;
        }

        // 检查token是否在黑名单中
        if (blacklistCache.getIfPresent(token) != null) {
            logger.debug("Token在黑名单中: {}", token);
            return false;
        }

        // 验证JWT token
        try {
            if (!jwtUtil.verify(token)) {
                logger.debug("JWT token验证失败: {}", token);
                return false;
            }
        } catch (Exception e) {
            logger.debug("JWT token验证异常: {}", token, e);
            return false;
        }

        return true;
    }

    @Override
    public Employee getCurrentUser(String token) {
        //验证token
        if (!validateSession(token)) {
            logger.warn("Token验证失败: {}", token);
            return null;
        }


        // 先从缓存中获取
        Employee cachedEmployee = sessionCache.getIfPresent(token);
        if (cachedEmployee != null) {
            return cachedEmployee;
        }

        // 缓存中没有，从JWT中解析用户名，然后从数据库查询
        String username = jwtUtil.getClaim(token, SecurityConsts.ACCOUNT);
        if (username != null) {
            Employee employee = employeeService.getByUsername(username);
            if (employee != null && employee.getEnabled() == 1) {
                // 重新放入缓存
                sessionCache.put(token, employee);
                return employee;
            }
        }

        return null;
    }

    @Override
    public void destroySession(String token) {
        if (token != null && !token.trim().isEmpty()) {
            // 从会话缓存中移除
            sessionCache.invalidate(token);

            // 将token加入黑名单
            blacklistCache.put(token, System.currentTimeMillis());

            logger.info("销毁用户会话: token={}", token);
        }
    }

    @Override
    public String refreshSession(String token) {
        Employee currentUser = getCurrentUser(token);
        if (currentUser == null) {
            throw new RuntimeException("无效的会话，无法刷新");
        }

        // 销毁旧会话
        destroySession(token);

        // 创建新会话
        String newToken = createSession(currentUser);

        logger.info("刷新用户会话成功: userId={}, username={}", currentUser.getUid(), currentUser.getUsername());
        return newToken;
    }
}
