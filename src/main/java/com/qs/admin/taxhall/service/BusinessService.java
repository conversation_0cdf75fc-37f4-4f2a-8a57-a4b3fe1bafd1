package com.qs.admin.taxhall.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.qs.admin.taxhall.model.Business;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.Collection;
import java.util.List;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-03
 */
public interface BusinessService extends IService<Business> {

    boolean delByIds(List<String> ids);

    Integer getBizUidByName(@NotBlank(message = "业务名称不能为空") String name);
}
