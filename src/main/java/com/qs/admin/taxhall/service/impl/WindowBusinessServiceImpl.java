package com.qs.admin.taxhall.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.qs.admin.common.constant.Constants;
//import com.qs.admin.common.datasource.DBIdentifier;
import com.qs.admin.taxhall.mapper.WindowBusinessMapper;
import com.qs.admin.taxhall.model.WindowBusiness;
import com.qs.admin.taxhall.service.WindowBusinessService;
import org.springframework.stereotype.Service;

import java.security.Key;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-03
 */
@Service

public class WindowBusinessServiceImpl extends ServiceImpl<WindowBusinessMapper, WindowBusiness> implements WindowBusinessService {


    @Override
    public boolean removeByKey(String key, String value) {
        QueryWrapper<WindowBusiness> queryWrapper = new QueryWrapper<>();
        if (key.equals("BIZ_UID")) {
            queryWrapper.eq("BIZ_UID", value);
            return this.remove(queryWrapper);
        }
        if (key.equals("WIN_UID")) {
            queryWrapper.eq("WIN_UID", value);
            return this.remove(queryWrapper);
        }
        return false;
    }
}
