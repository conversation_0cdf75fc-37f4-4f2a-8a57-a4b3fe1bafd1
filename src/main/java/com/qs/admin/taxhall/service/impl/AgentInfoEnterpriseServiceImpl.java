package com.qs.admin.taxhall.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.qs.admin.taxhall.mapper.AgentInfoEnterpriseMapper;
import com.qs.admin.taxhall.model.AgentInfoEnterprise;
import com.qs.admin.taxhall.service.AgentInfoEnterpriseService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class AgentInfoEnterpriseServiceImpl extends ServiceImpl<AgentInfoEnterpriseMapper, AgentInfoEnterprise> implements AgentInfoEnterpriseService {

    /**
     * 重写 save 方法，使用 baseMapper.insert 直接插入
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean save(AgentInfoEnterprise entity) {
        // 使用 baseMapper 直接插入，避免使用 MyBatis-Plus 的自动生成 SQL
        int result = baseMapper.insert(entity);
        return result > 0;
    }
}
