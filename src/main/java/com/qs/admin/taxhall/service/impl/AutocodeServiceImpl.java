package com.qs.admin.taxhall.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.qs.admin.taxhall.mapper.AutocodeMapper;
import com.qs.admin.taxhall.model.Autocode;
import com.qs.admin.taxhall.service.AutocodeService;
import org.springframework.stereotype.Service;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-03
 */
@Service
@DS("taxhall")
public class AutocodeServiceImpl extends ServiceImpl<AutocodeMapper, Autocode> implements AutocodeService {

}
