package com.qs.admin.taxhall.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.qs.admin.common.constant.Constants;
//import com.qs.admin.common.datasource.DBIdentifier;
import com.qs.admin.taxhall.mapper.SystemMapper;
import com.qs.admin.taxhall.model.System;
import com.qs.admin.taxhall.service.SystemService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-13
 */
@Service

public class SystemServiceImpl extends ServiceImpl<SystemMapper, System> implements SystemService {

    @Value("${spring.datasource.driver-class-name:}")
    private String driverClassName;

    @Value("${spring.datasource.url:}")
    private String url;

    @Value("${spring.datasource.jdbc-url:}")
    private String jdbcUrl;

    @Override
    public boolean updateByKey(System system) {
        QueryWrapper<System> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(getKeyFieldName(), system.getKey());
        return this.update(system, queryWrapper);
    }

    /**
     * 根据数据库类型获取KEY字段名
     */
    private String getKeyFieldName() {
        if (isSqlServer()) {
            return "[KEY]";  // SQL Server使用方括号
        } else {
            return "`KEY`";  // MySQL/OceanBase使用反引号
        }
    }

    /**
     * 判断是否为SQL Server数据库
     */
    private boolean isSqlServer() {
        return driverClassName.contains("sqlserver") ||
               getEffectiveUrl().contains("sqlserver");
    }

    /**
     * 获取有效的数据库URL
     */
    private String getEffectiveUrl() {
        if (jdbcUrl != null && !jdbcUrl.isEmpty()) {
            return jdbcUrl.toLowerCase();
        }
        return url != null ? url.toLowerCase() : "";
    }
}
