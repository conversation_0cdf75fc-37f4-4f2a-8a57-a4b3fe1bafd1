package com.qs.admin.taxhall.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.qs.admin.common.constant.Constants;
//import com.qs.admin.common.datasource.DBIdentifier;
import com.qs.admin.taxhall.mapper.SystemMapper;
import com.qs.admin.taxhall.model.System;
import com.qs.admin.taxhall.service.SystemService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-13
 */
@Service

public class SystemServiceImpl extends ServiceImpl<SystemMapper, System> implements SystemService {

    @Override
    public boolean updateByKey(System system) {
        QueryWrapper<System> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("[KEY]", system.getKey());
        return this.update(system, queryWrapper);
    }
}
