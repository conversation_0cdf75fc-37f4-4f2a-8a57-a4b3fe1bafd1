package com.qs.admin.taxhall.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.qs.admin.taxhall.mapper.TicketVerifyMapper;
import com.qs.admin.taxhall.model.TicketVerify;
import com.qs.admin.taxhall.service.TicketVerifyService;
import org.springframework.stereotype.Service;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-03
 */
@Service
@DS("taxhall")
public class TicketVerifyServiceImpl extends ServiceImpl<TicketVerifyMapper, TicketVerify> implements TicketVerifyService {

    @Override
    public TicketVerify getByTktUid(Integer tktUid) {
        QueryWrapper<TicketVerify> wrapper = new QueryWrapper<>();
        wrapper.eq("TKT_UID", tktUid);
        return this.getOne(wrapper);
    }

}
