package com.qs.admin.taxhall.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.qs.admin.taxhall.mapper.TicketVerifyMapper;
import com.qs.admin.taxhall.model.TicketVerify;
import com.qs.admin.taxhall.service.TicketVerifyService;
import org.springframework.stereotype.Service;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-03
 */
@Service

public class TicketVerifyServiceImpl extends ServiceImpl<TicketVerifyMapper, TicketVerify> implements TicketVerifyService {

    @Override
    public TicketVerify getByTktUid(Integer tktUid) {
        QueryWrapper<TicketVerify> wrapper = new QueryWrapper<>();
        wrapper.eq("TKT_UID", tktUid);
        // 使用 getOne(wrapper, false) 避免多条记录异常，返回第一条记录
        return this.getOne(wrapper, false);
    }

}
