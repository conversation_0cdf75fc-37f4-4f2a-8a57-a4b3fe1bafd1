package com.qs.admin.taxhall.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.qs.admin.common.domain.R;
import com.qs.admin.taxhall.model.Window;
import com.qs.admin.taxhall.model.vo.WindowVO;

import java.util.List;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-03
 */
public interface WindowService extends IService<Window> {

    List<Window> findAll();

    /**
     * 分页查询窗口数据
     * @param pageNum 页码
     * @param pageSize 每页大小
     * @return 分页结果
     */
    R<?> page(Integer pageNum, Integer pageSize);

    boolean insert(Window window) throws Exception;

    boolean updateByUid(Window window);

    boolean delByIds(List<String> ids);

}
