package com.qs.admin.taxhall.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.qs.admin.common.domain.R;
import com.qs.admin.taxhall.model.Window;
import com.qs.admin.taxhall.model.vo.WindowVO;
import com.qs.admin.taxhall.model.vo.WindowBusinessVO;
import com.qs.admin.taxhall.model.dto.WindowDTO;
import com.qs.admin.taxhall.model.dto.WindowBusinessDTO;
import javax.servlet.http.HttpServletRequest;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-03
 */
public interface WindowService extends IService<Window> {

    List<Window> findAll();

    /**
     * 分页查询窗口数据
     * @param pageNum 页码
     * @param pageSize 每页大小
     * @return 分页结果
     */
    R<?> page(Integer pageNum, Integer pageSize);

    boolean insert(Window window) throws Exception;

    boolean updateByUid(Window window);

    boolean delByIds(List<String> ids);

    /**
     * 根据窗口ID获取关联的业务列表
     * @param winUid 窗口ID
     * @return 业务列表
     */
    List<WindowBusinessVO> findByWin(Integer winUid);

    /**
     * 创建窗口及其关联业务
     * @param windowDTO 窗口数据传输对象
     * @param request HTTP请求对象
     * @return 操作结果
     */
    R<?> createWindowWithBusinesses(WindowDTO windowDTO, HttpServletRequest request);

    /**
     * 更新窗口及其关联业务
     * @param windowDTO 窗口数据传输对象
     * @return 操作结果
     */
    R<?> updateWindowWithBusinesses(WindowDTO windowDTO);

    /**
     * 批量删除窗口
     * @param idsParam 逗号分隔的ID字符串
     * @return 操作结果
     */
    R<?> batchDeleteWindows(String idsParam);

    /**
     * 获取窗口选项列表
     * @return 窗口选项
     */
    R<?> getWindowOptions();

    /**
     * 解析业务列表JSON字符串
     * @param bizListArray 业务列表JSON字符串数组
     * @return 解析后的业务DTO列表
     */
    List<WindowBusinessDTO> parseBizListFromRequest(String[] bizListArray);

}
