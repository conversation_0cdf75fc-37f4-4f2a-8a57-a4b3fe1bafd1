package com.qs.admin.taxhall.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.qs.admin.taxhall.model.Window;
import com.qs.admin.taxhall.model.vo.WindowVO;

import java.util.List;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-03
 */
public interface WindowService extends IService<Window> {

    List<Window> findAll();

    boolean insert(Window window) throws Exception;

    boolean updateByUid(Window window);

    boolean delByIds(List<String> ids);

}
