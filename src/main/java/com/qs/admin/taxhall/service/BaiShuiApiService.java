package com.qs.admin.taxhall.service;

import com.qs.admin.taxhall.model.RelatedEnterpriseRequest;
import com.qs.admin.taxhall.model.BaiShuiApiResponse;

/**
 * 佰税科技API服务接口
 * 用于与佰税科技提供的第三方接口进行交互
 */
public interface BaiShuiApiService {
    /**
     * 获取佰税API访问令牌
     * @return 访问令牌
     */
    String getToken();
    
    /**
     * 查询关联企业信息
     * @param req 关联企业查询请求参数
     * @return 查询结果
     */
    BaiShuiApiResponse getRelatedEnterprise(RelatedEnterpriseRequest req);

    String getAppKey();
}
