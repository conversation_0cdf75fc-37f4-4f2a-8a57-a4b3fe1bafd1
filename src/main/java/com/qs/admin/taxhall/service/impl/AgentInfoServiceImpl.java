package com.qs.admin.taxhall.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.qs.admin.taxhall.mapper.AgentInfoEnterpriseMapper;
import com.qs.admin.taxhall.mapper.AgentInfoMapper;
import com.qs.admin.taxhall.model.AgentInfo;
import com.qs.admin.taxhall.model.AgentInfoEnterprise;
import com.qs.admin.taxhall.model.Ticket;
import com.qs.admin.taxhall.model.TicketVerify;
import com.qs.admin.taxhall.model.dto.AgentInfoSaveRequest;
import com.qs.admin.taxhall.model.dto.EnterpriseDTO;
import com.qs.admin.taxhall.model.vo.AgentTodayEnterpriseVO;
import com.qs.admin.taxhall.service.AgentInfoEnterpriseService;
import com.qs.admin.taxhall.service.AgentInfoService;
import com.qs.admin.taxhall.service.TicketVerifyService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 代理机构人员信息服务实现类
 */
@Service
public class AgentInfoServiceImpl extends ServiceImpl<AgentInfoMapper, AgentInfo> implements AgentInfoService {

    private static final Logger logger = LoggerFactory.getLogger(AgentInfoServiceImpl.class);

    // 注入依赖的服务和映射器
    @Autowired
    private AgentInfoMapper agentInfoMapper;

    @Autowired
    private AgentInfoEnterpriseMapper agentInfoEnterpriseMapper;

    @Autowired
    private AgentInfoEnterpriseService agentInfoEnterpriseService;

    @Autowired
    private TicketVerifyService ticketVerifyService;

    // ObjectMapper 实例，用于 JSON 转换
    private static final ObjectMapper objectMapper = new ObjectMapper()
            .configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);

    /**
     * 保存代理人信息
     * @param agentInfo 代理人信息
     * @return 保存结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveAgentInfo(AgentInfo agentInfo) {
        if (agentInfo == null) {
            logger.error("代理人信息不能为空");
            return false;
        }

        try {
            logger.info("开始保存代理人信息: {}", agentInfo);
            int result = agentInfoMapper.insertAgentInfo(agentInfo);
            logger.info("保存代理人信息结果: {}", result);
            return result > 0;
        } catch (Exception e) {
            logger.error("保存代理人信息异常", e);
            throw e;
        }
    }

    /**
     * 验证并设置默认值
     * @param ent 企业信息
     */
    private void validateAndSetDefaultValues(AgentInfoEnterprise ent) {
        if (ent.getDjxh() == null) ent.setDjxh("");
        if (ent.getNsrsbh() == null) ent.setNsrsbh("");
        if (ent.getNsrmc() == null) ent.setNsrmc("");
        if (ent.getZgswjDm() == null) ent.setZgswjDm("");
        if (ent.getZgswksfjDm() == null) ent.setZgswksfjDm("");
        if (ent.getNsrztMc() == null) ent.setNsrztMc("");
        if (ent.getSfzj() == null) ent.setSfzj("");
    }

    /**
     * 保存代理人信息及其关联的企业信息
     * @param agentInfo 代理人信息
     * @param enterpriseList 企业信息列表
     * @return 保存结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveAgentInfoWithEnterprises(AgentInfo agentInfo, List<AgentInfoEnterprise> enterpriseList) {
        // 参数校验
        if (agentInfo == null) {
            logger.error("代理人信息不能为空");
            return false;
        }

        if (enterpriseList == null || enterpriseList.isEmpty()) {
            logger.warn("企业信息列表为空");
        }

        try {
            // 保存代理人信息
            logger.info("开始保存代理人信息: {}", agentInfo);
            int result = agentInfoMapper.insertAgentInfo(agentInfo);
            logger.info("保存代理人信息结果: {}", result);
            boolean saved = result > 0;
            if (!saved) {
                logger.error("保存代理人信息失败");
                return false;
            }

            // 如果没有企业信息，直接返回成功
            if (enterpriseList == null || enterpriseList.isEmpty()) {
                return true;
            }

            // 检查代理人 ID
            if (agentInfo.getId() == null) {
                logger.error("代理人 ID 为 null，无法设置企业明细的 agent_info_id");
                return false;
            }

            // 设置所有企业明细的 agent_info_id
            for (AgentInfoEnterprise ent : enterpriseList) {
                ent.setAgentInfoId(agentInfo.getId());
            }

            // 批量保存企业明细
            try {
                boolean allSuccess = true;
                for (AgentInfoEnterprise ent : enterpriseList) {
                    // 确保必要字段不为 null
                    validateAndSetDefaultValues(ent);

                    logger.info("开始保存企业明细: {}", ent);
                    int entResult = agentInfoEnterpriseMapper.insert(ent);
                    logger.info("保存企业明细结果: {}", entResult);
                    if (entResult <= 0) {
                        logger.error("保存企业明细失败: {}", ent);
                        allSuccess = false;
                    }
                }
                return allSuccess;
            } catch (Exception e) {
                logger.error("保存企业明细异常", e);
                throw e;
            }
        } catch (Exception e) {
            logger.error("保存代理人信息异常", e);
            throw e;
        }
    }

    /**
     * 保存代理人信息及其关联的企业信息
     * @param request 请求参数
     * @return 保存结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveAgentInfoWithEnterprises(AgentInfoSaveRequest request) {
        // 参数校验
        if (request == null) {
            logger.error("请求参数不能为空");
            return false;
        }

        if (request.getTicketInfo() == null) {
            logger.error("票据信息不能为空");
            return false;
        }

        try {
            // 1. 过滤 IsSelected=true 的企业
            List<EnterpriseDTO> selectedList = request.getSelectedEnterprices() != null ?
                    request.getSelectedEnterprices().stream()
                            .filter(Objects::nonNull)
                            .filter(e -> Boolean.TRUE.equals(e.getIsSelected()))
                            .collect(Collectors.toList()) :
                    new ArrayList<>();

            // 2. 获取 ticketInfo
            Ticket ticketInfo = request.getTicketInfo();

            // 3. 组装AgentInfo主表
            AgentInfo agentInfo = new AgentInfo();
            // agent_name、agent_id_card、agent_phone字段从TicketVerify表中获取
            Integer tktUid = null;
            try {
                tktUid = ticketInfo.getUid();
            } catch (Exception e) {
                // 兼容性处理，如果TktId不是数字
                logger.warn("获取票据 UID 异常", e);
            }

            TicketVerify ticketVerify = (tktUid != null) ? ticketVerifyService.getByTktUid(tktUid) : null;

            // 确保 agentName 不为 null
            String name = (ticketVerify != null && ticketVerify.getName() != null) ? ticketVerify.getName() : "未知";
            agentInfo.setAgentName(name);

            // 确保 agentIdCard 不为 null
            String idCard = (ticketVerify != null && ticketVerify.getCode() != null) ? ticketVerify.getCode() : "";
            agentInfo.setAgentIdCard(idCard);

            // 确保 agentPhone 不为 null
            String phone = (ticketVerify != null && ticketVerify.getPhone() != null) ? ticketVerify.getPhone() : "";
            agentInfo.setAgentPhone(phone);

            // 直接设置 Date 类型，确保不为 null
            agentInfo.setTktDateTime(ticketInfo.getTktDateTime() != null ? ticketInfo.getTktDateTime() : new Date());

            // 设置 tktUid，直接使用 ticketInfo 的 uid
            if (ticketInfo.getUid() != null) {
                agentInfo.setTktUid(ticketInfo.getUid());
            } else {
                // 如果 uid 为 null，设置为默认值 0
                logger.warn("票据 UID 为 null，使用默认值 0");
                agentInfo.setTktUid(0);
            }

            // 设置票据号，确保不为 null
            agentInfo.setTktId(ticketInfo.getTktId() != null ? ticketInfo.getTktId() : "");

            // 4. 组装企业明细
            List<AgentInfoEnterprise> enterpriseList = new ArrayList<>();
            for (EnterpriseDTO dto : selectedList) {
                if (dto == null) continue;

                AgentInfoEnterprise ent = new AgentInfoEnterprise();
                ent.setDjxh(dto.getDJXH());
                ent.setNsrsbh(dto.getNSRSBH());
                ent.setNsrmc(dto.getNSRMC());
                ent.setZgswjDm(dto.getZGSWJ_DM());
                ent.setZgswksfjDm(dto.getZGSWKSFJ_DM());
                ent.setNsrztMc(dto.getNSRZT_MC());
                ent.setSfzj(dto.getSFZJ());

                // 验证并设置默认值
                validateAndSetDefaultValues(ent);

                enterpriseList.add(ent);
            }

            // 5. 调用保存方法
            return saveAgentInfoWithEnterprises(agentInfo, enterpriseList);
        } catch (Exception e) {
            logger.error("处理请求参数异常", e);
            throw e;
        }
    }

    /**
     * 将请求参数转换为 AgentInfoSaveRequest 对象
     * @param params 请求参数
     * @return AgentInfoSaveRequest 对象
     * @throws Exception 异常信息
     */
    @Override
    public AgentInfoSaveRequest convertParamsToRequest(Map<String, Object> params) throws Exception {
        // 参数校验
        if (params == null || params.isEmpty()) {
            throw new IllegalArgumentException("参数不能为空");
        }

        // 手动构建 AgentInfoSaveRequest 对象
        AgentInfoSaveRequest request = new AgentInfoSaveRequest();

        try {
            // 处理 ticketInfo
            processTicketInfo(params, request);

            // 处理 selectedEnterprices
            processSelectedEnterprises(params, request);

            return request;
        } catch (Exception e) {
            logger.error("参数转换异常", e);
            throw e;
        }
    }

    /**
     * 处理票据信息
     * @param params 请求参数
     * @param request 请求对象
     * @throws IllegalArgumentException 参数异常
     */
    private void processTicketInfo(Map<String, Object> params, AgentInfoSaveRequest request) throws IllegalArgumentException {
        if (!params.containsKey("ticketInfo")) {
            throw new IllegalArgumentException("缺少票据信息");
        }

        Object ticketInfoObj = params.get("ticketInfo");
        Ticket ticketInfo = objectMapper.convertValue(ticketInfoObj, Ticket.class);
        request.setTicketInfo(ticketInfo);
    }

    /**
     * 处理选中的企业列表
     * @param params 请求参数
     * @param request 请求对象
     */
    private void processSelectedEnterprises(Map<String, Object> params, AgentInfoSaveRequest request) {
        if (!params.containsKey("selectedEnterprices")) {
            logger.warn("缺少选中的企业列表");
            return;
        }

        Object selectedEnterprisesObj = params.get("selectedEnterprices");
        if (!(selectedEnterprisesObj instanceof List)) {
            logger.warn("选中的企业列表格式错误");
            return;
        }

        List<?> enterpriseList = (List<?>) selectedEnterprisesObj;
        List<EnterpriseDTO> selectedEnterprises = new ArrayList<>();

        for (Object obj : enterpriseList) {
            if (obj instanceof Map) {
                EnterpriseDTO dto = convertMapToEnterpriseDTO((Map<?, ?>) obj);
                if (dto != null) {
                    selectedEnterprises.add(dto);
                }
            }
        }

        request.setSelectedEnterprices(selectedEnterprises);
    }

    /**
     * 将 Map 转换为 EnterpriseDTO 对象
     * @param map Map 对象
     * @return EnterpriseDTO 对象
     */
    private EnterpriseDTO convertMapToEnterpriseDTO(Map<?, ?> map) {
        if (map == null) {
            return null;
        }

        EnterpriseDTO dto = new EnterpriseDTO();

        // 手动设置属性，处理大小写问题
        if (map.containsKey("DJXH")) dto.setDJXH(String.valueOf(map.get("DJXH")));
        if (map.containsKey("NSRSBH")) dto.setNSRSBH(String.valueOf(map.get("NSRSBH")));
        if (map.containsKey("NSRMC")) dto.setNSRMC(String.valueOf(map.get("NSRMC")));
        if (map.containsKey("ZGSWJ_DM")) dto.setZGSWJ_DM(String.valueOf(map.get("ZGSWJ_DM")));
        if (map.containsKey("ZGSWKSFJ_DM")) dto.setZGSWKSFJ_DM(String.valueOf(map.get("ZGSWKSFJ_DM")));
        if (map.containsKey("NSRZT_MC")) dto.setNSRZT_MC(String.valueOf(map.get("NSRZT_MC")));
        if (map.containsKey("SFZJ")) dto.setSFZJ(String.valueOf(map.get("SFZJ")));

        // 处理 IsSelected 字段
        if (map.containsKey("IsSelected")) {
            Object isSelectedObj = map.get("IsSelected");
            if (isSelectedObj instanceof Boolean) {
                dto.setIsSelected((Boolean) isSelectedObj);
            } else if (isSelectedObj != null) {
                dto.setIsSelected(Boolean.valueOf(isSelectedObj.toString()));
            }
        }

        return dto;
    }

    @Override
    public List<AgentTodayEnterpriseVO> getTodayAgentEnterpriseList(String ticketId) {
        LocalDate today = LocalDate.now();
        LocalDateTime startOfDay = today.atStartOfDay();
        LocalDateTime endOfDay = today.plusDays(1).atStartOfDay().minusNanos(1);
        Date startDate = java.sql.Timestamp.valueOf(startOfDay);
        Date endDate = java.sql.Timestamp.valueOf(endOfDay);

        // 使用 QueryWrapper 查询当日的代理人信息，并根据ticketUid过滤
        QueryWrapper<AgentInfo> queryWrapper = new QueryWrapper<>();
        queryWrapper.ge("TKT_DATE_TIME", startDate);
        queryWrapper.le("TKT_DATE_TIME", endDate);
        if (ticketId != null && !ticketId.isEmpty()) {
            queryWrapper.eq("TKT_ID", ticketId);
        }
        List<AgentInfo> agentInfos = this.list(queryWrapper);

        if (agentInfos == null || agentInfos.size() == 0) {
            return null;
        }
        List<AgentTodayEnterpriseVO> result = new ArrayList<>();
        for (AgentInfo agent : agentInfos) {
            // 查询每个代理人对应的企业信息
            QueryWrapper<AgentInfoEnterprise> entWrapper = new QueryWrapper<>();
            entWrapper.eq("AGENT_INFO_ID", agent.getId());
            List<AgentInfoEnterprise> ents = agentInfoEnterpriseService.list(entWrapper);

            for (AgentInfoEnterprise ent : ents) {
                AgentTodayEnterpriseVO vo = new AgentTodayEnterpriseVO();
                vo.setTktId(agent.getTktId());
                vo.setName(agent.getAgentName());
                vo.setIdCard(agent.getAgentIdCard());
                vo.setPhone(agent.getAgentPhone());
                vo.setOrgName(agent.getAgentOrg() != null ? agent.getAgentOrg() : "");
                vo.setSerialNumber(ent.getDjxh());
                vo.setServiceTarget(ent.getNsrmc());
                vo.setServiceTargetCode(ent.getNsrsbh());
                vo.setTktDateTime(agent.getTktDateTime());
                result.add(vo);
            }
        }
        return result;
    }
}
