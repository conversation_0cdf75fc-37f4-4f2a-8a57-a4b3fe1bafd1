package com.qs.admin.taxhall.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.qs.admin.common.constant.Constants;
import com.qs.admin.common.util.PasswordUtil;
//import com.qs.admin.common.datasource.DBIdentifier;
import com.qs.admin.taxhall.mapper.EmployeeMapper;
import com.qs.admin.taxhall.model.Employee;
import com.qs.admin.taxhall.service.EmployeeService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;


import java.util.Arrays;

import java.util.List;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-03
 */
@Service
//@DS("taxhall")
public class EmployeeServiceImpl extends ServiceImpl<EmployeeMapper, Employee> implements EmployeeService {

    @Override
    public Employee login(String username, String password) {
        if (!StringUtils.hasText(username) || !StringUtils.hasText(password)) {
            return null;
        }

        // 先根据用户名查询用户
        QueryWrapper<Employee> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("USERNAME", username)
                   .eq("ENABLED", 1); // 只查询启用的用户

        Employee employee = this.getOne(queryWrapper);
        if (employee == null) {
            return null;
        }

        // 验证密码（兼容旧系统的简单加密和新系统的带盐加密）
        String storedPassword = employee.getPassword();

        // 先尝试简单MD5加密验证（兼容旧系统）
        if (PasswordUtil.simpleMatches(password, storedPassword)) {
            return employee;
        }

        // 如果简单加密验证失败，尝试直接对比（兼容明文密码）
        if (password.equals(storedPassword)) {
            return employee;
        }

        // TODO: 如果需要支持带盐加密，可以在这里添加逻辑
        // 需要在Employee表中添加salt字段

        return null;
    }

    @Override
    @Transactional
    public boolean deleteEmpolyeeByIds(List<String> uids) {
//        //DBIdentifier.setUnitCode(Constants.UNIT_CODE);
        if (this.baseMapper.deleteBatchIds(uids) == 1) {
            return true;
        }
        return false;

    }
}
