package com.qs.admin.taxhall.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.qs.admin.common.constant.Constants;
//import com.qs.admin.common.datasource.DBIdentifier;
import com.qs.admin.taxhall.mapper.EmployeeMapper;
import com.qs.admin.taxhall.model.Employee;
import com.qs.admin.taxhall.service.EmployeeService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;


import java.util.Arrays;

import java.util.List;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-03
 */
@Service
public class EmployeeServiceImpl extends ServiceImpl<EmployeeMapper, Employee> implements EmployeeService {

    @Override
    public Employee login(String username, String password) {
        if (!StringUtils.hasText(username) || !StringUtils.hasText(password)) {
            return null;
        }

        QueryWrapper<Employee> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("USERNAME", username)
                   .eq("PASSWORD", password)
                   .eq("ENABLED", 1); // 只查询启用的用户

        return this.getOne(queryWrapper);
    }

    @Override
    public Employee getByUsername(String username) {
        if (!StringUtils.hasText(username)) {
            return null;
        }

        QueryWrapper<Employee> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("USERNAME", username);

        return this.getOne(queryWrapper);
    }

    @Override
    @Transactional
    public boolean deleteEmpolyeeByIds(List<String> uids) {
//        //DBIdentifier.setUnitCode(Constants.UNIT_CODE);
        if (this.baseMapper.deleteBatchIds(uids) == 1) {
            return true;
        }
        return false;

    }
}
