package com.qs.admin.taxhall.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.qs.admin.taxhall.model.Employee;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-03
 */
public interface EmployeeService extends IService<Employee> {


     boolean deleteEmpolyeeByIds(List<String> uids);
}
