package com.qs.admin.taxhall.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.qs.admin.taxhall.model.Employee;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-03
 */
public interface EmployeeService extends IService<Employee> {

    /**
     * 根据用户名和密码进行登录验证
     * @param username 用户名
     * @param password 密码
     * @return 员工信息，如果验证失败返回null
     */
    Employee login(String username, String password);

    boolean deleteEmpolyeeByIds(List<String> uids);
}
