package com.qs.admin.taxhall.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.qs.admin.taxhall.model.RelatedEnterpriseRequest;
import com.qs.admin.taxhall.model.BaiShuiApiResponse;
import com.qs.admin.taxhall.service.BaiShuiApiService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.http.*;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

import com.qs.admin.taxhall.config.BaiShuiProperties;

/**
 * 佰税科技API服务实现类
 * 实现与佰税科技第三方接口的交互
 */
@Service
public class BaiShuiApiServiceImpl implements BaiShuiApiService {
    private static final Logger logger = LoggerFactory.getLogger(BaiShuiApiServiceImpl.class);

    /**
     * 业务ID: 查询关联企业
     */
    private static final String BUSINESS_ID_RELATED_ENTERPRISE = "nsfw.nsrGlqyCx2";

    /**
     * 成功状态码
     */
    private static final String SUCCESS_CODE = "00";

    @Autowired
    private BaiShuiProperties baiShuiProperties;

    @Resource
    private RestTemplate restTemplate;

    /**
     * 缓存的访问令牌
     */
    private String cachedToken;

    /**
     * 令牌过期时间（毫秒）
     */
    private long tokenExpireTime = 0;

    @Override
    public String getToken() {
        long currentTime = System.currentTimeMillis();
        if (cachedToken != null && currentTime < tokenExpireTime) {
            return cachedToken;
        }
        logger.info("获取佰税API访问令牌");
        Map<String, String> params = new HashMap<>();
        params.put("appKey", baiShuiProperties.getAppKey());
        params.put("appSecret", baiShuiProperties.getAppSecret());
        MultiValueMap<String, String> formParams = new LinkedMultiValueMap<>();
        formParams.setAll(params);
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<>(formParams, headers);
        logger.info("tokenUrl={}", baiShuiProperties.getTokenUrl());
        try {
            Map<String, Object> resp = restTemplate.postForObject(baiShuiProperties.getTokenUrl(), request, Map.class);
            if (resp != null && SUCCESS_CODE.equals(resp.get("code"))) {
                cachedToken = (String) resp.get("token");
                tokenExpireTime = currentTime + 3600000;
                logger.info("成功获取佰税API访问令牌");
                return cachedToken;
            }
            String errorMsg = resp != null ? (String) resp.get("mess") : "未知错误";
            logger.error("获取佰税API访问令牌失败: {}", errorMsg);
            throw new RuntimeException("授权失败: " + errorMsg);
        } catch (Exception e) {
            logger.error("获取佰税API访问令牌异常", e);
            throw new RuntimeException("授权请求异常: " + e.getMessage(), e);
        }
    }

    @Override
    public BaiShuiApiResponse getRelatedEnterprise(RelatedEnterpriseRequest req) {
        logger.info("查询关联企业信息: {}", req);
        String token = getToken();
        Map<String, String> params = new HashMap<>();
        params.put("ywId", BUSINESS_ID_RELATED_ENTERPRISE);
        params.put("appKey", baiShuiProperties.getAppKey());
        params.put("token", token);
        try {
            ObjectMapper mapper = new ObjectMapper();
            params.put("requestBody", mapper.writeValueAsString(req));
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
            HttpEntity<Map<String, String>> request = new HttpEntity<>(params, headers);
            ResponseEntity<BaiShuiApiResponse> resp = restTemplate.postForEntity(baiShuiProperties.getApiUrl(), request, BaiShuiApiResponse.class);
            BaiShuiApiResponse response = resp.getBody();
            if (response != null) {
                if (SUCCESS_CODE.equals(response.getCode())) {
                    logger.info("成功查询到关联企业信息，数量: {}",
                            response.getDataList() != null ? response.getDataList().size() : 0);
                } else {
                    logger.warn("查询关联企业信息失败: {}", response.getMess());
                }
            }
            return response;
        } catch (Exception e) {
            logger.error("查询关联企业信息异常", e);
            throw new RuntimeException("查询关联企业信息异常: " + e.getMessage(), e);
        }
    }

    @Override
    public String getAppKey() {
        return baiShuiProperties.getAppKey();
    }
}
