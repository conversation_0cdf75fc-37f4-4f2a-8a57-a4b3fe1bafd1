package com.qs.admin.taxhall.service;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.qs.admin.taxhall.model.Ticket;
import com.qs.admin.taxhall.model.vo.TicketVO;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-03
 */
public interface TicketService extends IService<Ticket> {
    IPage<Ticket> page(IPage<Ticket> page, Ticket ticket);

    /**
     * 分页查询票据VO（包含关联信息）
     * @param page 分页参数
     * @param ticket 查询条件
     * @return 票据VO分页结果
     */
    IPage<TicketVO> pageVO(Page<TicketVO> page, Ticket ticket);

}
