package com.qs.admin.taxhall.service.impl;


import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.qs.admin.common.constant.Constants;
import com.qs.admin.common.core.Query;
//import com.qs.admin.common.datasource.DBIdentifier;
import com.qs.admin.common.utils.DateUtil;
import com.qs.admin.taxhall.mapper.TicketLogMapper;
import com.qs.admin.taxhall.model.TicketLog;
import com.qs.admin.taxhall.service.TicketLogService;
import com.qs.admin.taxhall.service.WindowService;
import com.qs.admin.taxhall.model.Window;
import org.springframework.beans.factory.annotation.Autowired;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-03
 */
@Slf4j
@Service

public class TicketLogServiceImpl extends ServiceImpl<TicketLogMapper, TicketLog> implements TicketLogService {

    @Autowired
    private WindowService windowService;

    @Override
    public IPage<TicketLog> page(IPage<TicketLog> page, TicketLog ticketLog) {
//        //DBIdentifier.setUnitCode(Constants.UNIT_CODE);
        QueryWrapper<TicketLog> queryWrapper = new QueryWrapper<>();

        // 检查ticketLog是否为null
        if (ticketLog != null) {
            //票号查询
            queryWrapper.eq(StringUtils.isNotBlank(ticketLog.getTktId()),"TKT_ID", ticketLog.getTktId());
            //窗口查询
            queryWrapper.eq(StringUtils.isNotBlank(ticketLog.getWinId()),"WIN_ID", ticketLog.getWinId());
            //业务查询
            queryWrapper.eq(null != ticketLog.getBizUid(),"BIZ_UID", ticketLog.getBizUid());
            //员工查询
            queryWrapper.eq(null != ticketLog.getEmpUid(),"EMP_UID", ticketLog.getEmpUid());
            //状态查询
            queryWrapper.eq(null != ticketLog.getStatus(),"STATUS", ticketLog.getStatus());
            //维护人查询
            if(null!=ticketLog.getRank()){
                if(ticketLog.getRank()>20&&ticketLog.getRank()<30){
                    queryWrapper.ge("RANK", 2200)
                            .le("RANK",2210);
                } else if (ticketLog.getRank()>30) {
                    queryWrapper.ge("RANK", 3100)
                            .le("RANK",3115);
                }else{
                    queryWrapper.eq(null != ticketLog.getRank(),"RANK", ticketLog.getRank());
                }
            }

            // 修复空指针异常：检查startDateTime和endDateTime是否为null
            if (ticketLog.getStartDateTime() != null && ticketLog.getEndDateTime() != null &&
                    !ticketLog.getStartDateTime().equals(ticketLog.getEndDateTime())) {
                if (StringUtils.isNotBlank(ticketLog.getStartDateTime()) && StringUtils.isNotBlank(ticketLog.getEndDateTime())) {
                    String startDate = DateUtil.format(DateUtil.stringToDate(ticketLog.getStartDateTime(), DateUtil.DATE_TIME_PATTERN), DateUtil.DATE_PATTERN);
                    String endDate = DateUtil.format(DateUtil.stringToDate(ticketLog.getEndDateTime(), DateUtil.DATE_TIME_PATTERN), DateUtil.DATE_PATTERN);
                    
                    // 添加调试日志
                    log.info("时间查询条件 - 原始开始时间: {}, 原始结束时间: {}", ticketLog.getStartDateTime(), ticketLog.getEndDateTime());
                    log.info("时间查询条件 - 转换后开始日期: {}, 转换后结束日期: {}", startDate, endDate);
                    
                    //时段查询
                    queryWrapper
                            .ge("TKT_DATE", startDate)
                            .le("TKT_DATE", endDate);
                }
            }
            
            // 添加调试日志：打印其他查询条件
            log.info("查询条件 - TKT_ID: {}, WIN_ID: {}, BIZ_UID: {}, EMP_UID: {}, STATUS: {}, RANK: {}", 
                    ticketLog.getTktId(), ticketLog.getWinId(), ticketLog.getBizUid(), 
                    ticketLog.getEmpUid(), ticketLog.getStatus(), ticketLog.getRank());
        }
        
        // 添加调试日志：打印最终的SQL查询条件
        log.info("最终查询条件: {}", queryWrapper.getTargetSql());
        log.info("查询参数: {}", queryWrapper.getParamNameValuePairs());

        // 先查询总数，避免大数据量查询
        long totalCount = this.baseMapper.selectCount(queryWrapper);
        log.info("查询总记录数: {}", totalCount);
        
        // 如果数据量过大，限制查询范围
        if (totalCount > 10000) {
            log.warn("查询结果数据量过大 ({}条)，建议缩小查询范围", totalCount);
            // 可以考虑限制最大页数或提示用户缩小查询条件
            if (page.getCurrent() * page.getSize() > 10000) {
                log.warn("查询页数过大，限制在前10000条记录内");
                page.setCurrent(1);
                page.setSize(Math.min(page.getSize(), 100)); // 限制每页最大100条
            }
        }
        
        IPage<TicketLog> ticketLogIPage = this.baseMapper.selectPage(page, queryWrapper);
        
        // 添加调试日志：打印查询结果统计
        log.info("查询结果 - 总记录数: {}, 当前页记录数: {}", 
                ticketLogIPage.getTotal(), 
                ticketLogIPage.getRecords() != null ? ticketLogIPage.getRecords().size() : 0);
        if (null != ticketLogIPage.getRecords()) {
            List<TicketLog> ticketLogList = ticketLogIPage.getRecords();
            
            // 批量处理，避免逐条处理导致的性能问题
            log.info("开始处理 {} 条记录", ticketLogList.size());
            
            // 批量获取窗口信息，减少数据库查询次数
            Set<String> winIds = ticketLogList.stream()
                    .map(TicketLog::getWinId)
                    .filter(Objects::nonNull)
                    .filter(winId -> !winId.trim().isEmpty())
                    .collect(Collectors.toSet());
            
            Map<String, String> winNameMap = new HashMap<>();
            if (!winIds.isEmpty()) {
                try {
                    // 将String类型的winIds转换为Integer类型
                    Set<Integer> integerWinIds = winIds.stream()
                            .filter(winId -> winId.matches("\\d+")) // 只保留数字字符串
                            .map(Integer::parseInt)
                            .collect(Collectors.toSet());
                    
                    List<Window> windows = windowService.listByIds(integerWinIds);
                    winNameMap = windows.stream()
                            .filter(w -> w.getName() != null)
                            .collect(Collectors.toMap(
                                    w -> w.getUid().toString(), // Window的uid是Integer，转为String与TicketLog的winId匹配
                                    Window::getName,
                                    (existing, replacement) -> existing
                            ));
                } catch (Exception e) {
                    log.warn("批量获取窗口信息失败: {}", e.getMessage());
                }
            }
            
            // 处理每条记录
            for (TicketLog ticketLog1 : ticketLogList) {
                try {
                    // 安全调用setTktDateTime
                    ticketLog1.setTktDateTime();
                } catch (Exception e) {
                    log.warn("设置票据日志日期时间失败: {}", e.getMessage());
                }
                
                // 设置窗口名称（使用批量获取的数据）
                if (ticketLog1.getWinId() != null) {
                    String winName = winNameMap.get(ticketLog1.getWinId());
                    if (winName != null) {
                        ticketLog1.setWinName(winName);
                    } else {
                        ticketLog1.setWinName(ticketLog1.getWinId() + "号窗口");
                    }
                }
            }
            
            log.info("记录处理完成");
            ticketLogIPage.setRecords(ticketLogList);
            return ticketLogIPage;
        }
        return ticketLogIPage;
    }

}
