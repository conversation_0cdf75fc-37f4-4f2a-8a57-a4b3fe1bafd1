package com.qs.admin.taxhall.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.qs.admin.common.constant.Constants;
import com.qs.admin.common.core.Query;
//import com.qs.admin.common.datasource.DBIdentifier;
import com.qs.admin.common.utils.DateUtil;
import com.qs.admin.taxhall.mapper.TicketLogMapper;
import com.qs.admin.taxhall.model.TicketLog;
import com.qs.admin.taxhall.model.TicketLog;
import com.qs.admin.taxhall.service.TicketLogService;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-03
 */
@Slf4j
@Service
@DS("taxhall")
public class TicketLogServiceImpl extends ServiceImpl<TicketLogMapper, TicketLog> implements TicketLogService {

    @Override
    public IPage<TicketLog> page(IPage<TicketLog> page, TicketLog ticketLog) {
//        //DBIdentifier.setUnitCode(Constants.UNIT_CODE);
        QueryWrapper<TicketLog> queryWrapper = new QueryWrapper<>();

        // 检查ticketLog是否为null
        if (ticketLog != null) {
            //票号查询
            queryWrapper.eq(StringUtils.isNotBlank(ticketLog.getTktId()),"tktId", ticketLog.getTktId());
            //窗口查询
            queryWrapper.in(null != ticketLog.getWinId(),"win_id", ticketLog.getWinId());
            //业务查询
            queryWrapper.eq(null != ticketLog.getBizUid(),"biz_uid", ticketLog.getBizUid());
            //工作人员查询
            queryWrapper.eq(null != ticketLog.getEmpUid(),"emp_uid", ticketLog.getEmpUid());
            //状态查询
            queryWrapper.eq(null != ticketLog.getStatus(),"status", ticketLog.getStatus());
            //维护人查询
            if(null!=ticketLog.getRank()){
                if(ticketLog.getRank()>20&&ticketLog.getRank()<30){
                    queryWrapper.ge("rank", 2200)
                            .le("rank",2210);
                } else if (ticketLog.getRank()>30) {
                    queryWrapper.ge("rank", 3100)
                            .le("rank",3115);
                }else{
                    queryWrapper.eq(null != ticketLog.getRank(),"rank", ticketLog.getRank());
                }
            }

            if (StringUtils.isNotBlank(ticketLog.getStartDateTime()) && StringUtils.isNotBlank(ticketLog.getEndDateTime())) {
                String endTime = DateUtil.format(
                        DateUtil.addDateDays(DateUtil.stringToDate(ticketLog.getEndDateTime(), DateUtil.DATE_TIME_PATTERN), 1),
                        DateUtil.DATE_TIME_PATTERN);
                //报障时段查询
                queryWrapper
                        .ge("startTime", ticketLog.getStartDateTime())
                        .lt("endTime", endTime);
            }
        }

        IPage<TicketLog> ticketLogIPage = this.baseMapper.selectPage(page, queryWrapper);
        if (null != ticketLogIPage.getRecords()) {
            List<TicketLog> ticketLogList = ticketLogIPage.getRecords();
            for (TicketLog ticketLog1 : ticketLogList) {
                // 安全调用setTktDateTime
                try {
                    ticketLog1.setTktDateTime();
                } catch (Exception e) {
                    log.warn("设置票据日志日期时间失败: {}", e.getMessage());
                }
//                log.info(ticketLog1.toString());
            }
            ticketLogIPage.setRecords(ticketLogList);
            return ticketLogIPage;
        }
        return ticketLogIPage;
    }

}
