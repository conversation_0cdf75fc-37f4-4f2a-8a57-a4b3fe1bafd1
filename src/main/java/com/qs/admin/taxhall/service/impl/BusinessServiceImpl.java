package com.qs.admin.taxhall.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.qs.admin.common.constant.Constants;
//import com.qs.admin.common.datasource.DBIdentifier;
import com.qs.admin.taxhall.mapper.BusinessMapper;
import com.qs.admin.taxhall.model.Business;
import com.qs.admin.taxhall.model.WindowBusiness;
import com.qs.admin.taxhall.service.BusinessService;
import com.qs.admin.taxhall.service.WindowBusinessService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-03
 */
@Service
//@DS("taxhall")
public class BusinessServiceImpl extends ServiceImpl<BusinessMapper, Business> implements BusinessService {
    @Autowired
    private WindowBusinessService wbService;


    public boolean delByIds(List<String> ids) {
//        //DBIdentifier.setUnitCode(Constants.UNIT_CODE);
        if (this.removeByIds(ids)) {
            for (String str : ids) {
                wbService.removeByKey("BIZ_UID", str);
            }
            return true;
        }
        return false;
    }
}
