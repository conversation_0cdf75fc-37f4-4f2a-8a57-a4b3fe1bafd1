package com.qs.admin.taxhall.service;

import com.qs.admin.taxhall.model.Employee;

/**
 * 用户会话管理服务接口
 */
public interface UserSessionService {

    /**
     * 创建用户会话
     *
     * @param employee 员工信息
     * @return JWT token
     */
    String createSession(Employee employee);

    /**
     * 验证会话是否有效
     *
     * @param token JWT token
     * @return 是否有效
     */
    boolean validateSession(String token);

    /**
     * 获取当前会话用户信息
     *
     * @param token JWT token
     * @return 员工信息
     */
    Employee getCurrentUser(String token);

    /**
     * 销毁会话
     *
     * @param token JWT token
     */
    void destroySession(String token);

    /**
     * 刷新会话
     *
     * @param token 旧的JWT token
     * @return 新的JWT token
     */
    String refreshSession(String token);
}
