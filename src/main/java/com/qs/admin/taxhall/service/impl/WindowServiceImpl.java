package com.qs.admin.taxhall.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.qs.admin.common.constant.Constants;
import com.qs.admin.common.domain.R;
//import com.qs.admin.common.datasource.DBIdentifier;
import com.qs.admin.taxhall.mapper.WindowMapper;
import com.qs.admin.taxhall.mapper.vo.WindowBusinessVoMapper;
import com.qs.admin.taxhall.model.Window;
import com.qs.admin.taxhall.model.WindowBusiness;
import com.qs.admin.taxhall.model.vo.WindowBusinessVO;
import com.qs.admin.taxhall.service.WindowBusinessService;
import com.qs.admin.taxhall.service.WindowService;
//import com.qs.admin.taxhall.model.vo.WindowVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;



import java.util.List;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-03
 */
@Slf4j
@Service
@DS("taxhall")
public class WindowServiceImpl extends ServiceImpl<WindowMapper, Window> implements WindowService {

    @Autowired WindowMapper windowMapper;

    @Autowired
    private WindowBusinessVoMapper wbvoMapper;
    @Autowired
    private WindowBusinessService wbService;


    @Override
    public List<Window> findAll() {
        QueryWrapper<Window> queryWrapper = new QueryWrapper<>();
        List<Window> windowList = this.baseMapper.selectList(queryWrapper);
        for (Window window : windowList) {
            findByWin(window);
        }
        return windowList;
    }

    @Override
    public R<?> page(Integer pageNum, Integer pageSize) {
        Page<Window> windowPage = new Page<>(pageNum, pageSize);
        QueryWrapper<Window> wrapper = new QueryWrapper<>();
        IPage<Window> windowIPage = this.page(windowPage, wrapper);

        // 为每个窗口加载业务数据
        for (Window window : windowIPage.getRecords()) {
            findByWin(window);
        }

        return R.ok(windowIPage);
    }

    /**
     * 添加window 及window相关的windowsBusiness
     *
     * @param window
     * @return
     */
    @Override
    public boolean insert(Window window) throws Exception {
        List<WindowBusinessVO> wbvoList = window.getBizList();
        int b = windowMapper.insert(window);
        try {
            if (b == 1) {
                Integer winId = window.getUid();
                for (WindowBusinessVO wbvo : wbvoList) {
                    if (wbvo.getEnabled() == true) {
                        WindowBusiness wb = new WindowBusiness();
//                        log.info(wbvo.getUid().toString());
                        wb.setBizUid(wbvo.getUid());
                        wb.setWinUid(winId);
                        wb.setPriority(wbvo.getPriority());
                        wbService.save(wb);
                    }
                }
            }
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    @Override
    public boolean updateByUid(Window window) {
        try {
            List<WindowBusinessVO> wbvoList = window.getBizList();
            boolean b = updateById(window);
            if (b == true) {
                Integer winId = window.getUid();
                boolean sd = wbService.removeByKey("WIN_UID", winId.toString());
                if (sd == true) {
                    for (WindowBusinessVO wbvo : wbvoList) {
                        if (wbvo.getEnabled() == true) {
                            WindowBusiness wb = new WindowBusiness();
//                            log.info(wbvo.getUid().toString());
                            wb.setBizUid(wbvo.getUid());
                            wb.setWinUid(winId);
                            wb.setPriority(wbvo.getPriority());
                            wbService.save(wb);
                        }
                    }
                }
            }
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    @Override
    public boolean delByIds(List<String> ids) {
        if (this.removeByIds(ids)) {
            for (String str : ids) {
                wbService.removeByKey("WIN_UID", str);
            }
            return true;
        }
        return false;
    }

    private void findByWin(Window window) {
        List<WindowBusinessVO> bizList = this.wbvoMapper.findBizList(window.getUid().toString());
        window.setBizList(bizList);

    }
}
