package com.qs.admin.taxhall.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.qs.admin.common.constant.Constants;
import com.qs.admin.common.domain.R;
//import com.qs.admin.common.datasource.DBIdentifier;
import com.qs.admin.taxhall.mapper.WindowMapper;
import com.qs.admin.taxhall.mapper.vo.WindowBusinessVoMapper;
import com.qs.admin.taxhall.model.Window;
import com.qs.admin.taxhall.model.WindowBusiness;
import com.qs.admin.taxhall.model.dto.WindowDTO;
import com.qs.admin.taxhall.model.dto.WindowBusinessDTO;
import com.qs.admin.taxhall.model.vo.WindowBusinessVO;
import com.qs.admin.taxhall.service.WindowBusinessService;
import com.qs.admin.taxhall.service.WindowService;
//import com.qs.admin.taxhall.model.vo.WindowVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-03
 */
@Slf4j
@Service
public class WindowServiceImpl extends ServiceImpl<WindowMapper, Window> implements WindowService {

    @Autowired WindowMapper windowMapper;

    @Autowired
    private WindowBusinessVoMapper wbvoMapper;
    @Autowired
    private WindowBusinessService wbService;


    @Override
    public List<Window> findAll() {
        QueryWrapper<Window> queryWrapper = new QueryWrapper<>();
        List<Window> windowList = this.baseMapper.selectList(queryWrapper);
        for (Window window : windowList) {
            findByWin(window);
        }
        return windowList;
    }

    @Override
    public R<?> page(Integer pageNum, Integer pageSize) {
        try {
            // 分别处理count查询和数据查询，避免ORDER BY冲突

            // 1. 先查询总数（不带ORDER BY）
            QueryWrapper<Window> countWrapper = new QueryWrapper<>();
            long total = this.count(countWrapper);

            // 2. 再查询分页数据（带ORDER BY和分页）
            QueryWrapper<Window> dataWrapper = new QueryWrapper<>();
            dataWrapper.orderByDesc("UID");

            // 计算分页参数
            long offset = (pageNum - 1) * pageSize;
            dataWrapper.last("OFFSET " + offset + " ROWS FETCH NEXT " + pageSize + " ROWS ONLY");

            // 查询数据
            List<Window> records = this.list(dataWrapper);

            // 为每个窗口加载业务数据
            for (Window window : records) {
                findByWin(window);
            }

            // 手动构建分页结果
            Page<Window> page = new Page<>(pageNum, pageSize);
            page.setRecords(records);
            page.setTotal(total);
            page.setPages((total + pageSize - 1) / pageSize);

            return R.ok(page);
        } catch (Exception e) {
            log.error("分页查询窗口失败: {}", e.getMessage(), e);
            return R.fail("查询失败: " + e.getMessage());
        }
    }

    /**
     * 添加window 及window相关的windowsBusiness
     *
     * @param window
     * @return
     */
    @Override
    public boolean insert(Window window) throws Exception {
        List<WindowBusinessVO> wbvoList = window.getBizList();
        int b = windowMapper.insert(window);
        try {
            if (b == 1) {
                Integer winId = window.getUid();
                for (WindowBusinessVO wbvo : wbvoList) {
                    if (wbvo.getEnabled() == true) {
                        WindowBusiness wb = new WindowBusiness();
//                        log.info(wbvo.getUid().toString());
                        wb.setBizUid(wbvo.getUid());
                        wb.setWinUid(winId);
                        wb.setPriority(wbvo.getPriority());
                        wbService.save(wb);
                    }
                }
            }
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    @Override
    public boolean updateByUid(Window window) {
        try {
            List<WindowBusinessVO> wbvoList = window.getBizList();
            boolean b = updateById(window);
            if (b == true) {
                Integer winId = window.getUid();
                
                // 判断是否需要对WindowBusiness进行更新
                if (wbvoList != null) {
                    // 先查询是否存在相关的WindowBusiness记录
                    QueryWrapper<WindowBusiness> queryWrapper = new QueryWrapper<>();
                    queryWrapper.eq("WIN_UID", winId);
                    List<WindowBusiness> existingRecords = wbService.list(queryWrapper);
                    
                    // 如果存在相关数据则先删除
                    if (existingRecords != null && !existingRecords.isEmpty()) {
                        wbService.removeByKey("WIN_UID", winId.toString());
                    }
                    
                    // 添加新的窗口业务关联记录
                    if (!wbvoList.isEmpty()) {
                        for (WindowBusinessVO wbvo : wbvoList) {
                            // 根据需求修改：添加所有业务项，不再只添加enabled为true的项
                            WindowBusiness wb = new WindowBusiness();
                            wb.setBizUid(wbvo.getUid());
                            wb.setWinUid(winId);
                            wb.setPriority(wbvo.getPriority());
                            wbService.save(wb);
                        }
                    }
                }
                // 如果wbvoList为null，则不对WindowBusiness进行任何处理
            }
            return true;
        } catch (Exception e) {
            log.error("更新窗口信息失败", e);
            return false;
        }
    }

    @Override
    public boolean delByIds(List<String> ids) {
        if (this.removeByIds(ids)) {
            for (String str : ids) {
                wbService.removeByKey("WIN_UID", str);
            }
            return true;
        }
        return false;
    }

    @Override
    public List<WindowBusinessVO> findByWin(Integer winUid) {
        return this.wbvoMapper.findBizList(winUid.toString());
    }

    private void findByWin(Window window) {
        List<WindowBusinessVO> bizList = findByWin(window.getUid());
        window.setBizList(bizList);
    }

    @Override
    public R<?> createWindowWithBusinesses(WindowDTO windowDTO, HttpServletRequest request) {
        log.info("接收到添加窗口请求");

        // 验证必要参数
        if (windowDTO == null) {
            return R.fail("请求参数不能为空");
        }
        if (windowDTO.getName() == null || windowDTO.getName().trim().isEmpty()) {
            return R.fail("窗口名称不能为空");
        }

        // 解析业务列表
        String[] bizListArray = request.getParameterValues("bizListStr");
        if (bizListArray != null && bizListArray.length > 0) {
            List<WindowBusinessDTO> bizList = parseBizListFromRequest(bizListArray);
            windowDTO.setBizList(bizList);
        }

        log.info("处理后的WindowDTO: {}", windowDTO);
        
        // 创建Window对象
        Window window = new Window();
        window.setName(windowDTO.getName());
        window.setLedAddress(windowDTO.getLedAddress());
        window.setLedText(windowDTO.getLedText());
        window.setEnabled(windowDTO.getEnabled());
        window.setSid(windowDTO.getSid());
        window.setRankMode(windowDTO.getRankMode());
        
        try {
            // 先保存Window获取自增的uid
            boolean result = this.save(window);
            if (!result) {
                return R.fail("添加窗口失败");
            }
            
            // 处理bizList，保存到WindowBusiness表
            if (windowDTO.getBizList() != null && !windowDTO.getBizList().isEmpty()) {
                for (WindowBusinessDTO bizDTO : windowDTO.getBizList()) {
                    // 只处理enabled=true的业务
                    if (bizDTO.getEnabled() != null && bizDTO.getEnabled()) {
                        Integer bUid = bizDTO.getUid();
                        
                        if (bUid != null) {
                            // 创建WindowBusiness记录
                            WindowBusiness windowBusiness = new WindowBusiness();
                            windowBusiness.setWinUid(window.getUid());
                            windowBusiness.setBizUid(bizDTO.getUid());
                            wbService.save(windowBusiness);
                            log.info("保存WindowBusiness记录: winUid={}, bizUid={}, priority={}", 
                                    window.getUid(), bUid, bizDTO.getPriority());
                        } else {
                            log.warn("未找到名称为 '{}' 且启用状态的业务", bizDTO.getName());
                        }
                    } else {
                        log.debug("跳过未启用的业务: {}", bizDTO.getName());
                    }
                }
            }
            
            return R.ok("添加成功");
        } catch (Exception e) {
            log.error("添加窗口失败: {}", e.getMessage());
            return R.fail("添加失败: " + e.getMessage());
        }
    }

    @Override
    public R<?> batchDeleteWindows(String idsParam) {
        if (idsParam == null || idsParam.trim().isEmpty()) {
            return R.fail("ID列表不能为空");
        }
        
        // 解析逗号分隔的ID字符串
        String[] idArray = idsParam.split(",");
        List<String> ids = new ArrayList<>();
        for (String id : idArray) {
            if (id != null && !id.trim().isEmpty()) {
                ids.add(id.trim());
            }
        }
        
        if (ids.isEmpty()) {
            return R.fail("没有有效的ID");
        }
        
        boolean b = delByIds(ids);
        
        if (b) {
            Map<String, Object> result = new HashMap<>();
            result.put("deletedCount", ids.size());
            result.put("deletedIds", ids);
            return R.ok(result);
        } else {
            return R.fail("删除失败");
        }
    }

    @Override
    public R<?> getWindowOptions() {
        QueryWrapper<Window> wrapper = new QueryWrapper<>();
        wrapper.select("uid", "name"); // 只查询ID和名称
        List<Window> windowList = this.list(wrapper);

        // 转换为键值对格式
        List<Object> options = windowList.stream().map(window -> {
            return new Object() {
                public final Integer value = window.getUid();
                public final String label = window.getName();
                public final String text = window.getName(); // 兼容不同前端框架
            };
        }).collect(Collectors.toList());

        return R.ok(options);
    }

    @Override
    public List<WindowBusinessDTO> parseBizListFromRequest(String[] bizListArray) {
        ObjectMapper objectMapper = new ObjectMapper();
        List<WindowBusinessDTO> bizList = new ArrayList<>();
        
        for (String bizJson : bizListArray) {
            try {
                // 处理转义字符问题
                String cleanedJson = bizJson.replace("\\\"", "\"")
                        .replace("\\n", "\n")
                        .replace("\\r", "\r")
                        .replace("\\t", "\t");
                
                // 检查是否为JSON数组格式
                if (cleanedJson.trim().startsWith("[")) {
                    // 处理JSON数组格式
                    WindowBusinessDTO[] bizDTOArray = objectMapper.readValue(cleanedJson, WindowBusinessDTO[].class);
                    for (WindowBusinessDTO dto : bizDTOArray) {
                        bizList.add(dto);
                    }
                } else {
                    // 处理单个对象格式
                    WindowBusinessDTO bizDTO = objectMapper.readValue(cleanedJson, WindowBusinessDTO.class);
                    bizList.add(bizDTO);
                }
            } catch (Exception e) {
                log.error("解析业务数据失败: {}", e.getMessage());
            }
        }
        
        return bizList;
    }

    @Override
    public R<?> updateWindowWithBusinesses(WindowDTO windowDTO) {
        try {
            if (windowDTO == null || windowDTO.getUid() == null) {
                return R.fail("窗口ID不能为空");
            }

            // 1. 更新窗口基本信息
            Window window = new Window();
            org.springframework.beans.BeanUtils.copyProperties(windowDTO, window);
            boolean updateResult = updateById(window);

            if (!updateResult) {
                return R.fail("更新窗口基本信息失败");
            }

            // 2. 处理窗口业务关联
            if (windowDTO.getBizList() != null) {
                Integer winId = windowDTO.getUid();

                // 先删除现有的关联关系
                QueryWrapper<WindowBusiness> deleteWrapper = new QueryWrapper<>();
                deleteWrapper.eq("WIN_UID", winId);
                wbService.remove(deleteWrapper);

                // 添加新的关联关系
                for (WindowBusinessDTO bizDTO : windowDTO.getBizList()) {
                    if (bizDTO.getEnabled() != null && bizDTO.getEnabled()) {
                        WindowBusiness windowBusiness = new WindowBusiness();
                        windowBusiness.setWinUid(winId);
                        windowBusiness.setBizUid(bizDTO.getUid());
                        windowBusiness.setPriority(bizDTO.getPriority());
                        wbService.save(windowBusiness);
                        log.info("更新WindowBusiness记录: winUid={}, bizUid={}, priority={}",
                                winId, bizDTO.getUid(), bizDTO.getPriority());
                    }
                }
                log.info("窗口业务关联更新成功，窗口ID: {}", winId);
            }

            return R.ok("更新成功");
        } catch (Exception e) {
            log.error("更新窗口失败: {}", e.getMessage(), e);
            return R.fail("更新失败: " + e.getMessage());
        }
    }
}
