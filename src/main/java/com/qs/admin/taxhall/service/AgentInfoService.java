package com.qs.admin.taxhall.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.qs.admin.taxhall.model.AgentInfo;
import com.qs.admin.taxhall.model.dto.AgentInfoSaveRequest;
import com.qs.admin.taxhall.model.vo.AgentTodayEnterpriseVO;
import java.util.List;
import java.util.Map;

/**
 * 代理机构人员信息服务接口
 */
public interface AgentInfoService extends IService<AgentInfo> {

    /**
     * 保存代理机构人员信息
     *
     * @param agentInfo 代理机构人员信息
     * @return 保存结果
     */
    boolean saveAgentInfo(AgentInfo agentInfo);

    /**
     * 批量保存代理机构人员信息及企业明细
     * @param agentInfo 代理人取号主表
     * @param enterpriseList 企业明细集合
     * @return 保存结果
     */
    boolean saveAgentInfoWithEnterprises(AgentInfo agentInfo, List<com.qs.admin.taxhall.model.AgentInfoEnterprise> enterpriseList);

    /**
     * 批量保存代理机构人员信息及企业明细（参数为请求对象，业务组装全部在service层完成）
     * @param request 请求对象
     * @return 保存结果
     */
    boolean saveAgentInfoWithEnterprises(AgentInfoSaveRequest request);

    /**
     * 获取当日代理机构或代理人来办理业务所服务的企业信息
     * @param ticketId 票据唯一标识
     * @return 当日代理人服务企业信息列表
     */
    List<AgentTodayEnterpriseVO> getTodayAgentEnterpriseList(String ticketId);

    /**
     * 将请求参数转换为 AgentInfoSaveRequest 对象
     * @param params 请求参数
     * @return AgentInfoSaveRequest 对象
     */
    AgentInfoSaveRequest convertParamsToRequest(Map<String, Object> params) throws Exception;

    /**
     * 根据票号查询相关信息
     * 从AGENT_INFO、AGENT_INFO_ENTERPRISE、ticket_verify三个表中获取数据
     * @param ticketId 票号
     * @return 票号相关信息
     */
    com.qs.admin.taxhall.model.dto.TicketRelatedInfoDTO getTicketRelatedInfo(String ticketId);

    /**
     * 获取所有票号相关信息
     * 从AGENT_INFO、AGENT_INFO_ENTERPRISE、ticket_verify三个表中获取所有数据
     * @return 所有票号相关信息列表
     */
    List<com.qs.admin.taxhall.model.dto.TicketRelatedInfoDTO> getAllTicketRelatedInfo();
}