package com.qs.admin.taxhall.task;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.qs.admin.taxhall.model.System;
import com.qs.admin.taxhall.service.BaiShuiApiService;
import com.qs.admin.taxhall.service.SystemService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.Date;

@Component
public class BaiShuiTokenSyncTask {
    private static final Logger logger = LoggerFactory.getLogger(BaiShuiTokenSyncTask.class);

    @Resource
    private BaiShuiApiService baiShuiApiService;
    @Resource
    private SystemService systemService;

    @Value("${spring.datasource.driver-class-name:}")
    private String driverClassName;

    @Value("${spring.datasource.url:}")
    private String url;

    @Value("${spring.datasource.jdbc-url:}")
    private String jdbcUrl;

    // 项目启动时自动执行
    @PostConstruct
    public void initSync() {
        try {
            syncAppKeyAndToken();
        } catch (Exception e) {
            logger.error("启动时同步佰税API APPKEY和TOKEN异常（不影响项目运行）", e);
        }
    }

    // 每天早上8点自动执行
    @Scheduled(cron = "0 0 8 * * ?")
    public void syncAt8AM() {
        try {
            syncAppKeyAndToken();
        } catch (Exception e) {
            logger.error("每天8点同步佰税API APPKEY和TOKEN异常（不影响项目运行）", e);
        }
    }

    // 公共同步方法，异常只记录日志
    public void syncAppKeyAndToken() {
        try {
            String latestAppKey = baiShuiApiService.getAppKey();
            String latestToken = baiShuiApiService.getToken();

            // 检查 APPKEY
            QueryWrapper<System> appKeyWrapper = new QueryWrapper<>();
            appKeyWrapper.eq(getKeyFieldName(), "BS_API_APPKEY");
            System dbAppKey = systemService.getOne(appKeyWrapper);
            if (dbAppKey == null || !latestAppKey.equals(dbAppKey.getValue())) {
                System appKeyRecord = new System();
                appKeyRecord.setKey("BS_API_APPKEY");
                appKeyRecord.setValue(latestAppKey);
                appKeyRecord.setMemo("佰税科技API APPKEY，自动同步于" + new Date());
                boolean appKeyResult;
                if (dbAppKey != null) {
                    appKeyRecord.setKey(dbAppKey.getKey());
                    appKeyResult = systemService.updateByKey(appKeyRecord);
                } else {
                    appKeyResult = systemService.save(appKeyRecord);
                }
                if (appKeyResult) {
                    logger.info("APPKEY已同步更新");
                } else {
                    logger.error("APPKEY同步失败，未写入数据库！");
                }
            }

            // 检查 TOKEN
            QueryWrapper<System> tokenWrapper = new QueryWrapper<>();
            tokenWrapper.eq(getKeyFieldName(), "BS_API_TOKEN");
            System dbToken = systemService.getOne(tokenWrapper);
            if (dbToken == null || !latestToken.equals(dbToken.getValue())) {
                System tokenRecord = new System();
                tokenRecord.setKey("BS_API_TOKEN");
                tokenRecord.setValue(latestToken);
                tokenRecord.setMemo("佰税科技API授权令牌，自动同步于" + new Date());
                boolean tokenResult;
                if (dbToken != null) {
                    tokenRecord.setKey(dbToken.getKey());
                    tokenResult = systemService.updateByKey(tokenRecord);
                } else {
                    tokenResult = systemService.save(tokenRecord);
                }
                if (tokenResult) {
                    logger.info("TOKEN已同步更新");
                } else {
                    logger.error("TOKEN同步失败，未写入数据库！");
                }
            }
        } catch (Exception e) {
            logger.error("同步佰税API APPKEY和TOKEN异常（不影响项目运行）", e);
        }
    }

    /**
     * 根据数据库类型获取KEY字段名
     */
    private String getKeyFieldName() {
        if (isSqlServer()) {
            return "[KEY]";  // SQL Server使用方括号
        } else {
            return "`KEY`";  // MySQL/OceanBase使用反引号
        }
    }

    /**
     * 判断是否为SQL Server数据库
     */
    private boolean isSqlServer() {
        return driverClassName.contains("sqlserver") ||
               getEffectiveUrl().contains("sqlserver");
    }

    /**
     * 获取有效的数据库URL
     */
    private String getEffectiveUrl() {
        if (jdbcUrl != null && !jdbcUrl.isEmpty()) {
            return jdbcUrl.toLowerCase();
        }
        return url != null ? url.toLowerCase() : "";
    }
}