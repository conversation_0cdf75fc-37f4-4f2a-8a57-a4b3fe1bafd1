package com.qs.admin.taxhall.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.qs.admin.taxhall.model.Ticket;
import com.qs.admin.taxhall.model.vo.TicketVO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-03
 */
public interface TicketMapper extends BaseMapper<Ticket> {

    /**
     * 分页查询票据信息（包含关联的窗口、业务、员工名称）
     */
    IPage<TicketVO> selectTicketVOPage(Page<TicketVO> page);

    /**
     * 根据条件分页查询票据信息（包含关联的窗口、业务、员工名称）
     */
    IPage<TicketVO> selectTicketVOPageWithCondition(Page<TicketVO> page, @Param("condition") Ticket condition);

}
