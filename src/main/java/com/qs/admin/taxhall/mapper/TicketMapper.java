package com.qs.admin.taxhall.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.qs.admin.taxhall.model.Ticket;
import com.qs.admin.taxhall.model.vo.TicketVO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-03
 */
public interface TicketMapper extends BaseMapper<Ticket> {

    /**
     * 分页查询票据信息（包含关联的窗口、业务、员工名称）
     */
    @Select("SELECT " +
            "t.uid, t.TKT_ID, t.TKT_INTID, t.TKT_DATE, t.TKT_TIME, " +
            "t.WIN_ID, ISNULL(w.name, '窗口' + CAST(t.WIN_ID AS VARCHAR(10))) as windowName, " +
            "t.BIZ_UID, ISNULL(b.name, '业务' + CAST(t.BIZ_UID AS VARCHAR(10))) as businessName, " +
            "t.EMP_UID, ISNULL(e.name, '员工' + CAST(t.EMP_UID AS VARCHAR(10))) as employeeName, " +
            "t.START_TIME, t.END_TIME, t.RANK, t.STATUS, " +
            "t.FIRST_TIME, t.SEC_START_TIME " +
            "FROM ticket t " +
            "LEFT JOIN window w ON t.WIN_ID = w.uid " +
            "LEFT JOIN business b ON t.BIZ_UID = b.uid " +
            "LEFT JOIN employee e ON t.EMP_UID = e.uid " +
            "ORDER BY t.uid DESC")
    IPage<TicketVO> selectTicketVOPage(Page<TicketVO> page);

    /**
     * 根据条件分页查询票据信息（包含关联的窗口、业务、员工名称）
     */
    @Select("<script>" +
            "SELECT " +
            "t.uid, t.TKT_ID, t.TKT_INTID, t.TKT_DATE, t.TKT_TIME, " +
            "t.WIN_ID, ISNULL(w.name, '窗口' + CAST(t.WIN_ID AS VARCHAR(10))) as windowName, " +
            "t.BIZ_UID, ISNULL(b.name, '业务' + CAST(t.BIZ_UID AS VARCHAR(10))) as businessName, " +
            "t.EMP_UID, ISNULL(e.name, '员工' + CAST(t.EMP_UID AS VARCHAR(10))) as employeeName, " +
            "t.START_TIME, t.END_TIME, t.RANK, t.STATUS, " +
            "t.FIRST_TIME, t.SEC_START_TIME " +
            "FROM ticket t " +
            "LEFT JOIN window w ON t.WIN_ID = w.uid " +
            "LEFT JOIN business b ON t.BIZ_UID = b.uid " +
            "LEFT JOIN employee e ON t.EMP_UID = e.uid " +
            "WHERE 1=1 " +
            "<if test='condition.tktId != null and condition.tktId != \"\"'>" +
            "AND t.TKT_ID LIKE CONCAT('%', #{condition.tktId}, '%') " +
            "</if>" +
            "<if test='condition.winId != null'>" +
            "AND t.WIN_ID = #{condition.winId} " +
            "</if>" +
            "<if test='condition.bizUid != null'>" +
            "AND t.BIZ_UID = #{condition.bizUid} " +
            "</if>" +
            "<if test='condition.empUid != null'>" +
            "AND t.EMP_UID = #{condition.empUid} " +
            "</if>" +
            "<if test='condition.status != null'>" +
            "AND t.STATUS = #{condition.status} " +
            "</if>" +
            "ORDER BY t.uid DESC" +
            "</script>")
    IPage<TicketVO> selectTicketVOPageWithCondition(Page<TicketVO> page, @Param("condition") Ticket condition);

}
