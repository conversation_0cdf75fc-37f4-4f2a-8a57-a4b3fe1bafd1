package com.qs.admin.taxhall.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.qs.admin.taxhall.model.AgentInfo;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Options;

/**
 * 代理机构人员信息Mapper接口
 */
@Mapper
public interface AgentInfoMapper extends BaseMapper<AgentInfo> {

    /**
     * 插入代理人信息
     * @param agentInfo 代理人信息
     * @return 影响行数
     */
    @Insert("INSERT INTO [AGENT_INFO] ([AGENT_NAME], [AGENT_ID_CARD], [AGENT_PHONE], [TKT_DATE_TIME], [TKT_UID], [TKT_ID], [AGENT_ORG]) " +
           "VALUES (#{agentName}, #{agentIdCard}, #{agentPhone}, #{tktDateTime}, #{tktUid}, #{tktId}, #{agentOrg})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insertAgentInfo(AgentInfo agentInfo);
}