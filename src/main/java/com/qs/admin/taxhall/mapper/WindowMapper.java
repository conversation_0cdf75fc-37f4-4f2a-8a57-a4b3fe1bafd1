package com.qs.admin.taxhall.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.qs.admin.taxhall.model.Window;
//import com.qs.admin.taxhall.model.vo.WindowVO;
import com.qs.admin.taxhall.model.vo.WindowVO;
import org.apache.ibatis.annotations.Select;

import java.util.Map;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-03
 */
public interface WindowMapper extends BaseMapper<Window> {

}
