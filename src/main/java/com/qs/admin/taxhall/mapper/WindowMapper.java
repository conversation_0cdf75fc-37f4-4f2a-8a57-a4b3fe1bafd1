package com.qs.admin.taxhall.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.qs.admin.taxhall.model.Window;
//import com.qs.admin.taxhall.model.vo.WindowVO;
import com.qs.admin.taxhall.model.vo.WindowVO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-03
 */
public interface WindowMapper extends BaseMapper<Window> {

    /**
     * 自定义分页查询，避免方括号解析问题
     */
    IPage<Window> selectWindowPage(Page<Window> page);

    /**
     * 使用ROW_NUMBER分页查询，兼容SQL Server 2005+
     */
    List<Window> selectWindowsByPage(@Param("startRow") long startRow, @Param("endRow") long endRow);

    /**
     * 查询窗口选项
     */
    List<Map<String, Object>> selectWindowOptions();

}
