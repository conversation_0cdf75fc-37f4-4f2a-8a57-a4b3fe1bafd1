package com.qs.admin.taxhall.mapper.vo;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.qs.admin.taxhall.model.vo.WindowBusinessVO;
import com.qs.admin.taxhall.model.vo.WindowVO;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * @Author: ytx
 * @Date: 2020/4/14  14:51
 */
public interface WindowBusinessVoMapper extends BaseMapper<WindowBusinessVO> {

    @Select("select \n" +
            "B.UID,B.PREFIX,B.NAME,B.TYPE,wb.PRIORITY,\n" +
            "case when wb.win_uid is null then 'false' else 'true' end as ENABLED\n" +
            "from business B\n" +
            "left join window_business WB\n" +
            "on B.UID = WB.BIZ_UID\n" +
            "and WB.WIN_UID = #{winUid}\n"+
            "where B.ENABLED = 1")
    List<WindowBusinessVO> findBizList(String winUid);
}
