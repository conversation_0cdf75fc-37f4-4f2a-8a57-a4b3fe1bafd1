package com.qs.admin.taxhall.controller;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.qs.admin.common.domain.R;
import com.qs.admin.taxhall.model.System;
import com.qs.admin.taxhall.service.SystemService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @data 2020-04-13.
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/system")
@Api(tags = "systems", description = "系统相关的RESTful接口")
public class SystemController {

    @Autowired
    private SystemService systemService;

    @GetMapping("/page")
    @ApiOperation(value = "分页查询系统配置", notes = "分页条件查询系统配置数据", httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageNum", value = "查询页码", paramType = "query", dataType = "Integer", defaultValue = "1"),
            @ApiImplicitParam(name = "pageSize", value = "每页数据量", paramType = "query", dataType = "Integer", defaultValue = "10")
    })
    public R<?> list(@ApiIgnore @RequestParam Map<String, Object> params) {
        QueryWrapper<System> wrapper = new QueryWrapper<>();
        List<System> list = systemService.list(wrapper);
        return R.ok(list);
    }

    @PutMapping
    @ApiOperation(value = "更新数据", notes = "根据内容更新数据", httpMethod = "PUT")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "system", value = "更新的system实例", paramType = "body", dataType = "System", required = true)
    })
    public R<?> update(@RequestBody @Valid System system) {
        boolean b = systemService.updateByKey(system);
        return b ? R.ok() : R.fail();
    }
}