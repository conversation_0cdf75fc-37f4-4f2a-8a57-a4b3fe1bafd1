package com.qs.admin.taxhall.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import com.qs.admin.common.domain.R;
import com.qs.admin.taxhall.model.WindowBusiness;
import com.qs.admin.taxhall.model.dto.PageQueryDTO;
import com.qs.admin.taxhall.model.dto.WindowBusinessDTO;
import com.qs.admin.taxhall.service.WindowBusinessService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;



import javax.validation.Valid;


/**
 * <AUTHOR>
 * @data 2020-04-07.
 */
@RestController
@RequestMapping("/api/v1/window-business")
@Api(tags = "窗口业务管理", description = "窗口业务相关的RESTful接口")
public class WindowBusinessController {
    @Autowired
    private WindowBusinessService windowBusinessService;

    @GetMapping
    @ApiOperation(value = "获取全部",notes = "返回分页过后的数据",httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page",value = "查询页码", paramType = "query",dataType = "Integer",defaultValue = "1"),
            @ApiImplicitParam(name = "pageSize",value = "每页数据量", paramType = "query",dataType = "Integer",defaultValue = "10")
    })

    public R<?> list(@RequestBody @Valid PageQueryDTO pageQueryDTO) {
        Page<WindowBusiness> windowBusinessPage = new Page<>(pageQueryDTO.getPageNum(), pageQueryDTO.getPageSize());
        QueryWrapper<WindowBusiness> wrapper = new QueryWrapper<>();
        IPage<WindowBusiness> windowBusinessIPage = windowBusinessService.page(windowBusinessPage, wrapper);
        return R.ok(windowBusinessIPage);
    }
    @PostMapping
    @ApiOperation(value = "添加数据", notes = "添加新的数据", httpMethod = "POST")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "windowBusinessDTO", value = "待添加的windowBusinessDTO实例", paramType = "body", dataType = "WindowBusinessDTO", required = true)
    })
    public R<?> add (@RequestBody @Valid WindowBusinessDTO windowBusinessDTO){
        WindowBusiness windowBusiness = new WindowBusiness();
        BeanUtils.copyProperties(windowBusinessDTO, windowBusiness);
        boolean b = windowBusinessService.save(windowBusiness);
        return b ? R.ok() : R.fail();
    }

    @DeleteMapping("/{id}")
    @ApiOperation(value = "删除数据", notes = "根据id删除数据", httpMethod = "DELETE")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "查询的id", paramType = "path", required = true, dataType = "Integer"),
    })
    public R<?> delete (@PathVariable Integer id){
        boolean b = windowBusinessService.removeById(id);
        return b ? R.ok() : R.fail();
    }

    @PutMapping
    @ApiOperation(value = "更新数据", notes = "根据内容更新数据", httpMethod = "PUT")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "windowBusinessDTO", value = "更新的windowBusinessDTO实例", paramType = "body", dataType = "WindowBusinessDTO", required = true)
    })
    public R<?> update (@RequestBody @Valid WindowBusinessDTO windowBusinessDTO){
        WindowBusiness windowBusiness = new WindowBusiness();
        BeanUtils.copyProperties(windowBusinessDTO, windowBusiness);
        boolean b = windowBusinessService.updateById(windowBusiness);
        return b ? R.ok() : R.fail();
    }

    @GetMapping("/{id}")
    @ApiOperation(value = "获取单个值", notes = "查看单个项目的内容", httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "查询的id", paramType = "path", required = true, dataType = "Integer", defaultValue = "0")
    })
    public R<?> detail (@PathVariable Integer id){
        WindowBusiness windowBusiness = windowBusinessService.getById(id);
        return R.ok(windowBusiness);
    }
}