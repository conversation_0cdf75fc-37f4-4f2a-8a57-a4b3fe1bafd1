package com.qs.admin.taxhall.controller;

import com.qs.admin.common.domain.R;
import com.qs.admin.taxhall.model.Employee;
import com.qs.admin.taxhall.model.dto.LoginRequestDTO;
import com.qs.admin.taxhall.model.dto.LoginResponseDTO;
import com.qs.admin.taxhall.service.EmployeeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.UUID;

/**
 * 登录控制器
 */
@RestController
@RequestMapping("/api/v1/auth")
@Api(tags = "用户认证", description = "用户登录相关接口")
public class LoginController {

    private static final Logger logger = LoggerFactory.getLogger(LoginController.class);

    @Autowired
    private EmployeeService employeeService;

    /**
     * 用户登录
     * POST /api/v1/auth/login
     */
    @PostMapping("/login")
    @ApiOperation(value = "用户登录", notes = "使用用户名和密码进行登录验证")
    public R<?> login(@RequestBody @Valid LoginRequestDTO loginRequest) {
        try {
            logger.info("用户登录请求: username={}", loginRequest.getUsername());
            
            // 验证用户名和密码
            Employee employee = employeeService.login(loginRequest.getUsername(), loginRequest.getPassword());
            
            if (employee == null) {
                logger.warn("登录失败: 用户名或密码错误, username={}", loginRequest.getUsername());
                return R.fail("用户名或密码错误");
            }
            
            // 检查用户状态
            if (employee.getEnabled() == null || employee.getEnabled() != 1) {
                logger.warn("登录失败: 用户已被禁用, username={}", loginRequest.getUsername());
                return R.fail("用户已被禁用");
            }
            
            // 构建登录响应
            LoginResponseDTO response = new LoginResponseDTO();
            response.setUid(employee.getUid());
            response.setUsername(employee.getUsername());
            response.setName(employee.getName());
            response.setStatus(employee.getStatus());
            response.setEnabled(employee.getEnabled());
            response.setAccess(employee.getAccess());
            
            // 生成简单的登录令牌（实际项目中应该使用JWT或其他安全令牌）
            String token = generateToken(employee);
            response.setToken(token);
            
            logger.info("用户登录成功: username={}, uid={}", employee.getUsername(), employee.getUid());
            return R.ok(response);
            
        } catch (Exception e) {
            logger.error("登录异常", e);
            return R.fail("登录异常: " + e.getMessage());
        }
    }
    
    /**
     * 用户登出
     * POST /api/v1/auth/logout
     */
    @PostMapping("/logout")
    @ApiOperation(value = "用户登出", notes = "用户登出")
    public R<?> logout() {
        try {
            // 这里可以添加登出逻辑，比如清除session、令牌等
            logger.info("用户登出");
            return R.ok("登出成功");
        } catch (Exception e) {
            logger.error("登出异常", e);
            return R.fail("登出异常: " + e.getMessage());
        }
    }
    
    /**
     * 生成登录令牌
     * 注意：这是一个简单的实现，实际项目中应该使用JWT或其他安全令牌机制
     */
    private String generateToken(Employee employee) {
        return "TOKEN_" + employee.getUid() + "_" + UUID.randomUUID().toString().replace("-", "");
    }
}
