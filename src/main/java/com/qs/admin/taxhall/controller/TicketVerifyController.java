package com.qs.admin.taxhall.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.qs.admin.common.core.Query;
import com.qs.admin.common.domain.R;
import com.qs.admin.taxhall.model.TicketVerify;
import com.qs.admin.taxhall.service.TicketVerifyService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.qs.admin.taxhall.model.dto.TicketVerifyDTO;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Map;

/**
* <AUTHOR>
* @data 2020-04-07.
*/
@RestController
@RequestMapping("/api/v1/ticket-verify")
@Api(value = "TicketVerify控制类", description = "控制类接口测试")
public class TicketVerifyController {
@Autowired
private TicketVerifyService ticketVerifyService;

@GetMapping("/page")
@ApiOperation(value = "分页查询票据验证", notes = "分页条件查询票据验证数据", httpMethod = "GET")
@ApiImplicitParams({
@ApiImplicitParam(name = "pageNum", value = "查询页码", paramType = "query", dataType = "Integer", defaultValue = "1"),
@ApiImplicitParam(name = "pageSize", value = "每页数据量", paramType = "query", dataType = "Integer", defaultValue = "10")
})
public R<?> page(@RequestParam(defaultValue = "1") Integer pageNum,
                @RequestParam(defaultValue = "10") Integer pageSize) {
    Page<TicketVerify> ticketVerifyPage = new Page<>(pageNum, pageSize);
    QueryWrapper<TicketVerify> wrapper = new QueryWrapper<>();
    IPage<TicketVerify> ticketVerifyIPage = ticketVerifyService.page(ticketVerifyPage, wrapper);
    return R.ok(ticketVerifyIPage);
}

@PostMapping
@ApiOperation(value = "添加数据", notes = "添加新的数据", httpMethod = "POST")
@ApiImplicitParams({
    @ApiImplicitParam(name = "ticketVerifyDTO", value = "待添加的ticketVerify实例", paramType = "body", dataType = "TicketVerifyDTO", required = true)
})
public R<?> add(@RequestBody @Valid TicketVerifyDTO ticketVerifyDTO) {
    TicketVerify ticketVerify = new TicketVerify();
    org.springframework.beans.BeanUtils.copyProperties(ticketVerifyDTO, ticketVerify);
    boolean b = ticketVerifyService.save(ticketVerify);
    return b ? R.ok() : R.fail();
}

@DeleteMapping("/{id}")
@ApiOperation(value = "删除数据", notes = "根据id删除数据", httpMethod = "DELETE")
@ApiImplicitParams({
    @ApiImplicitParam(name = "id", value = "查询的id", paramType = "path", required = true, dataType = "Integer")
})
public R<?> delete(@PathVariable Integer id) {
    boolean b = ticketVerifyService.removeById(id);
    return b ? R.ok() : R.fail();
}

@PutMapping
@ApiOperation(value = "更新数据", notes = "根据内容更新数据", httpMethod = "PUT")
@ApiImplicitParams({
    @ApiImplicitParam(name = "ticketVerifyDTO", value = "更新的ticketVerify实例", paramType = "body", dataType = "TicketVerifyDTO", required = true)
})
public R<?> update(@RequestBody @Valid TicketVerifyDTO ticketVerifyDTO) {
    TicketVerify ticketVerify = new TicketVerify();
    org.springframework.beans.BeanUtils.copyProperties(ticketVerifyDTO, ticketVerify);
    boolean b = ticketVerifyService.updateById(ticketVerify);
    return b ? R.ok() : R.fail();
}

@GetMapping("/{id}")
@ApiOperation(value = "获取单个值", notes = "查看单个项目的内容", httpMethod = "GET")
@ApiImplicitParams({
    @ApiImplicitParam(name = "id", value = "查询的id", paramType = "path", required = true, dataType = "Integer", defaultValue = "0")
})
public R<?> detail(@PathVariable Integer id) {
    TicketVerify ticketVerify = ticketVerifyService.getById(id);
    return R.ok(ticketVerify);
}
}