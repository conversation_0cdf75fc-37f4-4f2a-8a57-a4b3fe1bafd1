package com.qs.admin.taxhall.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.qs.admin.common.core.Query;
import com.qs.admin.common.domain.R;
import com.qs.admin.taxhall.model.SystemLog;
import com.qs.admin.taxhall.service.SystemLogService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;
import java.util.Map;

/**
 * <AUTHOR>
 * @data 2020-04-07.
 */
@RestController
@RequestMapping("/api/v1/systemLog")
@Api(tags = "system-logs", description = "系统日志相关的RESTful接口")
public class SystemLogController {
    @Resource
    private SystemLogService systemLogService;

    @GetMapping("/page")
    @ApiOperation(value = "分页查询系统日志", notes = "分页条件查询系统日志数据", httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageNum", value = "查询页码", paramType = "query", dataType = "Integer", defaultValue = "1"),
            @ApiImplicitParam(name = "pageSize", value = "每页数据量", paramType = "query", dataType = "Integer", defaultValue = "10")
    })
    public R<Object> page(@RequestParam(defaultValue = "1") Integer pageNum,
                         @RequestParam(defaultValue = "10") Integer pageSize) {
        Page<SystemLog> systemLogPage = new Page<>(pageNum, pageSize);
        QueryWrapper<SystemLog> wrapper = new QueryWrapper<>();
        IPage<SystemLog> systemLogIPage = systemLogService.page(systemLogPage, wrapper);
        return R.ok(systemLogIPage);
    }
    @PostMapping
    @ApiOperation(value = "添加数据",notes = "添加新的数据",httpMethod = "POST")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "systemLog",value = "待添加的systemLog实例",paramType = "body",dataType = "SystemLog",required = true)
    })
    public R<Object> add(@RequestBody SystemLog systemLog) {
        boolean b =  systemLogService.save(systemLog);
        return R.ok(b);
    }

    @DeleteMapping("/{id}")
    @ApiOperation(value = "删除数据",notes = "根据id删除数据",httpMethod = "DELETE")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id",value = "查询的id", paramType = "path",required = true,dataType = "Integer"),
    })
    public R<Object> delete(@PathVariable Integer id) {
        boolean b = systemLogService.removeById(id);
        return R.ok(b);
    }

    @PutMapping
    @ApiOperation(value = "更新数据",notes = "根据内容更新数据",httpMethod = "PUT")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "systemLog",value = "更新的systemLog实例",paramType = "body",dataType = "SystemLog",required = true)
    })
    public R<Object> update(@RequestBody SystemLog systemLog) {
        boolean b = systemLogService.updateById(systemLog);
        return R.ok(b);
    }

    @GetMapping("/{id}")
    @ApiOperation(value = "获取单个值",notes = "查看单个项目的内容",httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id",value = "查询的id", paramType = "path",required = true,dataType = "Integer",defaultValue = "0")
    })
    public R<Object> detail(@PathVariable Integer id) {
        SystemLog systemLog = systemLogService.getById(id);
        return R.ok(systemLog);
    }
}