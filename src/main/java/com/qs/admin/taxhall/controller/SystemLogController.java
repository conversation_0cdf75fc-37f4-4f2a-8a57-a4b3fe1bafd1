package com.qs.admin.taxhall.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.qs.admin.common.core.Query;
import com.qs.admin.common.domain.R;
import com.qs.admin.taxhall.model.SystemLog;
import com.qs.admin.taxhall.service.SystemLogService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;


import javax.annotation.Resource;
import java.util.Map;

/**
 * <AUTHOR>
 * @data 2020-04-07.
 */
@RestController
@RequestMapping("/api/v1/systemLog")
@Tag(name = "system-logs", description = "系统日志相关的RESTful接口")
public class SystemLogController {
    @Resource
    private SystemLogService systemLogService;

    @GetMapping("/page")
    @Operation(summary = "分页查询系统日志", description = "分页条件查询系统日志数据")
    public R<Object> page(@Parameter(name = "pageNum", description = "查询页码", example = "1") @RequestParam(defaultValue = "1") Integer pageNum,
                         @Parameter(name = "pageSize", description = "每页数据量", example = "10") @RequestParam(defaultValue = "10") Integer pageSize) {
        Page<SystemLog> systemLogPage = new Page<>(pageNum, pageSize);
        QueryWrapper<SystemLog> wrapper = new QueryWrapper<>();
        IPage<SystemLog> systemLogIPage = systemLogService.page(systemLogPage, wrapper);
        return R.ok(systemLogIPage);
    }
    @PostMapping
    @Operation(summary = "添加数据", description = "添加新的数据")
    public R<Object> add(@RequestBody SystemLog systemLog) {
        boolean b =  systemLogService.save(systemLog);
        return R.ok(b);
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "删除数据", description = "根据id删除数据")
    public R<Object> delete(@Parameter(name = "id", description = "查询的id", example = "1") @PathVariable Integer id) {
        boolean b = systemLogService.removeById(id);
        return R.ok(b);
    }

    @PutMapping
    @Operation(summary = "更新数据", description = "根据内容更新数据")
    public R<Object> update(@RequestBody SystemLog systemLog) {
        boolean b = systemLogService.updateById(systemLog);
        return R.ok(b);
    }

    @GetMapping("/{id}")
    @Operation(summary = "获取单个值", description = "查看单个项目的内容")
    public R<Object> detail(@Parameter(name = "id", description = "查询的id", example = "1") @PathVariable Integer id) {
        SystemLog systemLog = systemLogService.getById(id);
        return R.ok(systemLog);
    }
}