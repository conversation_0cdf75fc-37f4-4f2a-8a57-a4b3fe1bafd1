package com.qs.admin.taxhall.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.qs.admin.common.domain.R;
import com.qs.admin.taxhall.model.AgentInfo;
import com.qs.admin.taxhall.model.RelatedEnterpriseRequest;
import com.qs.admin.taxhall.model.BaiShuiApiResponse;
import com.qs.admin.taxhall.model.System;
import com.qs.admin.taxhall.model.Ticket;
import com.qs.admin.taxhall.model.dto.AgentInfoSaveRequest;
import com.qs.admin.taxhall.model.dto.EnterpriseDTO;
import com.qs.admin.taxhall.model.vo.AgentTodayEnterpriseVO;
import com.qs.admin.taxhall.service.AgentInfoService;
import com.qs.admin.taxhall.service.BaiShuiApiService;
import com.qs.admin.taxhall.service.SystemService;
import com.qs.admin.taxhall.task.BaiShuiTokenSyncTask;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;


/**
 * 代理机构人员信息控制器
 */
@RestController
@RequestMapping("/api/v1/agentInfo")
public class AgentInfoController {

    @Autowired
    private AgentInfoService agentInfoService;

    @Autowired
    private BaiShuiApiService baiShuiApiService;

    @Autowired
    private SystemService systemService;

    @Autowired
    private BaiShuiTokenSyncTask baiShuiTokenSyncTask;

    Logger logger = LoggerFactory.getLogger(AgentInfoController.class);
    /**
     * 保存代理机构人员信息
     *
     * @param params 请求参数
     * @return 保存结果
     */
    @PostMapping("/save")
    public R<Boolean> saveAgentInfo(@RequestBody Map<String, Object> params) {

        logger.info("收到代理人信息保存请求: {}", params);

        try {
            // 调用 Service 层的方法将参数转换为 AgentInfoSaveRequest 对象
            AgentInfoSaveRequest request = agentInfoService.convertParamsToRequest(params);

            // 调用 Service 层的方法保存数据
            boolean result = agentInfoService.saveAgentInfoWithEnterprises(request);
            return result ? R.ok() : R.fail("保存失败");
        } catch (IllegalArgumentException e) {
            // 参数错误
            logger.error("参数错误", e);
            return R.fail(e.getMessage());
        } catch (Exception e) {
            // 其他异常
            logger.error("保存代理人信息异常", e);
            return R.fail("保存失败: " + e.getMessage());
        }
    }

    /**
     * 获取佰税科技API授权令牌并存储到系统表中
     * POST /api/v1/agentInfo/refresh-token
     */
    @PostMapping("/refresh-token")
    public R<?> refreshAndStoreToken() {
        try {
            // 获取令牌和appKey
            String token = baiShuiApiService.getToken();
            String appKey = baiShuiApiService.getAppKey();
            if (token == null || token.isEmpty()) {
                return R.fail("获取授权令牌失败");
            }

            // 存储 APPKEY
            System appKeyRecord = new System();
            appKeyRecord.setKey("BS_API_APPKEY");
            appKeyRecord.setValue(appKey);
            appKeyRecord.setMemo("佰税科技API APPKEY，自动更新于" + new java.util.Date());

            QueryWrapper<System> appKeyWrapper = new QueryWrapper<>();
            appKeyWrapper.eq("[KEY]", "BS_API_APPKEY");
            System existingAppKey = systemService.getOne(appKeyWrapper);

            boolean appKeyResult;
            if (existingAppKey != null) {
                appKeyRecord.setKey(existingAppKey.getKey());
                appKeyResult = systemService.updateByKey(appKeyRecord);
            } else {
                appKeyResult = systemService.save(appKeyRecord);
            }

            // 存储 TOKEN
            System tokenRecord = new System();
            tokenRecord.setKey("BS_API_TOKEN");
            tokenRecord.setValue(token);
            tokenRecord.setMemo("佰税科技API授权令牌，自动更新于" + new java.util.Date());

            QueryWrapper<System> tokenWrapper = new QueryWrapper<>();
            tokenWrapper.eq("[KEY]", "BS_API_TOKEN");
            System existingToken = systemService.getOne(tokenWrapper);

            boolean tokenResult;
            if (existingToken != null) {
                tokenRecord.setKey(existingToken.getKey());
                tokenResult = systemService.updateByKey(tokenRecord);
            } else {
                tokenResult = systemService.save(tokenRecord);
            }

            if (appKeyResult && tokenResult) {
                return R.ok("APPKEY和授权令牌已更新");
            } else {
                return R.fail("APPKEY或授权令牌存储失败");
            }
        } catch (Exception e) {
            logger.error("获取并存储佰税科技API授权令牌异常", e);
            return R.fail("获取并存储授权令牌异常: " + e.getMessage());
        }
    }

    /**
     * 获取当日代理机构或代理人来办理业务所服务的企业信息
     * GET /api/v1/agentInfo/today-enterprises/{ticketUid}
     *
     * @param ticketId 票据唯一标识
     * @return 包含票号、姓名、身份证、手机号、所属机构、服务对象的机构名称、服务对象机构代码、取号时间的列表
     */
    @GetMapping("/today-enterprises/{ticketId}")
    public R<?> getTodayEnterprises(@PathVariable String ticketId) {
        try {
            List<AgentTodayEnterpriseVO> result = agentInfoService.getTodayAgentEnterpriseList(ticketId);
            return R.ok(result);
        } catch (Exception e) {
            return R.fail("获取当日代理服务企业信息失败: " + e.getMessage());
        }
    }

    @PostMapping("/manual-sync-bs-token")
    public R<?> manualSyncBsToken() {
        try {
            baiShuiTokenSyncTask.syncAppKeyAndToken();
            return R.ok("手动同步成功");
        } catch (Exception e) {
            logger.error("手动同步佰税API APPKEY和TOKEN异常", e);
            return R.fail("手动同步失败: " + e.getMessage());
        }
    }
}