package com.qs.admin.taxhall.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.qs.admin.common.core.Query;
import com.qs.admin.common.domain.R; // 统一返回结构
import com.qs.admin.taxhall.model.Autocode;
import com.qs.admin.taxhall.service.AutocodeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;
import java.util.Map;

/**
 * <AUTHOR>
 * @data 2020-04-07.
 */
@RestController
@RequestMapping("/api/v1/autocode")
@Api(tags = "自动编码管理", description = "自动编码相关的RESTful接口")
public class AutocodeController {
    @Autowired
    private AutocodeService autocodeService;

    @GetMapping
    @ApiOperation(value = "获取全部",notes = "返回分页过后的数据",httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page",value = "查询页码", paramType = "query",dataType = "Integer",defaultValue = "1"),
            @ApiImplicitParam(name = "pageSize",value = "每页数据量", paramType = "query",dataType = "Integer",defaultValue = "10")
    })

    public R<?> list(@ApiIgnore @RequestParam Map<String, Object> params) {
        Page<Autocode> autocodePage = new Query<>(params);
        QueryWrapper<Autocode> wrapper = new QueryWrapper<>();
        IPage<Autocode> autocodeIPage = autocodeService.page(autocodePage, wrapper);
        return R.ok(autocodeIPage);
    }
    @PostMapping
    @ApiOperation(value = "添加数据",notes = "添加新的数据",httpMethod = "POST")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "autocode",value = "待添加的autocode实例",paramType = "body",dataType = "Autocode",required = true)
    })
    public R<?> add(@RequestBody @javax.validation.Valid Autocode autocode) {
        boolean b = autocodeService.save(autocode);
        return b ? R.ok() : R.fail();
    }

    @DeleteMapping("/{id}")
    @ApiOperation(value = "删除数据",notes = "根据id删除数据",httpMethod = "DELETE")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id",value = "查询的id", paramType = "path",required = true,dataType = "Integer"),
    })
    public R<?> delete(@PathVariable Integer id) {
        boolean b = autocodeService.removeById(id);
        return b ? R.ok() : R.fail();
    }

    @PutMapping
    @ApiOperation(value = "更新数据",notes = "根据内容更新数据",httpMethod = "PUT")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "autocode",value = "更新的autocode实例",paramType = "body",dataType = "Autocode",required = true)
    })
    public R<?> update(@RequestBody @javax.validation.Valid Autocode autocode) {
        boolean b = autocodeService.updateById(autocode);
        return b ? R.ok() : R.fail();
    }

    @GetMapping("/{id}")
    @ApiOperation(value = "获取单个值",notes = "查看单个项目的内容",httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id",value = "查询的id", paramType = "path",required = true,dataType = "Integer",defaultValue = "0")
    })
    public R<?> detail(@PathVariable Integer id) {
        Autocode autocode = autocodeService.getById(id);
        return R.ok(autocode);
    }
}