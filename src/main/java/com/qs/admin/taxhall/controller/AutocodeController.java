package com.qs.admin.taxhall.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.qs.admin.common.domain.R; // 统一返回结构
import com.qs.admin.taxhall.model.Autocode;
import com.qs.admin.taxhall.service.AutocodeService;
import io.swagger.v3.oas.annotations.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;



/**
 * <AUTHOR>
 * @data 2020-04-07.
 */
@RestController
@RequestMapping("/api/v1/autocode")
@Tag(name = "自动编码管理", description = "自动编码相关的RESTful接口")
public class AutocodeController {
    @Autowired
    private AutocodeService autocodeService;

    @GetMapping("/page")
    @Operation(summary = "分页查询自动编码", description = "分页条件查询自动编码数据")
    public R<?> page(@Parameter(name = "pageNum", description = "查询页码", example = "1") @RequestParam(defaultValue = "1") Integer pageNum,
                    @Parameter(name = "pageSize", description = "每页数据量", example = "10") @RequestParam(defaultValue = "10") Integer pageSize) {
        Page<Autocode> autocodePage = new Page<>(pageNum, pageSize);
        QueryWrapper<Autocode> wrapper = new QueryWrapper<>();
        IPage<Autocode> autocodeIPage = autocodeService.page(autocodePage, wrapper);
        return R.ok(autocodeIPage);
    }
    @PostMapping
    @Operation(summary = "添加数据", description = "添加新的数据")
    public R<?> add(@RequestBody @javax.validation.Valid Autocode autocode) {
        boolean b = autocodeService.save(autocode);
        return b ? R.ok() : R.fail();
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "删除数据", description = "根据id删除数据")
    public R<?> delete(@Parameter(name = "id", description = "查询的id", example = "1") @PathVariable Integer id) {
        boolean b = autocodeService.removeById(id);
        return b ? R.ok() : R.fail();
    }

    @PutMapping
    @Operation(summary = "更新数据", description = "根据内容更新数据")
    public R<?> update(@RequestBody @javax.validation.Valid Autocode autocode) {
        boolean b = autocodeService.updateById(autocode);
        return b ? R.ok() : R.fail();
    }

    @GetMapping("/{id}")
    @Operation(summary = "获取单个值", description = "查看单个项目的内容")
    public R<?> detail(@Parameter(name = "id", description = "查询的id", example = "1") @PathVariable Integer id) {
        Autocode autocode = autocodeService.getById(id);
        return R.ok(autocode);
    }
}