package com.qs.admin.taxhall.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.qs.admin.common.core.Query;
import com.qs.admin.common.domain.R;
import com.qs.admin.taxhall.model.WindowStatus;
import com.qs.admin.taxhall.model.dto.PageQueryDTO;
import com.qs.admin.taxhall.service.WindowStatusService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.commons.beanutils.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.qs.admin.taxhall.model.dto.WindowStatusDTO;
import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Map;

/**
* <AUTHOR>
* @data 2020-04-07.
*/
@RestController
@RequestMapping("/api/v1/window-status")
@Tag(name = "窗口状态管理", description = "窗口状态相关的RESTful接口")
public class WindowStatusController {
    @Autowired
    private WindowStatusService windowStatusService;

    @GetMapping("/page")
    @Operation(summary = "分页查询窗口状态", description = "分页条件查询窗口状态数据")
    @Parameter(name = "page", description = "查询页码", example = "1")
    @Parameter(name = "pageSize", description = "每页数据量", example = "10")
    public R<?> page(@RequestParam(defaultValue = "1") Integer page,
                    @RequestParam(defaultValue = "10") Integer pageSize) {
        Page<WindowStatus> wspage = new Page<>(page, pageSize);
        QueryWrapper<WindowStatus> wrapper = new QueryWrapper<>();
        // 可根据需要添加查询条件
        IPage<WindowStatus> windowStatusIPage = windowStatusService.page(wspage, wrapper);
        return R.ok(windowStatusIPage);
    }

    @PostMapping
    @Operation(summary = "添加数据", description = "添加新的数据")
    public R<?> add(@RequestBody @Valid WindowStatusDTO windowStatusDTO) {
        WindowStatus windowStatus = new WindowStatus();
        try {
            BeanUtils.copyProperties(windowStatusDTO, windowStatus);
        } catch (Exception e) {
            return R.fail("属性拷贝失败: " + e.getMessage());
        }
        boolean b = windowStatusService.save(windowStatus);
        return b ? R.ok() : R.fail();
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "删除数据", description = "根据id删除数据")
    @Parameter(name = "id", description = "查询的id", required = true)
    public R<?> delete(@PathVariable Integer id) {
        boolean b = windowStatusService.removeById(id);
        return b ? R.ok() : R.fail();
    }

    @PutMapping
    @Operation(summary = "更新数据", description = "根据内容更新数据")
    public R<?> update(@RequestBody @Valid WindowStatusDTO windowStatusDTO) {
        WindowStatus windowStatus = new WindowStatus();
        try {
            BeanUtils.copyProperties(windowStatusDTO, windowStatus);
        } catch (Exception e) {
            return R.fail("属性拷贝失败: " + e.getMessage());
        }
        boolean b = windowStatusService.updateById(windowStatus);
        return b ? R.ok() : R.fail();
    }

    @GetMapping("/{id}")
    @Operation(summary = "获取单个值", description = "查看单个项目的内容")
    @Parameter(name = "id", description = "查询的id", required = true, example = "0")
    public R<?> detail(@PathVariable Integer id) {
        WindowStatus windowStatus = windowStatusService.getById(id);
        return R.ok(windowStatus);
    }
}