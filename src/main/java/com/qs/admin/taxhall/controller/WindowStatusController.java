package com.qs.admin.taxhall.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.qs.admin.common.core.Query;
import com.qs.admin.common.domain.R;
import com.qs.admin.taxhall.model.WindowStatus;
import com.qs.admin.taxhall.model.dto.PageQueryDTO;
import com.qs.admin.taxhall.service.WindowStatusService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.beanutils.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.qs.admin.taxhall.model.dto.WindowStatusDTO;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Map;

/**
* <AUTHOR>
* @data 2020-04-07.
*/
@RestController
@RequestMapping("/api/v1/window-status")
@Api(tags = "窗口状态管理", description = "窗口状态相关的RESTful接口")
public class WindowStatusController {
    @Autowired
    private WindowStatusService windowStatusService;

    @PostMapping("/list")
    @ApiOperation(value = "分页查询", notes = "分页条件查询窗口状态", httpMethod = "POST")
    public R<?> list(@RequestBody @Valid PageQueryDTO pageQueryDTO) {
        Page<WindowStatus> page = new Page<>(pageQueryDTO.getPageNum(), pageQueryDTO.getPageSize());
        QueryWrapper<WindowStatus> wrapper = new QueryWrapper<>();
        // 可根据 pageQueryDTO 的其它字段拼接条件，如：
        // if (pageQueryDTO.getName() != null) wrapper.eq("name", pageQueryDTO.getName());
        IPage<WindowStatus> windowStatusIPage = windowStatusService.page(page, wrapper);
        return R.ok(windowStatusIPage);
    }

    @PostMapping
    @ApiOperation(value = "添加数据", notes = "添加新的数据", httpMethod = "POST")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "windowStatusDTO", value = "待添加的windowStatus实例", paramType = "body", dataType = "WindowStatusDTO", required = true)
    })
    public R<?> add(@RequestBody @Valid WindowStatusDTO windowStatusDTO) {
        WindowStatus windowStatus = new WindowStatus();
        try {
            BeanUtils.copyProperties(windowStatusDTO, windowStatus);
        } catch (Exception e) {
            return R.fail("属性拷贝失败: " + e.getMessage());
        }
        boolean b = windowStatusService.save(windowStatus);
        return b ? R.ok() : R.fail();
    }

    @DeleteMapping("/{id}")
    @ApiOperation(value = "删除数据", notes = "根据id删除数据", httpMethod = "DELETE")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "查询的id", paramType = "path", required = true, dataType = "Integer"),
    })
    public R<?> delete(@PathVariable Integer id) {
        boolean b = windowStatusService.removeById(id);
        return b ? R.ok() : R.fail();
    }

    @PutMapping
    @ApiOperation(value = "更新数据", notes = "根据内容更新数据", httpMethod = "PUT")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "windowStatusDTO", value = "更新的windowStatus实例", paramType = "body", dataType = "WindowStatusDTO", required = true)
    })
    public R<?> update(@RequestBody @Valid WindowStatusDTO windowStatusDTO) {
        WindowStatus windowStatus = new WindowStatus();
        try {
            BeanUtils.copyProperties(windowStatusDTO, windowStatus);
        } catch (Exception e) {
            return R.fail("属性拷贝失败: " + e.getMessage());
        }
        boolean b = windowStatusService.updateById(windowStatus);
        return b ? R.ok() : R.fail();
    }

    @GetMapping("/{id}")
    @ApiOperation(value = "获取单个值", notes = "查看单个项目的内容", httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "查询的id", paramType = "path", required = true, dataType = "Integer", defaultValue = "0")
    })
    public R<?> detail(@PathVariable Integer id) {
        WindowStatus windowStatus = windowStatusService.getById(id);
        return R.ok(windowStatus);
    }
}