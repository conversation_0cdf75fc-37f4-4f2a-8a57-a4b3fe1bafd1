package com.qs.admin.taxhall.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.qs.admin.common.core.Query;
import com.qs.admin.common.domain.R;
import com.qs.admin.taxhall.model.Things;
import com.qs.admin.taxhall.model.dto.BatchDeleteDTO;
import com.qs.admin.taxhall.service.ThingsService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;

import org.springframework.web.bind.annotation.*;


import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @data 2020-04-07.
 */
@RestController
@RequestMapping("/api/v1/things")
@Tag(name = "事项管理", description = "事项相关的RESTful接口")
public class ThingsController {

    @Resource
    private ThingsService thingsService;


    @GetMapping("/page")
    @Operation(summary = "分页查询事项", description = "分页条件查询事项数据")
    public R<Object> page(@Parameter(name = "page", description = "查询页码", example = "1") @RequestParam(defaultValue = "1") Integer page,
                         @Parameter(name = "pageSize", description = "每页数据量", example = "10") @RequestParam(defaultValue = "10") Integer pageSize) {
        Page<Things> thingsPage = new Page<>(page, pageSize);
        QueryWrapper<Things> wrapper = new QueryWrapper<>();
        IPage<Things> thingsIPage = thingsService.page(thingsPage, wrapper);
        return R.ok(thingsIPage);
    }
    @PostMapping
    @Operation(summary = "添加数据", description = "添加新的数据")
    public R<Object> add(@RequestBody(required = false) Things things) {
        if (things == null) {
            return R.fail("请求参数不能为空");
        }
        boolean b =  thingsService.save(things);
        return R.ok(b);
    }

    @DeleteMapping("/batch")
    @Operation(summary = "批量删除数据", description = "批量删除数据，传递ID列表")
    public R<Object> batchDelete(@RequestBody @Valid BatchDeleteDTO batchDeleteDTO) {
        List<String> ids = batchDeleteDTO.getIds().stream().map(String::valueOf).collect(Collectors.toList());
        boolean b = thingsService.removeByIds(ids);
        return R.ok(b);
    }

    @PutMapping
    @Operation(summary = "更新数据", description = "根据内容更新数据")
    public R<Object> update(@RequestBody(required = false) Things things) {
        if (things == null) {
            return R.fail("请求参数不能为空");
        }
        boolean b = thingsService.updateById(things);
        return R.ok(b);
    }

    @GetMapping("/{id}")
    @Operation(summary = "获取单个值", description = "查看单个项目的内容")
    public R<Object> detail(@Parameter(name = "id", description = "查询的id", example = "1") @PathVariable Integer id) {
        Things things = thingsService.getById(id);
        return R.ok(things);
    }
}