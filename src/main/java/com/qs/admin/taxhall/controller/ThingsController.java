package com.qs.admin.taxhall.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.qs.admin.common.core.Query;
import com.qs.admin.common.domain.R;
import com.qs.admin.taxhall.model.Things;
import com.qs.admin.taxhall.model.dto.BatchDeleteDTO;
import com.qs.admin.taxhall.service.ThingsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;

import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @data 2020-04-07.
 */
@RestController
@RequestMapping("/api/v1/things")
@Api(tags = "事项管理", description = "事项相关的RESTful接口")
public class ThingsController {

    @Resource
    private ThingsService thingsService;


    @GetMapping("/page")
    @ApiOperation(value = "分页查询事项", notes = "分页条件查询事项数据", httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageNum", value = "查询页码", paramType = "query", dataType = "Integer", defaultValue = "1"),
            @ApiImplicitParam(name = "pageSize", value = "每页数据量", paramType = "query", dataType = "Integer", defaultValue = "10")
    })
    public R<Object> page(@RequestParam(defaultValue = "1") Integer pageNum,
                         @RequestParam(defaultValue = "10") Integer pageSize) {
        Page<Things> thingsPage = new Page<>(pageNum, pageSize);
        QueryWrapper<Things> wrapper = new QueryWrapper<>();
        IPage<Things> thingsIPage = thingsService.page(thingsPage, wrapper);
        return R.ok(thingsIPage);
    }
    @PostMapping
    @ApiOperation(value = "添加数据",notes = "添加新的数据",httpMethod = "POST")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "things",value = "待添加的things实例",paramType = "body",dataType = "Things",required = true)
    })
    public R<Object> add(@RequestBody(required = false) Things things) {
        if (things == null) {
            return R.fail("请求参数不能为空");
        }
        boolean b =  thingsService.save(things);
        return R.ok(b);
    }

    @DeleteMapping("/batch")
    @ApiOperation(value = "批量删除数据",notes = "批量删除数据，传递ID列表",httpMethod = "DELETE")
    public R<Object> batchDelete(@RequestBody @Valid BatchDeleteDTO batchDeleteDTO) {
        List<String> ids = batchDeleteDTO.getIds().stream().map(String::valueOf).collect(Collectors.toList());
        boolean b = thingsService.removeByIds(ids);
        return R.ok(b);
    }

    @PutMapping
    @ApiOperation(value = "更新数据",notes = "根据内容更新数据",httpMethod = "PUT")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "things",value = "更新的things实例",paramType = "body",dataType = "Things",required = true)
    })
    public R<Object> update(@RequestBody(required = false) Things things) {
        if (things == null) {
            return R.fail("请求参数不能为空");
        }
        boolean b = thingsService.updateById(things);
        return R.ok(b);
    }

    @GetMapping("/{id}")
    @ApiOperation(value = "获取单个值",notes = "查看单个项目的内容",httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id",value = "查询的id", paramType = "path",required = true,dataType = "Integer",defaultValue = "0")
    })
    public R<Object> detail(@PathVariable Integer id) {
        Things things = thingsService.getById(id);
        return R.ok(things);
    }
}