package com.qs.admin.taxhall.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.qs.admin.common.core.Query;
import com.qs.admin.common.domain.R;
import com.qs.admin.taxhall.model.ClientInfo;
import com.qs.admin.taxhall.service.ClientInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;
import java.util.Map;

/**
 * <AUTHOR>
 * @data 2020-04-07.
 */
@RestController
@RequestMapping("/api/v1/client-info")
@Api(tags = "client-infos", description = "客户端信息相关的RESTful接口")
public class ClientInfoController {
    @Autowired
    private ClientInfoService clientInfoService;

    @GetMapping("/page")
    @ApiOperation(value = "分页查询客户端信息", notes = "分页条件查询客户端信息数据", httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageNum", value = "查询页码", paramType = "query", dataType = "Integer", defaultValue = "1"),
            @ApiImplicitParam(name = "pageSize", value = "每页数据量", paramType = "query", dataType = "Integer", defaultValue = "10")
    })
    public R<?> page(@RequestParam(defaultValue = "1") Integer pageNum,
                    @RequestParam(defaultValue = "10") Integer pageSize) {
        Page<ClientInfo> clientInfoPage = new Page<>(pageNum, pageSize);
        QueryWrapper<ClientInfo> wrapper = new QueryWrapper<>();
        IPage<ClientInfo> clientInfoIPage = clientInfoService.page(clientInfoPage, wrapper);
        return R.ok(clientInfoIPage);
    }
    @PostMapping
    @ApiOperation(value = "添加数据",notes = "添加新的数据",httpMethod = "POST")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "clientInfo",value = "待添加的clientInfo实例",paramType = "body",dataType = "ClientInfo",required = true)
    })
    public R<?> add(@RequestBody @javax.validation.Valid ClientInfo clientInfo) {
        boolean b =  clientInfoService.save(clientInfo);
        return b ? R.ok() : R.fail();
    }

    @DeleteMapping("/{id}")
    @ApiOperation(value = "删除数据",notes = "根据id删除数据",httpMethod = "DELETE")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id",value = "查询的id", paramType = "path",required = true,dataType = "Integer"),
    })
    public R<?> delete(@PathVariable Integer id) {
        boolean b = clientInfoService.removeById(id);
        return b ? R.ok() : R.fail();
    }

    @PutMapping
    @ApiOperation(value = "更新数据",notes = "根据内容更新数据",httpMethod = "PUT")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "clientInfo",value = "更新的clientInfo实例",paramType = "body",dataType = "ClientInfo",required = true)
    })
    public R<?> update(@RequestBody @javax.validation.Valid ClientInfo clientInfo) {
        boolean b = clientInfoService.updateById(clientInfo);
        return b ? R.ok() : R.fail();
    }

    @GetMapping("/{id}")
    @ApiOperation(value = "获取单个值",notes = "查看单个项目的内容",httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id",value = "查询的id", paramType = "path",required = true,dataType = "Integer",defaultValue = "0")
    })
    public R<?> detail(@PathVariable Integer id) {
        ClientInfo clientInfo = clientInfoService.getById(id);
        return R.ok(clientInfo);
    }
}