package com.qs.admin.taxhall.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.qs.admin.common.core.Query;
import com.qs.admin.common.domain.R;
import com.qs.admin.taxhall.model.ClientInfo;
import com.qs.admin.taxhall.service.ClientInfoService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;


import javax.annotation.Resource;
import java.util.Map;

/**
 * <AUTHOR>
 * @data 2020-04-07.
 */
@RestController
@RequestMapping("/api/v1/client-info")
@Tag(name = "client-infos", description = "客户端信息相关的RESTful接口")
public class ClientInfoController {
    @Autowired
    private ClientInfoService clientInfoService;

    @GetMapping("/page")
    @Operation(summary = "分页查询客户端信息", description = "分页条件查询客户端信息数据")
    public R<?> page(@Parameter(name = "pageNum", description = "查询页码", example = "1") @RequestParam(defaultValue = "1") Integer pageNum,
                    @Parameter(name = "pageSize", description = "每页数据量", example = "10") @RequestParam(defaultValue = "10") Integer pageSize) {
        Page<ClientInfo> clientInfoPage = new Page<>(pageNum, pageSize);
        QueryWrapper<ClientInfo> wrapper = new QueryWrapper<>();
        IPage<ClientInfo> clientInfoIPage = clientInfoService.page(clientInfoPage, wrapper);
        return R.ok(clientInfoIPage);
    }
    @PostMapping
    @Operation(summary = "添加数据", description = "添加新的数据")
    public R<?> add(@RequestBody @javax.validation.Valid ClientInfo clientInfo) {
        boolean b =  clientInfoService.save(clientInfo);
        return b ? R.ok() : R.fail();
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "删除数据", description = "根据id删除数据")
    public R<?> delete(@Parameter(name = "id", description = "查询的id", example = "1") @PathVariable Integer id) {
        boolean b = clientInfoService.removeById(id);
        return b ? R.ok() : R.fail();
    }

    @PutMapping
    @Operation(summary = "更新数据", description = "根据内容更新数据")
    public R<?> update(@RequestBody @javax.validation.Valid ClientInfo clientInfo) {
        boolean b = clientInfoService.updateById(clientInfo);
        return b ? R.ok() : R.fail();
    }

    @GetMapping("/{id}")
    @Operation(summary = "获取单个值", description = "查看单个项目的内容")
    public R<?> detail(@Parameter(name = "id", description = "查询的id", example = "1") @PathVariable Integer id) {
        ClientInfo clientInfo = clientInfoService.getById(id);
        return R.ok(clientInfo);
    }
}