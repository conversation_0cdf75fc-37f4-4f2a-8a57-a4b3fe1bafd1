package com.qs.admin.taxhall.controller;

import com.qs.admin.common.domain.R;
import com.qs.admin.common.shiro.security.ShiroJwtUtil;
import com.qs.admin.taxhall.model.Employee;
import com.qs.admin.taxhall.model.dto.LoginDTO;
import com.qs.admin.taxhall.model.dto.LoginResponseDTO;
import com.qs.admin.taxhall.model.dto.MenuDTO;
import com.qs.admin.taxhall.service.EmployeeService;
import com.qs.admin.taxhall.service.MenuService;

import java.util.List;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;

/**
 * 简单登录控制器
 * <AUTHOR> Admin Team
 */
@RestController
@RequestMapping("/api/v1/auth")
@Tag(name = "用户认证", description = "用户登录登出相关接口")
public class SimpleLoginController {

    private static final Logger logger = LoggerFactory.getLogger(SimpleLoginController.class);

    @Autowired
    private EmployeeService employeeService;

    @Autowired
    private MenuService menuService;

    @Autowired(required = false)
    private ShiroJwtUtil jwtUtil;

    @PostMapping("/login")
    @Operation(summary = "用户登录", description = "使用用户名和密码进行登录验证")
    public R<?> login(@RequestBody(required = false) LoginDTO loginDTO,
                     @RequestParam(required = false) String username,
                     @RequestParam(required = false) String password,
                     HttpServletRequest httpRequest) {
        try {
            String clientIp = getClientIp(httpRequest);

            // 确定用户名和密码，优先使用DTO
            String finalUsername;
            String finalPassword;
            
            if (loginDTO != null) {
                // 从DTO获取用户名和密码
                finalUsername = loginDTO.getUsername();
                finalPassword = loginDTO.getPassword();
            } else {
                // 从URL参数获取用户名和密码
                finalUsername = username;
                finalPassword = password;
            }
            
            logger.info("用户登录请求: username={}, ip={}", finalUsername, clientIp);

            if (finalUsername == null || finalPassword == null ||
                finalUsername.trim().isEmpty() || finalPassword.trim().isEmpty()) {
                return R.fail("用户名和密码不能为空");
            }

            finalUsername = finalUsername.trim();
            finalPassword = finalPassword.trim();

            Employee employee = employeeService.login(finalUsername, finalPassword);
            if (employee == null) {
                logger.warn("登录失败: 用户名或密码错误, username={}, ip={}", finalUsername, clientIp);
                return R.fail("用户名或密码错误");
            }

            // 检查用户状态
            if (employee.getEnabled() == null || employee.getEnabled() != 1) {
                logger.warn("登录失败: 用户已被禁用, username={}, ip={}", finalUsername, clientIp);
                return R.fail("用户已被禁用");
            }

            // 生成JWT token
            String token;
            if (jwtUtil != null) {
                token = jwtUtil.sign(employee.getUsername(), String.valueOf(System.currentTimeMillis()));
            } else {
                // 如果JWT工具不可用，使用简单token
                token = "SIMPLE_TOKEN_" + employee.getUid() + "_" + System.currentTimeMillis();
            }

            // 获取用户菜单
            List<MenuDTO> menus = menuService.getMenusByUserId(employee.getUid().longValue());

            // 构建登录响应
            LoginResponseDTO loginResponse = new LoginResponseDTO(
                employee.getUid().longValue(),
                employee.getUsername(),
                employee.getName(),
                "user", // 默认角色，后期可从数据库获取
                token,
                menus
            );

            logger.info("用户登录成功: username={}, uid={}, ip={}, 菜单数量={}", 
                employee.getUsername(), employee.getUid(), clientIp, menus.size());
            return R.ok("登录成功", loginResponse);

        } catch (Exception e) {
            logger.error("登录异常", e);
            return R.fail("登录异常: " + e.getMessage());
        }
    }

    @PostMapping("/logout")
    @Operation(summary = "用户登出", description = "用户登出")
    public R<?> logout() {
        try {
            logger.info("用户登出");
            return R.ok("登出成功");
        } catch (Exception e) {
            logger.error("登出异常", e);
            return R.fail("登出异常: " + e.getMessage());
        }
    }

    /**
     * 从参数数组中获取第一个有效的参数值
     * 解决前端同时在URL参数和表单数据中发送相同参数导致的重复值问题
     */
    private String getFirstValidParam(String[] params) {
        if (params == null || params.length == 0) {
            return null;
        }
        for (String param : params) {
            if (param != null && !param.trim().isEmpty()) {
                return param.trim();
            }
        }
        return null;
    }

    /**
     * 获取客户端IP地址
     */
    private String getClientIp(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty() && !"unknown".equalsIgnoreCase(xForwardedFor)) {
            return xForwardedFor.split(",")[0];
        }
        String xRealIp = request.getHeader("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty() && !"unknown".equalsIgnoreCase(xRealIp)) {
            return xRealIp;
        }
        return request.getRemoteAddr();
    }
}
