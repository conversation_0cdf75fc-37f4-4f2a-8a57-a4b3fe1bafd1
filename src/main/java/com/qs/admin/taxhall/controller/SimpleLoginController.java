package com.qs.admin.taxhall.controller;

import com.qs.admin.common.domain.R;
import com.qs.admin.common.shiro.security.JwtUtil;
import com.qs.admin.taxhall.model.Employee;
import com.qs.admin.taxhall.model.dto.LoginResponseDTO;
import com.qs.admin.taxhall.model.dto.MenuDTO;
import com.qs.admin.taxhall.service.EmployeeService;
import com.qs.admin.taxhall.service.MenuService;

import java.util.List;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/api/v1/auth")
@Api(tags = "用户认证", description = "用户登录相关接口")
public class SimpleLoginController {

    private static final Logger logger = LoggerFactory.getLogger(SimpleLoginController.class);

    @Autowired
    private EmployeeService employeeService;

    @Autowired
    private MenuService menuService;

    @Autowired(required = false)
    private JwtUtil jwtUtil;

    @PostMapping("/login")
    @ApiOperation(value = "用户登录", notes = "使用用户名和密码进行登录验证")
    public R<?> login(@RequestBody(required = false) Map<String, String> requestBody,
                     @RequestParam(required = false) String username,
                     @RequestParam(required = false) String password,
                     HttpServletRequest httpRequest) {
        try {
            String clientIp = getClientIp(httpRequest);

            // 优先从请求体获取参数，如果没有则从URL参数获取
            String finalUsername = null;
            String finalPassword = null;

            if (requestBody != null && !requestBody.isEmpty()) {
                finalUsername = requestBody.get("username");
                finalPassword = requestBody.get("password");
            } else {
                finalUsername = username;
                finalPassword = password;
            }

            logger.info("用户登录请求: username={}, ip={}", finalUsername, clientIp);

            if (finalUsername == null || finalPassword == null ||
                finalUsername.trim().isEmpty() || finalPassword.trim().isEmpty()) {
                return R.fail("用户名和密码不能为空");
            }

            Employee employee = employeeService.login(finalUsername, finalPassword);
            if (employee == null) {
                logger.warn("登录失败: 用户名或密码错误, username={}, ip={}", finalUsername, clientIp);
                return R.fail("用户名或密码错误");
            }

            // 检查用户状态
            if (employee.getEnabled() == null || employee.getEnabled() != 1) {
                logger.warn("登录失败: 用户已被禁用, username={}, ip={}", finalUsername, clientIp);
                return R.fail("用户已被禁用");
            }

            // 生成简单的token
            String token = "SIMPLE_TOKEN_" + employee.getUid() + "_" + System.currentTimeMillis();

            // 获取用户菜单
            List<MenuDTO> menus = menuService.getMenusByUserId(employee.getUid());

            // 构建登录响应
            LoginResponseDTO loginResponse = new LoginResponseDTO(
                employee.getUid(),
                employee.getUsername(),
                employee.getName(),
                "user", // 默认角色，后期可从数据库获取
                token,
                menus
            );

            logger.info("用户登录成功: username={}, uid={}, ip={}, 菜单数量={}",
                employee.getUsername(), employee.getUid(), clientIp, menus.size());
            return R.ok("登录成功", loginResponse);

        } catch (Exception e) {
            logger.error("登录异常", e);
            return R.fail("登录异常: " + e.getMessage());
        }
    }

    @GetMapping("/login")
    @ApiOperation(value = "用户登录(GET)", notes = "使用GET方式进行登录验证，仅用于测试")
    public R<?> loginGet(@RequestParam String username,
                        @RequestParam String password,
                        HttpServletRequest httpRequest) {
        try {
            String clientIp = getClientIp(httpRequest);
            logger.info("用户登录请求(GET): username={}, ip={}", username, clientIp);

            if (username == null || password == null ||
                username.trim().isEmpty() || password.trim().isEmpty()) {
                return R.fail("用户名和密码不能为空");
            }

            Employee employee = employeeService.login(username, password);
            if (employee == null) {
                logger.warn("登录失败: 用户名或密码错误, username={}, ip={}", username, clientIp);
                return R.fail("用户名或密码错误");
            }

            // 检查用户状态
            if (employee.getEnabled() == null || employee.getEnabled() != 1) {
                logger.warn("登录失败: 用户已被禁用, username={}, ip={}", username, clientIp);
                return R.fail("用户已被禁用");
            }

            // 生成简单的token
            String token = "SIMPLE_TOKEN_" + employee.getUid() + "_" + System.currentTimeMillis();

            // 获取用户菜单
            List<MenuDTO> menus = menuService.getMenusByUserId(employee.getUid());

            // 构建登录响应
            LoginResponseDTO loginResponse = new LoginResponseDTO(
                employee.getUid(),
                employee.getUsername(),
                employee.getName(),
                "user", // 默认角色，后期可从数据库获取
                token,
                menus
            );

            logger.info("用户登录成功(GET): username={}, uid={}, ip={}, 菜单数量={}",
                employee.getUsername(), employee.getUid(), clientIp, menus.size());
            return R.ok("登录成功", loginResponse);

        } catch (Exception e) {
            logger.error("登录异常", e);
            return R.fail("登录异常: " + e.getMessage());
        }
    }

    @PostMapping("/logout")
    @ApiOperation(value = "用户登出", notes = "用户登出")
    public R<?> logout() {
        try {
            logger.info("用户登出");
            return R.ok("登出成功");
        } catch (Exception e) {
            logger.error("登出异常", e);
            return R.fail("登出异常: " + e.getMessage());
        }
    }

    /**
     * 获取客户端IP地址
     */
    private String getClientIp(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty() && !"unknown".equalsIgnoreCase(xForwardedFor)) {
            return xForwardedFor.split(",")[0];
        }
        String xRealIp = request.getHeader("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty() && !"unknown".equalsIgnoreCase(xRealIp)) {
            return xRealIp;
        }
        return request.getRemoteAddr();
    }
}
