package com.qs.admin.taxhall.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import com.qs.admin.common.domain.R;
import com.qs.admin.taxhall.model.Notice;
import com.qs.admin.taxhall.model.dto.NoticeDTO;
import com.qs.admin.taxhall.model.dto.PageQueryDTO;
import com.qs.admin.taxhall.service.NoticeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;



import javax.validation.Valid;


/**
 * <AUTHOR>
 * @data 2020-04-07.
 */
@RestController
@RequestMapping("/api/v1/notice")
@Tag(name = "通知管理", description = "通知相关的RESTful接口")
public class NoticeController {
    @Autowired
    private NoticeService noticeService;

    @GetMapping("/page")
    @Operation(summary = "分页查询通知", description = "分页条件查询通知数据")
    public R<?> page(@Parameter(name = "pageNum", description = "查询页码", example = "1") @RequestParam(defaultValue = "1") Integer pageNum,
                    @Parameter(name = "pageSize", description = "每页数据量", example = "10") @RequestParam(defaultValue = "10") Integer pageSize) {
        Page<Notice> noticePage = new Page<>(pageNum, pageSize);
        QueryWrapper<Notice> wrapper = new QueryWrapper<>();
        IPage<Notice> noticeIPage = noticeService.page(noticePage, wrapper);
        return R.ok(noticeIPage);
    }
    @PostMapping
    @Operation(summary = "添加数据", description = "添加新的数据")
    public R<?> add(@RequestBody(required = false) @Valid NoticeDTO noticeDTO) {
        if (noticeDTO == null) {
            return R.fail("请求参数不能为空");
        }
        Notice notice = new Notice();
        org.springframework.beans.BeanUtils.copyProperties(noticeDTO, notice);
        boolean b = noticeService.save(notice);
        return b ? R.ok() : R.fail();
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "删除数据", description = "根据id删除数据")
    public R<?> delete(@Parameter(name = "id", description = "查询的id", example = "1") @PathVariable Integer id) {
        boolean b = noticeService.removeById(id);
        return b ? R.ok() : R.fail();
    }

    @PutMapping
    @Operation(summary = "更新数据", description = "根据内容更新数据")
    public R<?> update(@RequestBody(required = false) @Valid NoticeDTO noticeDTO) {
        if (noticeDTO == null) {
            return R.fail("请求参数不能为空");
        }
        Notice notice = new Notice();
        org.springframework.beans.BeanUtils.copyProperties(noticeDTO, notice);
        boolean b = noticeService.updateById(notice);
        return b ? R.ok() : R.fail();
    }

    @GetMapping("/{id}")
    @Operation(summary = "获取单个值", description = "查看单个项目的内容")
    public R<?> detail(@Parameter(name = "id", description = "查询的id", example = "1") @PathVariable Integer id) {
        Notice notice = noticeService.getById(id);
        return R.ok(notice);
    }
}