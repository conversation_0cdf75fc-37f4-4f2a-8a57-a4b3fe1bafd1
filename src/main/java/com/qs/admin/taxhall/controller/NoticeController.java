package com.qs.admin.taxhall.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import com.qs.admin.common.domain.R;
import com.qs.admin.taxhall.model.Notice;
import com.qs.admin.taxhall.model.dto.NoticeDTO;
import com.qs.admin.taxhall.model.dto.PageQueryDTO;
import com.qs.admin.taxhall.service.NoticeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;



import javax.validation.Valid;


/**
 * <AUTHOR>
 * @data 2020-04-07.
 */
@RestController
@RequestMapping("/api/v1/notice")
@Api(tags = "通知管理", description = "通知相关的RESTful接口")
public class NoticeController {
    @Autowired
    private NoticeService noticeService;

    @GetMapping
    @ApiOperation(value = "获取全部",notes = "返回分页过后的数据",httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page",value = "查询页码", paramType = "query",dataType = "Integer",defaultValue = "1"),
            @ApiImplicitParam(name = "pageSize",value = "每页数据量", paramType = "query",dataType = "Integer",defaultValue = "10")
    })

    public R<?> list(@RequestBody(required = false) @Valid PageQueryDTO pageQueryDTO) {
        if (pageQueryDTO == null) {
            pageQueryDTO = new PageQueryDTO(); // 使用默认值
        }
        Page<Notice> noticePage = new Page<>(pageQueryDTO.getPageNum(), pageQueryDTO.getPageSize());
        QueryWrapper<Notice> wrapper = new QueryWrapper<>();
        IPage<Notice> noticeIPage = noticeService.page(noticePage,wrapper);
        return R.ok(noticeIPage);
    }
    @PostMapping
    @ApiOperation(value = "添加数据",notes = "添加新的数据",httpMethod = "POST")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "notice",value = "待添加的notice实例",paramType = "body",dataType = "Notice",required = true)
    })
    public R<?> add(@RequestBody(required = false) @Valid NoticeDTO noticeDTO) {
        if (noticeDTO == null) {
            return R.fail("请求参数不能为空");
        }
        Notice notice = new Notice();
        org.springframework.beans.BeanUtils.copyProperties(noticeDTO, notice);
        boolean b = noticeService.save(notice);
        return b ? R.ok() : R.fail();
    }

    @DeleteMapping("/{id}")
    @ApiOperation(value = "删除数据",notes = "根据id删除数据",httpMethod = "DELETE")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id",value = "查询的id", paramType = "path",required = true,dataType = "Integer"),
    })
    public R<?> delete(@PathVariable Integer id) {
        boolean b = noticeService.removeById(id);
        return b ? R.ok() : R.fail();
    }

    @PutMapping
    @ApiOperation(value = "更新数据",notes = "根据内容更新数据",httpMethod = "PUT")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "notice",value = "更新的notice实例",paramType = "body",dataType = "Notice",required = true)
    })
    public R<?> update(@RequestBody(required = false) @Valid NoticeDTO noticeDTO) {
        if (noticeDTO == null) {
            return R.fail("请求参数不能为空");
        }
        Notice notice = new Notice();
        org.springframework.beans.BeanUtils.copyProperties(noticeDTO, notice);
        boolean b = noticeService.updateById(notice);
        return b ? R.ok() : R.fail();
    }

    @GetMapping("/{id}")
    @ApiOperation(value = "获取单个值",notes = "查看单个项目的内容",httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id",value = "查询的id", paramType = "path",required = true,dataType = "Integer",defaultValue = "0")
    })
    public R<?> detail(@PathVariable Integer id) {
        Notice notice = noticeService.getById(id);
        return R.ok(notice);
    }
}