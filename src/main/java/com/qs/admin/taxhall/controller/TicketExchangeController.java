package com.qs.admin.taxhall.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.qs.admin.common.core.Query;
import com.qs.admin.common.domain.R;
import com.qs.admin.taxhall.model.TicketExchange;
import com.qs.admin.taxhall.model.dto.TicketExchangeDTO;
import com.qs.admin.taxhall.service.TicketExchangeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;


import javax.validation.Valid;
import java.util.Map;

/**
* <AUTHOR>
* @data 2020-04-07.
*/
@RestController
@RequestMapping("/api/v1/ticket-exchange")
@Tag(name = "ticket-exchanges", description = "票据兑换相关的RESTful接口")
public class TicketExchangeController {
@Autowired
private TicketExchangeService ticketExchangeService;

@GetMapping("/page")
@Operation(summary = "分页查询票据兑换", description = "分页条件查询票据兑换数据")
public R<?> page(@Parameter(name = "page", description = "查询页码", example = "1") @RequestParam(defaultValue = "1") Integer page,
                @Parameter(name = "pageSize", description = "每页数据量", example = "10") @RequestParam(defaultValue = "10") Integer pageSize) {
    Page<TicketExchange> ticketExchangePage = new Page<>(page, pageSize);
    QueryWrapper<TicketExchange> wrapper = new QueryWrapper<>();
    IPage<TicketExchange> ticketExchangeIPage = ticketExchangeService.page(ticketExchangePage, wrapper);
    return R.ok(ticketExchangeIPage);
}

@PostMapping
@Operation(summary = "添加数据", description = "添加新的数据")
public R<?> add(@RequestBody @Valid TicketExchangeDTO ticketExchangeDTO) {
    TicketExchange ticketExchange = new TicketExchange();
    org.springframework.beans.BeanUtils.copyProperties(ticketExchangeDTO, ticketExchange);
    boolean b = ticketExchangeService.save(ticketExchange);
    return b ? R.ok() : R.fail();
}

@DeleteMapping("/{id}")
@Operation(summary = "删除数据", description = "根据id删除数据")
public R<?> delete(@Parameter(name = "id", description = "查询的id", example = "1") @PathVariable Integer id) {
    boolean b = ticketExchangeService.removeById(id);
    return b ? R.ok() : R.fail();
}

@PutMapping
@Operation(summary = "更新数据", description = "根据内容更新数据")
public R<?> update(@RequestBody @Valid TicketExchangeDTO ticketExchangeDTO) {
    TicketExchange ticketExchange = new TicketExchange();
    org.springframework.beans.BeanUtils.copyProperties(ticketExchangeDTO, ticketExchange);
    boolean b = ticketExchangeService.updateById(ticketExchange);
    return b ? R.ok() : R.fail();
}

@GetMapping("/{id}")
@Operation(summary = "获取单个值", description = "查看单个项目的内容")
public R<?> detail(@Parameter(name = "id", description = "查询的id", example = "1") @PathVariable Integer id) {
    TicketExchange ticketExchange = ticketExchangeService.getById(id);
    return R.ok(ticketExchange);
}
}