package com.qs.admin.taxhall.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.qs.admin.common.core.Query;
import com.qs.admin.common.domain.R;
//import com.qs.admin.common.datasource.DBIdentifier;
//import com.qs.admin.common.datasource.ProjectDBMgr;
import com.qs.admin.common.shiro.LoginAccount;
import com.qs.admin.common.shiro.security.AccountContext;
import com.qs.admin.common.utils.QscUtil;
//import com.qs.admin.system.service.QscAccountService;
import com.qs.admin.taxhall.model.Employee;
import com.qs.admin.taxhall.model.System;
import com.qs.admin.taxhall.model.dto.BatchDeleteDTO;
import com.qs.admin.taxhall.model.dto.EmployeeDTO;
import com.qs.admin.taxhall.model.dto.PageQueryDTO;
import com.qs.admin.taxhall.service.EmployeeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.util.List;
import java.util.ArrayList;
import java.util.Collections;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @data 2020-04-07.
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/employee")
@Api(tags = "员工管理", description = "员工相关的RESTful接口")
public class EmployeeController {

    @Autowired
    private EmployeeService employeeService;

    @GetMapping("/page")
    @ApiOperation(value = "分页查询员工", notes = "分页条件查询员工数据", httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageNum", value = "查询页码", paramType = "query", dataType = "Integer", defaultValue = "1"),
            @ApiImplicitParam(name = "pageSize", value = "每页数据量", paramType = "query", dataType = "Integer", defaultValue = "10")
    })
    public R<?> page(@RequestParam(defaultValue = "1") Integer pageNum,
                    @RequestParam(defaultValue = "10") Integer pageSize) {
        Page<Employee> employeePage = new Page<>(pageNum, pageSize);
        QueryWrapper<Employee> wrapper = new QueryWrapper<>();
        IPage<Employee> employeeIPage = employeeService.page(employeePage, wrapper);
        return R.ok(employeeIPage);
    }

    @PostMapping
    @ApiOperation(value = "添加数据", notes = "添加新的数据", httpMethod = "POST")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "params", value = "待添加的employee实例", paramType = "body", dataType = "Employee", required = true)
    })
    public R<?> add(@RequestBody(required = false) @Valid EmployeeDTO employeeDTO) {
        if (employeeDTO == null) {
            return R.fail("请求参数不能为空");
        }
        Employee employee = new Employee();
        org.springframework.beans.BeanUtils.copyProperties(employeeDTO, employee);
        boolean b = employeeService.save(employee);
        return b ? R.ok() : R.fail();
    }

    @DeleteMapping("/{unitCode}/{uid}")
    @ApiOperation(value = "删除数据", notes = "根据id删除数据", httpMethod = "DELETE")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uid", value = "查询的id", paramType = "path", required = true, dataType = "String")
    })
    public R<?> delete(@NotBlank(message = "{required}") @PathVariable String unitCode, @PathVariable String uid) {
//        LoginAccount account = AccountContext.getCurrentAccount();
//        String unitCode = accountService.findByUserName(account.getUsername()).getUnitCode();

        //DBIdentifier.setUnitCode(unitCode);
        String[] ids = uid.split(StringPool.COMMA);
        ArrayList<String> idList = new ArrayList<String>(ids.length);
        Collections.addAll(idList, ids);
        boolean b = employeeService.removeByIds(idList);

        return b ? R.ok() : R.fail();
    }

    @PutMapping
    @ApiOperation(value = "更新数据", notes = "根据内容更新数据", httpMethod = "PUT")
    public R<?> update(@RequestBody @Valid EmployeeDTO employeeDTO) {
        Employee employee = new Employee();
        org.springframework.beans.BeanUtils.copyProperties(employeeDTO, employee);
        boolean b = employeeService.updateById(employee);

        return b ? R.ok() : R.fail();
    }

    @PostMapping("/batchDelete")
    @ApiOperation(value = "批量删除数据", notes = "根据id批量删除数据", httpMethod = "POST")
    public R<?> batchDelete(@RequestBody @Valid BatchDeleteDTO batchDeleteDTO) {
        List<String> ids = batchDeleteDTO.getIds().stream().map(String::valueOf).collect(Collectors.toList());
        boolean b = employeeService.deleteEmpolyeeByIds(ids);
        return b ? R.ok() : R.fail();
    }

    @GetMapping("/{unitCode}/{id}")
    @ApiOperation(value = "获取单个值", notes = "查看单个项目的内容", httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "查询的id", paramType = "path", required = true, dataType = "Integer", defaultValue = "0")
    })
    public R<?> detail(@PathVariable String unitCode, @PathVariable Integer id) {
//        LoginAccount account = AccountContext.getCurrentAccount();
//        String unitCode = accountService.findByUserName(account.getUsername()).getUnitCode();
        //DBIdentifier.setUnitCode(unitCode);
        Employee employee = employeeService.getById(id);
        return R.ok(employee);
    }
}