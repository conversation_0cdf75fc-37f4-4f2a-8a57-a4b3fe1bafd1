package com.qs.admin.taxhall.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.qs.admin.common.domain.R;
import com.qs.admin.taxhall.model.Employee;
import com.qs.admin.taxhall.model.dto.EmployeeDTO;
import com.qs.admin.taxhall.service.EmployeeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;
import org.springframework.web.multipart.MultipartFile;
import com.qs.admin.common.utils.FileUtil;
import java.io.File;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.UUID;

/**
 * <AUTHOR>
 * @data 2020-04-07.
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/employee")
@Tag(name = "员工管理", description = "员工相关的RESTful接口")
public class EmployeeController {

    @Autowired
    private EmployeeService employeeService;

    @GetMapping("/page")
    @Operation(summary = "分页查询员工", description = "分页条件查询员工数据")
    public R<?> page(@Parameter(name = "page", description = "查询页码", example = "1") @RequestParam(defaultValue = "1") Integer page,
                    @Parameter(name = "pageSize", description = "每页数据量", example = "10") @RequestParam(defaultValue = "10") Integer pageSize) {
        Page<Employee> employeePage = new Page<>(page, pageSize);
        QueryWrapper<Employee> wrapper = new QueryWrapper<>();
        IPage<Employee> employeeIPage = employeeService.page(employeePage, wrapper);
        return R.ok(employeeIPage);
    }

    @GetMapping("/list")
    @Operation(summary = "获取所有员工列表", description = "获取所有员工的简化信息，用于下拉框等场景")
    public R<?> list() {
        QueryWrapper<Employee> wrapper = new QueryWrapper<>();
        wrapper.select("uid", "name", "username"); // 只查询必要字段
        List<Employee> employeeList = employeeService.list(wrapper);
        return R.ok(employeeList);
    }

    @GetMapping("/options")
    @Operation(summary = "获取员工选项", description = "获取员工ID和姓名的键值对，专用于下拉框")
    public R<?> options() {
        QueryWrapper<Employee> wrapper = new QueryWrapper<>();
        wrapper.select("uid", "name"); // 只查询ID和姓名
        List<Employee> employeeList = employeeService.list(wrapper);

        // 转换为键值对格式
        List<Object> options = employeeList.stream().map(emp -> {
            return new Object() {
                public final Integer value = emp.getUid();
                public final String label = emp.getName();
                public final String text = emp.getName(); // 兼容不同前端框架
            };
        }).collect(Collectors.toList());

        return R.ok(options);
    }

    @GetMapping
    @Operation(summary = "获取所有员工", description = "获取所有员工信息（RESTful风格）")
    public R<?> getAll() {
        List<Employee> employeeList = employeeService.list();
        return R.ok(employeeList);
    }

    @PostMapping
    @Operation(summary = "添加数据", description = "添加新的数据")
    public R<?> add(@Valid EmployeeDTO employeeDTO) {
        if (employeeDTO == null) {
            return R.fail("请求参数不能为空");
        }
        Employee employee = new Employee();
        org.springframework.beans.BeanUtils.copyProperties(employeeDTO, employee);
        boolean b = employeeService.save(employee);
        return b ? R.ok() : R.fail();
    }

    @PutMapping
    @Operation(summary = "更新数据", description = "根据内容更新数据")
    public R<?> update(@Valid EmployeeDTO employeeDTO) {
        Employee employee = new Employee();
        BeanUtils.copyProperties(employeeDTO, employee);
        boolean b = employeeService.updateById(employee);
        return b ? R.ok() : R.fail();
    }

    @PostMapping("/batchDelete")
    @Operation(summary = "批量删除数据", description = "根据id批量删除数据")
    public R<?> batchDelete(@Parameter(name = "ids", description = "待删除的ID列表，用逗号分隔", example = "1,2,3") @RequestParam("ids") String idsParam) {
        if (idsParam == null || idsParam.trim().isEmpty()) {
            return R.fail("ID列表不能为空");
        }
        // 解析逗号分隔的ID字符串
        String[] idArray = idsParam.split(",");
        List<String> ids = new ArrayList<>();
        for (String id : idArray) {
            if (id != null && !id.trim().isEmpty()) {
                ids.add(id.trim());
            }
        }
        
        if (ids.isEmpty()) {
            return R.fail("没有有效的ID");
        }
        
        boolean b = employeeService.deleteEmpolyeeByIds(ids);
        
        if (b) {
            Map<String, Object> result = new HashMap<>();
            result.put("deletedCount", ids.size());
            result.put("deletedIds", ids);
            return R.ok(result);
        } else {
            return R.fail("删除失败");
        }
    }

    @GetMapping("/{unitCode}/{id}")
    @Operation(summary = "获取单个值", description = "查看单个项目的内容")
    public R<?> detail(@Parameter(name = "unitCode", description = "单位代码") @PathVariable String unitCode, 
                      @Parameter(name = "id", description = "查询的id", example = "1") @PathVariable Integer id) {
        Employee employee = employeeService.getById(id);
        return R.ok(employee);
    }

    @PostMapping("/uploadAvatar")
    @Operation(summary = "上传员工头像", description = "上传员工头像图片文件")
    public R<?> uploadAvatar(
            @Parameter(name = "file", description = "头像文件") @RequestParam("file") MultipartFile file) {
        
        if (file.isEmpty()) {
            return R.fail("请选择要上传的文件");
        }
        
        // 检查文件类型
        String originalFilename = file.getOriginalFilename();
        if (originalFilename == null || originalFilename.isEmpty()) {
            return R.fail("文件名不能为空");
        }
        
        String fileExtension = FileUtil.getFileExt(originalFilename).toLowerCase();
        String[] allowedExtensions = {"jpg", "jpeg", "png", "gif", "bmp"};
        boolean isValidType = false;
        for (String ext : allowedExtensions) {
            if (ext.equals(fileExtension)) {
                isValidType = true;
                break;
            }
        }
        
        if (!isValidType) {
            return R.fail("只支持上传 jpg、jpeg、png、gif、bmp 格式的图片文件");
        }
        
        // 检查文件大小（限制为20MB）
        if (file.getSize() > 20 * 1024 * 1024) {
            return R.fail("文件大小不能超过20MB");
        }
        
        try {
            // 生成文件名
            String fileName = UUID.randomUUID().toString() + "." + fileExtension;
            
            // 直接保存到employee目录
            String relativePath = "employee/";
            String fullPath = FileUtil.fileUploadPath + relativePath;
            
            // 创建目录
            File directory = new File(fullPath);
            if (!directory.exists()) {
                directory.mkdirs();
            }
            
            // 保存文件
            File targetFile = new File(fullPath + fileName);
            file.transferTo(targetFile);
            
            // 返回图片URL地址
            String imageUrl = relativePath + fileName;
            String fullUrl = FileUtil.getFileUrl(imageUrl);
            
            Map<String, Object> result = new HashMap<>();
            result.put("imageUrl", imageUrl);
            result.put("fullUrl", fullUrl);
            return R.ok(result);
            
        } catch (IOException e) {
            log.error("文件上传失败", e);
            return R.fail("文件上传失败：" + e.getMessage());
        } catch (Exception e) {
            log.error("上传头像时发生错误", e);
            return R.fail("上传失败：" + e.getMessage());
        }
    }

}