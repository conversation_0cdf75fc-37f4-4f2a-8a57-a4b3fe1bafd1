package com.qs.admin.taxhall.controller;

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.qs.admin.common.core.Query;
import com.qs.admin.common.domain.R;
import com.qs.admin.common.utils.DataExportUtil;
//import com.qs.admin.common.datasource.DBIdentifier;
import com.qs.admin.taxhall.model.TicketLog;
import com.qs.admin.taxhall.model.dto.TicketLogDTO;
import com.qs.admin.taxhall.service.TicketLogService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;


import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Map;

/**
 * <AUTHOR>
 * @data 2020-04-07.
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/ticket-log")
@Tag(name = "票据日志管理", description = "票据日志相关的RESTful接口")
public class TicketLogController {

    @Autowired
    private TicketLogService ticketLogService;

    @GetMapping("/page")
    @Operation(summary = "分页查询票据日志", description = "分页条件查询票据日志数据")
    public R<?> page(@Parameter(name = "page", description = "查询页码", example = "1") @RequestParam(defaultValue = "1") Integer page,
                    @Parameter(name = "pageSize", description = "每页数据量", example = "10") @RequestParam(defaultValue = "10") Integer pageSize,
                    @Parameter(name = "ticketLog", description = "查询条件(JSON格式)") @RequestParam(required = false) String ticketLog,
                     @Parameter(name = "unitCode", description = "单位代码") @RequestParam(required = false) String unitCode) {
        try {
            // 限制每页最大数据量，防止单次查询过多数据
            if (pageSize > 500) {
                log.warn("请求的每页数据量过大: {}, 限制为500条", pageSize);
                pageSize = 500;
            }
            
            Page<TicketLog> ticketLogPage = new Page<>(page, pageSize);
            TicketLog ticketLogCondition = null;
            
            if (ticketLog != null && !ticketLog.trim().isEmpty()) {
                try {
                    ticketLogCondition = JSONObject.parseObject(ticketLog, TicketLog.class);
                } catch (Exception e) {
                    log.error("解析查询条件失败: {}", e.getMessage());
                    return R.fail("查询条件格式错误");
                }
            }
            
            log.info("开始查询票据日志 - 页码: {}, 每页: {}条", page, pageSize);
            long startTime = System.currentTimeMillis();
            
            IPage<TicketLog> ticketLogIPage = ticketLogService.page(ticketLogPage, ticketLogCondition);
            
            long endTime = System.currentTimeMillis();
            long queryTime = endTime - startTime;
            
            // 记录查询性能
            log.info("查询完成 - 耗时: {}ms, 总记录: {}条, 当前页: {}条", 
                    queryTime, 
                    ticketLogIPage.getTotal(), 
                    ticketLogIPage.getRecords() != null ? ticketLogIPage.getRecords().size() : 0);
            
            // 如果查询时间过长或数据量过大，给出提示
            if (queryTime > 5000) {
                log.warn("查询耗时过长: {}ms，建议优化查询条件", queryTime);
            }
            
            if (ticketLogIPage.getTotal() > 50000) {
                log.warn("查询结果数据量过大: {}条，可能影响响应性能", ticketLogIPage.getTotal());
            }
            
            return R.ok(ticketLogIPage);
            
        } catch (Exception e) {
            log.error("查询票据日志失败: {}", e.getMessage(), e);
            return R.fail("查询失败，请稍后重试");
        }
    }

    @GetMapping("/{id}")
    @Operation(summary = "获取详情", description = "返回单个数据")
    public R<?> detail(@Parameter(name = "id", description = "主键", example = "1") @PathVariable Integer id) {
        return R.ok(ticketLogService.getById(id));
    }

    @PostMapping
    @Operation(summary = "添加", description = "添加数据")
    public R<?> add(@RequestBody @Valid TicketLogDTO ticketLogDTO) {
        TicketLog ticketLog = new TicketLog();
        org.springframework.beans.BeanUtils.copyProperties(ticketLogDTO, ticketLog);
        return ticketLogService.save(ticketLog) ? R.ok() : R.fail();
    }

    @PutMapping
    @Operation(summary = "更新", description = "更新数据")
    public R<?> update(@RequestBody @Valid TicketLogDTO ticketLogDTO) {
        TicketLog ticketLog = new TicketLog();
        org.springframework.beans.BeanUtils.copyProperties(ticketLogDTO, ticketLog);
        return ticketLogService.saveOrUpdate(ticketLog) ? R.ok() : R.fail();
    }
    
}