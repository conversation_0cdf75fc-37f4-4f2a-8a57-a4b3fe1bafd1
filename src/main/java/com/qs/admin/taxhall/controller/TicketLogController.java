package com.qs.admin.taxhall.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.qs.admin.common.core.Query;
import com.qs.admin.common.domain.R;
//import com.qs.admin.common.datasource.DBIdentifier;
import com.qs.admin.taxhall.model.TicketLog;
import com.qs.admin.taxhall.model.dto.TicketLogDTO;
import com.qs.admin.taxhall.service.TicketLogService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.validation.Valid;
import java.util.Map;

/**
 * <AUTHOR>
 * @data 2020-04-07.
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/ticket-log")
@Api(tags = "票据日志管理", description = "票据日志相关的RESTful接口")
public class TicketLogController {

    @Autowired
    private TicketLogService ticketLogService;

    @GetMapping("/page")
    @ApiOperation(value = "分页查询票据日志", notes = "分页条件查询票据日志数据", httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageNum", value = "查询页码", paramType = "query", dataType = "Integer", defaultValue = "1"),
            @ApiImplicitParam(name = "pageSize", value = "每页数据量", paramType = "query", dataType = "Integer", defaultValue = "10"),
            @ApiImplicitParam(name = "ticketLog", value = "查询条件(JSON格式)", paramType = "query", dataType = "String", required = false)
    })
    public R<?> page(@RequestParam(defaultValue = "1") Integer pageNum,
                    @RequestParam(defaultValue = "10") Integer pageSize,
                    @RequestParam(required = false) String ticketLog) {
        Page<TicketLog> ticketLogPage = new Page<>(pageNum, pageSize);
        TicketLog ticketLogCondition = null;
        if (ticketLog != null && !ticketLog.trim().isEmpty()) {
            ticketLogCondition = JSONObject.parseObject(ticketLog, TicketLog.class);
        }
        IPage<TicketLog> ticketLogIPage = ticketLogService.page(ticketLogPage, ticketLogCondition);
        return R.ok(ticketLogIPage);
    }

    @GetMapping("/{id}")
    @ApiOperation(value = "获取详情", notes = "返回单个数据", httpMethod = "GET")
    @ApiImplicitParam(name = "id", value = "主键", paramType = "path", dataType = "Integer", required = true)
    public R<?> detail(@PathVariable Integer id) {
        return R.ok(ticketLogService.getById(id));
    }

    @PostMapping
    @ApiOperation(value = "添加", notes = "添加数据", httpMethod = "POST")
    public R<?> add(@RequestBody @Valid TicketLogDTO ticketLogDTO) {
        TicketLog ticketLog = new TicketLog();
        org.springframework.beans.BeanUtils.copyProperties(ticketLogDTO, ticketLog);
        return ticketLogService.save(ticketLog) ? R.ok() : R.fail();
    }

    @PutMapping
    @ApiOperation(value = "更新", notes = "更新数据", httpMethod = "PUT")
    public R<?> update(@RequestBody @Valid TicketLogDTO ticketLogDTO) {
        TicketLog ticketLog = new TicketLog();
        org.springframework.beans.BeanUtils.copyProperties(ticketLogDTO, ticketLog);
        return ticketLogService.saveOrUpdate(ticketLog) ? R.ok() : R.fail();
    }
}