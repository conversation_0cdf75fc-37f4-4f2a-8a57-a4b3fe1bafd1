package com.qs.admin.taxhall.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.qs.admin.common.core.Query;
import com.qs.admin.common.domain.R;
//import com.qs.admin.common.datasource.DBIdentifier;
import com.qs.admin.taxhall.model.TicketLog;
import com.qs.admin.taxhall.model.dto.TicketLogDTO;
import com.qs.admin.taxhall.service.TicketLogService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.validation.Valid;
import java.util.Map;

/**
 * <AUTHOR>
 * @data 2020-04-07.
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/ticket-log")
@Api(tags = "票据日志管理", description = "票据日志相关的RESTful接口")
public class TicketLogController {

    @Autowired
    private TicketLogService ticketLogService;

    @GetMapping
    @ApiOperation(value = "获取全部", notes = "返回分页过后的数据", httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "查询页码", paramType = "query", dataType = "Integer", defaultValue = "1"),
            @ApiImplicitParam(name = "pageSize", value = "每页数据量", paramType = "query", dataType = "Integer", defaultValue = "10")
    })
    public R<?> list(@ApiIgnore @RequestParam Map<String, Object> params) {
    //        DBIdentifier.setUnitCode((String) params.get("unitCode"));
        Page ticketLogPage = new Query<>(params);
        TicketLog ticketLog = null;
        String ticketLogParam = (String) params.get("ticketLog");
        if (ticketLogParam != null && !ticketLogParam.trim().isEmpty()) {
            ticketLog = JSONObject.parseObject(ticketLogParam, TicketLog.class);
        }
        IPage<TicketLog> ticketLogIPage = ticketLogService.page(ticketLogPage, ticketLog);
        return R.ok(ticketLogIPage);
    }

    @GetMapping("/{id}")
    @ApiOperation(value = "获取详情", notes = "返回单个数据", httpMethod = "GET")
    @ApiImplicitParam(name = "id", value = "主键", paramType = "path", dataType = "Integer", required = true)
    public R<?> detail(@PathVariable Integer id) {
        return R.ok(ticketLogService.getById(id));
    }

    @PostMapping
    @ApiOperation(value = "添加", notes = "添加数据", httpMethod = "POST")
    public R<?> add(@RequestBody @Valid TicketLogDTO ticketLogDTO) {
        TicketLog ticketLog = new TicketLog();
        org.springframework.beans.BeanUtils.copyProperties(ticketLogDTO, ticketLog);
        return ticketLogService.save(ticketLog) ? R.ok() : R.fail();
    }

    @PutMapping
    @ApiOperation(value = "更新", notes = "更新数据", httpMethod = "PUT")
    public R<?> update(@RequestBody @Valid TicketLogDTO ticketLogDTO) {
        TicketLog ticketLog = new TicketLog();
        org.springframework.beans.BeanUtils.copyProperties(ticketLogDTO, ticketLog);
        return ticketLogService.saveOrUpdate(ticketLog) ? R.ok() : R.fail();
    }
}