package com.qs.admin.taxhall.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.qs.admin.common.domain.R;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.qs.admin.common.core.Query;

import com.qs.admin.taxhall.model.TicketBook;
import com.qs.admin.taxhall.model.dto.TicketBookDTO;
import com.qs.admin.taxhall.service.TicketBookService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;

import org.springframework.web.bind.annotation.*;



import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Map;

/**
 * <AUTHOR>
 * @data 2020-04-07.
 */
@RestController
@RequestMapping("/api/v1/ticket-books")
@Tag(name = "票据预约管理", description = "票据预约相关的RESTful接口")
public class TicketBookController {
    @Resource
    private TicketBookService ticketBookService;

    @GetMapping("/page")
    @Operation(summary = "分页查询票据预约", description = "分页条件查询票据预约数据")
    public R<?> page(@Parameter(name = "page", description = "查询页码", example = "1") @RequestParam(defaultValue = "1") Integer page,
                    @Parameter(name = "pageSize", description = "每页数据量", example = "10") @RequestParam(defaultValue = "10") Integer pageSize) {
        Page<TicketBook> ticketBookPage = new Page<>(page, pageSize);
        QueryWrapper<TicketBook> wrapper = new QueryWrapper<>();
        IPage<TicketBook> ticketBookIPage = ticketBookService.page(ticketBookPage, wrapper);
        return R.ok(ticketBookIPage);
    }
    @PostMapping
    @Operation(summary = "添加数据", description = "添加新的数据")
    public R<?> add(@RequestBody @Valid TicketBookDTO ticketBookDTO) {
        TicketBook ticketBook = new TicketBook();
        org.springframework.beans.BeanUtils.copyProperties(ticketBookDTO, ticketBook);
        boolean b =  ticketBookService.save(ticketBook);
        return R.ok(b);
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "删除数据", description = "根据id删除数据")
    public R<?> delete(@Parameter(name = "id", description = "查询的id", example = "1") @PathVariable Integer id) {
        boolean b = ticketBookService.removeById(id);
        return R.ok(b);
    }

    @PutMapping
    @Operation(summary = "更新数据", description = "根据内容更新数据")
    public R<?> update(@RequestBody @Valid TicketBookDTO ticketBookDTO) {
        TicketBook ticketBook = new TicketBook();
        org.springframework.beans.BeanUtils.copyProperties(ticketBookDTO, ticketBook);
        boolean b = ticketBookService.updateById(ticketBook);
        return R.ok(b);
    }

    @GetMapping("/{id}")
    @Operation(summary = "获取单个值", description = "查看单个项目的内容")
    public R<?> detail(@Parameter(name = "id", description = "查询的id", example = "1") @PathVariable Integer id) {
        TicketBook ticketBook = ticketBookService.getById(id);
        return R.ok(ticketBook);
    }
}