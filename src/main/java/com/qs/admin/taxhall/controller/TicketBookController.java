package com.qs.admin.taxhall.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.qs.admin.common.domain.R;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.qs.admin.common.core.Query;

import com.qs.admin.taxhall.model.TicketBook;
import com.qs.admin.taxhall.model.dto.TicketBookDTO;
import com.qs.admin.taxhall.service.TicketBookService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;

import org.springframework.web.bind.annotation.*;

import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Map;

/**
 * <AUTHOR>
 * @data 2020-04-07.
 */
@RestController
@RequestMapping("/api/v1/ticket-books")
@Api(tags = "票据预约管理", description = "票据预约相关的RESTful接口")
public class TicketBookController {
    @Resource
    private TicketBookService ticketBookService;

    @GetMapping("/page")
    @ApiOperation(value = "分页查询票据预约", notes = "分页条件查询票据预约数据", httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageNum", value = "查询页码", paramType = "query", dataType = "Integer", defaultValue = "1"),
            @ApiImplicitParam(name = "pageSize", value = "每页数据量", paramType = "query", dataType = "Integer", defaultValue = "10")
    })
    public R<?> page(@RequestParam(defaultValue = "1") Integer pageNum,
                    @RequestParam(defaultValue = "10") Integer pageSize) {
        Page<TicketBook> ticketBookPage = new Page<>(pageNum, pageSize);
        QueryWrapper<TicketBook> wrapper = new QueryWrapper<>();
        IPage<TicketBook> ticketBookIPage = ticketBookService.page(ticketBookPage, wrapper);
        return R.ok(ticketBookIPage);
    }
    @PostMapping
    @ApiOperation(value = "添加数据",notes = "添加新的数据",httpMethod = "POST")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "ticketBookDTO",value = "待添加的ticketBookDTO实例",paramType = "body",dataType = "TicketBookDTO",required = true)
    })
    public R<?> add(@RequestBody @Valid TicketBookDTO ticketBookDTO) {
        TicketBook ticketBook = new TicketBook();
        org.springframework.beans.BeanUtils.copyProperties(ticketBookDTO, ticketBook);
        boolean b =  ticketBookService.save(ticketBook);
        return R.ok(b);
    }

    @DeleteMapping("/{id}")
    @ApiOperation(value = "删除数据",notes = "根据id删除数据",httpMethod = "DELETE")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id",value = "查询的id", paramType = "path",required = true,dataType = "Integer"),
    })
    public R<?> delete(@PathVariable Integer id) {
        boolean b = ticketBookService.removeById(id);
        return R.ok(b);
    }

    @PutMapping
    @ApiOperation(value = "更新数据",notes = "根据内容更新数据",httpMethod = "PUT")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "ticketBookDTO",value = "更新的ticketBookDTO实例",paramType = "body",dataType = "TicketBookDTO",required = true)
    })
    public R<?> update(@RequestBody @Valid TicketBookDTO ticketBookDTO) {
        TicketBook ticketBook = new TicketBook();
        org.springframework.beans.BeanUtils.copyProperties(ticketBookDTO, ticketBook);
        boolean b = ticketBookService.updateById(ticketBook);
        return R.ok(b);
    }

    @GetMapping("/{id}")
    @ApiOperation(value = "获取单个值",notes = "查看单个项目的内容",httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id",value = "查询的id", paramType = "path",required = true,dataType = "Integer",defaultValue = "0")
    })
    public R<?> detail(@PathVariable Integer id) {
        TicketBook ticketBook = ticketBookService.getById(id);
        return R.ok(ticketBook);
    }
}