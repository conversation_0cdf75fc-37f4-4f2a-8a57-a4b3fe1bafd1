package com.qs.admin.taxhall.controller;


import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.qs.admin.common.annotation.Log;
import com.qs.admin.common.domain.R;
import com.qs.admin.taxhall.model.Business;
import com.qs.admin.taxhall.model.dto.BatchDeleteDTO;
import com.qs.admin.taxhall.model.dto.BusinessDTO;
import com.qs.admin.taxhall.model.dto.BusinessQueryDTO;
import com.qs.admin.taxhall.service.BusinessService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.util.*;
import java.util.stream.Collectors;
import com.baomidou.mybatisplus.core.toolkit.StringPool;

/**
 * <AUTHOR>
 * @data 2020-04-07.
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/business")
@Tag(name = "业务管理", description = "业务相关的RESTful接口")
public class BusinessController {

    @Autowired
    private BusinessService businessService;


    @Log("查询业务数据")
    @GetMapping("/page")
    @Operation(summary = "分页查询业务", description = "分页条件查询业务数据")
    public R<?> page(@Parameter(name = "page", description = "查询页码", example = "1") @RequestParam(defaultValue = "1") Integer page,
                     @Parameter(name = "pageSize", description = "每页数据量", example = "10") @RequestParam(defaultValue = "10") Integer pageSize,
                     @Parameter(name = "business", description = "查询条件(JSON格式)") @RequestParam(required = false) String business,
                     @Parameter(name = "unitCode", description = "单位代码") @RequestParam(required = false) String unitCode) {
        try {
            Page<Business> businessPage = new Page<>(page, pageSize);
            QueryWrapper<Business> wrapper = new QueryWrapper<>();
            
            // 解析查询条件
            if (business != null && !business.trim().isEmpty()) {
                try {
                    Business businessCondition = JSONObject.parseObject(business, Business.class);
                    if (businessCondition.getName() != null) {
                        wrapper.like("NAME", businessCondition.getName());
                    }
                    if (businessCondition.getPrefix() != null) {
                        wrapper.like("PREFIX", businessCondition.getPrefix());
                    }
                    if (businessCondition.getEnabled() != null) {
                        wrapper.eq("ENABLED", businessCondition.getEnabled());
                    }
                } catch (Exception e) {
                    log.warn("Failed to parse business condition: " + e.getMessage());
                }
            }
            
            // 添加单位代码条件
            if (unitCode != null && !unitCode.trim().isEmpty()) {
                wrapper.eq("UNIT_CODE", unitCode);
            }
            
            IPage<Business> result = businessService.page(businessPage, wrapper);
            return R.ok(result);
        } catch (Exception e) {
            log.error("查询业务数据失败: " + e.getMessage(), e);
            return R.fail("查询失败: " + e.getMessage());
        }
    }

    @GetMapping("/list")
    @Operation(summary = "获取所有业务列表", description = "获取所有业务的简化信息，用于下拉框等场景")
    public R<?> list() {
        try {
            QueryWrapper<Business> wrapper = new QueryWrapper<>();
            wrapper.select("uid", "name", "description", "enabled"); // 只查询必要字段
            wrapper.eq("enabled", 1); // 只查询启用的业务
            List<Business> businessList = businessService.list(wrapper);
            return R.ok(businessList);
        } catch (Exception e) {
            log.error("获取业务列表失败: " + e.getMessage(), e);
            return R.fail("获取业务列表失败: " + e.getMessage());
        }
    }

    @GetMapping("/options")
    @Operation(summary = "获取业务选项", description = "获取业务ID和名称的键值对，专用于下拉框")
    public R<?> options() {
        try {
            QueryWrapper<Business> wrapper = new QueryWrapper<>();
            wrapper.select("uid", "name"); // 只查询ID和名称
            wrapper.eq("enabled", 1); // 只查询启用的业务
            List<Business> businessList = businessService.list(wrapper);

            // 转换为键值对格式
            List<Object> options = businessList.stream().map(business -> {
                return new Object() {
                    public final Integer value = business.getUid();
                    public final String label = business.getName();
                    public final String text = business.getName(); // 兼容不同前端框架
                };
            }).collect(Collectors.toList());

            return R.ok(options);
        } catch (Exception e) {
            log.error("获取业务选项失败: " + e.getMessage(), e);
            return R.fail("获取业务选项失败: " + e.getMessage());
        }
    }

    @GetMapping
    @Operation(summary = "获取所有业务", description = "获取所有业务信息（RESTful风格）")
    public R<?> getAll() {
        try {
            List<Business> businessList = businessService.list();
            return R.ok(businessList);
        } catch (Exception e) {
            log.error("获取所有业务失败: " + e.getMessage(), e);
            return R.fail("获取业务列表失败: " + e.getMessage());
        }
    }

    @GetMapping("/{id}")
    @Operation(summary = "获取业务详情", description = "根据ID获取业务详情")
    public R<?> detail(@Parameter(name = "id", description = "业务ID", example = "1") @PathVariable Integer id) {
        try {
            if (id == null) {
                return R.fail("业务ID不能为空");
            }
            Business business = businessService.getById(id);
            if (business == null) {
                return R.fail("业务不存在");
            }
            return R.ok(business);
        } catch (Exception e) {
            log.error("获取业务详情失败: " + e.getMessage(), e);
            return R.fail("获取业务详情失败: " + e.getMessage());
        }
    }

    @Log("添加业务数据")
    @PostMapping
    @Operation(summary = "创建新业务", description = "创建一个新的业务记录")
    public R<?> add(@Valid BusinessDTO businessDTO) {
        try {
            if (businessDTO == null) {
                return R.fail("请求参数不能为空");
            }
            Business business = new Business();
            BeanUtils.copyProperties(businessDTO, business);
            boolean result = businessService.save(business);
            if (result) {
                log.info("成功添加业务: {}", business.getName());
                return R.ok("业务添加成功");
            } else {
                return R.fail("业务添加失败");
            }
        } catch (Exception e) {
            log.error("添加业务失败: " + e.getMessage(), e);
            return R.fail("添加业务失败: " + e.getMessage());
        }
    }

    @PostMapping("/batchDelete")
    @Operation(summary = "批量删除数据", description = "根据id批量删除数据")
    public R<?> batchDelete(@Parameter(name = "ids", description = "待删除的ID列表，用逗号分隔", example = "1,2,3") @RequestParam("ids") String idsParam) {
        if (idsParam == null || idsParam.trim().isEmpty()) {
            return R.fail("ID列表不能为空");
        }
        // 解析逗号分隔的ID字符串
        String[] idArray = idsParam.split(",");
        List<String> ids = new ArrayList<>();
        for (String id : idArray) {
            if (id != null && !id.trim().isEmpty()) {
                ids.add(id.trim());
            }
        }
        
        if (ids.isEmpty()) {
            return R.fail("没有有效的ID");
        }
        
        // 将字符串ID转换为Integer类型
         List<Integer> intIds = new ArrayList<>();
         for (String idStr : ids) {
             try {
                 intIds.add(Integer.parseInt(idStr.trim()));
             } catch (NumberFormatException e) {
                 log.warn("无效的ID格式: {}", idStr);
             }
         }
        
        if (intIds.isEmpty()) {
            return R.fail("没有有效的ID");
        }
        
        QueryWrapper<Business> wrapper = new QueryWrapper<>();
        wrapper.in("UID", intIds);
        boolean b = businessService.remove(wrapper);
        
        if (b) {
            Map<String, Object> result = new HashMap<>();
            result.put("deletedCount", ids.size());
            result.put("deletedIds", ids);
            return R.ok(result);
        } else {
            return R.fail("删除失败");
        }
    }

    @Log("更新业务数据")
    @PutMapping
    @Operation(summary = "更新业务", description = "根据内容更新业务")
    public R<?> update(@Valid BusinessDTO businessDTO) {
        try {
            if (businessDTO == null) {
                return R.fail("请求参数不能为空");
            }
            if (businessDTO.getUid() == null) {
                return R.fail("业务ID不能为空");
            }
            
            Business business = new Business();
            BeanUtils.copyProperties(businessDTO, business);
            
            // 处理Boolean到Integer的转换
            if (businessDTO.getEnabled() != null) {
                business.setEnabled(businessDTO.getEnabled() ? 1 : 0);
            }
            if (businessDTO.getIsSpecial() != null) {
                business.setIsSpecial(businessDTO.getIsSpecial() ? 1 : 0);
            }
            
            boolean result = businessService.updateById(business);
            if (result) {
                log.info("更新业务成功，业务ID: {}, 业务名称: {}", businessDTO.getUid(), businessDTO.getName());
                return R.ok("更新成功");
            } else {
                return R.fail("更新失败，业务不存在");
            }
        } catch (Exception e) {
            log.error("更新业务失败: " + e.getMessage(), e);
            return R.fail("更新失败: " + e.getMessage());
        }
    }


}