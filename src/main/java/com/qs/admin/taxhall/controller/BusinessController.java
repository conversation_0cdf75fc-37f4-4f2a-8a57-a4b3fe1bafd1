package com.qs.admin.taxhall.controller;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.qs.admin.common.annotation.Log;
import com.qs.admin.common.constant.Constants;
import com.qs.admin.common.constant.SecurityConsts;
import com.qs.admin.common.core.Constant;
import com.qs.admin.common.domain.R;
import com.qs.admin.common.shiro.LoginAccount;
import com.qs.admin.common.shiro.security.AccountContext;
import com.qs.admin.common.utils.QscUtil;
import com.qs.admin.taxhall.model.Business;
import com.qs.admin.taxhall.model.dto.BatchDeleteDTO;
import com.qs.admin.taxhall.model.dto.BusinessDTO;
import com.qs.admin.taxhall.model.dto.BusinessQueryDTO;
import com.qs.admin.taxhall.service.BusinessService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.flogger.Flogger;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.security.Security;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @data 2020-04-07.
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/business")
@Api(tags = "业务管理", description = "业务相关的RESTful接口")
public class BusinessController {

    @Autowired
    private BusinessService businessService;


    @Log("查询业务数据")
    @GetMapping("/page")
    @ApiOperation(value = "分页查询业务", notes = "分页条件查询业务数据")
    public R<?> page(@Valid BusinessQueryDTO queryDTO) {
        QueryWrapper<Business> wrapper = new QueryWrapper<>();
        // 可根据queryDTO设置wrapper条件
        // wrapper.lambda().eq(...)
        // 分页参数
        Page<Business> page = new Page<>(queryDTO.getPageNum(), queryDTO.getPageSize());
        IPage<Business> result = businessService.page(page, wrapper);
        return R.ok(result);
    }

    @GetMapping("/{id}")
    @ApiOperation(value = "获取业务详情", notes = "根据ID获取业务详情")
    public R<?> detail(@PathVariable Integer id) {
        Business business = businessService.getById(id);
        return R.ok(business);
    }

    @Log("添加业务数据")
    @PostMapping
    @ApiOperation(value = "创建新业务", notes = "创建一个新的业务记录")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "params", value = "待添加的business实例", paramType = "body", dataType = "Business", required = true)
    })
    public R<?> add(@RequestBody(required = false) @Valid BusinessDTO businessDTO) {
        if (businessDTO == null) {
            return R.fail("请求参数不能为空");
        }
        Business business = new Business();
        org.springframework.beans.BeanUtils.copyProperties(businessDTO, business);
        boolean b = businessService.save(business);
        return b ? R.ok() : R.fail();
    }

    @Log("删除业务数据")
    @DeleteMapping("/batch")
    @ApiOperation(value = "批量删除业务", notes = "批量删除业务，传递ID列表")
    public R<?> batchDelete(@RequestBody @Valid BatchDeleteDTO batchDeleteDTO) {
        List<String> ids = batchDeleteDTO.getIds().stream().map(String::valueOf).collect(Collectors.toList());
        boolean b = businessService.delByIds(ids);
        return b ? R.ok() : R.fail();
    }

    @Log("更新业务数据")
    @PutMapping
    @ApiOperation(value = "更新业务", notes = "根据内容更新业务")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "business", value = "更新的business实例", paramType = "body", dataType = "Business", required = true)
    })
    public R<?> update(@RequestBody @Valid BusinessDTO businessDTO) {
        Business business = new Business();
        org.springframework.beans.BeanUtils.copyProperties(businessDTO, business);
        boolean b = businessService.updateById(business);
        return b ? R.ok() : R.fail();
    }


}