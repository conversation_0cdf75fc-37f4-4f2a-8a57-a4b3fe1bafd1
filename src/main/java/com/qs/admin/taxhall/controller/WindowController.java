package com.qs.admin.taxhall.controller;

import com.qs.admin.common.domain.R;
import com.qs.admin.taxhall.model.Window;
import com.qs.admin.taxhall.model.dto.WindowDTO;
import com.qs.admin.taxhall.model.dto.WindowBusinessDTO;
import com.qs.admin.taxhall.model.vo.WindowBusinessVO;
import com.qs.admin.taxhall.service.WindowService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.List;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;

/**
 * <AUTHOR>
 * @data 2020-04-07.
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/window")
@Tag(name = "窗口管理", description = "窗口相关的RESTful接口")
public class WindowController {

    @Autowired
    private WindowService windowService;

    @GetMapping("/page")
    @Operation(summary = "分页查询窗口", description = "分页条件查询窗口数据")
    public R<?> page(@Parameter(name = "page", description = "查询页码", example = "1") @RequestParam(defaultValue = "1") Integer page,
                    @Parameter(name = "pageSize", description = "每页数据量", example = "10") @RequestParam(defaultValue = "10") Integer pageSize,
                    @Parameter(name = "unitCode", description = "单位代码") @RequestParam(required = false) String unitCode) {
        return windowService.page(page, pageSize);
    }

    @GetMapping("/list")
    @Operation(summary = "获取所有窗口列表", description = "获取所有窗口的简化信息，用于下拉框等场景")
    public R<?> list() {
        QueryWrapper<Window> wrapper = new QueryWrapper<>();
        wrapper.select("uid", "name", "description"); // 只查询必要字段
        List<Window> windowList = windowService.list(wrapper);
        return R.ok(windowList);
    }

    @GetMapping("/options")
    @Operation(summary = "获取窗口选项", description = "获取窗口ID和名称的键值对，专用于下拉框")
    public R<?> options() {
        return windowService.getWindowOptions();
    }

    @PostMapping
    @Operation(summary = "添加数据", description = "添加新的数据")
    public R<?> add(WindowDTO windowDTO, HttpServletRequest request) {
        return windowService.createWindowWithBusinesses(windowDTO, request);
    }

    @PostMapping("/batchDelete")
    @Operation(summary = "批量删除数据", description = "根据id批量删除数据")
    public R<?> batchDelete(@Parameter(name = "ids", description = "待删除的ID列表，用逗号分隔", example = "1,2,3") @RequestParam("ids") String idsParam) {
        return windowService.batchDeleteWindows(idsParam);
    }

    @PutMapping
    @Operation(summary = "更新数据", description = "根据内容更新数据")
    public R<?> update(@Valid WindowDTO windowDTO, HttpServletRequest request) {
        try {
            // 处理bizListStr参数，就像add方法一样
            String[] bizListArray = request.getParameterValues("bizListStr");
            if (bizListArray != null && bizListArray.length > 0) {
                List<WindowBusinessDTO> bizList = windowService.parseBizListFromRequest(bizListArray);
                windowDTO.setBizList(bizList);
                log.info("解析到业务列表，数量: {}", bizList.size());
                for (WindowBusinessDTO dto : bizList) {
                    log.info("业务项: uid={}, name={}, enabled={}, priority={}",
                            dto.getUid(), dto.getName(), dto.getEnabled(), dto.getPriority());
                }
            }

            return windowService.updateWindowWithBusinesses(windowDTO);
        } catch (Exception e) {
            log.error("更新窗口失败: ", e);
            return R.fail("更新失败: " + e.getMessage());
        }
    }

    @GetMapping("/{uid}/businesses")
    @Operation(summary = "获取窗口关联的业务列表", description = "根据窗口ID获取该窗口关联的所有业务信息")
    public R<List<WindowBusinessVO>> getWindowBusinesses(@Parameter(name = "uid", description = "窗口ID", example = "1") @PathVariable Integer uid) {
        try {
            List<WindowBusinessVO> businessList = windowService.findByWin(uid);
            return R.ok(businessList);
        } catch (Exception e) {
            log.error("获取窗口关联业务失败: {}", e.getMessage(), e);
            return R.fail("获取窗口关联业务失败");
        }
    }

}