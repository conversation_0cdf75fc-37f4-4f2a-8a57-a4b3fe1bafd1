package com.qs.admin.taxhall.controller;



import com.qs.admin.common.domain.R;

//import com.qs.admin.system.manager.QscAccountManager;
//import com.qs.admin.common.datasource.DBIdentifier;
//import com.qs.admin.common.datasource.ProjectDBMgr;


//import com.qs.admin.system.service.QscAccountService;
import com.qs.admin.taxhall.model.Window;
import com.qs.admin.taxhall.model.dto.WindowDTO;
import com.qs.admin.taxhall.service.WindowService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.validation.Valid;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @data 2020-04-07.
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/window")
@Api(tags = "窗口管理", description = "窗口相关的RESTful接口")
public class WindowController {
    @Autowired
    private WindowService windowService;

    @GetMapping("/page")
    @ApiOperation(value = "分页查询窗口", notes = "分页条件查询窗口数据", httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageNum", value = "查询页码", paramType = "query", dataType = "Integer", defaultValue = "1"),
            @ApiImplicitParam(name = "pageSize", value = "每页数据量", paramType = "query", dataType = "Integer", defaultValue = "10")
    })
    public R<?> page(@RequestParam(defaultValue = "1") Integer pageNum,
                    @RequestParam(defaultValue = "10") Integer pageSize) {
        return windowService.page(pageNum, pageSize);
    }

    @GetMapping
    @ApiOperation(value = "获取全部窗口", notes = "返回所有Window的数据", httpMethod = "GET")
    public R<List<Window>> list() {
        List<Window> list = windowService.findAll();
        return R.ok(list);
    }

    @PostMapping
    @ApiOperation(value = "添加数据", notes = "添加新的数据", httpMethod = "POST")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "window", value = "待添加的window实例", paramType = "body", dataType = "Window", required = true)
    })
    public R<?> add(@RequestBody @Valid WindowDTO windowDTO) throws Exception {
    Window window = new Window();
    org.springframework.beans.BeanUtils.copyProperties(windowDTO, window);
        try {
            //DBIdentifier.setUnitCode(params.get("unitCode").toString());
            boolean b = windowService.insert(window);
            return b ? R.ok() : R.fail();
        } catch (Exception e) {
            return R.fail();
        }
    }

    @DeleteMapping("/{uid}")
    @ApiOperation(value = "删除数据", notes = "根据id删除数据", httpMethod = "DELETE")
    @ApiImplicitParam(name = "uid", value = "id", paramType = "path", required = true, dataType = "String")
    public R<?> delete(@PathVariable String uid) {
        boolean b = windowService.delByIds(java.util.Collections.singletonList(uid));
        return b ? R.ok() : R.fail();
    }

    @DeleteMapping("/batch")
    @ApiOperation(value = "批量删除数据", notes = "根据id列表删除数据", httpMethod = "DELETE")
    @ApiImplicitParam(name = "uids", value = "id列表", paramType = "body", required = true, dataType = "List<String>")
    public R<?> batchDelete(@RequestBody List<String> uids) {
        boolean b = windowService.delByIds(uids);
        return b ? R.ok() : R.fail();
    }

    @PutMapping
    @ApiOperation(value = "更新数据", notes = "根据内容更新数据", httpMethod = "PUT")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "window", value = "更新的window实例", paramType = "body", dataType = "Window", required = true)
    })
    public R<?> update(@RequestBody @Valid WindowDTO windowDTO) {
    Window window = new Window();
    org.springframework.beans.BeanUtils.copyProperties(windowDTO, window);
//        String unitCode = window.getUnitCode();
//        log.info("windows update: " + unitCode);
        //DBIdentifier.setUnitCode(unitCode);
        boolean b = windowService.updateByUid(window);
        return b ? R.ok() : R.fail();
    }

}