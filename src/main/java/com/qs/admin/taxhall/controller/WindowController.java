package com.qs.admin.taxhall.controller;



import com.qs.admin.common.domain.R;

//import com.qs.admin.system.manager.QscAccountManager;
//import com.qs.admin.common.datasource.DBIdentifier;
//import com.qs.admin.common.datasource.ProjectDBMgr;


//import com.qs.admin.system.service.QscAccountService;
import com.qs.admin.taxhall.model.Window;
import com.qs.admin.taxhall.model.dto.WindowDTO;
import com.qs.admin.taxhall.service.WindowService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.validation.Valid;

import java.util.List;
import java.util.stream.Collectors;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import java.util.Map;

/**
 * <AUTHOR>
 * @data 2020-04-07.
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/window")
@Api(tags = "窗口管理", description = "窗口相关的RESTful接口")
public class WindowController {
    @Autowired
    private WindowService windowService;

    @GetMapping("/page")
    @ApiOperation(value = "分页查询窗口", notes = "分页条件查询窗口数据", httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "查询页码", paramType = "query", dataType = "Integer", defaultValue = "1"),
            @ApiImplicitParam(name = "pageSize", value = "每页数据量", paramType = "query", dataType = "Integer", defaultValue = "10"),
            @ApiImplicitParam(name = "unitCode", value = "单位代码", paramType = "query", dataType = "String", required = false)
    })
    public R<?> page(@RequestParam(defaultValue = "1") Integer page,
                    @RequestParam(defaultValue = "10") Integer pageSize,
                    @RequestParam(required = false) String unitCode) {
        return windowService.page(page, pageSize);
    }

    @GetMapping
    @ApiOperation(value = "获取全部窗口", notes = "返回所有Window的数据（RESTful风格）", httpMethod = "GET")
    public R<List<Window>> getAll() {
        List<Window> list = windowService.findAll();
        return R.ok(list);
    }

    @GetMapping("/list")
    @ApiOperation(value = "获取所有窗口列表", notes = "获取所有窗口的简化信息，用于下拉框等场景", httpMethod = "GET")
    public R<?> list() {
        QueryWrapper<Window> wrapper = new QueryWrapper<>();
        wrapper.select("uid", "name", "description"); // 只查询必要字段
        List<Window> windowList = windowService.list(wrapper);
        return R.ok(windowList);
    }

    @GetMapping("/options")
    @ApiOperation(value = "获取窗口选项", notes = "获取窗口ID和名称的键值对，专用于下拉框", httpMethod = "GET")
    public R<?> options() {
        QueryWrapper<Window> wrapper = new QueryWrapper<>();
        wrapper.select("uid", "name"); // 只查询ID和名称
        List<Window> windowList = windowService.list(wrapper);

        // 转换为键值对格式
        List<Object> options = windowList.stream().map(window -> {
            return new Object() {
                public final Integer value = window.getUid();
                public final String label = window.getName();
            };
        }).collect(Collectors.toList());

        return R.ok(options);
    }

    @PostMapping
    @ApiOperation(value = "添加数据", notes = "添加新的数据", httpMethod = "POST")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "window", value = "待添加的window实例", paramType = "body", dataType = "Window", required = true)
    })
    public R<?> add(@RequestBody @Valid WindowDTO windowDTO) throws Exception {
    Window window = new Window();
    org.springframework.beans.BeanUtils.copyProperties(windowDTO, window);
        try {
            //DBIdentifier.setUnitCode(params.get("unitCode").toString());
            boolean b = windowService.insert(window);
            return b ? R.ok() : R.fail();
        } catch (Exception e) {
            return R.fail();
        }
    }

    @DeleteMapping("/{uid}")
    @ApiOperation(value = "删除数据", notes = "根据id删除数据", httpMethod = "DELETE")
    @ApiImplicitParam(name = "uid", value = "id", paramType = "path", required = true, dataType = "String")
    public R<?> delete(@PathVariable String uid) {
        boolean b = windowService.delByIds(java.util.Collections.singletonList(uid));
        return b ? R.ok() : R.fail();
    }

    @DeleteMapping("/batch")
    @ApiOperation(value = "批量删除数据", notes = "根据id列表删除数据", httpMethod = "DELETE")
    @ApiImplicitParam(name = "uids", value = "id列表", paramType = "body", required = true, dataType = "List<String>")
    public R<?> batchDelete(@RequestBody List<String> uids) {
        boolean b = windowService.delByIds(uids);
        return b ? R.ok() : R.fail();
    }

    @PutMapping
    @ApiOperation(value = "更新数据", notes = "根据内容更新数据", httpMethod = "PUT")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "window", value = "更新的window实例", paramType = "body", dataType = "Window", required = true)
    })
    public R<?> update(@RequestBody @Valid WindowDTO windowDTO) {
    Window window = new Window();
    org.springframework.beans.BeanUtils.copyProperties(windowDTO, window);
//        String unitCode = window.getUnitCode();
//        log.info("windows update: " + unitCode);
        //DBIdentifier.setUnitCode(unitCode);
        boolean b = windowService.updateByUid(window);
        return b ? R.ok() : R.fail();
    }

}