package com.qs.admin.taxhall.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.qs.admin.common.core.Query;
import com.qs.admin.common.domain.R;
//import com.qs.admin.common.datasource.DBIdentifier;
import com.qs.admin.taxhall.model.Ticket;
import com.qs.admin.taxhall.model.dto.TicketDTO;
import com.qs.admin.taxhall.model.dto.PageRequestDTO;
import com.qs.admin.taxhall.model.vo.TicketVO;
import com.qs.admin.taxhall.service.TicketService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.validation.Valid;
import java.util.Map;

/**
 * <AUTHOR>
 * @data 2020-04-07.
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/ticket")
@Api(tags = "票据管理", description = "票据相关的RESTful接口")
public class TicketController {
    @Autowired
    private TicketService ticketService;

    @GetMapping("/page")
    @ApiOperation(value = "分页查询票据", notes = "分页条件查询票据数据", httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "查询页码", paramType = "query", dataType = "Integer", defaultValue = "1"),
            @ApiImplicitParam(name = "pageSize", value = "每页数据量", paramType = "query", dataType = "Integer", defaultValue = "10"),
            @ApiImplicitParam(name = "ticket", value = "查询条件(JSON格式)", paramType = "query", dataType = "String", required = false),
            @ApiImplicitParam(name = "unitCode", value = "单位代码", paramType = "query", dataType = "String", required = false)
    })
    public R<IPage<Ticket>> page(@RequestParam(defaultValue = "1") Integer page,
                                @RequestParam(defaultValue = "10") Integer pageSize,
                                @RequestParam(required = false) String ticket,
                                @RequestParam(required = false) String unitCode) {
        Page<Ticket> ticketPage = new Page<>(page, pageSize);
        Ticket ticketCondition = null;
        if (ticket != null && !ticket.trim().isEmpty()) {
            ticketCondition = JSONObject.parseObject(ticket, Ticket.class);
        }
        IPage<Ticket> ticketIPage = ticketService.page(ticketPage, ticketCondition);
        return R.ok(ticketIPage);
    }

    @GetMapping("/public/page")
    @ApiOperation(value = "公开分页查询票据", notes = "无需认证的分页查询接口，仅用于前端调试", httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "查询页码", paramType = "query", dataType = "Integer", defaultValue = "1"),
            @ApiImplicitParam(name = "pageSize", value = "每页数据量", paramType = "query", dataType = "Integer", defaultValue = "10"),
            @ApiImplicitParam(name = "ticket", value = "查询条件(JSON格式)", paramType = "query", dataType = "String", required = false),
            @ApiImplicitParam(name = "unitCode", value = "单位代码", paramType = "query", dataType = "String", required = false)
    })
    public R<IPage<Ticket>> publicPage(@RequestParam(defaultValue = "1") Integer page,
                                      @RequestParam(defaultValue = "10") Integer pageSize,
                                      @RequestParam(required = false) String ticket,
                                      @RequestParam(required = false) String unitCode) {
        Page<Ticket> ticketPage = new Page<>(page, pageSize);
        Ticket ticketCondition = null;
        if (ticket != null && !ticket.trim().isEmpty()) {
            ticketCondition = JSONObject.parseObject(ticket, Ticket.class);
        }
        IPage<Ticket> ticketIPage = ticketService.page(ticketPage, ticketCondition);
        return R.ok(ticketIPage);
    }

    @PostMapping("/page")
    @ApiOperation(value = "分页查询票据(POST)", notes = "使用POST方式分页查询票据数据，支持复杂查询条件", httpMethod = "POST")
    public R<IPage<Ticket>> pagePost(@RequestBody(required = false) PageRequestDTO pageRequest) {
        if (pageRequest == null) {
            pageRequest = new PageRequestDTO();
        }

        Page<Ticket> ticketPage = new Page<>(pageRequest.getPage(), pageRequest.getPageSize());
        Ticket ticketCondition = null;

        // 处理搜索条件
        if (pageRequest.getSearchForm() != null && !pageRequest.getSearchForm().trim().isEmpty()) {
            try {
                ticketCondition = JSONObject.parseObject(pageRequest.getSearchForm(), Ticket.class);
            } catch (Exception e) {
                // 如果JSON解析失败，忽略搜索条件
                System.err.println("Failed to parse search form: " + e.getMessage());
            }
        }

        IPage<Ticket> ticketIPage = ticketService.page(ticketPage, ticketCondition);
        return R.ok(ticketIPage);
    }

    @PostMapping
    @ApiOperation(value = "新增", notes = "新增数据", httpMethod = "POST")
    public R<?> add(@RequestBody(required = false) @Valid TicketDTO ticketDTO) {
        if (ticketDTO == null) {
            return R.fail("请求参数不能为空");
        }
        Ticket ticket = new Ticket();
        org.springframework.beans.BeanUtils.copyProperties(ticketDTO, ticket);
        return ticketService.save(ticket) ? R.ok() : R.fail();
    }

    @PutMapping
    @ApiOperation(value = "更新", notes = "更新数据", httpMethod = "PUT")
    public R<?> update(@RequestBody(required = false) @Valid TicketDTO ticketDTO) {
        if (ticketDTO == null) {
            return R.fail("请求参数不能为空");
        }
        Ticket ticket = new Ticket();
        org.springframework.beans.BeanUtils.copyProperties(ticketDTO, ticket);
        return ticketService.updateById(ticket) ? R.ok() : R.fail();
    }
}