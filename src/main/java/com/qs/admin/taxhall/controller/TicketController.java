package com.qs.admin.taxhall.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.qs.admin.common.core.Query;
import com.qs.admin.common.domain.R;
//import com.qs.admin.common.datasource.DBIdentifier;
import com.qs.admin.taxhall.model.Ticket;
import com.qs.admin.taxhall.model.dto.TicketDTO;
import com.qs.admin.taxhall.service.TicketService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.validation.Valid;
import java.util.Map;

/**
 * <AUTHOR>
 * @data 2020-04-07.
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/ticket")
@Api(tags = "票据管理", description = "票据相关的RESTful接口")
public class TicketController {
    @Autowired
    private TicketService ticketService;

    @GetMapping
    @ApiOperation(value = "获取全部", notes = "返回分页过后的数据", httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "查询页码", paramType = "query", dataType = "Integer", defaultValue = "1"),
            @ApiImplicitParam(name = "page-size", value = "每页数据量", paramType = "query", dataType = "Integer", defaultValue = "10")
    })
    public R<IPage<Ticket>> list(@ApiIgnore @RequestParam Map<String, Object> params) {
        Page ticketPage = new Query<>(params);
        Ticket ticket = null;
        String ticketParam = (String) params.get("ticket");
        if (ticketParam != null && !ticketParam.trim().isEmpty()) {
            ticket = JSONObject.parseObject(ticketParam, Ticket.class);
        }
        IPage<Ticket> ticketIPage = ticketService.page(ticketPage, ticket);
        return R.ok(ticketIPage);
    }

    @PostMapping
    @ApiOperation(value = "新增", notes = "新增数据", httpMethod = "POST")
    public R<?> add(@RequestBody(required = false) @Valid TicketDTO ticketDTO) {
        if (ticketDTO == null) {
            return R.fail("请求参数不能为空");
        }
        Ticket ticket = new Ticket();
        org.springframework.beans.BeanUtils.copyProperties(ticketDTO, ticket);
        return ticketService.save(ticket) ? R.ok() : R.fail();
    }

    @PutMapping
    @ApiOperation(value = "更新", notes = "更新数据", httpMethod = "PUT")
    public R<?> update(@RequestBody @Valid TicketDTO ticketDTO) {
        Ticket ticket = new Ticket();
        org.springframework.beans.BeanUtils.copyProperties(ticketDTO, ticket);
        return ticketService.updateById(ticket) ? R.ok() : R.fail();
    }
}