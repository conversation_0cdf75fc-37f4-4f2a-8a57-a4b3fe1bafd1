package com.qs.admin.taxhall.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.qs.admin.common.core.Query;
import com.qs.admin.common.domain.R;
//import com.qs.admin.common.datasource.DBIdentifier;
import com.qs.admin.taxhall.model.Ticket;
import com.qs.admin.taxhall.model.dto.TicketDTO;
import com.qs.admin.taxhall.model.dto.PageRequestDTO;
import com.qs.admin.taxhall.model.vo.TicketVO;
import com.qs.admin.taxhall.service.TicketService;
import com.qs.admin.taxhall.service.WindowService;
import com.qs.admin.taxhall.service.BusinessService;
import com.qs.admin.taxhall.service.EmployeeService;
import com.qs.admin.taxhall.model.Window;
import com.qs.admin.taxhall.model.Business;
import com.qs.admin.taxhall.model.Employee;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.validation.Valid;
import java.util.Map;

/**
 * <AUTHOR>
 * @data 2020-04-07.
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/ticket")
@Api(tags = "票据管理", description = "票据相关的RESTful接口")
public class TicketController {
    @Autowired
    private TicketService ticketService;

    @Autowired
    private WindowService windowService;

    @Autowired
    private BusinessService businessService;

    @Autowired
    private EmployeeService employeeService;

    @GetMapping("/page")
    @ApiOperation(value = "分页查询票据VO", notes = "分页查询票据数据，包含关联的窗口、业务、员工名称", httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "查询页码", paramType = "query", dataType = "Integer", defaultValue = "1"),
            @ApiImplicitParam(name = "pageSize", value = "每页数据量", paramType = "query", dataType = "Integer", defaultValue = "10"),
            @ApiImplicitParam(name = "ticket", value = "查询条件(JSON格式)", paramType = "query", dataType = "String", required = false),
            @ApiImplicitParam(name = "unitCode", value = "单位代码", paramType = "query", dataType = "String", required = false)
    })
    public R<?> page(@RequestParam(defaultValue = "1") Integer page,
                                    @RequestParam(defaultValue = "10") Integer pageSize,
                                    @RequestParam(required = false) String ticket,
                                    @RequestParam(required = false) String unitCode) {
        try {
            // 暂时使用原有的Ticket查询，避免XML配置问题
            Page<Ticket> ticketPage = new Page<>(page, pageSize);
            Ticket ticketCondition = null;
            if (ticket != null && !ticket.trim().isEmpty()) {
                try {
                    ticketCondition = JSONObject.parseObject(ticket, Ticket.class);
                } catch (Exception e) {
                    System.err.println("Failed to parse ticket condition: " + e.getMessage());
                }
            }

            IPage<Ticket> ticketIPage = ticketService.page(ticketPage, ticketCondition);

            // 手动添加关联名称信息
            System.out.println("开始为 " + ticketIPage.getRecords().size() + " 条票据添加关联名称");
            for (Ticket t : ticketIPage.getRecords()) {
                enrichTicketWithNames(t);
                System.out.println("票据 " + t.getTktId() + " 添加名称: WindowName=" + t.getWindowName() +
                                 ", BusinessName=" + t.getBusinessName() + ", EmployeeName=" + t.getEmployeeName());
            }

            return R.ok(ticketIPage);
        } catch (Exception e) {
            return R.fail("查询失败: " + e.getMessage());
        }
    }

    @PostMapping
    @ApiOperation(value = "新增", notes = "新增数据", httpMethod = "POST")
    public R<?> add(@RequestBody(required = false) @Valid TicketDTO ticketDTO) {
        if (ticketDTO == null) {
            return R.fail("请求参数不能为空");
        }
        Ticket ticket = new Ticket();
        org.springframework.beans.BeanUtils.copyProperties(ticketDTO, ticket);
        return ticketService.save(ticket) ? R.ok() : R.fail();
    }

    @PutMapping
    @ApiOperation(value = "更新", notes = "更新数据", httpMethod = "PUT")
    public R<?> update(@RequestBody(required = false) @Valid TicketDTO ticketDTO) {
        if (ticketDTO == null) {
            return R.fail("请求参数不能为空");
        }
        Ticket ticket = new Ticket();
        org.springframework.beans.BeanUtils.copyProperties(ticketDTO, ticket);
        return ticketService.updateById(ticket) ? R.ok() : R.fail();
    }

    @GetMapping("/test")
    @ApiOperation(value = "测试字段名", notes = "测试大写驼峰字段名", httpMethod = "GET")
    public R<?> test() {
        Ticket ticket = new Ticket();
        ticket.setTktId("TEST001");
        ticket.setWinId(1);
        ticket.setBizUid(2);
        ticket.setEmpUid(3);
        ticket.setWindowName("测试窗口");
        ticket.setBusinessName("测试业务");
        ticket.setEmployeeName("测试员工");
        return R.ok(ticket);
    }

    /**
     * 为Ticket对象添加关联名称信息
     */
    private void enrichTicketWithNames(Ticket ticket) {
        System.out.println("正在处理票据: " + ticket.getTktId() + ", WIN_ID=" + ticket.getWinId() +
                          ", BIZ_UID=" + ticket.getBizUid() + ", EMP_UID=" + ticket.getEmpUid());
        try {
            // 添加窗口名称
            if (ticket.getWinId() != null) {
                Window window = windowService.getById(ticket.getWinId());
                if (window != null && window.getName() != null) {
                    // 使用反射或者添加字段的方式设置窗口名称
                    // 这里我们可以添加一个临时字段或者使用Map
                    ticket.setWindowName(window.getName());
                } else {
                    ticket.setWindowName(ticket.getWinId() + "号窗口");
                }
            }

            // 添加业务名称
            if (ticket.getBizUid() != null) {
                Business business = businessService.getById(ticket.getBizUid());
                if (business != null && business.getName() != null) {
                    ticket.setBusinessName(business.getName());
                } else {
                    ticket.setBusinessName("业务" + ticket.getBizUid());
                }
            }

            // 添加员工姓名
            if (ticket.getEmpUid() != null) {
                Employee employee = employeeService.getById(ticket.getEmpUid());
                if (employee != null && employee.getName() != null) {
                    ticket.setEmployeeName(employee.getName());
                } else {
                    ticket.setEmployeeName("员工" + ticket.getEmpUid());
                }
            }
        } catch (Exception e) {
            System.err.println("Failed to enrich ticket with names: " + e.getMessage());
            // 如果查询失败，设置默认名称
            if (ticket.getWinId() != null) {
                ticket.setWindowName(ticket.getWinId() + "号窗口");
            }
            if (ticket.getBizUid() != null) {
                ticket.setBusinessName("业务" + ticket.getBizUid());
            }
            if (ticket.getEmpUid() != null) {
                ticket.setEmployeeName("员工" + ticket.getEmpUid());
            }
        }
    }
}