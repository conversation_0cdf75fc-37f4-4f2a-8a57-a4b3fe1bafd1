package com.qs.admin.taxhall.controller;

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.qs.admin.common.core.Query;
import com.qs.admin.common.domain.R;
//import com.qs.admin.common.datasource.DBIdentifier;
import com.qs.admin.taxhall.model.Ticket;
import com.qs.admin.taxhall.model.dto.TicketDTO;
import com.qs.admin.taxhall.model.dto.PageRequestDTO;
import com.qs.admin.taxhall.model.vo.TicketVO;
import com.qs.admin.taxhall.service.TicketService;
import com.qs.admin.taxhall.service.WindowService;
import com.qs.admin.taxhall.service.BusinessService;
import com.qs.admin.taxhall.service.EmployeeService;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import java.util.List;
import java.util.stream.Collectors;
import com.qs.admin.taxhall.model.Window;
import com.qs.admin.taxhall.model.Business;
import com.qs.admin.taxhall.model.Employee;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;


import javax.validation.Valid;
import java.util.Map;

/**
 * <AUTHOR>
 * @data 2020-04-07.
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/ticket")
@Tag(name = "票据管理", description = "票据相关的RESTful接口")
public class TicketController {
    @Autowired
    private TicketService ticketService;

    @Autowired
    private WindowService windowService;

    @Autowired
    private BusinessService businessService;

    @Autowired
    private EmployeeService employeeService;

    @GetMapping("/page")
    @Operation(summary = "分页查询票据VO", description = "分页查询票据数据，包含关联的窗口、业务、员工名称")
    public R<?> page(@Parameter(name = "page", description = "查询页码", example = "1") @RequestParam(defaultValue = "1") Integer page,
                                    @Parameter(name = "pageSize", description = "每页数据量", example = "10") @RequestParam(defaultValue = "10") Integer pageSize,
                                    @Parameter(name = "ticket", description = "查询条件(JSON格式)") @RequestParam(required = false) String ticket,
                                    @Parameter(name = "unitCode", description = "单位代码") @RequestParam(required = false) String unitCode) {
        try {
            // 暂时使用原有的Ticket查询，避免XML配置问题
            Page<Ticket> ticketPage = new Page<>(page, pageSize);
            Ticket ticketCondition = null;
            if (ticket != null && !ticket.trim().isEmpty()) {
                try {
                    ticketCondition = JSONObject.parseObject(ticket, Ticket.class);
                } catch (Exception e) {
                    System.err.println("Failed to parse ticket condition: " + e.getMessage());
                }
            }

            IPage<Ticket> ticketIPage = ticketService.page(ticketPage, ticketCondition);

            // 手动添加关联名称信息
            System.out.println("开始为 " + ticketIPage.getRecords().size() + " 条票据添加关联名称");
            for (Ticket t : ticketIPage.getRecords()) {
                enrichTicketWithNames(t);
                System.out.println("票据 " + t.getTktId() + " 添加名称: WindowName=" + t.getWindowName() +
                                 ", BusinessName=" + t.getBusinessName() + ", EmployeeName=" + t.getEmployeeName());
            }

            return R.ok(ticketIPage);
        } catch (Exception e) {
            return R.fail("查询失败: " + e.getMessage());
        }
    }

    @GetMapping
    @Operation(summary = "获取所有票据", description = "获取所有票据信息（RESTful风格）")
    public R<?> getAll() {
        try {
            List<Ticket> ticketList = ticketService.list();
            // 为每个票据添加关联名称
            for (Ticket ticket : ticketList) {
                enrichTicketWithNames(ticket);
            }
            return R.ok(ticketList);
        } catch (Exception e) {
            return R.fail("获取票据列表失败: " + e.getMessage());
        }
    }

    @GetMapping("/list")
    @Operation(summary = "获取所有票据列表", description = "获取所有票据的简化信息，用于下拉框等场景")
    public R<?> list() {
        try {
            QueryWrapper<Ticket> wrapper = new QueryWrapper<>();
            wrapper.select("uid", "TKT_ID", "WIN_ID", "BIZ_UID", "EMP_UID", "STATUS"); // 只查询必要字段
            List<Ticket> ticketList = ticketService.list(wrapper);
            // 为每个票据添加关联名称
            for (Ticket ticket : ticketList) {
                enrichTicketWithNames(ticket);
            }
            return R.ok(ticketList);
        } catch (Exception e) {
            return R.fail("获取票据列表失败: " + e.getMessage());
        }
    }

    @GetMapping("/options")
    @Operation(summary = "获取票据选项", description = "获取票据ID和票据号的键值对，专用于下拉框")
    public R<?> options() {
        try {
            QueryWrapper<Ticket> wrapper = new QueryWrapper<>();
            wrapper.select("uid", "TKT_ID"); // 只查询ID和票据号
            List<Ticket> ticketList = ticketService.list(wrapper);

            // 转换为键值对格式
            List<Object> options = ticketList.stream().map(ticket -> {
                return new Object() {
                    public final Integer value = ticket.getUid();
                    public final String label = ticket.getTktId();
                    public final String text = ticket.getTktId(); // 兼容不同前端框架
                };
            }).collect(Collectors.toList());

            return R.ok(options);
        } catch (Exception e) {
            return R.fail("获取票据选项失败: " + e.getMessage());
        }
    }

    @PostMapping
    @Operation(summary = "新增", description = "新增数据")
    public R<?> add(@RequestBody(required = false) @Valid TicketDTO ticketDTO) {
        if (ticketDTO == null) {
            return R.fail("请求参数不能为空");
        }
        Ticket ticket = new Ticket();
        org.springframework.beans.BeanUtils.copyProperties(ticketDTO, ticket);
        return ticketService.save(ticket) ? R.ok() : R.fail();
    }

    @PutMapping
    @Operation(summary = "更新", description = "更新数据")
    public R<?> update(@RequestBody(required = false) @Valid TicketDTO ticketDTO) {
        if (ticketDTO == null) {
            return R.fail("请求参数不能为空");
        }
        Ticket ticket = new Ticket();
        org.springframework.beans.BeanUtils.copyProperties(ticketDTO, ticket);
        return ticketService.updateById(ticket) ? R.ok() : R.fail();
    }

    @GetMapping("/test")
    @Operation(summary = "测试字段名", description = "测试大写驼峰字段名")
    public R<?> test() {
        Ticket ticket = new Ticket();
        ticket.setTktId("TEST001");
        ticket.setWinId(1);
        ticket.setBizUid(2);
        ticket.setEmpUid(3);
        ticket.setWindowName("测试窗口");
        ticket.setBusinessName("测试业务");
        ticket.setEmployeeName("测试员工");
        return R.ok(ticket);
    }

    /**
     * 为Ticket对象添加关联名称信息
     */
    private void enrichTicketWithNames(Ticket ticket) {
        System.out.println("正在处理票据: " + ticket.getTktId() + ", WIN_ID=" + ticket.getWinId() +
                          ", BIZ_UID=" + ticket.getBizUid() + ", EMP_UID=" + ticket.getEmpUid());
        try {
            // 添加窗口名称
            if (ticket.getWinId() != null) {
                Window window = windowService.getById(ticket.getWinId());
                if (window != null && window.getName() != null) {
                    // 使用反射或者添加字段的方式设置窗口名称
                    // 这里我们可以添加一个临时字段或者使用Map
                    ticket.setWindowName(window.getName());
                } else {
                    ticket.setWindowName(ticket.getWinId() + "号窗口");
                }
            }

            // 添加业务名称
            if (ticket.getBizUid() != null) {
                Business business = businessService.getById(ticket.getBizUid());
                if (business != null && business.getName() != null) {
                    ticket.setBusinessName(business.getName());
                } else {
                    ticket.setBusinessName("业务" + ticket.getBizUid());
                }
            }

            // 添加员工姓名
            if (ticket.getEmpUid() != null) {
                Employee employee = employeeService.getById(ticket.getEmpUid());
                if (employee != null && employee.getName() != null) {
                    ticket.setEmployeeName(employee.getName());
                } else {
                    ticket.setEmployeeName("员工" + ticket.getEmpUid());
                }
            }
        } catch (Exception e) {
            System.err.println("Failed to enrich ticket with names: " + e.getMessage());
            // 如果查询失败，设置默认名称
            if (ticket.getWinId() != null) {
                ticket.setWindowName(ticket.getWinId() + "号窗口");
            }
            if (ticket.getBizUid() != null) {
                ticket.setBusinessName("业务" + ticket.getBizUid());
            }
            if (ticket.getEmpUid() != null) {
                ticket.setEmployeeName("员工" + ticket.getEmpUid());
            }
        }
    }
}