package com.qs.admin.taxhall.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Date;

/**
 * 票据视图对象 - 包含关联信息
 * <AUTHOR> Admin Team
 */
@Schema(description = "票据视图对象")
public class TicketVO {

    @Schema(description = "主键ID")
    private Integer uid;

    @Schema(description = "票据号")
    @JsonProperty("TKT_ID")
    private String tktId;

    @Schema(description = "票据内部ID")
    @JsonProperty("TKT_INTID")
    private Integer tktIntid;

    @Schema(description = "票据日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @JsonProperty("TKT_DATE")
    private Date tktDate;

    @Schema(description = "票据时间")
    @JsonFormat(pattern = "HH:mm:ss", timezone = "GMT+8")
    @JsonProperty("TKT_TIME")
    private Date tktTime;

    @Schema(description = "窗口ID")
    @JsonProperty("WIN_ID")
    private Integer winId;

    @Schema(description = "窗口名称")
    private String windowName;

    @Schema(description = "业务ID")
    @JsonProperty("BIZ_UID")
    private Integer bizUid;

    @Schema(description = "业务名称")
    private String businessName;

    @Schema(description = "员工ID")
    @JsonProperty("EMP_UID")
    private Integer empUid;

    @Schema(description = "员工姓名")
    private String employeeName;

    @Schema(description = "开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonProperty("START_TIME")
    private Date startTime;

    @Schema(description = "结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonProperty("END_TIME")
    private Date endTime;

    @Schema(description = "排队号")
    @JsonProperty("RANK")
    private Integer rank;

    @Schema(description = "状态")
    @JsonProperty("STATUS")
    private Integer status;

    @Schema(description = "首次时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonProperty("FIRST_TIME")
    private Date firstTime;

    @Schema(description = "二次开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonProperty("SEC_START_TIME")
    private Date secStartTime;

    @Schema(description = "等待时长")
    private String waitingTime;

    // 构造函数
    public TicketVO() {}

    // Getters and Setters
    public Integer getUid() {
        return uid;
    }

    public void setUid(Integer uid) {
        this.uid = uid;
    }

    public String getTktId() {
        return tktId;
    }

    public void setTktId(String tktId) {
        this.tktId = tktId;
    }

    public Integer getTktIntid() {
        return tktIntid;
    }

    public void setTktIntid(Integer tktIntid) {
        this.tktIntid = tktIntid;
    }

    public Date getTktDate() {
        return tktDate;
    }

    public void setTktDate(Date tktDate) {
        this.tktDate = tktDate;
    }

    public Date getTktTime() {
        return tktTime;
    }

    public void setTktTime(Date tktTime) {
        this.tktTime = tktTime;
    }

    public Integer getWinId() {
        return winId;
    }

    public void setWinId(Integer winId) {
        this.winId = winId;
    }

    public String getWindowName() {
        return windowName;
    }

    public void setWindowName(String windowName) {
        this.windowName = windowName;
    }

    public Integer getBizUid() {
        return bizUid;
    }

    public void setBizUid(Integer bizUid) {
        this.bizUid = bizUid;
    }

    public String getBusinessName() {
        return businessName;
    }

    public void setBusinessName(String businessName) {
        this.businessName = businessName;
    }

    public Integer getEmpUid() {
        return empUid;
    }

    public void setEmpUid(Integer empUid) {
        this.empUid = empUid;
    }

    public String getEmployeeName() {
        return employeeName;
    }

    public void setEmployeeName(String employeeName) {
        this.employeeName = employeeName;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public Integer getRank() {
        return rank;
    }

    public void setRank(Integer rank) {
        this.rank = rank;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Date getFirstTime() {
        return firstTime;
    }

    public void setFirstTime(Date firstTime) {
        this.firstTime = firstTime;
    }

    public Date getSecStartTime() {
        return secStartTime;
    }

    public void setSecStartTime(Date secStartTime) {
        this.secStartTime = secStartTime;
    }

    public String getWaitingTime() {
        return waitingTime;
    }

    public void setWaitingTime(String waitingTime) {
        this.waitingTime = waitingTime;
    }
}
