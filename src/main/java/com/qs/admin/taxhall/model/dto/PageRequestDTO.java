package com.qs.admin.taxhall.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 通用分页请求参数
 * <AUTHOR> Admin Team
 */
@Schema(description = "分页请求参数")
public class PageRequestDTO {

    @Schema(description = "页码", example = "1")
    private Integer page = 1;

    @Schema(description = "每页大小", example = "10")
    private Integer pageSize = 10;

    @Schema(description = "单位代码", example = "qsc_unitcode")
    private String unitCode;

    @Schema(description = "搜索条件(JSON字符串)", example = "{}")
    private String searchForm;

    public PageRequestDTO() {}

    public PageRequestDTO(Integer page, Integer pageSize) {
        this.page = page;
        this.pageSize = pageSize;
    }

    public PageRequestDTO(Integer page, Integer pageSize, String unitCode) {
        this.page = page;
        this.pageSize = pageSize;
        this.unitCode = unitCode;
    }

    // Getters and Setters
    public Integer getPage() {
        return page;
    }

    public void setPage(Integer page) {
        this.page = page;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public String getUnitCode() {
        return unitCode;
    }

    public void setUnitCode(String unitCode) {
        this.unitCode = unitCode;
    }

    public String getSearchForm() {
        return searchForm;
    }

    public void setSearchForm(String searchForm) {
        this.searchForm = searchForm;
    }

    @Override
    public String toString() {
        return "PageRequestDTO{" +
                "page=" + page +
                ", pageSize=" + pageSize +
                ", unitCode='" + unitCode + '\'' +
                ", searchForm='" + searchForm + '\'' +
                '}';
    }
}
