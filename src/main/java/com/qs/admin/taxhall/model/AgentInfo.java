package com.qs.admin.taxhall.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 代理机构人员信息实体类
 */
@Data
@TableName(value="AGENT_INFO")
public class AgentInfo {

    /**
     * 主键ID
     */
    @TableId(value = "ID", type = IdType.AUTO)
    private Long id;

    /**
     * 代理人姓名
     */
    @TableField("AGENT_NAME")
    private String agentName;

    /**
     * 代理人身份证号
     */
    @TableField("AGENT_ID_CARD")
    private String agentIdCard;

    /**
     * 代理人联系电话
     */
    @TableField("AGENT_PHONE")
    private String agentPhone;

//    /**
//     * 代理人单位
//     */
//    @TableField("AGENT_COMPANY")
//    private String agentCompany;

    /**
     * 代理人所属机构
     */
    @TableField("AGENT_ORG")
    private String agentOrg;

    /**
     * 取号时间
     */
    @TableField("TKT_DATE_TIME")
    private Date tktDateTime;

    /**
     * 取号编号
     */
    @TableField("TKT_UID")
    private Integer tktUid;

    /**
     * 票据号（如需展示）
     */
    @TableField("TKT_ID")
    private String tktId;
}