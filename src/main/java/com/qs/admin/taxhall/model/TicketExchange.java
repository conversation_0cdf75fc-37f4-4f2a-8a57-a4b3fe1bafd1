package com.qs.admin.taxhall.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-03
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("ticket_exchange")
@Schema(description = "TicketExchange对象")
public class TicketExchange extends Model<TicketExchange> {

    private static final long serialVersionUID = 1L;
    @TableId(type = IdType.AUTO)
    private Integer uid;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField("DATE")
    private Date date;

    @TableField("BIZ_UID")
    private Integer bizUid;

    @TableField("TYPE")
    private Integer type;

    @TableField("TAX_CODE")
    private String taxCode;

    @TableField("ID_CODE")
    private String idCode;

    @TableField("VERIFY_CODE")
    private String verifyCode;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField("EXPIRE_TIME")
    private Date expireTime;

    @TableField("NJ")
    private String nj;

    @TableField("TKT_UID")
    private Integer tktUid;


    @Override
    public Serializable pkVal() {
        return null;
    }

}
