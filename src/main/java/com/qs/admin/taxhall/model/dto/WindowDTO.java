package com.qs.admin.taxhall.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import javax.validation.constraints.*;

@Data
@ApiModel("窗口新增/更新DTO")
public class WindowDTO {
    @ApiModelProperty(value = "窗口名称", required = true)
    @NotBlank(message = "窗口名称不能为空")
    @Size(max = 50, message = "窗口名称不能超过50字符")
    private String name;

    @ApiModelProperty(value = "窗口编号", required = true)
    @NotBlank(message = "窗口编号不能为空")
    @Pattern(regexp = "\\d{1,10}", message = "窗口编号必须为1-10位数字")
    private String code;

    @ApiModelProperty(value = "所属部门")
    private String department;

    @ApiModelProperty(value = "状态")
    private Integer status;
}
