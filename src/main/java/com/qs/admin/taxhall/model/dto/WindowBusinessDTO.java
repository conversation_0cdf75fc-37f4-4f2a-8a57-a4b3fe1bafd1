package com.qs.admin.taxhall.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import javax.validation.constraints.*;

@Data
@ApiModel("窗口业务DTO")
public class WindowBusinessDTO {
    @ApiModelProperty(value = "业务名称", required = true)
    @NotBlank(message = "业务名称不能为空")
    private String name;

    @ApiModelProperty(value = "类型", required = true)
    @NotNull(message = "类型不能为空")
    private Integer type;

    @ApiModelProperty(value = "优先级")
    private Integer priority;

    @ApiModelProperty(value = "是否启用")
    private Boolean enabled;
}
