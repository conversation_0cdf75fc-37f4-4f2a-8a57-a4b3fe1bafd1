package com.qs.admin.taxhall.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-13
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="System对象", description="")
@TableName("system")
public class System extends Model<System> {

    private static final long serialVersionUID = 1L;

    @TableField("[KEY]")
    private String key;

    @TableField("VALUE")
    private String value;

    @TableField("MEMO")
    private String memo;


    @Override
    protected Serializable pkVal() {
        return null;
    }

}
