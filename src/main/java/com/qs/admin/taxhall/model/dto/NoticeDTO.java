package com.qs.admin.taxhall.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import javax.validation.constraints.*;

@Data
@Schema(description = "公告新增/更新DTO")
public class NoticeDTO {
    @Schema(description = "标题", required = true)
    @NotBlank(message = "标题不能为空")
    @Size(max = 100, message = "标题长度不能超过100字符")
    private String title;

    @Schema(description = "内容", required = true)
    @NotBlank(message = "内容不能为空")
    private String content;

    @Schema(description = "公告类型")
    private Integer type;

    @Schema(description = "发布人")
    @NotBlank(message = "发布人不能为空")
    private String publisher;

    @Schema(description = "发布时间")
    private String publishTime;
}
