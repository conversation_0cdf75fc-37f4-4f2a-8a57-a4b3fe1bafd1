package com.qs.admin.taxhall.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import javax.validation.constraints.*;

@Data
@ApiModel("公告新增/更新DTO")
public class NoticeDTO {
    @ApiModelProperty(value = "标题", required = true)
    @NotBlank(message = "标题不能为空")
    @Size(max = 100, message = "标题长度不能超过100字符")
    private String title;

    @ApiModelProperty(value = "内容", required = true)
    @NotBlank(message = "内容不能为空")
    private String content;

    @ApiModelProperty(value = "公告类型")
    private Integer type;

    @ApiModelProperty(value = "发布人")
    @NotBlank(message = "发布人不能为空")
    private String publisher;

    @ApiModelProperty(value = "发布时间")
    private String publishTime;
}
