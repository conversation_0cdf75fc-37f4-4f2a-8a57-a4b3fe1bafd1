package com.qs.admin.taxhall.model.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;

/**
 * 票据搜索条件DTO
 * <AUTHOR> Admin Team
 */
@ApiModel(description = "票据搜索条件")
public class TicketSearchDTO {

    @ApiModelProperty(value = "票据号")
    private String tktId;

    @ApiModelProperty(value = "窗口ID")
    private Integer winId;

    @ApiModelProperty(value = "业务ID")
    private Integer bizUid;

    @ApiModelProperty(value = "员工ID")
    private Integer empUid;

    @ApiModelProperty(value = "状态")
    private Integer status;

    @ApiModelProperty(value = "开始日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date startDate;

    @ApiModelProperty(value = "结束日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date endDate;

    @ApiModelProperty(value = "开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTime;

    @ApiModelProperty(value = "结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;

    @ApiModelProperty(value = "排队号")
    private Integer rank;

    @ApiModelProperty(value = "票据内部ID")
    private Integer tktIntid;

    // 构造函数
    public TicketSearchDTO() {}

    // Getters and Setters
    public String getTktId() {
        return tktId;
    }

    public void setTktId(String tktId) {
        this.tktId = tktId;
    }

    public Integer getWinId() {
        return winId;
    }

    public void setWinId(Integer winId) {
        this.winId = winId;
    }

    public Integer getBizUid() {
        return bizUid;
    }

    public void setBizUid(Integer bizUid) {
        this.bizUid = bizUid;
    }

    public Integer getEmpUid() {
        return empUid;
    }

    public void setEmpUid(Integer empUid) {
        this.empUid = empUid;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public Integer getRank() {
        return rank;
    }

    public void setRank(Integer rank) {
        this.rank = rank;
    }

    public Integer getTktIntid() {
        return tktIntid;
    }

    public void setTktIntid(Integer tktIntid) {
        this.tktIntid = tktIntid;
    }
}
