package com.qs.admin.taxhall.model.dto;

import lombok.Data;
import java.util.Date;
import java.util.List;

/**
 * 票号相关信息DTO
 * 包含代理人信息、企业信息和票据验证信息
 */
@Data
public class TicketRelatedInfoDTO {
    
    // TicketVerify 表字段
    private Integer uid;
    private String name;  // 代理人姓名
    private String code;  // 身份证号
    private String comparecode;
    private String comparevalue;
    private Integer tktUid;
    private Date date;
    private String phone; // 代理人电话
    
    // AgentInfo 表字段
    private Long agentInfoId;
    private String agentName;     // 代理人姓名
    private String agentIdCard;   // 代理人身份证号
    private String agentPhone;    // 代理人联系电话
    private String agentOrg;      // 代理人所属机构
    private Date tktDateTime;     // 取号时间
    private String tktId;         // 票据号
    
    // AgentInfoEnterprise 表字段列表
    private List<EnterpriseInfo> enterprises;
    
    /**
     * 企业信息内部类
     */
    @Data
    public static class EnterpriseInfo {
        private Long id;
        private Long agentInfoId;
        private String djxh;         // 登记序号
        private String nsrsbh;       // 纳税人识别号
        private String nsrmc;        // 纳税人名称
        private String zgswjDm;      // 主管税务机关代码
        private String zgswksfjDm;   // 主管税务科所分局代码
        private String nsrztMc;      // 纳税人状态名称
        private String sfzj;         // 是否正常
    }
}