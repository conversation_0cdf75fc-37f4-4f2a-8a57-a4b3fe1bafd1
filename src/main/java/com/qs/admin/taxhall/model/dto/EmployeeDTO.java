package com.qs.admin.taxhall.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import javax.validation.constraints.*;

@Data
@Schema(description = "员工新增/更新DTO")
public class EmployeeDTO {
    @Schema(description = "用户ID")
    private Integer uid;

    @Schema(description = "姓名", required = true)
    @NotBlank(message = "姓名不能为空")
    @Size(max = 30, message = "姓名不能超过30字符")
    private String name;

    @Schema(description = "工号", required = true)
    @NotBlank(message = "工号不能为空")
    private String id;

    @Schema(description = "用户名", required = true)
    @NotBlank(message = "用户名不能为空")
    private String username;

    @Schema(description = "密码")
    @Size(max = 20, message = "密码长度不能超过20位")
    private String password;

    @Schema(description = "头像")
    private String image;

    @Schema(description = "状态")
    private Integer status;

    @Schema(description = "是否启用")
    private Integer enabled;

    @Schema(description = "访问权限")
    private Integer access;
}
