package com.qs.admin.taxhall.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import javax.validation.constraints.*;

@Data
@ApiModel("员工新增/更新DTO")
public class EmployeeDTO {
    @ApiModelProperty(value = "姓名", required = true)
    @NotBlank(message = "姓名不能为空")
    @Size(max = 30, message = "姓名不能超过30字符")
    private String name;

    @ApiModelProperty(value = "工号", required = true)
    @NotBlank(message = "工号不能为空")
    @Pattern(regexp = "[A-Za-z0-9]{4,20}", message = "工号为4-20位字母或数字")
    private String jobNumber;

    @ApiModelProperty(value = "手机号", required = true)
    @NotBlank(message = "手机号不能为空")
    @Pattern(regexp = "1[3-9]\\d{9}", message = "手机号格式不正确")
    private String phone;

    @ApiModelProperty(value = "邮箱")
    @Email(message = "邮箱格式不正确")
    private String email;

    @ApiModelProperty(value = "状态")
    private Integer status;
}
