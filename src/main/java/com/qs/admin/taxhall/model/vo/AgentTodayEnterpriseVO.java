package com.qs.admin.taxhall.model.vo;

import lombok.Data;
import java.util.Date;

/**
 * 前端获取当日代理机构或代理人所服务企业信息VO
 */
@Data
public class AgentTodayEnterpriseVO {
    private String tktId;           // 票号（数据库字段：TKT_ID）
    private String name;            // 姓名
    private String idCard;          // 身份证
    private String phone;           // 手机号
    private String orgName;         // 所属机构
    private String serialNumber;    // DJXH登记序号
    private String serviceTarget;   // 服务对象机构名称
    private String serviceTargetCode; // 服务对象机构代码
    private Date tktDateTime;      // 取号时间（数据库字段：TKT_DATE_TIME）
}
