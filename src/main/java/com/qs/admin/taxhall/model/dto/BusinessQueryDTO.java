package com.qs.admin.taxhall.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

@Data
@ApiModel("业务分页查询DTO")
public class BusinessQueryDTO {
    public Integer getPageNum() {
        return pageNum;
    }
    public Integer getPageSize() {
        return pageSize;
    }
    @ApiModelProperty(value = "页码", required = true, example = "1")
    @NotNull(message = "页码不能为空")
    @Min(value = 1, message = "页码最小为1")
    private Integer pageNum = 1;

    @ApiModelProperty(value = "每页数量", required = true, example = "10")
    @NotNull(message = "每页数量不能为空")
    @Min(value = 1, message = "每页最小为1")
    private Integer pageSize = 10;

    // 可补充业务相关的查询条件字段
    @ApiModelProperty(value = "业务名称")
    private String name;

    @ApiModelProperty(value = "业务类型")
    private Integer type;
}
