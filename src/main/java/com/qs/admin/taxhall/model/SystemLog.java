package com.qs.admin.taxhall.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-03
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("system_log")
@ApiModel(value="SystemLog对象", description="")
public class SystemLog extends Model<SystemLog> {

    private static final long serialVersionUID = 1L;
    @TableId(type = IdType.AUTO)
    private Long uid;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField("LOG_TIME")
    private Date logTime;

    @TableField("LOG_TYPE")
    private Integer logType;

    @TableField("LOG_CONTENT")
    private String logContent;


    @Override
    public Serializable pkVal() {
        return null;
    }

}
