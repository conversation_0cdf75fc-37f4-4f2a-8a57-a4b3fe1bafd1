package com.qs.admin.taxhall.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

/**
 * 菜单数据传输对象
 * <AUTHOR> Admin Team
 */
@Schema(description = "菜单信息")
public class MenuDTO {

    @Schema(description = "菜单图标", example = "el-icon-lx-home")
    private String icon;

    @Schema(description = "菜单索引/路由", example = "dashboard")
    private String index;

    @Schema(description = "菜单标题", example = "人员统计/预警")
    private String title;

    @Schema(description = "访问权限级别", example = "0")
    private Integer access;

    @Schema(description = "子菜单列表")
    private List<MenuDTO> subs;

    public MenuDTO() {}

    public MenuDTO(String icon, String index, String title, Integer access) {
        this.icon = icon;
        this.index = index;
        this.title = title;
        this.access = access;
    }

    public MenuDTO(String icon, String index, String title, Integer access, List<MenuDTO> subs) {
        this.icon = icon;
        this.index = index;
        this.title = title;
        this.access = access;
        this.subs = subs;
    }

    // Getters and Setters
    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public String getIndex() {
        return index;
    }

    public void setIndex(String index) {
        this.index = index;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public Integer getAccess() {
        return access;
    }

    public void setAccess(Integer access) {
        this.access = access;
    }

    public List<MenuDTO> getSubs() {
        return subs;
    }

    public void setSubs(List<MenuDTO> subs) {
        this.subs = subs;
    }
}
