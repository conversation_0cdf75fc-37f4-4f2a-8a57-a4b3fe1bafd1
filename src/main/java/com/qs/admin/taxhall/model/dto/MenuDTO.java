package com.qs.admin.taxhall.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * 菜单数据传输对象
 * <AUTHOR> Admin Team
 */
@ApiModel(description = "菜单信息")
public class MenuDTO {

    @ApiModelProperty(value = "菜单图标", example = "el-icon-lx-home")
    private String icon;

    @ApiModelProperty(value = "菜单索引/路由", example = "dashboard")
    private String index;

    @ApiModelProperty(value = "菜单标题", example = "人员统计/预警")
    private String title;

    @ApiModelProperty(value = "访问权限级别", example = "0")
    private Integer access;

    @ApiModelProperty(value = "子菜单列表")
    private List<MenuDTO> subs;

    public MenuDTO() {}

    public MenuDTO(String icon, String index, String title, Integer access) {
        this.icon = icon;
        this.index = index;
        this.title = title;
        this.access = access;
    }

    public MenuDTO(String icon, String index, String title, Integer access, List<MenuDTO> subs) {
        this.icon = icon;
        this.index = index;
        this.title = title;
        this.access = access;
        this.subs = subs;
    }

    // Getters and Setters
    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public String getIndex() {
        return index;
    }

    public void setIndex(String index) {
        this.index = index;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public Integer getAccess() {
        return access;
    }

    public void setAccess(Integer access) {
        this.access = access;
    }

    public List<MenuDTO> getSubs() {
        return subs;
    }

    public void setSubs(List<MenuDTO> subs) {
        this.subs = subs;
    }
}
