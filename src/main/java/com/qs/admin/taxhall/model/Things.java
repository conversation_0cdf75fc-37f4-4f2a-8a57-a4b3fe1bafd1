package com.qs.admin.taxhall.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import com.baomidou.mybatisplus.annotation.IdType;

import java.io.Serializable;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-03
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName(value="things")
@ApiModel(value="Things对象", description="")
public class Things extends Model<Things> {

    private static final long serialVersionUID = 1L;

    @TableField("gdslb")
    private String gdslb;

    @TableField("sssx_dl_dm")
    private String sssxDlDm;

    @TableField("sssx_dl_mc")
    private String sssxDlMc;

    @TableField("sssx_dm")
    private String sssxDm;

    @TableField("sssx_mc")
    private String sssxMc;
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;


    @Override
    public Serializable pkVal() {
        return null;
    }

}
