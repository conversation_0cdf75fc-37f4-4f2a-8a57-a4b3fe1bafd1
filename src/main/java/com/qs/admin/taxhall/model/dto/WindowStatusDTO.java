package com.qs.admin.taxhall.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Schema(description = "窗口状态DTO")
public class WindowStatusDTO implements Serializable {
    @Schema(description = "窗口ID", required = true)
    @NotNull(message = "窗口ID不能为空")
    private Integer windowId;

    @Schema(description = "状态", required = true)
    @NotNull(message = "状态不能为空")
    private String status;

    public Integer getWindowId() { return windowId; }
    public void setWindowId(Integer windowId) { this.windowId = windowId; }

    public String getStatus() { return status; }
    public void setStatus(String status) { this.status = status; }
}
