package com.qs.admin.taxhall.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

@ApiModel("窗口状态DTO")
public class WindowStatusDTO implements Serializable {
    @ApiModelProperty(value = "窗口ID", required = true)
    @NotNull(message = "窗口ID不能为空")
    private Integer windowId;

    @ApiModelProperty(value = "状态", required = true)
    @NotNull(message = "状态不能为空")
    private String status;

    public Integer getWindowId() { return windowId; }
    public void setWindowId(Integer windowId) { this.windowId = windowId; }

    public String getStatus() { return status; }
    public void setStatus(String status) { this.status = status; }
}
