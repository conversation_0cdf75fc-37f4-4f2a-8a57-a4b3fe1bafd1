package com.qs.admin.taxhall.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import javax.validation.constraints.*;

@Data
@ApiModel("业务新增/更新DTO")
public class BusinessDTO {
    @ApiModelProperty(value = "业务名称", required = true)
    @NotBlank(message = "业务名称不能为空")
    private String name;

    @ApiModelProperty(value = "业务类型", required = true)
    @NotNull(message = "业务类型不能为空")
    private Integer type;

    @ApiModelProperty(value = "优先级")
    @Min(value = 1, message = "优先级最小为1")
    @Max(value = 999, message = "优先级最大为999")
    private Integer priority;

    @ApiModelProperty(value = "是否启用")
    private Boolean enabled;
    // 其它字段请根据实际业务补充
}
