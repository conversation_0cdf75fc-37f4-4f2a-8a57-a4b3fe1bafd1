package com.qs.admin.taxhall.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-03
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="Employee对象", description="")
@TableName(value="employee")
public class Employee extends Model<Employee> {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    private Integer uid;

    @TableField("NAME")
    private String name;

    @TableField("ID")
    private String id;

    @TableField("USERNAME")
    private String username;

    @TableField("PASSWORD")
    private String password;

    @TableField("IMAGE")
    private String image;

    @TableField("STATUS")
    private Integer status;

    @TableField("ENABLED")
    private Integer enabled;

    @TableField("ACCESS")
    private Integer access;


    @Override
    public Serializable pkVal() {
        return null;
    }

}
