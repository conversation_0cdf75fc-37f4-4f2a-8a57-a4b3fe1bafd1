package com.qs.admin.taxhall.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-03
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Schema(description = "Business对象")
@TableName(value="business")
public class Business extends Model<Business> {

    private static final long serialVersionUID = 1L;
    @TableId(value = "UID", type = IdType.AUTO)
    private Integer uid;

    @TableField("PREFIX")
    private String prefix;

    @TableField("NAME")
    private String name;

    @TableField("ENABLED")
    private Integer enabled;

    @TableField("TYPE")
    private Integer type;

    @TableField("HANDLE_COUNT")
    private Integer handleCount;

    @TableField("IS_SPECIAL")
    private Integer isSpecial;

    @Override
    public Serializable pkVal() {
        return this.uid;
    }

}
