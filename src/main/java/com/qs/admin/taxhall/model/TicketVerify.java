package com.qs.admin.taxhall.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-03
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("ticket_verify")
@Schema(description = "TicketVerify对象")
public class TicketVerify extends Model<TicketVerify> {

    private static final long serialVersionUID = 1L;
    @TableId(type = IdType.AUTO)
    private Integer uid;

    @TableField("NAME")
    private String name;

    @TableField("CODE")
    private String code;

    @TableField("COMPARECODE")
    private String comparecode;

    @TableField("COMPAREVALUE")
    private String comparevalue;

    @TableField("TKT_UID")
    private Integer tktUid;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField("DATE")
    private Date date;

    @TableField("PHONE")
    private String phone;

    @Override
    public Serializable pkVal() {
        return null;
    }

}
