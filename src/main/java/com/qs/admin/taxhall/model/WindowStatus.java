package com.qs.admin.taxhall.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-03
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("window_status")
@ApiModel(value = "WindowStatus对象", description = "")
public class WindowStatus extends Model<WindowStatus> {

    private static final long serialVersionUID = 1L;
    @TableId(type = IdType.AUTO)
    private Integer winUid;

    @TableField("EMP_UID")
    private Integer empUid;

    @TableField("STATUS")
    private Integer status;

    @TableField("PC_NAME")
    private String pcName;

    @TableField("CLIENT_VER")
    private String clientVer;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField("UPDATE_TIME")
    private Date updateTime;


    @Override
    public Serializable pkVal() {
        return null;
    }

}
