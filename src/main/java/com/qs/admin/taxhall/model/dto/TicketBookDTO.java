package com.qs.admin.taxhall.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import javax.validation.constraints.*;
import java.util.Date;

@Data
@Schema(description = "票本新增/更新DTO")
public class TicketBookDTO {
    @Schema(description = "UUID", required = true)
    @NotBlank(message = "UUID不能为空")
    private String uuid;

    @Schema(description = "业务ID", required = true)
    @NotNull(message = "业务ID不能为空")
    private Integer bizUid;

    @Schema(description = "姓名", required = true)
    @NotBlank(message = "姓名不能为空")
    @Size(max = 50, message = "姓名不能超过50字符")
    private String name;

    @Schema(description = "身份证号")
    @Pattern(regexp = "\\d{15}|\\d{18}", message = "身份证号格式不正确")
    private String idCode;

    @Schema(description = "手机号")
    @Pattern(regexp = "1[3-9]\\d{9}", message = "手机号格式不正确")
    private String phone;

    @Schema(description = "纳税人识别号")
    private String taxCode;

    @Schema(description = "验证码")
    private String verifyCode;

    @Schema(description = "过期时间")
    private Date expireTime;

    @Schema(description = "年级")
    private String nj;

    @Schema(description = "票据UID")
    private Integer tktUid;

    @Schema(description = "状态")
    private Integer status;
}
