package com.qs.admin.taxhall.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import javax.validation.constraints.*;
import java.util.Date;

@Data
@ApiModel("票本新增/更新DTO")
public class TicketBookDTO {
    @ApiModelProperty(value = "UUID", required = true)
    @NotBlank(message = "UUID不能为空")
    private String uuid;

    @ApiModelProperty(value = "业务ID", required = true)
    @NotNull(message = "业务ID不能为空")
    private Integer bizUid;

    @ApiModelProperty(value = "姓名", required = true)
    @NotBlank(message = "姓名不能为空")
    @Size(max = 50, message = "姓名不能超过50字符")
    private String name;

    @ApiModelProperty(value = "身份证号")
    @Pattern(regexp = "\\d{15}|\\d{18}", message = "身份证号格式不正确")
    private String idCode;

    @ApiModelProperty(value = "手机号")
    @Pattern(regexp = "1[3-9]\\d{9}", message = "手机号格式不正确")
    private String phone;

    @ApiModelProperty(value = "纳税人识别号")
    private String taxCode;

    @ApiModelProperty(value = "验证码")
    private String verifyCode;

    @ApiModelProperty(value = "过期时间")
    private Date expireTime;

    @ApiModelProperty(value = "年级")
    private String nj;

    @ApiModelProperty(value = "票据UID")
    private Integer tktUid;

    @ApiModelProperty(value = "状态")
    private Integer status;
}
