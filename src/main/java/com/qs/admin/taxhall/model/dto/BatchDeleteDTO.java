package com.qs.admin.taxhall.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import javax.validation.constraints.NotEmpty;
import java.util.List;

@Data
@Schema(description = "批量删除DTO")
public class BatchDeleteDTO {
    public List<Integer> getIds() {
        return ids;
    }
    @Schema(description = "待删除ID列表", required = true)
    @NotEmpty(message = "ID列表不能为空")
    private List<Integer> ids;
}
