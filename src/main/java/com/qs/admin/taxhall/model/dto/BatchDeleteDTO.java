package com.qs.admin.taxhall.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import javax.validation.constraints.NotEmpty;
import java.util.List;

@Data
@ApiModel("批量删除DTO")
public class BatchDeleteDTO {
    public java.util.List<Integer> getIds() {
        return ids;
    }
    @ApiModelProperty(value = "待删除ID列表", required = true)
    @NotEmpty(message = "ID列表不能为空")
    private List<Integer> ids;
}
