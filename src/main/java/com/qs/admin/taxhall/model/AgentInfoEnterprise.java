package com.qs.admin.taxhall.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 代理人取号与企业关联表
 */
@Data
@TableName("AGENT_INFO_ENTERPRISE")
public class AgentInfoEnterprise {
    @TableId(value = "ID", type = IdType.AUTO)
    private Long id;

    @TableField("AGENT_INFO_ID")
    private Long agentInfoId;

    @TableField("DJXH")
    private String djxh;

    @TableField("NSRSBH")
    private String nsrsbh;

    @TableField("NSRMC")
    private String nsrmc;

    @TableField("ZGSWJ_DM")
    private String zgswjDm;

    @TableField("ZGSWKSFJ_DM")
    private String zgswksfjDm;

    @TableField("NSRZT_MC")
    private String nsrztMc;

    @TableField("SFZJ")
    private String sfzj;
}
