package com.qs.admin.taxhall.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Schema(description = "票据核销DTO")
public class TicketVerifyDTO implements Serializable {
    @Schema(description = "票据ID", required = true)
    @NotNull(message = "票据ID不能为空")
    private Integer ticketId;

    @Schema(description = "核销状态", required = true)
    @NotNull(message = "核销状态不能为空")
    private String verifyStatus;

    public Integer getTicketId() { return ticketId; }
    public void setTicketId(Integer ticketId) { this.ticketId = ticketId; }

    public String getVerifyStatus() { return verifyStatus; }
    public void setVerifyStatus(String verifyStatus) { this.verifyStatus = verifyStatus; }
}
