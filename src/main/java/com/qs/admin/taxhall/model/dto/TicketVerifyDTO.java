package com.qs.admin.taxhall.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

@ApiModel("票据核销DTO")
public class TicketVerifyDTO implements Serializable {
    @ApiModelProperty(value = "票据ID", required = true)
    @NotNull(message = "票据ID不能为空")
    private Integer ticketId;

    @ApiModelProperty(value = "核销状态", required = true)
    @NotNull(message = "核销状态不能为空")
    private String verifyStatus;

    public Integer getTicketId() { return ticketId; }
    public void setTicketId(Integer ticketId) { this.ticketId = ticketId; }

    public String getVerifyStatus() { return verifyStatus; }
    public void setVerifyStatus(String verifyStatus) { this.verifyStatus = verifyStatus; }
}
