package com.qs.admin.taxhall.model;

import cn.hutool.core.date.DateTime;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.qs.admin.common.utils.DateUtil;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-03
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("ticket_log")
@ApiModel(value="TicketLog对象", description="")
public class TicketLog extends Model<TicketLog> {

    private static final long serialVersionUID = 1L;
    @TableId(type = IdType.AUTO)
    private Long uid;

    @TableField("TKT_ID")
    private String tktId;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @TableField("TKT_DATE")
    private Date tktDate;

    @JsonFormat(pattern = "HH:mm:ss", timezone = "GMT+8")
    @TableField("TKT_TIME")
    private Date tktTime;

    @TableField("WIN_ID")
    private String winId;

    @TableField("BIZ_UID")
    private Integer bizUid;

    @TableField("BIZ_PREFIX")
    private String bizPrefix;

    @TableField("BIZ_NAME")
    private String bizName;

    @TableField("EMP_UID")
    private Integer empUid;

    @TableField("EMP_ID")
    private String empId;

    @TableField("EMP_NAME")
    private String empName;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField("START_TIME")
    private Date startTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField("END_TIME")
    private Date endTime;

    @TableField("RANK")
    private Integer rank;

    @TableField("STATUS")
    private Integer status;

    @TableField("SYNC_STATUS")
    private Integer syncStatus;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField("SYNC_DATE")
    private Date syncDate;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField("SEC_START_TIME")
    private Date secStartTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private transient Date tktDateTime;

    private transient String startDateTime;
    private transient String endDateTime;
    
    @Override
    public Serializable pkVal() {
        return null;
    }

    public void setTktDateTime() {
        // 检查tktDate和tktTime是否为null，避免空指针异常
        if (this.tktDate != null && this.tktTime != null) {
            try {
                String tktDt = DateUtil.format(this.tktDate, DateUtil.DATE_PATTERN) + " " + DateUtil.format(this.tktTime, DateUtil.TIME_PATTERN);
                this.tktDateTime = DateTime.of(tktDt, DateUtil.DATE_TIME_PATTERN);
            } catch (Exception e) {
                // 如果日期格式化失败，设置为null
                this.tktDateTime = null;
            }
        } else {
            this.tktDateTime = null;
        }
    }
}
