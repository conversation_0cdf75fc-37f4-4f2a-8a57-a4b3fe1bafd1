package com.qs.admin.taxhall.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * 登录响应数据传输对象
 * <AUTHOR> Admin Team
 */
@ApiModel(description = "登录响应信息")
public class LoginResponseDTO {

    @ApiModelProperty(value = "用户ID", example = "1")
    private Long userId;

    @ApiModelProperty(value = "用户名", example = "admin")
    private String username;

    @ApiModelProperty(value = "用户昵称", example = "管理员")
    private String nickname;

    @ApiModelProperty(value = "用户角色", example = "admin")
    private String role;

    @ApiModelProperty(value = "访问令牌", example = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...")
    private String token;

    @ApiModelProperty(value = "菜单列表")
    private List<MenuDTO> menus;

    public LoginResponseDTO() {}

    public LoginResponseDTO(Long userId, String username, String nickname, String role, String token, List<MenuDTO> menus) {
        this.userId = userId;
        this.username = username;
        this.nickname = nickname;
        this.role = role;
        this.token = token;
        this.menus = menus;
    }

    // Getters and Setters
    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getNickname() {
        return nickname;
    }

    public void setNickname(String nickname) {
        this.nickname = nickname;
    }

    public String getRole() {
        return role;
    }

    public void setRole(String role) {
        this.role = role;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public List<MenuDTO> getMenus() {
        return menus;
    }

    public void setMenus(List<MenuDTO> menus) {
        this.menus = menus;
    }
}
