package com.qs.admin.taxhall.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 登录响应DTO
 */
@Data
@ApiModel("登录响应DTO")
public class LoginResponseDTO {
    
    @ApiModelProperty("用户ID")
    private Integer uid;
    
    @ApiModelProperty("用户名")
    private String username;
    
    @ApiModelProperty("姓名")
    private String name;
    
    @ApiModelProperty("状态")
    private Integer status;
    
    @ApiModelProperty("是否启用")
    private Integer enabled;
    
    @ApiModelProperty("访问权限")
    private Integer access;
    
    @ApiModelProperty("登录令牌")
    private String token;
}
