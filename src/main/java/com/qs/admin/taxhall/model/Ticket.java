package com.qs.admin.taxhall.model;

import cn.hutool.core.date.DateTime;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.qs.admin.common.utils.DateUtil;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-03
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "Ticket对象", description = "")
@TableName(value="ticket")
public class Ticket extends Model<Ticket> {

    private static final long serialVersionUID = 1L;
    @TableId
    @TableField("UID")
    private Integer uid;

    @TableField("TKT_ID")
    private String tktId;

    @TableField("TKT_INTID")
    private Integer tktIntid;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @TableField("TKT_DATE")
    private Date tktDate;

    @JsonFormat(pattern = "HH:mm:ss", timezone = "GMT+8")
    @TableField("TKT_TIME")
    private Date tktTime;

    @TableField("WIN_ID")
    private Integer winId;

    @TableField("BIZ_UID")
    private Integer bizUid;

    @TableField("EMP_UID")
    private Integer empUid;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField("START_TIME")
    private Date startTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField("END_TIME")
    private Date endTime;

    @TableField("RANK")
    private Integer rank;

    @TableField("STATUS")
    private Integer status;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField("FIRST_TIME")
    private Date firstTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField("SEC_START_TIME")
    private Date secStartTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private transient Date tktDateTime;

    private transient String startDateTime;
    private transient String endDateTime;

    @Override
    protected Serializable pkVal() {
        return null;
    }

    //    @Override
    public void setTktDateTime() {
        String tktDt = DateUtil.format(this.tktDate, DateUtil.DATE_PATTERN) + " " + DateUtil.format(this.tktTime, DateUtil.TIME_PATTERN);
        this.tktDateTime = DateTime.of(tktDt, DateUtil.DATE_TIME_PATTERN);
    }
}
