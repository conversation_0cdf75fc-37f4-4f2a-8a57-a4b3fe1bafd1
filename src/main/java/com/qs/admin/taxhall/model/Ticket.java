package com.qs.admin.taxhall.model;

import cn.hutool.core.date.DateTime;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.qs.admin.common.utils.DateUtil;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import com.baomidou.mybatisplus.annotation.IdType;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-03
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "Ticket对象", description = "")
@TableName(value="ticket")
public class Ticket extends Model<Ticket> {

    private static final long serialVersionUID = 1L;
    @TableId(type = IdType.AUTO)
    private Integer uid;

    @TableField("TKT_ID")
    private String tktId;

    @TableField("TKT_INTID")
    private Integer tktIntid;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @TableField("TKT_DATE")
    private Date tktDate;

    @JsonFormat(pattern = "HH:mm:ss", timezone = "GMT+8")
    @TableField("TKT_TIME")
    private Date tktTime;

    @TableField("WIN_ID")
    private Integer winId;

    @TableField("BIZ_UID")
    private Integer bizUid;

    @TableField("EMP_UID")
    @JsonProperty("EMP_UID")
    private Integer empUid;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField("START_TIME")
    private Date startTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField("END_TIME")
    private Date endTime;

    @TableField("RANK")
    private Integer rank;

    @TableField("STATUS")
    @JsonProperty("STATUS")
    private Integer status;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField("FIRST_TIME")
    private Date firstTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField("SEC_START_TIME")
    private Date secStartTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private transient Date tktDateTime;

    private transient String startDateTime;
    private transient String endDateTime;

    // 关联名称字段（不存储到数据库）
    @TableField(exist = false)
    @JsonProperty("winName")
    private String WindowName;
    @TableField(exist = false)
    @JsonProperty("bizName")
    private String BusinessName;
    @TableField(exist = false)
    @JsonProperty("empName")
    private String EmployeeName;



    @Override
    public Serializable pkVal() {
        return this.uid;
    }

    //    @Override
    public void setTktDateTime() {
        // 检查tktDate和tktTime是否为null，避免空指针异常
        if (this.tktDate != null && this.tktTime != null) {
            try {
                String tktDt = DateUtil.format(this.tktDate, DateUtil.DATE_PATTERN) + " " + DateUtil.format(this.tktTime, DateUtil.TIME_PATTERN);
                this.tktDateTime = DateTime.of(tktDt, DateUtil.DATE_TIME_PATTERN);
            } catch (Exception e) {
                // 如果日期格式化失败，设置为null
                this.tktDateTime = null;
            }
        } else {
            this.tktDateTime = null;
        }
    }

    // 关联名称字段的getter和setter方法
    @JsonProperty("WIN_NAME")
    public String getWindowName() {
        return WindowName;
    }

    public void setWindowName(String WindowName) {
        this.WindowName = WindowName;
    }

    @JsonProperty("BIZ_NAME")
    public String getBusinessName() {
        return BusinessName;
    }

    public void setBusinessName(String BusinessName) {
        this.BusinessName = BusinessName;
    }

    @JsonProperty("EMP_Name")
    public String getEmployeeName() {
        return EmployeeName;
    }

    public void setEmployeeName(String EmployeeName) {
        this.EmployeeName = EmployeeName;
    }
}
