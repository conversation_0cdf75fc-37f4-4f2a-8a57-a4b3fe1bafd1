package com.qs.admin.taxhall.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import javax.validation.constraints.*;
import java.util.Date;

@Data
@ApiModel("票据日志DTO")
public class TicketLogDTO {
    @ApiModelProperty(value = "票据ID", required = true)
    @NotBlank(message = "票据ID不能为空")
    private String tktId;

    @ApiModelProperty(value = "日志时间", required = true)
    @NotNull(message = "日志时间不能为空")
    private Date tktDate;

    @ApiModelProperty(value = "窗口ID")
    private Integer winId;

    @ApiModelProperty(value = "状态")
    private Integer status;

    // 可根据实际业务补充其它字段及校验
}
