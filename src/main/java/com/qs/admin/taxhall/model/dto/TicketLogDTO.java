package com.qs.admin.taxhall.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import javax.validation.constraints.*;
import java.util.Date;

@Data
@Schema(description = "票据日志DTO")
public class TicketLogDTO {
    @Schema(description = "票据ID", required = true)
    @NotBlank(message = "票据ID不能为空")
    private String tktId;

    @Schema(description = "日志时间", required = true)
    @NotNull(message = "日志时间不能为空")
    private Date tktDate;

    @Schema(description = "窗口ID")
    private Integer winId;

    @Schema(description = "状态")
    private Integer status;

    // 可根据实际业务补充其它字段及校验
}
