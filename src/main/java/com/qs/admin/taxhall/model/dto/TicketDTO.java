package com.qs.admin.taxhall.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import javax.validation.constraints.*;
import java.util.Date;

@Data
@Schema(description = "票据新增/更新DTO")
public class TicketDTO {
    @Schema(description = "票据ID", required = true)
    @NotBlank(message = "票据ID不能为空")
    private String tktId;

    @Schema(description = "票据内部编号")
    private Integer tktIntid;

    @Schema(description = "票据日期", required = true)
    @NotNull(message = "票据日期不能为空")
    private Date tktDate;

    @Schema(description = "票据时间")
    private Date tktTime;

    @Schema(description = "业务ID")
    private Integer bizUid;

    @Schema(description = "窗口ID")
    private Integer winUid;

    @Schema(description = "状态")
    private Integer status;
    // 其它字段请根据实际业务补充
}
