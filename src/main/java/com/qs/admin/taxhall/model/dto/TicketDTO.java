package com.qs.admin.taxhall.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import javax.validation.constraints.*;
import java.util.Date;

@Data
@ApiModel("票据新增/更新DTO")
public class TicketDTO {
    @ApiModelProperty(value = "票据ID", required = true)
    @NotBlank(message = "票据ID不能为空")
    private String tktId;

    @ApiModelProperty(value = "票据内部编号")
    private Integer tktIntid;

    @ApiModelProperty(value = "票据日期", required = true)
    @NotNull(message = "票据日期不能为空")
    private Date tktDate;

    @ApiModelProperty(value = "票据时间")
    private Date tktTime;

    @ApiModelProperty(value = "业务ID")
    private Integer bizUid;

    @ApiModelProperty(value = "窗口ID")
    private Integer winUid;

    @ApiModelProperty(value = "状态")
    private Integer status;
    // 其它字段请根据实际业务补充
}
