package com.qs.admin.taxhall.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-03
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("client_info")
@Schema(description = "ClientInfo对象")
public class ClientInfo extends Model<ClientInfo> {

    private static final long serialVersionUID = 1L;
    @TableId(value = "unit_code")
    @Schema(description = "办税大厅序号")
    private String unitCode;

    @Schema(description = "客户端或服务类型")
    @TableField("client_type")
    private Integer clientType;

    @TableField("machine_code")
    private String machineCode;

    @Schema(description = "版本号")
    @TableField("version")
    private String version;

    @TableField("ip")
    private String ip;

    @Schema(description = "json配置文件")
    @TableField("config_info")
    private String configInfo;


    @Override
    public Serializable pkVal() {
        return null;
    }

}
