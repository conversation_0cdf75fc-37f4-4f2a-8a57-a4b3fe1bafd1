package com.qs.admin.taxhall.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-03
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("window_business")
@Schema(description = "WindowBusiness对象")
public class WindowBusiness extends Model<WindowBusiness> {

    private static final long serialVersionUID = 1L;

    @TableField("BIZ_UID")
    private Integer bizUid;

    @TableField("WIN_UID")
    private Integer winUid;

    @TableField("PRIORITY")
    private Integer priority;

    @Override
    public Serializable pkVal() {
        return null;
    }

}
