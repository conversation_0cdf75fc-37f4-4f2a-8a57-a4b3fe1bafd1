package com.qs.admin.taxhall.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import javax.validation.constraints.*;
import java.util.Date;

@Data
@Schema(description = "票据兑换DTO")
public class TicketExchangeDTO {
    @Schema(description = "业务ID", required = true)
    @NotNull(message = "业务ID不能为空")
    private Integer bizUid;

    @Schema(description = "类型", required = true)
    @NotNull(message = "类型不能为空")
    private Integer type;

    @Schema(description = "纳税人识别号", required = true)
    @NotBlank(message = "纳税人识别号不能为空")
    private String taxCode;

    @Schema(description = "身份证号", required = true)
    @NotBlank(message = "身份证号不能为空")
    private String idCode;

    @Schema(description = "验证码", required = true)
    @NotBlank(message = "验证码不能为空")
    private String verifyCode;

    @Schema(description = "过期时间")
    private Date expireTime;

    @Schema(description = "年级")
    private String nj;

    @Schema(description = "票据UID")
    private Integer tktUid;
}
