package com.qs.admin.taxhall.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.qs.admin.taxhall.model.vo.WindowBusinessVO;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Value;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-03
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName(value="dbo.WINDOW")
public class Window extends Model<Window> {

    private static final long serialVersionUID = 1L;
    @TableId(type = IdType.AUTO)
    private Integer uid;

    @TableField("[NAME]")
    private String name;

    @TableField("LED_ADDRESS")
    private String ledAddress;

    @TableField("LED_TEXT")
    private String ledText;

    @TableField("ENABLED")
    private Integer enabled;

    @TableField("SID")
    private String sid;

    @TableField("RANK_MODE")
    private String rankMode;

    @TableField("RANK_ADDRESS")
    private String rankAddress;


    private transient List<WindowBusinessVO> bizList;

    @Override
    public Serializable pkVal() {
        return null;
    }

}
