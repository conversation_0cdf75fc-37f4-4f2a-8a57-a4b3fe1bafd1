package com.qs.admin.system.controller;

import com.qs.admin.common.core.Constant;
import com.qs.admin.common.core.Result;
import com.qs.admin.common.core.ResultCode;
import com.qs.admin.common.utils.ComUtil;
import com.qs.admin.common.utils.FileUtil;
import com.qs.admin.common.utils.QscUtil;
import com.qs.admin.system.model.QscAccount;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import springfox.documentation.annotations.ApiIgnore;

import java.util.*;

/**
 * <AUTHOR>
 * @since on 2018/5/11.
 */
@RestController
@RequestMapping("/resource")
//不加入swagger ui里
@ApiIgnore
@Slf4j
public class ResourceController {


    @PostMapping
    public Result uploadResource(@RequestParam("files") MultipartFile[] multipartFiles) throws Exception {

        List<String> filePaths = new ArrayList<>();
        if (!ComUtil.isEmpty(multipartFiles) && multipartFiles.length != 0) {
            for (MultipartFile multipartFile : multipartFiles) {
                int fileType = FileUtil.getFileType(multipartFile.getOriginalFilename());
                filePaths.add(
                        FileUtil.saveFile(multipartFile.getInputStream(), fileType, multipartFile.getOriginalFilename(), null)
                );
            }
        }
        return Result.success(filePaths);
    }


    @ApiOperation(value = "上传图片,返回原图和缩略图", notes = "文件MultipartFile类型", produces = "application/from-data")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "files", value = "文件"
                    , required = true, dataType = "MultipartFile", paramType = "form")
    })
    @PostMapping("/uploadImage")
    public Result uploadImage(@RequestParam Map<String, String> map, @RequestParam(value = "file", required = false) MultipartFile[] multipartFiles) throws Exception {
        List<HashMap> filePaths = new ArrayList<>();
        if (!ComUtil.isEmpty(multipartFiles) && multipartFiles.length != 0) {
            for (MultipartFile multipartFile : multipartFiles) {
                String postFix = multipartFile.getOriginalFilename().split("//.")[multipartFile.getOriginalFilename().split("//.").length - 1];
                if (Arrays.asList(Constant.FilePostFix.IMAGES).contains(postFix)) {
                    throw new Exception("请上传图片");
                }
                HashMap<String, String> retMap = new HashMap<>();
                String url = FileUtil.getFileUrl(FileUtil.saveFile(multipartFile.getInputStream(), 1, multipartFile.getOriginalFilename(), null));
                retMap.put("url", url);
                filePaths.add(retMap);
            }
        }
        return Result.success(filePaths);
    }

    @DeleteMapping
    public Result deleteResource(@RequestParam("filePaths") List<String> filePaths) {
        if (!ComUtil.isEmpty(filePaths) && filePaths.size() != 0) {
            for (String item : filePaths) {
                if (!FileUtil.deleteUploadedFile(item)) {
                    return Result.failure(ResultCode.FALSE);
                }
            }
        }
        return Result.success(filePaths);
    }

}
