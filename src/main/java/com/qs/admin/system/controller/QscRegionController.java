package com.qs.admin.system.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.qs.admin.common.constant.Constants;
import com.qs.admin.common.core.Query;
import com.qs.admin.common.core.Result;
import com.qs.admin.common.core.ResultCode;
import com.qs.admin.common.datasource.DBIdentifier;
import com.qs.admin.common.domain.Area;
import com.qs.admin.common.shiro.LoginAccount;
import com.qs.admin.common.shiro.security.AccountContext;
import com.qs.admin.system.model.QscDbserver;
import com.qs.admin.system.model.QscRegion;
import com.qs.admin.system.model.QscTaxUnit;
import com.qs.admin.system.service.QscRegionService;
import com.qs.admin.system.service.QscTaxUnitService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @data 2020-04-07.
 */
@Slf4j
@RestController
@RequestMapping("/qscRegion")
@Api(value = "QscRegion控制类", description = "控制类接口测试")
public class QscRegionController {
    @Resource
    private QscRegionService regionService;

    @Autowired
    private QscTaxUnitService taxUnitService;

    @GetMapping
    @ApiOperation(value = "获取全部", notes = "返回分页过后的数据", httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "查询页码", paramType = "query", dataType = "Integer", defaultValue = "1"),
            @ApiImplicitParam(name = "pageSize", value = "每页数据量", paramType = "query", dataType = "Integer", defaultValue = "10")
    })

    public Result list(@ApiIgnore @RequestParam Map<String, Object> params) {
        DBIdentifier.setUnitCode(Constants.UNIT_CODE);
        Page qscRegionPage = new Query<>(params);
        QueryWrapper<QscRegion> wrapper = new QueryWrapper<>();
        wrapper.eq("level", params.get("level"));
        wrapper.eq("pid", params.get("unitCode"));
        IPage<QscRegion> qscRegionIPage = regionService.page(qscRegionPage, wrapper);
        return Result.success(qscRegionIPage);
    }

    @PostMapping
    @ApiOperation(value = "添加数据", notes = "添加新的数据", httpMethod = "POST")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "qscRegion", value = "待添加的qscRegion实例", paramType = "body", dataType = "QscRegion", required = true)
    })
    public Result add(@ApiIgnore @RequestParam Map<String, Object> params) {
        boolean b = regionService.save(params);
        if (b) {
            return Result.success(b);
        }
        return Result.failure(ResultCode.FALSE);
    }

    @DeleteMapping("/{id}")
    @ApiOperation(value = "删除数据", notes = "根据id删除数据", httpMethod = "DELETE")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "查询的id", paramType = "path", required = true, dataType = "String"),
    })
    public Result delete(@PathVariable String id) {
        DBIdentifier.setUnitCode(Constants.UNIT_CODE);
        if(null!=id){
//            Integer sid = Integer.valueOf(id.trim());
            boolean b = regionService.removeById(id);
            return Result.success(b);
        }
        return Result.failure(ResultCode.DATA_IS_WRONG);

    }

    @PutMapping
    @ApiOperation(value = "更新数据", notes = "根据内容更新数据", httpMethod = "PUT")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "qscRegion", value = "更新的qscRegion实例", paramType = "body", dataType = "QscRegion", required = true)
    })
    public Result update(@ApiIgnore @RequestParam Map<String, Object> params) {
        boolean b = regionService.update(params);
        if (b) {
            return Result.success(b);
        }
        return Result.failure(ResultCode.FALSE);

    }

    @GetMapping("/{id}")
    @ApiOperation(value = "获取单个值", notes = "查看单个项目的内容", httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "查询的id", paramType = "path", required = true, dataType = "String", defaultValue = "0")
    })
    public Result detail(@PathVariable Integer id) {
        DBIdentifier.setUnitCode(Constants.UNIT_CODE);
        QscRegion qscRegion = regionService.getById(id);
        return Result.success(qscRegion);
    }
}