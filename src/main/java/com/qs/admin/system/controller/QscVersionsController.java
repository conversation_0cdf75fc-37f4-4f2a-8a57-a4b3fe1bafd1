package com.qs.admin.system.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.qs.admin.common.core.Query;
import com.qs.admin.common.core.Result;
import com.qs.admin.system.model.QscVersions;
import com.qs.admin.system.service.QscVersionsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;
import java.util.Map;

/**
* <AUTHOR>
* @data 2020-04-07.
*/
@RestController
@RequestMapping("/qscVersions")
@Api(value = "QscVersions控制类", description = "控制类接口测试")
public class QscVersionsController {
@Resource
private QscVersionsService qscVersionsService;

@GetMapping
@ApiOperation(value = "获取全部",notes = "返回分页过后的数据",httpMethod = "GET")
@ApiImplicitParams({
@ApiImplicitParam(name = "page",value = "查询页码", paramType = "query",dataType = "Integer",defaultValue = "1"),
@ApiImplicitParam(name = "pageSize",value = "每页数据量", paramType = "query",dataType = "Integer",defaultValue = "10")
})

public Result list(@ApiIgnore @RequestParam Map<String, Object> params) {
Page qscVersionsPage = new Query<>(params);
QueryWrapper<QscVersions> wrapper = new QueryWrapper<>();
IPage<QscVersions> qscVersionsIPage = qscVersionsService.page(qscVersionsPage,wrapper);
return Result.success(qscVersionsIPage);
}
@PostMapping
@ApiOperation(value = "添加数据",notes = "添加新的数据",httpMethod = "POST")
@ApiImplicitParams({
@ApiImplicitParam(name = "qscVersions",value = "待添加的qscVersions实例",paramType = "body",dataType = "QscVersions",required = true)
})
public Result add(@RequestBody QscVersions qscVersions) {
boolean b =  qscVersionsService.save(qscVersions);
return Result.success(b);
}

@DeleteMapping("/{id}")
@ApiOperation(value = "删除数据",notes = "根据id删除数据",httpMethod = "DELETE")
@ApiImplicitParams({
@ApiImplicitParam(name = "id",value = "查询的id", paramType = "path",required = true,dataType = "Integer"),
})
public Result delete(@PathVariable Integer id) {
boolean b = qscVersionsService.removeById(id);
return Result.success(b);
}

@PutMapping
@ApiOperation(value = "更新数据",notes = "根据内容更新数据",httpMethod = "PUT")
@ApiImplicitParams({
@ApiImplicitParam(name = "qscVersions",value = "更新的qscVersions实例",paramType = "body",dataType = "QscVersions",required = true)
})
public Result update(@RequestBody QscVersions qscVersions) {
boolean b = qscVersionsService.updateById(qscVersions);
return Result.success(b);
}

@GetMapping("/{id}")
@ApiOperation(value = "获取单个值",notes = "查看单个项目的内容",httpMethod = "GET")
@ApiImplicitParams({
@ApiImplicitParam(name = "id",value = "查询的id", paramType = "path",required = true,dataType = "Integer",defaultValue = "0")
})
public Result detail(@PathVariable Integer id) {
QscVersions qscVersions = qscVersionsService.getById(id);
return Result.success(qscVersions);
}
}