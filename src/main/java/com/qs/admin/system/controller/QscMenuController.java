package com.qs.admin.system.controller;

import com.qs.admin.common.constant.Constants;
import com.qs.admin.common.core.Result;
import com.qs.admin.common.datasource.DBIdentifier;
import com.qs.admin.system.model.QscMenu;
import com.qs.admin.system.service.QscMenuService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotBlank;
import java.util.Map;

/**
 * <AUTHOR>
 * @data 2020-04-09.
 */
@Slf4j
@RestController
@RequestMapping("/qscMenu")
@Api(value = "QscMenu控制类", description = "控制类接口测试")
public class QscMenuController {
    @Autowired
    private QscMenuService menuService;

    @GetMapping("/{username}")
    @ApiOperation(value = "获取单个值", notes = "查看单个项目的内容", httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "username", value = "查询的用户名", paramType = "path", required = true, dataType = "String")
    })
    public Result getAccountMenu(@NotBlank(message = "{required}") @PathVariable String username) {
        DBIdentifier.setUnitCode(Constants.UNIT_CODE);
        Map<String, Object> map = menuService.findByAccount(username);
        return Result.success(map);
    }


    @PostMapping
    @ApiOperation(value = "添加数据", notes = "添加新的数据", httpMethod = "POST")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "qscMenu", value = "待添加的qscMenu实例", paramType = "body", dataType = "QscMenu", required = true)
    })
    public Result add(@RequestBody QscMenu qscMenu) {
        DBIdentifier.setUnitCode(Constants.UNIT_CODE);
        boolean b = menuService.save(qscMenu);
        return Result.success(b);
    }

    @DeleteMapping("/{id}")
    @ApiOperation(value = "删除数据", notes = "根据id删除数据", httpMethod = "DELETE")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "查询的id", paramType = "path", required = true, dataType = "Integer"),
    })
    public Result delete(@PathVariable Integer id) {
        DBIdentifier.setUnitCode(Constants.UNIT_CODE);
        boolean b = menuService.removeById(id);
        return Result.success(b);
    }

    @PutMapping
    @ApiOperation(value = "更新数据", notes = "根据内容更新数据", httpMethod = "PUT")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "qscMenu", value = "更新的qscMenu实例", paramType = "body", dataType = "QscMenu", required = true)
    })
    public Result update(@RequestBody QscMenu qscMenu) {
        DBIdentifier.setUnitCode(Constants.UNIT_CODE);
        boolean b = menuService.updateById(qscMenu);
        return Result.success(b);
    }

}