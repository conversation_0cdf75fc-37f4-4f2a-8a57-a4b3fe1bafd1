package com.qs.admin.system.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.qs.admin.common.constant.Constants;
import com.qs.admin.common.core.Query;
import com.qs.admin.common.core.Result;
import com.qs.admin.common.datasource.DBIdentifier;
import com.qs.admin.system.model.QscAccount;
import com.qs.admin.system.model.QscDbserver;
import com.qs.admin.system.service.QscDbserverService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Map;

/**
 * <AUTHOR>
 * @data 2020-04-07.
 */
@RestController
@RequestMapping("/qscDbserver")
@Api(value = "QscDbserver控制类", description = "控制类接口测试")
public class QscDbserverController {
    @Resource
    private QscDbserverService qscDbserverService;

    @GetMapping
    @ApiOperation(value = "获取全部", notes = "返回分页过后的数据", httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "查询页码", paramType = "query", dataType = "Integer", defaultValue = "1"),
            @ApiImplicitParam(name = "pageSize", value = "每页数据量", paramType = "query", dataType = "Integer", defaultValue = "10")
    })
    public Result list(@ApiIgnore @RequestParam Map<String, Object> params) {
        DBIdentifier.setUnitCode(Constants.UNIT_CODE);
        Page qscDbserverPage = new Query<>(params);
        QueryWrapper<QscDbserver> wrapper = new QueryWrapper<>();
        IPage<QscDbserver> qscDbserverIPage = qscDbserverService.page(qscDbserverPage, wrapper);
        return Result.success(qscDbserverIPage);
    }

    @PostMapping
    @ApiOperation(value = "添加数据", notes = "添加新的数据", httpMethod = "POST")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "qscDbserver", value = "待添加的qscDbserver实例", paramType = "body", dataType = "QscDbserver", required = true)
    })
    public Result add(@ApiIgnore @RequestParam Map<String, Object> params) {
        DBIdentifier.setUnitCode(Constants.UNIT_CODE);
        QscDbserver qscDbserver = JSONObject.parseObject(params.get("params").toString(), QscDbserver.class);
        boolean b = qscDbserverService.save(qscDbserver);
        return Result.success(b);
    }

    @DeleteMapping("/{unitCode}")
    @ApiOperation(value = "删除数据", notes = "根据id删除数据", httpMethod = "DELETE")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "unitCode", value = "查询的id", paramType = "path", required = true, dataType = "Integer"),
    })
    public Result delete(@PathVariable String unitCode) {
        DBIdentifier.setUnitCode(Constants.UNIT_CODE);
        String[] ids = unitCode.split(StringPool.COMMA);
        boolean b = qscDbserverService.delByIds(ids);
        return Result.success(b);
    }

    @PutMapping
    @ApiOperation(value = "更新数据", notes = "根据内容更新数据", httpMethod = "PUT")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "qscDbserver", value = "更新的qscDbserver实例", paramType = "body", dataType = "QscDbserver", required = true)
    })
    public Result update(@ApiIgnore @RequestParam Map<String, Object> params) {
        boolean b = qscDbserverService.update(params);
        return Result.success(b);
    }

    @GetMapping("/{unitCode}")
    @ApiOperation(value = "获取单个值", notes = "查看单个项目的内容", httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "unitCode", value = "查询的id", paramType = "path", required = true, dataType = "Integer", defaultValue = "0")
    })
    public Result detail(@PathVariable String unitCode) {
        DBIdentifier.setUnitCode(Constants.UNIT_CODE);
        QscDbserver qscDbserver = qscDbserverService.getById(unitCode);
        return Result.success(qscDbserver);
    }
}