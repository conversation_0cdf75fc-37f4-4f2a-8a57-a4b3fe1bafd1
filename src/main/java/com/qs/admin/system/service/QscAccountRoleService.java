package com.qs.admin.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.qs.admin.system.model.QscAccountRole;

import java.util.List;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-10
 */
public interface QscAccountRoleService extends IService<QscAccountRole> {

    void deleteAccountRolesByRoleId(String[] roleIds);

    void deleteAccountRolesByAccountId(Integer[] accountIds);

    List<String> findAccountIdsByRoleId(String[] roleIds);

    Integer findRoleIdByAccountId(Integer accountId);
}
