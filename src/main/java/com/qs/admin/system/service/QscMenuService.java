package com.qs.admin.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.qs.admin.common.core.Tree;
import com.qs.admin.system.model.QscMenu;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-09
 */
public interface QscMenuService extends IService<QscMenu> {

//    List<QscMenu> findAccountPermissions(String username);

    Map<String, Object> findByAccount(String username);

//    Map<String, Object> findMenus(QscMenu menu);

    Tree<QscMenu> findByRoleId(String roleId);

    List<QscMenu> findMenuList(QscMenu menu);


    void createMenu(QscMenu menu);

    void updateMenu(QscMenu menu) throws Exception;

    /**
     * 递归删除菜单/按钮
     *
     * @param menuIds menuIds
     */
    void deleteMeuns(String[] menuIds) throws Exception;

}
