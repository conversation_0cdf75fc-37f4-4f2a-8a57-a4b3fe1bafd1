package com.qs.admin.system.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.qs.admin.common.domain.QueryRequest;
import com.qs.admin.system.model.QscRole;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-03
 */
public interface QscRoleService extends IService<QscRole> {

    IPage<QscRole> findRoles(QscRole role, QueryRequest request);

    List<QscRole> findAccountRole(String userName);

    QscRole findByRoleName(String roleName);

    void createRole(QscRole role);

    void deleteRoles(String[] roleIds) throws Exception;

    void updateRole(QscRole role) throws Exception;
}
