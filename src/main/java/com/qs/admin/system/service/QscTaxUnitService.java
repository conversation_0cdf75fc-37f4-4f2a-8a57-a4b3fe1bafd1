package com.qs.admin.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.qs.admin.system.model.QscAccount;
import com.qs.admin.system.model.QscDbserver;
import com.qs.admin.system.model.QscTaxUnit;
import com.qs.admin.system.model.vo.TaxUnitVO;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-03
 */
public interface QscTaxUnitService extends IService<QscTaxUnit> {
    /**
     * 通过regionId查询相关taxUnit
     *
     * @param regionId
     * @return
     */
    List<QscTaxUnit> findByRegion(String regionId);

    List<TaxUnitVO> findByRegionId(String regionId);


    TaxUnitVO findByUnitCode(String unitCode);

//    List<QscTaxUnit> findByRegionLevel(Integer level, String regionId, String unitCode);

    boolean save(Map<String, Object> params);

    boolean update(Map<String, Object> params);

    boolean isExistByUnitCode(String unitCode);

    boolean delete(String unitCode);
}
