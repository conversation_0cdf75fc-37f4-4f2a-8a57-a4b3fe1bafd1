package com.qs.admin.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.qs.admin.system.model.QscAccount;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-03
 */
public interface QscAccountMapper extends BaseMapper<QscAccount> {
    IPage<QscAccount> findAccountDetail(Page page, @Param("account") QscAccount account);

    QscAccount findDetail(String username);

}
