package com.qs.admin.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.qs.admin.system.model.QscRole;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-03
 */
public interface QscRoleMapper extends BaseMapper<QscRole> {
    @Select("select r.*\n" +
            "from qsc_role r\n" +
            "left join qsc_account_role ur on (r.role_id = ur.role_id)\n" +
            "left join qsc_account u on (u.uid = ur.account_id)\n" +
            "where u.username = #{userName}")
    List<QscRole> findAccountRole(String userName);
}
