package com.qs.admin.system.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2020-12-08
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("qsc_nvr")
@ApiModel(value = "QscNvr对象", description = "")
public class QscNvr implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableField("ip")
    private String ip;

    @TableField("username")
    private String username;

    @TableField("password")
    private String password;

    @TableField("port")
    private Integer port;

    @TableField("unit_code")
    private String unitCode;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @TableField("device_type")
    private Integer deviceType;

    @TableField("device_name")
    private String deviceName;

    @TableField("productor")
    private Integer productor;

}
