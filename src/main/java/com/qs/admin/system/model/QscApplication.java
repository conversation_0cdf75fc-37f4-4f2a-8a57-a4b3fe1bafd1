package com.qs.admin.system.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-03
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("qsc_application")
@ApiModel(value="QscApplication对象", description="")
public class QscApplication extends Model<QscApplication> {

    private static final long serialVersionUID = 1L;

    @TableId
    @TableField("APPID")
    private String appid;

    @TableField("SECRET_KEY")
    private String secretKey;

    @TableField("ACCESS_TOKEN")
    private String accessToken;

    @TableField("EXPIRE_DATE")
    private Date expireDate;

    @TableField("LAST_UPDATE")
    private Date lastUpdate;

    @TableField("STATUS_CALLBACK_URL")
    private String statusCallbackUrl;


    @Override
    protected Serializable pkVal() {
        return null;
    }

}
