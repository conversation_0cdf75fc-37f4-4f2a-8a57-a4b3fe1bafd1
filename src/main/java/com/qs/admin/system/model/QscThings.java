package com.qs.admin.system.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-03
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("qsc_things")
@ApiModel(value="QscThings对象", description="")
public class QscThings extends Model<QscThings> {

    private static final long serialVersionUID = 1L;

    @TableField("UID")
    private Integer uid;

    @TableField("SWSX_DM")
    private String swsxDm;

    @TableField("SWSX_MC")
    private String swsxMc;

    @TableField("SJ_SWSX_DM")
    private String sjSwsxDm;

    @TableField("XYBZ")
    private String xybz;

    @TableField("YXBZ")
    private String yxbz;

    @TableField("PRINT_JSP")
    private String printJsp;

    @TableField("VIEW_JSP")
    private String viewJsp;

    @TableField("COPY_BZ")
    private String copyBz;

    @TableField("COPY_JSP")
    private String copyJsp;

    @TableField("CYSXBZ")
    private String cysxbz;

    @TableField("SLSWSX_DM")
    private String slswsxDm;

    @TableField("LJDZ")
    private String ljdz;

    @TableField("YSHBZ")
    private String yshbz;

    @TableField("JSBZ")
    private String jsbz;

    @TableField("XSD_ID")
    private String xsdId;

    @TableField("ZDDR")
    private String zddr;

    @TableField("DRRY")
    private String drry;

    @TableField("ZG_XTGNDM")
    private String zgXtgndm;

    @TableField("ZG_XTGNMC")
    private String zgXtgnmc;

    @TableField("WSBZ")
    private String wsbz;

    @TableField("CFTJ")
    private String cftj;

    @TableField("TABLE_NAME")
    private String tableName;

    @TableField("APP_VIEW")
    private String appView;

    @TableField("TSDXBZ")
    private String tsdxbz;


    @Override
    protected Serializable pkVal() {
        return null;
    }

}
