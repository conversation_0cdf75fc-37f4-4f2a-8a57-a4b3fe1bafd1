package com.qs.admin.system.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-03
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("qsc_system_log")
@ApiModel(value="QscSystemLog对象", description="")
public class QscSystemLog extends Model<QscSystemLog> {

    private static final long serialVersionUID = 1L;

    @TableField("uid")
    private Long uid;

    @TableField("log_time")
    private Date logTime;

    @TableField("log_type")
    private String logType;

    @TableField("log_level")
    private String logLevel;

    @TableField("threadid")
    private String threadid;

    @TableField("unit_code")
    private String unitCode;

    @TableField("log_module")
    private String logModule;

    @TableField("log_content")
    private String logContent;


    @Override
    protected Serializable pkVal() {
        return null;
    }

}
