package com.qs.admin.system.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-03
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("qsc_tax_unit")
@ApiModel(value="QscTaxUnit对象", description="")
public class QscTaxUnit extends Model<QscTaxUnit> {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "办税大厅登记序号")
    @TableId
    @TableField("unit_code")
    private String unitCode;

    @ApiModelProperty(value = "办税大厅名称")
    @TableField("unit_name")
    private String unitName;

    @ApiModelProperty(value = "办税大厅地址")
    @TableField("unit_address")
    private String unitAddress;

    @TableField("unit_ip")
    private String unitIp;

    @TableField("subject_province")
    private String subjectProvince;

    @TableField("subject_city")
    private String subjectCity;

    @TableField("subject_district")
    private String subjectDistrict;

    @TableField("subject_street")
    private String subjectStreet;

    @ApiModelProperty(value = "税务机关代码")
    @TableField("govt_code")
    private String govtCode;

    @ApiModelProperty(value = "税务机关名称")
    @TableField("govt_name")
    private String govtName;

    @ApiModelProperty(value = "经度（百度）")
    @TableField("longitude")
    private Double longitude;

    @ApiModelProperty(value = "维度（百度）")
    @TableField("latitude")
    private Double latitude;

    @TableField("last_update_date")
    private Date lastUpdateDate;

    @TableField("last_update_user")
    private String lastUpdateUser;

    @ApiModelProperty(value = "电话")
    @TableField("unit_phone")
    private String unitPhone;

    @ApiModelProperty(value = "业务范围描述")
    @TableField("business_description")
    private String businessDescription;

    @ApiModelProperty(value = "工作时间-上午")
    @TableField("work_time_am")
    private String workTimeAm;

    @ApiModelProperty(value = "工作时间-下午")
    @TableField("work_time_pm")
    private String workTimePm;

    @ApiModelProperty(value = "公交")
    @TableField("traffic_routes")
    private String trafficRoutes;

    @ApiModelProperty(value = "人流量显示标志")
    @TableField("visitors_flowrate_flg")
    private Integer visitorsFlowrateFlg;

    @ApiModelProperty(value = "预约叫号标志")
    @TableField("order_ticket_flg")
    private Integer orderTicketFlg;

    @ApiModelProperty(value = "手机取号标志")
    @TableField("phone_ticket_flg")
    private Integer phoneTicketFlg;

    @ApiModelProperty(value = "叫号信息标志")
    @TableField("ticket_info_flg")
    private Integer ticketInfoFlg;

    @ApiModelProperty(value = "当前等候人数（人）")
    @TableField("cur_wait_num")
    private Integer curWaitNum;

    @ApiModelProperty(value = "平均等候时间（分钟）")
    @TableField("average_wait_time")
    private Integer averageWaitTime;

    @ApiModelProperty(value = "预计等候时间（分钟）")
    @TableField("evaluate_wait_time")
    private Integer evaluateWaitTime;

    @ApiModelProperty(value = "最后统计时间")
    @TableField("last_statistical_date")
    private Date lastStatisticalDate;

    @ApiModelProperty(value = "可预约天数")
    @TableField("order_number_day")
    private Integer orderNumberDay;

    @ApiModelProperty(value = "可预约时间段")
    @TableField("order_time_period")
    private String orderTimePeriod;

    @ApiModelProperty(value = "逻辑删除")
    @TableField("del")
    private Integer del;

    @ApiModelProperty(value = "当日可否预约标志")
    @TableField("today_book_flg")
    private Integer todayBookFlg;


    @Override
    protected Serializable pkVal() {
        return null;
    }

}
