package com.qs.admin.system.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-03
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("qsc_calendar_special")
@ApiModel(value="QscCalendarSpecial对象", description="")
public class QscCalendarSpecial extends Model<QscCalendarSpecial> {

    private static final long serialVersionUID = 1L;

    @TableField("unit_code")
    private String unitCode;

    @TableField("year")
    private Integer year;

    @TableField("month")
    private Integer month;

    @TableField("day")
    private Integer day;

    @TableField("status")
    private Integer status;


    @Override
    protected Serializable pkVal() {
        return null;
    }

}
