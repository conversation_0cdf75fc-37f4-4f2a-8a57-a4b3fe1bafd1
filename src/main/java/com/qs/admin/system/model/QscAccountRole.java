package com.qs.admin.system.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("qsc_account_role")
@ApiModel(value="QscAccountRole对象", description="")
public class QscAccountRole extends Model<QscAccountRole> {

    private static final long serialVersionUID = 1L;

    @TableField("account_id")
    private Integer accountId;

    @TableField("role_id")
    private Integer roleId;


    @Override
    protected Serializable pkVal() {
        return null;
    }

}
