package com.qs.admin.system.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.transaction.annotation.Transactional;

import java.io.Serializable;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-03
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("qsc_versions_package")
@ApiModel(value="QscVersionsPackage对象", description="")
public class QscVersionsPackage extends Model<QscVersionsPackage> {

    private static final long serialVersionUID = 1L;

    @TableField("product_key")
    private String productKey;

    @TableField("version")
    private String version;

    @TableField("path")
    private String path;

    @TableField("memo")
    private String memo;


    @Override
    protected Serializable pkVal() {
        return null;
    }

}
