package com.qs.admin.system.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-09
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("qsc_menu")
@ApiModel(value="QscMenu对象", description="")
public class QscMenu extends Model<QscMenu> {

    private static final long serialVersionUID = 1L;

    public static final String TYPE_MENU = "0";

    public static final String TYPE_BUTTON = "1";

    @ApiModelProperty(value = "菜单/按钮ID")
    @TableField("menu_id")
    private Integer menuId;

    @ApiModelProperty(value = "上级菜单ID")
    @TableField("parent_id")
    private Integer parentId;

    @ApiModelProperty(value = "菜单/按钮名称")
    @TableField("menu_name")
    private String menuName;

    @ApiModelProperty(value = "对应路由path")
    @TableField("path")
    private String path;

    @ApiModelProperty(value = "对应路由组件component")
    @TableField("component")
    private String component;

    @ApiModelProperty(value = "图标")
    @TableField("icon")
    private String icon;

    @ApiModelProperty(value = "排序")
    @TableField("order_num")
    private Integer orderNum;

    @ApiModelProperty(value = "创建时间")
    @TableField("create_date")
    private Date createDate;

    @ApiModelProperty(value = "修改时间")
    @TableField("update_date")
    private Date updateDate;

    @ApiModelProperty(value = "修改人员")
    @TableField("update_by")
    private Integer updateBy;

    private transient String createDateFrom;
    private transient String createDateTo;

    @Override
    protected Serializable pkVal() {
        return null;
    }

}
