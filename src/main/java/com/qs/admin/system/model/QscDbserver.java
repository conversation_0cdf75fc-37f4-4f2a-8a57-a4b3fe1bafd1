package com.qs.admin.system.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-03
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("qsc_dbserver")
@ApiModel(value="QscDbserver对象", description="")
public class QscDbserver extends Model<QscDbserver> {

    private static final long serialVersionUID = 1L;

    @TableId
    @TableField("unit_code")
    private String unitCode;

    @TableField("unit_name")
    private String unitName;

    @TableField("server")
    private String server;

    @TableField("dbname")
    private String dbname;

    @TableField("dbuser")
    private String dbuser;

    @TableField("dbpass")
    private String dbpass;

//    @TableField("subject_province")
//    private String subjectProvince;
//
//    @TableField("subject_city")
//    private String subjectCity;

//    @TableField("subject_district")
//    private String subjectDistrict;

    @TableField("last_update_user")
    private String lastUpdateUser;

    @TableField("last_update_date")
    private Date lastUpdateDate;

    @TableField("del")
    private Integer del;


    @Override
    protected Serializable pkVal() {
        return null;
    }

}
