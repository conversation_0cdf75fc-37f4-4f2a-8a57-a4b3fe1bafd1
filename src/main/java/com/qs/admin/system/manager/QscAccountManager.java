//package com.qs.admin.system.manager;
//
//import com.qs.admin.common.service.CacheService;
//import com.qs.admin.common.utils.QscUtil;
//import com.qs.admin.system.model.QscAccount;
//import com.qs.admin.system.model.QscRole;
//import com.qs.admin.system.service.QscAccountService;
//import com.qs.admin.system.service.QscMenuService;
//import com.qs.admin.system.service.QscRoleService;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Service;
//
//import java.util.List;
//import java.util.Map;
//import java.util.Set;
//import java.util.stream.Collectors;
//
///**
// * @Author: ytx
// * @Date: 2020/4/8  15:08
// */
//@Service
//public class QscAccountManager {
//    @Autowired
//    private CacheService cacheService;
//    @Autowired
//    private QscRoleService roleService;
//    @Autowired
//    private QscMenuService menuService;
//    @Autowired
//    private QscAccountService accountService;
//
//    /**
//     * 通过用户名获取用户基本信息
//     *
//     * @param username 用户名
//     * @return 用户基本信息
//     */
//    public QscAccount getAccount(String username) {
//        return QscUtil.selectCacheByTemplate(
//                () -> this.cacheService.getAccount(username),
//                () -> this.accountService.findByUserName(username));
//    }
//
//    /**
//     * 通过用户名获取用户角色集合
//     *
//     * @param username 用户名
//     * @return 角色集合
//     */
//    public Set<String> getAccountRoles(String username) {
//        List<QscRole> roleList = QscUtil.selectCacheByTemplate(
//                () -> this.cacheService.getRoles(username),
//                () -> this.roleService.findAccountRole(username));
//        return roleList.stream().map(QscRole::getRoleName).collect(Collectors.toSet());
//    }
////将menu放入返回实体中
////    public List<String> getAccountRoles(String username) {
////        List<QscMenu> menuList = QscUtil.selectCacheByTemplate(
////                () -> this.cacheService.getRoles(username),
////                () -> this.menuService.findAccountMenus(username));
////        return menuList.stream().m(QscRole::getRoleName).collect(Collectors.toSet());
////    }
//
//
//
//    /**
//     * 将用户相关信息添加到 Redis缓存中
//     *
//     * @param account
//     */
//    public void loadAccountRedisCache(QscAccount account) throws Exception {
//        List<QscRole> roleList  = roleService.findAccountRole(account.getUsername());
//        Map<String,Object> menuMap = menuService.findByAccount(account.getUsername());
////        account.setMenuTree(menuList);
//        // 缓存用户
//        cacheService.saveAccount(account.getUsername());
//    }
//
//    /**
//     * 将用户角色和权限添加到 Redis缓存中
//     *
//     * @param userIds userIds
//     */
//    public void loadAccountPermissionRoleRedisCache(List<String> userIds) throws Exception {
//        for (String userId : userIds) {
//            QscAccount qscAccount = accountService.getById(userId);
//            // 缓存用户角色
//            cacheService.saveRoles(qscAccount.getUsername());
//        }
//    }
//
//    /**
//     * 通过用户 id集合批量删除用户 Redis缓存
//     *
//     * @param accountIds
//     */
//    public void deleteAccountRedisCache(String... accountIds) throws Exception {
//        for (String accountId : accountIds) {
//            QscAccount qscAccount = accountService.getById(accountId);
//            if (qscAccount != null) {
//                cacheService.deleteAccount(qscAccount.getUsername());
//            }
//        }
//    }
//
//}
