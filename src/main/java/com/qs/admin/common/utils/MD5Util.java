package com.qs.admin.common.utils;

import com.qs.admin.common.shiro.ShiroKit;
import org.apache.shiro.codec.Hex;
import org.apache.shiro.crypto.hash.SimpleHash;
import org.apache.shiro.util.ByteSource;

public class MD5Util {

	protected MD5Util(){

	}

	private static final String ALGORITH_NAME = "md5";

	private static final int HASH_ITERATIONS = 2;

	public static String encrypt(String password) {
		return new SimpleHash(ALGORITH_NAME, password, ByteSource.Util.bytes(password), HASH_ITERATIONS).toHex();
	}

	public static String encrypt(String username, String password) {
		return new SimpleHash(ALGORITH_NAME, password, ByteSource.Util.bytes(username.toLowerCase() + password),
				HASH_ITERATIONS).toHex();
	}

	public static String dencrypt(String encodeToString) {
		return new String(Hex.encodeToString(encodeToString.getBytes()));
	}
	public static void main(String[] args) {
		System.out.println(encrypt("ziyun","admin12345"));
		System.out.println(dencrypt("e836b90bbfdaaee4896b5348a9982213"));

		System.out.println(ShiroKit.aesEncrypt("123456"));
		System.out.println(ShiroKit.aesDecrypt("2afb96317e610a6e5c0deb2f4f2ba904"));
	}

}
