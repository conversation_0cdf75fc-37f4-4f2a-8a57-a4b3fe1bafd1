package com.qs.admin.common.utils;

import org.springframework.util.DigestUtils;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.util.Base64;

/**
 * 密码加密工具类
 */
public class PasswordUtil {

    /**
     * 生成盐值
     *
     * @return 盐值
     */
    public static String generateSalt() {
        SecureRandom random = new SecureRandom();
        byte[] salt = new byte[16];
        random.nextBytes(salt);
        return Base64.getEncoder().encodeToString(salt);
    }

    /**
     * 使用MD5加密密码（带盐值）
     *
     * @param password 原始密码
     * @param salt     盐值
     * @return 加密后的密码
     */
    public static String encryptPassword(String password, String salt) {
        return DigestUtils.md5DigestAsHex((password + salt).getBytes());
    }

    /**
     * 使用SHA256加密密码（带盐值）
     *
     * @param password 原始密码
     * @param salt     盐值
     * @return 加密后的密码
     */
    public static String encryptPasswordSHA256(String password, String salt) {
        try {
            MessageDigest md = MessageDigest.getInstance("SHA-256");
            md.update((password + salt).getBytes());
            byte[] digest = md.digest();
            return Base64.getEncoder().encodeToString(digest);
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException("SHA-256算法不可用", e);
        }
    }

    /**
     * 验证密码
     *
     * @param rawPassword     原始密码
     * @param encodedPassword 加密后的密码
     * @param salt            盐值
     * @return 是否匹配
     */
    public static boolean matches(String rawPassword, String encodedPassword, String salt) {
        return encryptPassword(rawPassword, salt).equals(encodedPassword);
    }

    /**
     * 简单的MD5加密（不带盐值，用于兼容现有系统）
     *
     * @param password 原始密码
     * @return 加密后的密码
     */
    public static String simpleEncrypt(String password) {
        return DigestUtils.md5DigestAsHex(password.getBytes());
    }

    /**
     * 验证简单加密的密码
     *
     * @param rawPassword     原始密码
     * @param encodedPassword 加密后的密码
     * @return 是否匹配
     */
    public static boolean simpleMatches(String rawPassword, String encodedPassword) {
        return simpleEncrypt(rawPassword).equals(encodedPassword);
    }
}
