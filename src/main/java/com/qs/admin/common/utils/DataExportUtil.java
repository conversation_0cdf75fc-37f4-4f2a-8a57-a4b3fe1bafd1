package com.qs.admin.common.utils;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.util.List;
import java.util.function.Function;

/**
 * 数据导出工具类
 * 用于处理大数据量的分批导出
 */
@Slf4j
public class DataExportUtil {

    /**
     * 分批导出数据为CSV格式
     * 
     * @param response HTTP响应对象
     * @param fileName 导出文件名
     * @param headers CSV表头
     * @param dataProvider 数据提供函数，接收页码和每页大小，返回分页数据
     * @param rowConverter 行转换函数，将数据对象转换为CSV行
     * @param <T> 数据类型
     */
    public static <T> void exportToCsv(HttpServletResponse response, 
                                       String fileName,
                                       String[] headers,
                                       Function<Page<T>, IPage<T>> dataProvider,
                                       Function<T, String[]> rowConverter) {
        
        response.setContentType("text/csv;charset=UTF-8");
        response.setHeader("Content-Disposition", "attachment; filename=\"" + fileName + "\"");
        response.setHeader("Cache-Control", "no-cache");
        
        try (PrintWriter writer = response.getWriter()) {
            // 写入BOM以支持Excel正确显示中文
            writer.write("\uFEFF");
            
            // 写入表头
            writer.println(String.join(",", headers));
            writer.flush();
            
            int pageNum = 1;
            int pageSize = 1000; // 每批处理1000条
            long totalProcessed = 0;
            
            while (true) {
                try {
                    Page<T> page = new Page<>(pageNum, pageSize);
                    IPage<T> result = dataProvider.apply(page);
                    
                    List<T> records = result.getRecords();
                    if (records == null || records.isEmpty()) {
                        break;
                    }
                    
                    // 写入数据行
                    for (T record : records) {
                        try {
                            String[] row = rowConverter.apply(record);
                            writer.println(String.join(",", row));
                            totalProcessed++;
                            
                            // 每100条刷新一次
                            if (totalProcessed % 100 == 0) {
                                writer.flush();
                            }
                        } catch (Exception e) {
                            log.warn("转换数据行失败: {}", e.getMessage());
                        }
                    }
                    
                    log.info("已导出第{}页，共{}条记录，累计{}条", pageNum, records.size(), totalProcessed);
                    
                    // 如果当前页记录数小于页大小，说明已经是最后一页
                    if (records.size() < pageSize) {
                        break;
                    }
                    
                    pageNum++;
                    
                } catch (Exception e) {
                    log.error("导出第{}页数据失败: {}", pageNum, e.getMessage());
                    break;
                }
            }
            
            writer.flush();
            log.info("数据导出完成，总计{}条记录", totalProcessed);
            
        } catch (IOException e) {
            log.error("导出数据时发生IO异常: {}", e.getMessage());
            if (e.getMessage() != null && e.getMessage().contains("你的主机中的软件中止了一个已建立的连接")) {
                log.warn("客户端在导出过程中断开连接");
            }
        } catch (Exception e) {
            log.error("导出数据失败: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 检查是否应该使用流式导出
     * 
     * @param totalCount 总记录数
     * @return 是否使用流式导出
     */
    public static boolean shouldUseStreamExport(long totalCount) {
        return totalCount > 5000; // 超过5000条记录使用流式导出
    }
    
    /**
     * 转义CSV字段值
     * 
     * @param value 字段值
     * @return 转义后的值
     */
    public static String escapeCsvValue(String value) {
        if (value == null) {
            return "";
        }
        
        // 如果包含逗号、双引号或换行符，需要用双引号包围并转义内部双引号
        if (value.contains(",") || value.contains("\"") || value.contains("\n") || value.contains("\r")) {
            return "\"" + value.replace("\"", "\"\"") + "\"";
        }
        
        return value;
    }
}