package com.qs.admin.common.aspect;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 简单接口幂等性AOP（基于内存，生产建议用Redis或分布式方案）
 */
@Aspect
@Component
@Order(1)
public class IdempotencyAspect {
    private static final ConcurrentHashMap<String, Boolean> tokenStore = new ConcurrentHashMap<>();

    @Around("@annotation(postMapping)")
    public Object around(ProceedingJoinPoint joinPoint, PostMapping postMapping) throws Throwable {
        Object[] args = joinPoint.getArgs();
        String token = null;
        for (Object arg : args) {
            if (arg instanceof String && ((String) arg).startsWith("IDEMP-")) {
                token = (String) arg;
                break;
            }
        }
        if (token != null) {
            if (tokenStore.putIfAbsent(token, Boolean.TRUE) != null) {
                throw new RuntimeException("请勿重复提交");
            }
        }
        try {
            return joinPoint.proceed();
        } finally {
            if (token != null) tokenStore.remove(token);
        }
    }
}
