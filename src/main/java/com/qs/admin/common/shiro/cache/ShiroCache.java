package com.qs.admin.common.shiro.cache;

import com.qs.admin.common.shiro.security.JwtProperties;
import com.qs.admin.common.shiro.security.JwtUtil;
import com.qs.admin.common.utils.LocalCacheManager;
import org.apache.shiro.cache.Cache;
import org.apache.shiro.cache.CacheException;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;

/**
 * 重写Shiro的Cache保存读取
 *
 * @param <K>
 * @param <V>
 */
public class ShiroCache<K, V> implements Cache<K, V> {
    @Autowired
    private JwtUtil jwtUtil;
    @Autowired
    private LocalCacheManager localCacheManager;
    @Autowired
    private JwtProperties jwtProperties;

    public ShiroCache(LocalCacheManager localCacheManager, JwtProperties jwtProperties) {
        this.localCacheManager = localCacheManager;
        this.jwtProperties = jwtProperties;
    }

    /**
     * 获取缓存
     *
     * @param key
     * @return
     * @throws CacheException
     */
    @Override
    public V get(K key) throws CacheException {
        String tempKey = this.getKey(key);
        return (V) localCacheManager.get(tempKey);
    }

    /**
     * 保存缓存
     *
     * @param key
     * @param value
     * @return
     * @throws CacheException
     */
    @Override
    public V put(K key, V value) throws CacheException {
        String tempKey = this.getKey(key);
        localCacheManager.put(tempKey, value);
        return value;
    }

    /**
     * 移除缓存
     *
     * @param key
     * @return
     * @throws CacheException
     */
    @Override
    public V remove(K key) throws CacheException {
        String tempKey = this.getKey(key);
        V value = (V) localCacheManager.get(tempKey);
        localCacheManager.remove(tempKey);
        return value;
    }

    @Override
    public void clear() throws CacheException {
        // Caffeine不支持单独清理命名空间下所有key，这里不做实现
    }

    @Override
    public int size() {
        // 估算值
        return 10000;
    }

    @Override
    public Set<K> keys() {
        // Caffeine不支持直接获取所有key，这里返回空
        return Collections.emptySet();
    }

    @Override
    public Collection<V> values() {
        // Caffeine不支持直接获取所有value，这里返回空
        return Collections.emptyList();
    }

    /**
     * 缓存的key名称获取为shiro:cache:account
     *
     * @param key
     * @return
     */
    private String getKey(Object key) {
        return "shiro:cache:" + key.toString();
    }
}
