package com.qs.admin.common.shiro.cache;

import com.qs.admin.common.shiro.security.JwtProperties;
import com.qs.admin.common.utils.LocalCacheManager;
import org.apache.shiro.cache.Cache;
import org.apache.shiro.cache.CacheException;
import org.apache.shiro.cache.CacheManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class ShiroCacheManager implements CacheManager {

    @Autowired
    LocalCacheManager localCacheManager;

    @Autowired
    JwtProperties jwtProperties;

    @Override
    public <K, V> Cache<K, V> getCache(String s) throws CacheException {
        return new ShiroCache<K,V>(localCacheManager, jwtProperties);
    }
}
