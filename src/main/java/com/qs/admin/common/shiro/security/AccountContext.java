package com.qs.admin.common.shiro.security;


import com.qs.admin.common.shiro.LoginAccount;

public class AccountContext implements AutoCloseable {

    static final ThreadLocal<LoginAccount> current = new ThreadLocal<>();

    public AccountContext(LoginAccount account) {
        current.set(account);
    }

    public static LoginAccount getCurrentAccount() {
        return current.get();
    }

    public static void setCurrentAccount(LoginAccount account) {
        current.set(account);
    }

    public void close() {
        current.remove();
    }
}