package com.qs.admin.common.shiro.security;


import com.qs.admin.common.constant.Constants;
import com.qs.admin.common.constant.SecurityConsts;
import com.qs.admin.common.datasource.DBIdentifier;
import com.qs.admin.common.datasource.ProjectDBMgr;
import com.qs.admin.common.shiro.LoginAccount;

import javax.servlet.*;
import javax.servlet.annotation.WebFilter;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;

@WebFilter(filterName = "accountContextFilter",urlPatterns = "/*")
public class AccountContextFilter implements Filter {
    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
    }

    @Override
    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain filterChain) throws IOException, ServletException {
        HttpServletRequest httpServletRequest = (HttpServletRequest) servletRequest;
        String authorization = httpServletRequest.getHeader(SecurityConsts.REQUEST_AUTH_HEADER);
        if(authorization!=null){
            String account = JwtUtil.getClaim(authorization, SecurityConsts.ACCOUNT);
            LoginAccount loginAccount = new LoginAccount(account);
            try (AccountContext context = new AccountContext(loginAccount)) {
                filterChain.doFilter(servletRequest, servletResponse);
            }
        }else{
            filterChain.doFilter(servletRequest, servletResponse);
        }
    }

    @Override
    public void destroy() {
    }
}
