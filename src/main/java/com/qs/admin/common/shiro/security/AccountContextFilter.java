//package com.qs.admin.common.shiro.security;
//
//
//import com.qs.admin.common.constant.Constants;
//import com.qs.admin.common.constant.SecurityConsts;
////import com.qs.admin.common.datasource.DBIdentifier;
////import com.qs.admin.common.datasource.ProjectDBMgr;
//import com.qs.admin.common.shiro.LoginAccount;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.web.context.support.SpringBeanAutowiringSupport;
//
//import javax.servlet.*;
//import javax.servlet.annotation.WebFilter;
//import javax.servlet.http.HttpServletRequest;
//import java.io.IOException;
//
////@WebFilter(filterName = "accountContextFilter",urlPatterns = "/*")
//public class AccountContextFilter implements Filter {
//    @Autowired
//    private ShiroJwtUtil jwtUtil;
//
//        @Override
//    public void init(FilterConfig filterConfig) throws ServletException {
//        try {
//            SpringBeanAutowiringSupport.processInjectionBasedOnServletContext(this, filterConfig.getServletContext());
//            if (jwtUtil == null) {
//                System.err.println("警告: JwtUtil 注入失败，AccountContextFilter 将跳过JWT验证");
//            }
//        } catch (Exception e) {
//            System.err.println("AccountContextFilter 初始化异常: " + e.getMessage());
//        }
//    }
//
//    @Override
//    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain filterChain) throws IOException, ServletException {
//        HttpServletRequest httpServletRequest = (HttpServletRequest) servletRequest;
//        String requestURI = httpServletRequest.getRequestURI();
//        // 白名单放行
//        if (isWhiteListPath(requestURI)) {
//            filterChain.doFilter(servletRequest, servletResponse);
//            return;
//        }
//        String authorization = httpServletRequest.getHeader(SecurityConsts.REQUEST_AUTH_HEADER);
//        if(authorization != null && jwtUtil != null){
//            try {
//                String account = jwtUtil.getClaim(authorization, SecurityConsts.ACCOUNT);
//                if (account != null && jwtUtil.verify(authorization)) {
//                    LoginAccount loginAccount = new LoginAccount(account);
//                    try (AccountContext context = new AccountContext(loginAccount)) {
//                        filterChain.doFilter(servletRequest, servletResponse);
//                    }
//                } else {
//                    // JWT验证失败，继续执行但不设置账户上下文
//                    filterChain.doFilter(servletRequest, servletResponse);
//                }
//            } catch (Exception e) {
//                // JWT解析异常，记录日志并继续执行
//                System.err.println("JWT解析异常: " + e.getMessage());
//                filterChain.doFilter(servletRequest, servletResponse);
//            }
//        } else {
//            filterChain.doFilter(servletRequest, servletResponse);
//        }
//    }
//
//    @Override
//    public void destroy() {
//    }
//
//    /**
//     * 判断是否为白名单路径
//     */
//    private boolean isWhiteListPath(String requestURI) {
//        // API接口白名单
//        if (requestURI.startsWith("/api/v1/agentInfo/") ||
//            requestURI.startsWith("/api/v1/auth/")) {
//            return true;
//        }
//
//        // 静态资源和页面
//        if (requestURI.startsWith("/login") ||
//            requestURI.startsWith("/static/") ||
//            requestURI.startsWith("/public/") ||
//            requestURI.startsWith("/assets/") ||
//            requestURI.startsWith("/css/") ||
//            requestURI.startsWith("/js/") ||
//            requestURI.startsWith("/images/") ||
//            requestURI.startsWith("/fonts/")) {
//            return true;
//        }
//
//        // Knife4j API文档
//        if (requestURI.startsWith("/v2/api-docs") ||
//            requestURI.startsWith("/webjars/") ||
//            requestURI.startsWith("/doc.html") ||
//            requestURI.equals("/favicon.ico")) {
//            return true;
//        }
//
//        // 健康检查和错误页面
//        if (requestURI.equals("/") ||
//            requestURI.equals("/health") ||
//            requestURI.startsWith("/error") ||
//            requestURI.startsWith("/actuator/")) {
//            return true;
//        }
//
//        // 文件扩展名白名单
//        if (requestURI.endsWith(".html") ||
//            requestURI.endsWith(".js") ||
//            requestURI.endsWith(".css") ||
//            requestURI.endsWith(".ico") ||
//            requestURI.endsWith(".png") ||
//            requestURI.endsWith(".jpg") ||
//            requestURI.endsWith(".gif") ||
//            requestURI.endsWith(".svg") ||
//            requestURI.endsWith(".woff") ||
//            requestURI.endsWith(".woff2") ||
//            requestURI.endsWith(".ttf") ||
//            requestURI.endsWith(".eot")) {
//            return true;
//        }
//
//        return false;
//    }
//}
