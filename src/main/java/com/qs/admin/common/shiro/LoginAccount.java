package com.qs.admin.common.shiro;

import lombok.Data;

import java.io.Serializable;

/**
 * 
 * <AUTHOR>
 *
 */
@Data
public class LoginAccount implements Serializable {

    private static final long serialVersionUID = 1L;

    public Integer accountId;          // 主键ID
    public String username;      // 账号
    public String name;         // 姓名
	public String unitCode;      // 组织ID

	public LoginAccount() {
	}

	public LoginAccount(String account) {
		this.username=account;
	}

	public LoginAccount(Integer accountId, String account, String name, String unitCode) {
		this.accountId = accountId;
		this.username = account;
		this.name = name;
		this.unitCode = unitCode;
	}

}
