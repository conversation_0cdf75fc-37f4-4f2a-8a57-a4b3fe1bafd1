package com.qs.admin.common.shiro.security;

import com.alibaba.fastjson2.JSON;
import com.qs.admin.common.constant.Constants;
import com.qs.admin.common.constant.SecurityConsts;
import com.qs.admin.common.domain.R;
import com.qs.admin.common.utils.JwtUtil;
import org.apache.shiro.subject.Subject;
import org.apache.shiro.web.filter.authc.LogoutFilter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StringUtils;

import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.io.PrintWriter;

import org.springframework.beans.factory.annotation.Autowired;

public class SystemLogoutFilter extends LogoutFilter {
    @Autowired
    private JwtUtil jwtUtil;

    private static final Logger logger = LoggerFactory.getLogger(SystemLogoutFilter.class);

    public SystemLogoutFilter() {
    }

    @Override
    protected boolean preHandle(ServletRequest request, ServletResponse response) {
        Subject subject = getSubject(request, response);
        try {
            HttpServletRequest httpServletRequest = (HttpServletRequest) request;
            String authorization = httpServletRequest.getHeader(SecurityConsts.REQUEST_AUTH_HEADER);
            String account = jwtUtil.getUsername(authorization);
            boolean valid = jwtUtil.validateToken(authorization);

            if (valid && !StringUtils.isEmpty(account)) {
                // 清除可能存在的Shiro权限信息缓存（本地缓存已自动过期，无需手动删除）
            }

            subject.logout();
        } catch (Exception ex) {
            logger.error("退出登录错误",ex);
        }

        this.writeResult(response);
        //不执行后续的过滤器
        return false;
    }

    private void writeResult(ServletResponse response){
        //响应Json结果
        PrintWriter out = null;
        try {
            out = response.getWriter();
            R<Object> result = R.ok(Constants.TOKEN_CHECK_SUCCESS);
            out.append(JSON.toJSONString(result));
        } catch (IOException e) {
            logger.error("返回Response信息出现IOException异常:" + e.getMessage());
        } finally {
            if (out != null) {
                out.close();
            }
        }
    }
}
