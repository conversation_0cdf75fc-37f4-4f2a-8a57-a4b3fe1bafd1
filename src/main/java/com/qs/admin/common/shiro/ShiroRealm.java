package com.qs.admin.common.shiro;


import com.qs.admin.common.constant.SecurityConsts;
import com.qs.admin.common.shiro.security.JwtToken;
import com.qs.admin.common.shiro.security.JwtUtil;
import com.qs.admin.system.model.QscAccount;
import com.qs.admin.system.model.QscRole;
import com.qs.admin.system.service.QscAccountService;
import com.qs.admin.system.service.QscRoleService;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.authc.AuthenticationException;
import org.apache.shiro.authc.AuthenticationInfo;
import org.apache.shiro.authc.AuthenticationToken;
import org.apache.shiro.authc.SimpleAuthenticationInfo;
import org.apache.shiro.authz.AuthorizationInfo;
import org.apache.shiro.authz.SimpleAuthorizationInfo;
import org.apache.shiro.realm.AuthorizingRealm;
import org.apache.shiro.subject.PrincipalCollection;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.UnsupportedEncodingException;
import java.util.List;

@Slf4j
@Service
public class ShiroRealm extends AuthorizingRealm {

	@Autowired
//	private UserService userService;
private QscAccountService accountService;
	@Autowired
//	private RoleService roleService;
private QscRoleService roleService;
//	@Autowired
//	private AuthorityService authorityService;

	@Override
	public boolean supports(AuthenticationToken token) {
		return token instanceof JwtToken;
	}

	/**
	 * 默认使用此方法进行用户名正确与否验证，错误抛出异常即可。
	 * @param auth
	 * @return
	 * @throws AuthenticationException
	 */
	@Override
	protected AuthenticationInfo doGetAuthenticationInfo(AuthenticationToken auth)
	        throws AuthenticationException {
		String token = (String)auth.getPrincipal();
		String account  = JwtUtil.getClaim(token, SecurityConsts.ACCOUNT);

		if (account == null) {
			throw new AuthenticationException("token invalid");
		}

		try {
			if (JwtUtil.verify(token)) {
				return new SimpleAuthenticationInfo(token, token, "shiroRealm");
			}
		} catch (UnsupportedEncodingException e) {
			e.printStackTrace();
		}
		throw new AuthenticationException("Token expired or incorrect.");
	}

	/**
	 * 只有当需要检测用户权限的时候才会调用此方法，例如checkRole,checkPermission之类的
	 * @param principals
	 * @return
	 */
	@Override
	protected AuthorizationInfo doGetAuthorizationInfo(PrincipalCollection principals) {
//		logger.info("调用doGetAuthorizationInfo方法获取权限");

		SimpleAuthorizationInfo authorizationInfo = new SimpleAuthorizationInfo();

		String account = JwtUtil.getClaim(principals.toString(), SecurityConsts.ACCOUNT);
//		User UserInfo = userService.findUserByAccount(account);
		QscAccount accountInfo = accountService.findByUserName(account);

		//获取role
//		List<Role> RoleList = roleService.findRoleByUserId(UserInfo.getId());
		List<QscRole> roleList = roleService.findAccountRole(account);
		//获取权限
//		List<Object> AuthorityList = authorityService.findByUserId(UserInfo.getId());
//		for(QscRole Role : RoleList){
//			authorizationInfo.addRole(Role.getName());
//			for(Object auth: AuthorityList){
//				authorizationInfo.addStringPermission(auth.toString());
//			}
//		}
		return authorizationInfo;
	}

}
