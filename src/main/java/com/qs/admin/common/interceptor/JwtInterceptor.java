package com.qs.admin.common.interceptor;

import com.alibaba.fastjson.JSON;
import com.qs.admin.common.domain.R;
import com.qs.admin.taxhall.model.Employee;
import com.qs.admin.taxhall.service.UserSessionService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;

/**
 * JWT拦截器
 * 用于验证用户身份和权限
 */
@Component
public class JwtInterceptor implements HandlerInterceptor {

    private static final Logger logger = LoggerFactory.getLogger(JwtInterceptor.class);

    @Autowired
    private UserSessionService userSessionService;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        // 跨域预检请求直接放行
        if ("OPTIONS".equals(request.getMethod())) {
            return true;
        }

        // 获取请求路径
        String requestURI = request.getRequestURI();
        
        // 白名单路径，不需要验证token
        if (isWhiteList(requestURI)) {
            return true;
        }

        // 获取token
        String token = getTokenFromRequest(request);
        if (token == null || token.trim().isEmpty()) {
            logger.warn("访问受保护资源缺少token: {}", requestURI);
            writeErrorResponse(response, "缺少认证令牌");
            return false;
        }

        // 验证token并获取用户信息
        Employee currentUser = userSessionService.getCurrentUser(token);
        if (currentUser == null) {
            logger.warn("无效的token访问受保护资源: {}, token: {}", requestURI, token);
            writeErrorResponse(response, "认证令牌无效或已过期");
            return false;
        }

        // 将用户信息存储到request中，供后续使用
        request.setAttribute("currentUser", currentUser);
        request.setAttribute("currentUserId", currentUser.getUid());
        request.setAttribute("currentUsername", currentUser.getUsername());

        logger.debug("用户访问受保护资源: userId={}, username={}, uri={}", 
                    currentUser.getUid(), currentUser.getUsername(), requestURI);
        
        return true;
    }

    /**
     * 判断是否为白名单路径
     */
    private boolean isWhiteList(String requestURI) {
        // 登录相关接口
        if (requestURI.startsWith("/api/v1/auth/")) {
            return true;
        }

        // 公开接口（无需认证）
        if (requestURI.contains("/public/")) {
            return true;
        }

        // 下拉框选项接口（无需认证）
        if (requestURI.endsWith("/options") ||
            requestURI.endsWith("/list") ||
            requestURI.endsWith("/public-options") ||
            (requestURI.matches(".*/employee$") ||
             requestURI.matches(".*/business$") ||
             requestURI.matches(".*/window$"))) {
            return true;
        }

        // 分页查询接口（临时开放，便于前端调试）
        if (requestURI.endsWith("/page")) {
            return true;
        }

        // 临时调试：开放所有GET请求（仅用于开发调试）
        if ("GET".equalsIgnoreCase(request.getMethod())) {
            logger.info("临时开放GET请求: {}", requestURI);
            return true;
        }

        // Swagger相关接口
        if (requestURI.startsWith("/swagger-") ||
            requestURI.startsWith("/v2/api-docs") ||
            requestURI.startsWith("/webjars/") ||
            requestURI.startsWith("/doc.html")) {
            return true;
        }

        // 静态资源
        if (requestURI.startsWith("/static/") ||
            requestURI.startsWith("/public/") ||
            requestURI.endsWith(".html") ||
            requestURI.endsWith(".js") ||
            requestURI.endsWith(".css") ||
            requestURI.endsWith(".ico")) {
            return true;
        }

        // 健康检查等
        if (requestURI.equals("/") ||
            requestURI.equals("/health") ||
            requestURI.equals("/actuator/health")) {
            return true;
        }

        return false;
    }

    /**
     * 从请求中获取Token
     */
    private String getTokenFromRequest(HttpServletRequest request) {
        String token = request.getHeader("Authorization");
        if (token != null && token.startsWith("Bearer ")) {
            return token.substring(7);
        }
        return request.getHeader("X-Token");
    }

    /**
     * 写入错误响应
     */
    private void writeErrorResponse(HttpServletResponse response, String message) throws IOException {
        response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
        response.setContentType("application/json;charset=UTF-8");
        
        R<?> result = R.fail(401, message);
        
        try (PrintWriter writer = response.getWriter()) {
            writer.write(JSON.toJSONString(result));
            writer.flush();
        }
    }
}
