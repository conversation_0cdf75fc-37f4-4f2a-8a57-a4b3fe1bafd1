package com.qs.admin.common.core;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class Tree<T> {

    private String id;

    private String icon;

    private String title;

    private String text;

    private Integer order;

    private String index;

    private List<Tree<T>> subs;

    private String access;

    private boolean hasAccess = false;

    private boolean hasSubs = false;

    private Date createTime;

    private Date modifyTime;

    public void initChildren() {
        this.subs = new ArrayList<>();
    }

}
