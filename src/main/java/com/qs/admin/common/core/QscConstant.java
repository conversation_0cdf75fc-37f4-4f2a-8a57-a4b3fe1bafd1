package com.qs.admin.common.core;

/**
 * @Author: ytx
 * @Date: 2020/4/8  14:52
 */
public class QscConstant {
    // account缓存前缀
    public static final String ACCOUNT_CACHE_PREFIX = "qs.cache.account.";
    // account角色缓存前缀
    public static final String ACCOUNT_ROLE_CACHE_PREFIX = "qs.cache.account.role.";
    // account权限缓存前缀
    public static final String ACCOUNT_PERMISSION_CACHE_PREFIX = "qs.cache.account.permission.";
    // account个性化配置前缀
    public static final String ACCOUNT_CONFIG_CACHE_PREFIX = "qs.cache.account.config.";
    // token缓存前缀
    public static final String TOKEN_CACHE_PREFIX = "qs.cache.token.";

    // 存储在线用户的 QS前缀
    public static final String ACTIVE_ACCOUNT_QS_PREFIX = "qs.account.active";

    // 排序规则： descend 降序
    public static final String ORDER_DESC = "descend";
    // 排序规则： ascend 升序
    public static final String ORDER_ASC = "ascend";

    // 按钮
    public static final String TYPE_BUTTON = "1";
    // 菜单
    public static final String TYPE_MENU = "0";
}
