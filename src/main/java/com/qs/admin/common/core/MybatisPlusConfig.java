package com.qs.admin.common.core;

import com.baomidou.mybatisplus.autoconfigure.SpringBootVFS;
import com.baomidou.mybatisplus.core.MybatisConfiguration;
import com.baomidou.mybatisplus.core.MybatisXMLLanguageDriver;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;
import com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean;
import org.apache.ibatis.mapping.DatabaseIdProvider;
import org.apache.ibatis.mapping.VendorDatabaseIdProvider;
import org.apache.ibatis.plugin.Interceptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;

import java.util.Properties;

import javax.annotation.Resource;
import javax.sql.DataSource;

/**
 * <AUTHOR>
 * @date 2018/7/19 20:27
 */
@Configuration
public class MybatisPlusConfig {
    @Resource
    private DataSource dataSource;

    @Autowired(required = false)
    private DatabaseIdProvider databaseIdProvider;

    @Value("${spring.datasource.driver-class-name:}")
    private String driverClassName;

    @Value("${spring.datasource.url:}")
    private String url;

    @Value("${spring.datasource.jdbc-url:}")
    private String jdbcUrl;



    /**
     * 这里全部使用mybatis-autoconfigure 已经自动加载的资源。不手动指定
     * 配置文件和mybatis-boot的配置文件同步
     * @return
     */
    @Bean
    public MybatisSqlSessionFactoryBean mybatisSqlSessionFactoryBean() {
        MybatisSqlSessionFactoryBean mybatisPlus = new MybatisSqlSessionFactoryBean();
        mybatisPlus.setDataSource(dataSource);
        mybatisPlus.setVfs(SpringBootVFS.class);
        mybatisPlus.setTypeAliasesPackage("com.qs.admin.*.model");
        
        // Manually add the pagination interceptor to avoid circular dependency
        MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();
        // 动态获取数据库类型
        DbType dbType = getDbType();
        PaginationInnerInterceptor paginationInterceptor = new PaginationInnerInterceptor(dbType);
        // Set overflow handling to false
        paginationInterceptor.setOverflow(false);
        // Set max limit to avoid performance issues
        paginationInterceptor.setMaxLimit(1000L);
        // 根据数据库类型设置SQL优化
        if (dbType == DbType.SQL_SERVER || dbType == DbType.SQL_SERVER2005) {
            // SQL Server禁用SQL优化，避免方括号解析问题
            paginationInterceptor.setOptimizeJoin(false);
        }
        interceptor.addInnerInterceptor(paginationInterceptor);
        
        mybatisPlus.setPlugins(new Interceptor[]{interceptor});
        
        MybatisConfiguration mc = new MybatisConfiguration();
        mc.setDefaultScriptingLanguage(MybatisXMLLanguageDriver.class);
        // 设置SQL Server兼容性
        mc.setUseGeneratedKeys(false);
        mc.setMapUnderscoreToCamelCase(true);
        
        // 强制设置为SQL Server的关键字包装方式
        mc.setDatabaseId("sqlserver");
        
        mybatisPlus.setConfiguration(mc);
        
        // 全局配置将通过application.yml文件进行设置
        
        if (this.databaseIdProvider != null) {
            mybatisPlus.setDatabaseIdProvider(this.databaseIdProvider);
        }
        
        // Set type aliases package
        mybatisPlus.setTypeAliasesPackage("com.qs.admin.*.entity,com.qs.admin.*.model");
        
        // Set mapper locations
        try {
            PathMatchingResourcePatternResolver resolver = new PathMatchingResourcePatternResolver();
            org.springframework.core.io.Resource[] resources = resolver.getResources("classpath*:mapper/**/*.xml");
            mybatisPlus.setMapperLocations(resources);
        } catch (Exception e) {
            // Ignore if mapper files not found
        }
        
        return mybatisPlus;
    }

    /**
     * 配置数据库ID提供者
     */
    @Bean
    public DatabaseIdProvider databaseIdProvider() {
        VendorDatabaseIdProvider databaseIdProvider = new VendorDatabaseIdProvider();
        Properties properties = new Properties();
        properties.setProperty("SQL Server", "sqlserver");
        properties.setProperty("MySQL", "mysql");
        properties.setProperty("Oracle", "oracle");
        properties.setProperty("PostgreSQL", "postgresql");
        databaseIdProvider.setProperties(properties);
        return databaseIdProvider;
    }



    /**
     * 动态获取数据库类型
     */
    private DbType getDbType() {
        String effectiveUrl = getEffectiveUrl();

        if (driverClassName.contains("sqlserver") || effectiveUrl.contains("sqlserver")) {
            return DbType.SQL_SERVER2005;
        } else if (driverClassName.contains("mysql") || effectiveUrl.contains("mysql")) {
            if (effectiveUrl.contains("oceanbase")) {
                return DbType.MYSQL; // OceanBase兼容MySQL
            }
            return DbType.MYSQL;
        } else if (driverClassName.contains("oracle") || effectiveUrl.contains("oracle")) {
            return DbType.ORACLE;
        } else if (driverClassName.contains("postgresql") || effectiveUrl.contains("postgresql")) {
            return DbType.POSTGRE_SQL;
        } else {
            return DbType.SQL_SERVER2005; // 默认保持原有行为
        }
    }

    /**
     * 判断是否为SQL Server数据库
     */
    private boolean isSqlServer() {
        String effectiveUrl = getEffectiveUrl();
        return driverClassName.contains("sqlserver") || effectiveUrl.contains("sqlserver");
    }

    /**
     * 获取有效的数据库URL
     */
    private String getEffectiveUrl() {
        if (jdbcUrl != null && !jdbcUrl.isEmpty()) {
            return jdbcUrl.toLowerCase();
        }
        return url != null ? url.toLowerCase() : "";
    }

    // Removed mybatisPlusInterceptor bean to avoid circular dependency
    // The interceptor is now created directly in mybatisSqlSessionFactoryBean method



    /**
     * 打印 sql
     */
//    @Bean
//    public PerformanceInterceptor performanceInterceptor() {
//        PerformanceInterceptor performanceInterceptor = new PerformanceInterceptor();
//        //格式化sql语句
//        Properties properties = new Properties();
//        properties.setProperty("format", "true");
//        performanceInterceptor.setProperties(properties);
//        return performanceInterceptor;
//    }
}
