package com.qs.admin.common.core;

import com.baomidou.mybatisplus.autoconfigure.SpringBootVFS;
import com.baomidou.mybatisplus.core.MybatisConfiguration;
import com.baomidou.mybatisplus.core.MybatisXMLLanguageDriver;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;
import com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean;
import org.apache.ibatis.mapping.DatabaseIdProvider;
import org.apache.ibatis.plugin.Interceptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import javax.sql.DataSource;

/**
 * <AUTHOR>
 * @date 2018/7/19 20:27
 */
@Configuration
public class MybatisPlusConfig {
    @Resource
    private DataSource dataSource;

    @Autowired(required = false)
    private DatabaseIdProvider databaseIdProvider;



    /**
     * 这里全部使用mybatis-autoconfigure 已经自动加载的资源。不手动指定
     * 配置文件和mybatis-boot的配置文件同步
     * @return
     */
    @Bean
    public MybatisSqlSessionFactoryBean mybatisSqlSessionFactoryBean() {
        MybatisSqlSessionFactoryBean mybatisPlus = new MybatisSqlSessionFactoryBean();
        mybatisPlus.setDataSource(dataSource);
        mybatisPlus.setVfs(SpringBootVFS.class);
        
        // Manually add the pagination interceptor to avoid circular dependency
        MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();
        // Use SQL Server database type to avoid SQL parsing issues with reserved keywords
        PaginationInnerInterceptor paginationInterceptor = new PaginationInnerInterceptor(DbType.SQL_SERVER2005);
        // Set overflow handling to false
        paginationInterceptor.setOverflow(false);
        // Set max limit to avoid performance issues
        paginationInterceptor.setMaxLimit(1000L);
        // 禁用SQL优化，避免方括号解析问题
        paginationInterceptor.setOptimizeJoin(false);
        interceptor.addInnerInterceptor(paginationInterceptor);
        mybatisPlus.setPlugins(new Interceptor[]{interceptor});
        
        MybatisConfiguration mc = new MybatisConfiguration();
        mc.setDefaultScriptingLanguage(MybatisXMLLanguageDriver.class);
        // 设置SQL Server兼容性
        mc.setUseGeneratedKeys(false);
        mc.setMapUnderscoreToCamelCase(true);
        mybatisPlus.setConfiguration(mc);
        
        if (this.databaseIdProvider != null) {
            mybatisPlus.setDatabaseIdProvider(this.databaseIdProvider);
        }
        
        // Set type aliases package
        mybatisPlus.setTypeAliasesPackage("com.qs.admin.*.entity,com.qs.admin.*.model");
        
        // Set mapper locations
        try {
            PathMatchingResourcePatternResolver resolver = new PathMatchingResourcePatternResolver();
            mybatisPlus.setMapperLocations(resolver.getResources("classpath*:mapper/**/*.xml"));
        } catch (Exception e) {
            // Ignore if mapper files not found
        }
        
        return mybatisPlus;
    }


    // Removed mybatisPlusInterceptor bean to avoid circular dependency
    // The interceptor is now created directly in mybatisSqlSessionFactoryBean method



    /**
     * 打印 sql
     */
//    @Bean
//    public PerformanceInterceptor performanceInterceptor() {
//        PerformanceInterceptor performanceInterceptor = new PerformanceInterceptor();
//        //格式化sql语句
//        Properties properties = new Properties();
//        properties.setProperty("format", "true");
//        performanceInterceptor.setProperties(properties);
//        return performanceInterceptor;
//    }
}
