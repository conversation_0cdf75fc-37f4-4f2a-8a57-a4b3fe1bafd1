package com.qs.admin.common.core;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * @Author:xpj
 * @version:1.0
 * @explain:
 * @Date: created in 12:27 2019/1/11
 */
public class Query<T> extends Page<T> {
    private static final String PAGE = "page";
    private static final String PAGE_SIZE = "pageSize";
    private static final String ORDER_BY_FIELD = "orderByField";
    private static final String IS_ASC = "isAsc";

    @Getter
    private Map<String, Object> condition;

    public Query<T> setCondition(Map<String, Object> condition) {
        this.condition = condition;
        return this;
    }

    // 3.x没有condition()方法，这里自定义一个
    public Map<Object, Object> condition() {
        return new HashMap<>(this.getCondition());
    }

    public Query(Map<String, Object> params) {
        // 3.x的Page构造器参数类型是long
        super(
            Long.parseLong(params.getOrDefault(PAGE, 1).toString()),
            Long.parseLong(params.getOrDefault(PAGE_SIZE, 10).toString())
        );

        // 移除不包含查询条件的字段
        params.entrySet().removeIf(
            entry -> ObjectUtil.isNull(entry.getValue()) || StrUtil.isEmpty(entry.getValue().toString())
        );

        // 判断是否是升序
        boolean isAsc = Boolean.parseBoolean(params.getOrDefault(IS_ASC, Boolean.TRUE).toString());

        // 用于排序的字段
        String orderByField = params.getOrDefault(ORDER_BY_FIELD, "").toString();
        if (StrUtil.isNotEmpty(orderByField)) {
            if (isAsc) {
                this.addOrder(OrderItem.asc(orderByField));
            } else {
                this.addOrder(OrderItem.desc(orderByField));
            }
        }
        params.remove(PAGE);
        params.remove(PAGE_SIZE);
        params.remove(ORDER_BY_FIELD);
        params.remove(IS_ASC);
        this.setCondition(params);
    }
}

