//package com.qs.admin.common.service.impl;
//
//import com.fasterxml.jackson.databind.JavaType;
//import com.fasterxml.jackson.databind.ObjectMapper;
//import com.qs.admin.common.core.QscConstant;
//import com.qs.admin.common.service.CacheService;
////import com.qs.admin.common.service.RedisService;
//import com.qs.admin.system.mapper.QscAccountMapper;
//import com.qs.admin.system.model.QscAccount;
//import com.qs.admin.system.model.QscMenu;
//import com.qs.admin.system.model.QscRole;
//import com.qs.admin.system.service.QscAccountService;
//import com.qs.admin.system.service.QscMenuService;
//import com.qs.admin.system.service.QscRoleService;
//import org.apache.commons.lang3.StringUtils;
//import org.apache.shiro.authc.Account;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Service;
//
//import java.util.List;
//
//@Service("cacheService")
//public class CacheServiceImpl implements CacheService {
//
////    @Autowired
////    private RedisService redisService;
//
//    @Autowired
//    private QscRoleService roleService;
//
//    @Autowired
//    private QscMenuService menuService;
//
//    @Autowired
//    private QscAccountService accountService;
//
//    @Autowired
//    private ObjectMapper mapper;
//
//    @Override
//    public void testConnect() throws Exception {
//        this.redisService.exists("test");
//    }
//
//    @Override
//    public QscAccount getAccount(String username) throws Exception {
//        String userString = this.redisService.get(QscConstant.ACCOUNT_CACHE_PREFIX + username);
//        if (StringUtils.isBlank(userString))
//            throw new Exception();
//        else
//            return this.mapper.readValue(userString, QscAccount.class);
//    }
//
//    @Override
//    public List<QscRole> getRoles(String username) throws Exception {
//        String roleListString = this.redisService.get(QscConstant.ACCOUNT_ROLE_CACHE_PREFIX + username);
//        if (StringUtils.isBlank(roleListString)) {
//            throw new Exception();
//        } else {
//            JavaType type = mapper.getTypeFactory().constructParametricType(List.class, QscRole.class);
//            return this.mapper.readValue(roleListString, type);
//        }
//    }
//
//    @Override
//    public void saveAccount(QscAccount account) throws Exception {
//        String username = account.getUsername();
//        this.deleteAccount(username);
//        redisService.set(QscConstant.ACCOUNT_CACHE_PREFIX + username, mapper.writeValueAsString(account));
//    }
//
//    @Override
//    public void saveAccount(String username) throws Exception {
//        QscAccount account = accountService.findDetal(username);
//        this.deleteAccount(username);
//        redisService.set(QscConstant.ACCOUNT_CACHE_PREFIX + username, mapper.writeValueAsString(account));
//    }
//
//    @Override
//    public void saveRoles(String username) throws Exception {
//        List<QscRole> roleList = this.roleService.findAccountRole(username);
//        if (!roleList.isEmpty()) {
//            this.deleteRoles(username);
//            redisService.set(QscConstant.ACCOUNT_ROLE_CACHE_PREFIX + username, mapper.writeValueAsString(roleList));
//        }
//
//    }
//
//    @Override
//    public void deleteAccount(String username) throws Exception {
//        username = username.toLowerCase();
//        redisService.del(QscConstant.ACCOUNT_CACHE_PREFIX + username);
//    }
//
//    @Override
//    public void deleteRoles(String username) throws Exception {
//        username = username.toLowerCase();
//        redisService.del(QscConstant.ACCOUNT_ROLE_CACHE_PREFIX + username);
//    }
//
//}
