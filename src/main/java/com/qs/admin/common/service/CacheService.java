//package com.qs.admin.common.service;
//
//import com.qs.admin.system.model.QscAccount;
//import com.qs.admin.system.model.QscMenu;
//import com.qs.admin.system.model.QscRole;
//
//import java.util.List;
//
//public interface CacheService {
//
//    /**
//     * 测试 Redis是否连接成功
//     */
//    void testConnect() throws Exception;
//
//    /**
//     * 从缓存中获取用户
//     *
//     * @param username 用户名
//     * @return User
//     */
//    QscAccount getAccount(String username) throws Exception;
//
//    /**
//     * 从缓存中获取用户角色
//     *
//     * @param username 用户名
//     * @return 角色集
//     */
//    List<QscRole> getRoles(String username) throws Exception;
//
//
//    /**
//     * 缓存用户信息，只有当用户信息是查询出来的，完整的，才应该调用这个方法
//     * 否则需要调用下面这个重载方法
//     *
//     * @param QscAccount 用户信息
//     */
//    void saveAccount(QscAccount QscAccount) throws Exception;
//
//    /**
//     * 缓存用户信息
//     *
//     * @param username 用户名
//     */
//    void saveAccount(String username) throws Exception;
//
//    /**
//     * 缓存用户角色信息
//     *
//     * @param username 用户名
//     */
//    void saveRoles(String username) throws Exception;
//
//    /**
//     * 删除用户信息
//     *
//     * @param username 用户名
//     */
//    void deleteAccount(String username) throws Exception;
//
//    /**
//     * 删除用户角色信息
//     *
//     * @param username 用户名
//     */
//    void deleteRoles(String username) throws Exception;
//
//}
