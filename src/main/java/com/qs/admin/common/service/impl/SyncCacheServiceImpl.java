package com.qs.admin.common.service.impl;

import com.qs.admin.common.service.SyncCacheService;
import com.qs.admin.common.shiro.security.JwtProperties;
import com.qs.admin.common.utils.LocalCacheManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;

@Service
public class SyncCacheServiceImpl implements SyncCacheService {
    private static final Logger LOGGER = LoggerFactory.getLogger(SyncCacheServiceImpl.class);

    // 用于本地锁模拟
    private final Map<String, AtomicLong> lockMap = new ConcurrentHashMap<>();

    /**
     * 获取本地锁，乐观锁实现（仅适用于单机环境）
     * @param lockName
     * @param expireTime 锁的失效时间（本地缓存自动过期，参数无实际作用）
     * @return
     */
    @Override
    public Boolean getLock(String lockName, int expireTime) {
        Boolean result = Boolean.FALSE;
        try {
            lockMap.putIfAbsent(lockName, new AtomicLong(0));
            long reVal = lockMap.get(lockName).incrementAndGet();
            if (reVal == 1L) {
                //获取锁
                result = Boolean.TRUE;
                LOGGER.info("获取本地锁:"+lockName+",成功");
            } else {
                LOGGER.info("获取本地锁:"+lockName+",失败"+reVal);
            }
        } catch (Exception e) {
            LOGGER.error("获取本地锁失败:"+lockName, e);
        }
        return result;
    }

    /**
     * 释放锁，直接重置计数（本地锁，非分布式）
     * @param lockName
     * @return
     */
    @Override
    public Boolean releaseLock(String lockName) {
        Boolean result = Boolean.FALSE;
        try {
            lockMap.remove(lockName);
            LOGGER.info("释放本地锁:"+lockName+",成功");
        } catch (Exception e) {
            LOGGER.error("释放本地锁失败:"+lockName, e);
        }
        return result;
    }
}
