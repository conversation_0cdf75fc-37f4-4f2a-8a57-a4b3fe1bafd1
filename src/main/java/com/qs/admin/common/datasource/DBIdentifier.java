package com.qs.admin.common.datasource;

/**
 * 数据库标识管理类。用于区分数据源连接的不同数据库。
 * @Author: ytx
 * @Date: 2020/4/16  15:47
 */
public class DBIdentifier {

    /**
     * 用不同的工程编码来区分数据库
     */
    private static ThreadLocal<String> unitCode = new ThreadLocal<String>();

    public static String getUnitCode() {
        return unitCode.get();
    }

    public static void setUnitCode(String code) {
        unitCode.set(code);
    }
}