package com.qs.admin.common.util;

import com.qs.admin.taxhall.model.Employee;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;

/**
 * 用户工具类
 * 用于在Controller中方便地获取当前登录用户信息
 */
public class UserUtil {

    /**
     * 获取当前登录用户
     */
    public static Employee getCurrentUser() {
        HttpServletRequest request = getCurrentRequest();
        if (request != null) {
            Object currentUser = request.getAttribute("currentUser");
            if (currentUser instanceof Employee) {
                return (Employee) currentUser;
            }
        }
        return null;
    }

    /**
     * 获取当前登录用户ID
     */
    public static Integer getCurrentUserId() {
        HttpServletRequest request = getCurrentRequest();
        if (request != null) {
            Object currentUserId = request.getAttribute("currentUserId");
            if (currentUserId instanceof Integer) {
                return (Integer) currentUserId;
            }
        }
        return null;
    }

    /**
     * 获取当前登录用户名
     */
    public static String getCurrentUsername() {
        HttpServletRequest request = getCurrentRequest();
        if (request != null) {
            Object currentUsername = request.getAttribute("currentUsername");
            if (currentUsername instanceof String) {
                return (String) currentUsername;
            }
        }
        return null;
    }

    /**
     * 获取当前请求
     */
    private static HttpServletRequest getCurrentRequest() {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        return attributes != null ? attributes.getRequest() : null;
    }
}
