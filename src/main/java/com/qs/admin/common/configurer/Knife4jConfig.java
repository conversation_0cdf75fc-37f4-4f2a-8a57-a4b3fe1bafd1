package com.qs.admin.common.configurer;

import com.github.xiaoymin.knife4j.spring.annotations.EnableKnife4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
import springfox.documentation.builders.ApiInfoBuilder;
import springfox.documentation.builders.PathSelectors;
import springfox.documentation.builders.RequestHandlerSelectors;
import springfox.documentation.service.ApiInfo;
import springfox.documentation.service.Contact;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spring.web.plugins.Docket;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

/**
 * Knife4j API文档配置
 * <AUTHOR> Admin Team
 */
@Configuration
@EnableSwagger2
@EnableKnife4j
public class Knife4jConfig implements WebMvcConfigurer {

    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        // Knife4j 静态资源配置
        registry.addResourceHandler("doc.html")
                .addResourceLocations("classpath:/META-INF/resources/");
        registry.addResourceHandler("/webjars/**")
                .addResourceLocations("classpath:/META-INF/resources/webjars/");
    }

    @Bean
    public Docket createRestApi() {
        return new Docket(DocumentationType.SWAGGER_2)
                .apiInfo(apiInfo())
                .select()
                .apis(RequestHandlerSelectors.basePackage("com.qs.admin.taxhall.controller"))
                .paths(PathSelectors.any())
                .build();
    }

    private ApiInfo apiInfo() {
        return new ApiInfoBuilder()
                .title("QS Admin API 接口文档")
                .description("紫云综合管理系统 RESTful API 接口文档\n\n" +
                        "## 接口说明\n" +
                        "- 所有接口返回格式统一为 JSON\n" +
                        "- 分页查询统一使用 pageNum 和 pageSize 参数\n" +
                        "- 认证接口无需token，其他接口需要在Header中携带Authorization\n\n" +
                        "## 快速开始\n" +
                        "1. 首先调用登录接口获取token\n" +
                        "2. 在需要认证的接口Header中添加：Authorization: Bearer {token}")
                .termsOfServiceUrl("http://localhost:8823/doc.html")
                .contact(new Contact("QS Admin Team", "http://localhost:8823/doc.html", "<EMAIL>"))
                .version("2.0.0")
                .build();
    }
}
