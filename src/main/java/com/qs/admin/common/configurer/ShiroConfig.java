package com.qs.admin.common.configurer;

import com.qs.admin.common.service.SyncCacheService;
import com.qs.admin.common.shiro.ShiroFilterProperties;
import com.qs.admin.common.shiro.ShiroRealm;
import com.qs.admin.common.shiro.cache.ShiroCacheManager;
import com.qs.admin.common.shiro.security.AccountContextFilter;
import com.qs.admin.common.shiro.security.JwtFilter;
import com.qs.admin.common.shiro.security.JwtProperties;
import com.qs.admin.common.shiro.security.SystemLogoutFilter;
import com.qs.admin.common.utils.LocalCacheManager;
import org.apache.shiro.mgt.DefaultSessionStorageEvaluator;
import org.apache.shiro.mgt.DefaultSubjectDAO;
import org.apache.shiro.spring.LifecycleBeanPostProcessor;
import org.apache.shiro.spring.security.interceptor.AuthorizationAttributeSourceAdvisor;
import org.apache.shiro.spring.web.ShiroFilterFactoryBean;
import org.apache.shiro.web.mgt.DefaultWebSecurityManager;
import org.springframework.aop.framework.autoproxy.DefaultAdvisorAutoProxyCreator;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.DependsOn;

import javax.servlet.Filter;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

//@Configuration
public class ShiroConfig {

    @Bean
    public LifecycleBeanPostProcessor lifecycleBeanPostProcessor() {
        return new LifecycleBeanPostProcessor();
    }

    @Bean
    @DependsOn("lifecycleBeanPostProcessor")
    public static DefaultAdvisorAutoProxyCreator getLifecycleBeanPostProcessor() {
        DefaultAdvisorAutoProxyCreator defaultAdvisorAutoProxyCreator = new DefaultAdvisorAutoProxyCreator();
        // 强制使用cglib
        defaultAdvisorAutoProxyCreator.setProxyTargetClass(true);
        return defaultAdvisorAutoProxyCreator;
    }

    @Bean
    public AuthorizationAttributeSourceAdvisor authorizationAttributeSourceAdvisor(DefaultWebSecurityManager securityManager) {
        AuthorizationAttributeSourceAdvisor advisor = new AuthorizationAttributeSourceAdvisor();
        advisor.setSecurityManager(securityManager);
        return advisor;
    }

    @Bean
    public DefaultWebSecurityManager  securityManager(ShiroRealm shiroRealm, ShiroCacheManager shiroCacheManager){
        DefaultWebSecurityManager securityManager =  new DefaultWebSecurityManager();

        securityManager.setRealm(shiroRealm);

        //关闭shiro自带的session
        DefaultSubjectDAO subjectDAO = new DefaultSubjectDAO();
        DefaultSessionStorageEvaluator defaultSessionStorageEvaluator = new DefaultSessionStorageEvaluator();
        defaultSessionStorageEvaluator.setSessionStorageEnabled(false);
        subjectDAO.setSessionStorageEvaluator(defaultSessionStorageEvaluator);
        securityManager.setSubjectDAO(subjectDAO);

        securityManager.setCacheManager(shiroCacheManager);
        return securityManager;
    }

    @Bean
    public ShiroFilterFactoryBean shiroFilter(DefaultWebSecurityManager securityManager, LocalCacheManager localCacheManager, JwtProperties jwtProp, SyncCacheService syncCacheService) {
        ShiroFilterFactoryBean shiroFilter = new ShiroFilterFactoryBean();
        shiroFilter.setSecurityManager(securityManager);

        // 添加jwt过滤器
        Map<String, Filter> filterMap = new HashMap<>();
        filterMap.put("jwt", new JwtFilter(jwtProp,syncCacheService,localCacheManager));
        filterMap.put("logout", new SystemLogoutFilter());
        shiroFilter.setFilters(filterMap);

        //动态配置拦截器注入
        Map<String, String> filterRuleMap = new LinkedHashMap<>(16);

        // 首先添加基础的匿名访问路径
        filterRuleMap.put("/doc.html", "anon");
        filterRuleMap.put("/v2/api-docs", "anon");
        filterRuleMap.put("/v2/api-docs/**", "anon");
        filterRuleMap.put("/swagger-resources", "anon");
        filterRuleMap.put("/swagger-resources/**", "anon");
        filterRuleMap.put("/webjars/**", "anon");
        filterRuleMap.put("/favicon.ico", "anon");
        filterRuleMap.put("/api/v1/auth/**", "anon");
        filterRuleMap.put("/login", "anon");
        filterRuleMap.put("/logout", "anon");
        filterRuleMap.put("/register", "anon");
        filterRuleMap.put("/updatePassword", "anon");
        filterRuleMap.put("/queuingsystem/**", "anon");

        // 从配置文件读取其他权限配置
        try {
            List<Map<String, String>> perms = this.getShiroFilterProperties().getPerms();
            if (perms != null && !perms.isEmpty()) {
                perms.forEach(perm -> {
                    String key = perm.get("key");
                    String value = perm.get("value");
                    if (key != null && value != null && !filterRuleMap.containsKey(key)) {
                        filterRuleMap.put(key, value);
                    }
                });
            }
        } catch (Exception e) {
            System.err.println("Warning: Failed to load permission config from yml: " + e.getMessage());
        }

        // 最后加 jwt 拦截所有其他路径
        filterRuleMap.put("/**", "jwt");
        // 确保配置不为空
        if (filterRuleMap.isEmpty()) {
            System.err.println("ERROR: filterRuleMap is empty! Adding default rules.");
            filterRuleMap.put("/api/v1/auth/**", "anon");
            filterRuleMap.put("/**", "jwt");
        }

        // 打印最终权限配置用于调试
        System.out.println("Shiro filterChainDefinitionMap size: " + filterRuleMap.size());
        System.out.println("Shiro filterChainDefinitionMap: " + filterRuleMap);

        shiroFilter.setFilterChainDefinitionMap(filterRuleMap);

        return shiroFilter;
    }

    @Bean
    public ShiroFilterProperties getShiroFilterProperties(){
        return new ShiroFilterProperties();
    }

    // 暂时禁用AccountContextFilter，避免与登录系统冲突
    /*
    @Bean
    public FilterRegistrationBean filterRegistrationBean(){
        FilterRegistrationBean bean = new FilterRegistrationBean();
        bean.setFilter(new AccountContextFilter());
        bean.setOrder(1); // 设置过滤器顺序
        bean.setEnabled(false); // 暂时禁用此过滤器
        return bean;
    }
    */

}
