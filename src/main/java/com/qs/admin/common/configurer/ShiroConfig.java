package com.qs.admin.common.configurer;

import com.qs.admin.common.service.SyncCacheService;
import com.qs.admin.common.shiro.ShiroFilterProperties;
import com.qs.admin.common.shiro.ShiroRealm;
import com.qs.admin.common.shiro.cache.ShiroCacheManager;
import com.qs.admin.common.shiro.security.AccountContextFilter;
import com.qs.admin.common.shiro.security.JwtFilter;
import com.qs.admin.common.shiro.security.JwtProperties;
import com.qs.admin.common.shiro.security.SystemLogoutFilter;
import com.qs.admin.common.utils.LocalCacheManager;
import org.apache.shiro.mgt.DefaultSessionStorageEvaluator;
import org.apache.shiro.mgt.DefaultSubjectDAO;
import org.apache.shiro.spring.LifecycleBeanPostProcessor;
import org.apache.shiro.spring.security.interceptor.AuthorizationAttributeSourceAdvisor;
import org.apache.shiro.spring.web.ShiroFilterFactoryBean;
import org.apache.shiro.web.mgt.DefaultWebSecurityManager;
import org.springframework.aop.framework.autoproxy.DefaultAdvisorAutoProxyCreator;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.DependsOn;

import javax.servlet.Filter;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@Configuration
public class ShiroConfig {

    @Bean
    public LifecycleBeanPostProcessor lifecycleBeanPostProcessor() {
        return new LifecycleBeanPostProcessor();
    }

    @Bean
    @DependsOn("lifecycleBeanPostProcessor")
    public static DefaultAdvisorAutoProxyCreator getLifecycleBeanPostProcessor() {
        DefaultAdvisorAutoProxyCreator defaultAdvisorAutoProxyCreator = new DefaultAdvisorAutoProxyCreator();
        // 强制使用cglib
        defaultAdvisorAutoProxyCreator.setProxyTargetClass(true);
        return defaultAdvisorAutoProxyCreator;
    }

    @Bean
    public AuthorizationAttributeSourceAdvisor authorizationAttributeSourceAdvisor(DefaultWebSecurityManager securityManager) {
        AuthorizationAttributeSourceAdvisor advisor = new AuthorizationAttributeSourceAdvisor();
        advisor.setSecurityManager(securityManager);
        return advisor;
    }

    @Bean
    public DefaultWebSecurityManager  securityManager(ShiroRealm shiroRealm, ShiroCacheManager shiroCacheManager){
        DefaultWebSecurityManager securityManager =  new DefaultWebSecurityManager();

        securityManager.setRealm(shiroRealm);

        //关闭shiro自带的session
        DefaultSubjectDAO subjectDAO = new DefaultSubjectDAO();
        DefaultSessionStorageEvaluator defaultSessionStorageEvaluator = new DefaultSessionStorageEvaluator();
        defaultSessionStorageEvaluator.setSessionStorageEnabled(false);
        subjectDAO.setSessionStorageEvaluator(defaultSessionStorageEvaluator);
        securityManager.setSubjectDAO(subjectDAO);

        securityManager.setCacheManager(shiroCacheManager);
        return securityManager;
    }

    @Bean
    public ShiroFilterFactoryBean shiroFilter(DefaultWebSecurityManager securityManager, LocalCacheManager localCacheManager, JwtProperties jwtProp, SyncCacheService syncCacheService) {
        ShiroFilterFactoryBean shiroFilter = new ShiroFilterFactoryBean();
        shiroFilter.setSecurityManager(securityManager);

        // 添加jwt过滤器
        Map<String, Filter> filterMap = new HashMap<>();
        filterMap.put("jwt", new JwtFilter(jwtProp,syncCacheService,localCacheManager));
        filterMap.put("logout", new SystemLogoutFilter());
        shiroFilter.setFilters(filterMap);

        //动态配置拦截器注入
        Map<String, String> filterRuleMap = new LinkedHashMap<>(16);
        // 其他权限配置
        List<Map<String, String>> perms = this.getShiroFilterProperties().getPerms();
        perms.forEach(perm -> filterRuleMap.put(perm.get("key"), perm.get("value")));
        // Knife4j API文档相关路径，必须放在最前面
        filterRuleMap.put("/doc.html", "anon");
        filterRuleMap.put("/v2/api-docs", "anon");
        filterRuleMap.put("/v2/api-docs/**", "anon");
        filterRuleMap.put("/swagger-resources", "anon");
        filterRuleMap.put("/swagger-resources/**", "anon");
        filterRuleMap.put("/webjars/**", "anon");
        filterRuleMap.put("/favicon.ico", "anon");

        // 允许匿名访问测试接口，无需JWT，必须放在 jwt 规则前面
        filterRuleMap.put("/api/v1/agent/save", "anon");
        // 允许登录接口匿名访问
        filterRuleMap.put("/api/v1/auth/login", "anon");
        filterRuleMap.put("/api/v1/auth/logout", "anon");
        filterRuleMap.put("/api/v1/auth/**", "anon");
        // 最后加 jwt 拦截所有
        filterRuleMap.put("/**", "jwt");
        // 仅开发环境打印最终权限配置，生产环境请注释掉
        if (!"prod".equals(System.getProperty("spring.profiles.active"))) {
            System.out.println("Shiro filterChainDefinitionMap: " + filterRuleMap);
        }
        shiroFilter.setFilterChainDefinitionMap(filterRuleMap);

        return shiroFilter;
    }

    @Bean
    public ShiroFilterProperties getShiroFilterProperties(){
        return new ShiroFilterProperties();
    }

    // 暂时禁用AccountContextFilter，避免与登录系统冲突
    /*
    @Bean
    public FilterRegistrationBean filterRegistrationBean(){
        FilterRegistrationBean bean = new FilterRegistrationBean();
        bean.setFilter(new AccountContextFilter());
        bean.setOrder(1); // 设置过滤器顺序
        bean.setEnabled(false); // 暂时禁用此过滤器
        return bean;
    }
    */

}
