package com.qs.admin.common.configurer;

import com.qs.admin.common.datasource.DynamicDataSource;
import com.qs.admin.common.datasource.ProjectDBMgr;
import com.qs.admin.common.utils.PropUtils;
import lombok.extern.slf4j.Slf4j;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.jdbc.DataSourceBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import javax.sql.DataSource;
import java.sql.*;

/**
 * 数据源配置管理。
 * @Author: ytx
 * @Date: 2020/4/16  15:44
 */
@Slf4j
@Configuration
@MapperScan(basePackages="com.qs.admin.*.mapper", value="MybatisSqlSessionFactoryBean")
public class DataSourceConfig {
    /**
     * 根据配置参数创建数据源。使用派生的子类。
     * @return 数据源
     */
    @Bean(name="dataSource")
    @ConfigurationProperties(prefix="spring.datasource")
    public DataSource getDataSource() {
        log.info("创建数据源,读取url地址并初始化...");
        String dbUrl = PropUtils.getProp("dbUrl");
        String dbUsername = PropUtils.getProp("dbUsername");
        String dbPassword = PropUtils.getProp("dbPassword");
        String UnitCode = PropUtils.getProp("unitCode");
//        String waitTime = PropUtils.getProp("waitTime");
        DataSourceBuilder builder = DataSourceBuilder.create();
        builder.url(dbUrl);
        builder.username(dbUsername);
        builder.password(dbPassword);
        builder.type(DynamicDataSource.class);
        DataSource build = builder.build();
        try {
            Connection connection = build.getConnection();
            Statement statement = connection.createStatement();
            ResultSet resultSet = statement.executeQuery("select * from qsc_dbserver");
            log.info("读取数据库中dbserver配置");
            while (resultSet.next()) {
                String dbname = resultSet.getString("dbname");
                String ip = resultSet.getString("server");
                String unitCode = resultSet.getString("unit_code");
                String username = resultSet.getString("dbuser");
                String password = resultSet.getString("dbpass");

                log.info("dbserver中的税务机关代码: "+unitCode);
                ProjectDBMgr.dbNameMap.put(unitCode, dbname);
                ProjectDBMgr.dbIPMap.put(unitCode, ip);
                ProjectDBMgr.dbUserName.put(unitCode,username);
                ProjectDBMgr.dbPassword.put(unitCode,password);
                log.info("unitcode中对应的ip："+unitCode+"----"+ProjectDBMgr.dbIPMap.get(unitCode));
            }
            log.info("初始化完成...");
        } catch (SQLException e) {
            e.printStackTrace();
        }
        return builder.build();
    }

}

