package com.qs.admin.common.configurer;

import com.zaxxer.hikari.HikariDataSource;
import lombok.extern.slf4j.Slf4j;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import javax.sql.DataSource;

/**
 * 数据源配置管理。
 * @Author: ytx
 * @Date: 2020/4/16  15:44
 */
@Slf4j
@Configuration
@MapperScan(basePackages="com.qs.admin.*.mapper", sqlSessionFactoryRef="mybatisSqlSessionFactoryBean")
public class DataSourceConfig {
    /**
     * 配置单一数据源
     * @return 数据源
     */
    @Bean(name="dataSource")
    @ConfigurationProperties(prefix="spring.datasource.hikari")
    public DataSource getDataSource() {
        HikariDataSource dataSource = new HikariDataSource();
        dataSource.setJdbcUrl("*********************************************************************************************************************************************************************************");
        dataSource.setDriverClassName("com.microsoft.sqlserver.jdbc.SQLServerDriver");
        dataSource.setUsername("sa");
        dataSource.setPassword("windows2008R2");
        return dataSource;
    }

}

