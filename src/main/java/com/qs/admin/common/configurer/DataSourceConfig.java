package com.qs.admin.common.configurer;

import com.zaxxer.hikari.HikariDataSource;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import javax.sql.DataSource;

/**
 * 数据源配置管理。
 * @Author: ytx
 * @Date: 2020/4/16  15:44
 */
@Slf4j
@Configuration
@MapperScan(basePackages="com.qs.admin.*.mapper", sqlSessionFactoryRef="mybatisSqlSessionFactoryBean")
public class DataSourceConfig {
    /**
     * 配置单一数据源
     * @return 数据源
     */
    @Bean(name="dataSource")
    @Primary
    @ConfigurationProperties(prefix="spring.datasource")
    public DataSource getDataSource() {
        return new HikariDataSource();
    }

    /**
     * 配置SqlSessionFactory
     */
    @Bean(name="mybatisSqlSessionFactoryBean")
    @Primary
    public SqlSessionFactory sqlSessionFactory() throws Exception {
        SqlSessionFactoryBean sessionFactory = new SqlSessionFactoryBean();
        sessionFactory.setDataSource(getDataSource());

        // 设置mapper文件位置
        PathMatchingResourcePatternResolver resolver = new PathMatchingResourcePatternResolver();
        sessionFactory.setMapperLocations(resolver.getResources("classpath:mapper/*Mapper.xml"));
        sessionFactory.setMapperLocations(resolver.getResources("classpath:mapper/*/*Mapper.xml"));

        return sessionFactory.getObject();
    }

}

