package com.qs.admin.common.configurer;

import org.apache.shiro.spring.web.ShiroFilterFactoryBean;
import org.apache.shiro.web.mgt.DefaultWebSecurityManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * 简化的Shiro配置
 * 解决chainDefinition为空的问题
 */
//@Configuration
public class SimpleShiroConfig {

    @Bean
    @Primary
    public ShiroFilterFactoryBean simpleShiroFilter() {
        ShiroFilterFactoryBean shiroFilter = new ShiroFilterFactoryBean();
        
        // 设置安全管理器
        shiroFilter.setSecurityManager(new DefaultWebSecurityManager());
        
        // 设置登录页面
        shiroFilter.setLoginUrl("/login");
        shiroFilter.setSuccessUrl("/");
        shiroFilter.setUnauthorizedUrl("/unauthorized");
        
        // 配置过滤器链 - 确保不为空
        Map<String, String> filterChainDefinitionMap = new LinkedHashMap<>();
        
        // API文档相关
        filterChainDefinitionMap.put("/doc.html", "anon");
        filterChainDefinitionMap.put("/v2/api-docs", "anon");
        filterChainDefinitionMap.put("/v2/api-docs/**", "anon");
        filterChainDefinitionMap.put("/swagger-resources", "anon");
        filterChainDefinitionMap.put("/swagger-resources/**", "anon");
        filterChainDefinitionMap.put("/webjars/**", "anon");
        filterChainDefinitionMap.put("/favicon.ico", "anon");
        
        // 登录相关
        filterChainDefinitionMap.put("/api/v1/auth/**", "anon");
        filterChainDefinitionMap.put("/login", "anon");
        filterChainDefinitionMap.put("/logout", "anon");
        filterChainDefinitionMap.put("/register", "anon");
        filterChainDefinitionMap.put("/updatePassword", "anon");
        
        // 业务相关
        filterChainDefinitionMap.put("/queuingsystem/**", "anon");
        filterChainDefinitionMap.put("/api/v1/**", "anon");
        
        // 静态资源
        filterChainDefinitionMap.put("/static/**", "anon");
        filterChainDefinitionMap.put("/css/**", "anon");
        filterChainDefinitionMap.put("/js/**", "anon");
        filterChainDefinitionMap.put("/images/**", "anon");
        
        // 其他所有请求需要认证
        filterChainDefinitionMap.put("/**", "anon"); // 暂时设为anon，避免认证问题
        
        // 确保配置不为空
        if (filterChainDefinitionMap.isEmpty()) {
            filterChainDefinitionMap.put("/**", "anon");
        }
        
        System.out.println("SimpleShiro filterChainDefinitionMap size: " + filterChainDefinitionMap.size());
        System.out.println("SimpleShiro filterChainDefinitionMap: " + filterChainDefinitionMap);
        
        shiroFilter.setFilterChainDefinitionMap(filterChainDefinitionMap);
        
        return shiroFilter;
    }
}
