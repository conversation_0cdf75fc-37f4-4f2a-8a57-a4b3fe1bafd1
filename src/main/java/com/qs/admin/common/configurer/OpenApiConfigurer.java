package com.qs.admin.common.configurer;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * <AUTHOR>
 */
@Configuration
public class OpenApiConfigurer implements WebMvcConfigurer {


    /**
     * SpringDoc OpenAPI 3 自动处理资源文件，无需手动配置
     */
    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        // SpringDoc OpenAPI 3 会自动配置swagger-ui资源
    }


    @Bean
    public OpenAPI customOpenAPI() {
        return new OpenAPI()
                .info(new Info()
                        .title("QS Admin API 接口文档")
                        .description("紫云综合管理系统 RESTful API 接口文档")
                        .version("2.0.0")
                        .contact(new Contact()
                                .name("QS Admin Team")
                                .url("http://localhost:8823/swagger-ui.html")
                                .email("<EMAIL>")));
    }

}
