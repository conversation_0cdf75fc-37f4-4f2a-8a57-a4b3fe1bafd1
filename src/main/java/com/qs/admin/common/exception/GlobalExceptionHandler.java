package com.qs.admin.common.exception;

import com.qs.admin.common.domain.R;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

@RestControllerAdvice
public class GlobalExceptionHandler {

    private static final Logger logger = LoggerFactory.getLogger(GlobalExceptionHandler.class);

    @ExceptionHandler(MethodArgumentNotValidException.class)
    public R<?> handleValidationException(MethodArgumentNotValidException ex) {
        String msg = ex.getBindingResult().getFieldError().getDefaultMessage();
        return R.fail(msg);
    }

    /**
     * 处理空指针异常
     */
    @ExceptionHandler(NullPointerException.class)
    public R<?> handleNullPointerException(NullPointerException ex) {
        logger.error("空指针异常", ex);
        return R.fail("系统异常：数据为空，请检查请求参数");
    }

    @ExceptionHandler(Exception.class)
    public R<?> handleAllException(Exception ex) {
        logger.error("系统异常", ex);
        return R.fail("系统异常: " + ex.getMessage());
    }
}
