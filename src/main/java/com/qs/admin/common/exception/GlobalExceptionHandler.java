package com.qs.admin.common.exception;

import com.qs.admin.common.domain.R;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

@RestControllerAdvice
public class GlobalExceptionHandler {
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public R<?> handleValidationException(MethodArgumentNotValidException ex) {
        String msg = ex.getBindingResult().getFieldError().getDefaultMessage();
        return R.fail(msg);
    }

    @ExceptionHandler(Exception.class)
    public R<?> handleAllException(Exception ex) {
        return R.fail("系统异常: " + ex.getMessage());
    }
}
