package com.qs.admin.common.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.Ordered;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * 响应优化配置类
 * 用于优化大数据量响应和防止连接中断
 */
@Slf4j
@Configuration
public class ResponseOptimizationConfig implements WebMvcConfigurer {

    /**
     * 配置Jackson消息转换器
     */
    @Override
    public void configureMessageConverters(List<HttpMessageConverter<?>> converters) {
        // 移除默认的Jackson转换器
        converters.removeIf(converter -> converter instanceof MappingJackson2HttpMessageConverter);
        
        // 添加优化的Jackson转换器
        MappingJackson2HttpMessageConverter converter = new MappingJackson2HttpMessageConverter();
        ObjectMapper objectMapper = new ObjectMapper();
        
        // 配置ObjectMapper以优化性能
        objectMapper.getFactory().disable(com.fasterxml.jackson.core.JsonGenerator.Feature.AUTO_CLOSE_TARGET);
        objectMapper.getFactory().disable(com.fasterxml.jackson.core.JsonParser.Feature.AUTO_CLOSE_SOURCE);
        
        converter.setObjectMapper(objectMapper);
        converters.add(converter);
        
        // 注意：不要移除其他默认的转换器，让Spring Boot自动配置处理表单数据等其他类型
    }

    /**
     * 注册响应优化过滤器
     */
    @Bean
    public FilterRegistrationBean<ResponseOptimizationFilter> responseOptimizationFilter() {
        FilterRegistrationBean<ResponseOptimizationFilter> registration = new FilterRegistrationBean<>();
        registration.setFilter(new ResponseOptimizationFilter());
        registration.addUrlPatterns("/api/v1/ticket-log/*");
        registration.setOrder(Ordered.HIGHEST_PRECEDENCE + 1);
        registration.setName("responseOptimizationFilter");
        return registration;
    }

    /**
     * 响应优化过滤器
     */
    public static class ResponseOptimizationFilter implements Filter {

        @Override
        public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
                throws IOException, ServletException {
            
            HttpServletRequest httpRequest = (HttpServletRequest) request;
            HttpServletResponse httpResponse = (HttpServletResponse) response;
            
            try {
                // 设置响应头优化
                httpResponse.setHeader("Cache-Control", "no-cache, no-store, must-revalidate");
                httpResponse.setHeader("Pragma", "no-cache");
                httpResponse.setHeader("Expires", "0");
                
                // 设置连接保持
                httpResponse.setHeader("Connection", "keep-alive");
                
                // 记录请求开始时间
                long startTime = System.currentTimeMillis();
                
                // 继续处理请求
                chain.doFilter(request, response);
                
                // 记录响应时间
                long endTime = System.currentTimeMillis();
                long responseTime = endTime - startTime;
                
                if (responseTime > 3000) {
                    log.warn("响应时间过长: {}ms, URI: {}", responseTime, httpRequest.getRequestURI());
                }
                
            } catch (Exception e) {
                log.error("响应处理异常: {}, URI: {}", e.getMessage(), httpRequest.getRequestURI());
                
                // 如果是连接中断异常，记录但不抛出
                if (e.getMessage() != null && e.getMessage().contains("你的主机中的软件中止了一个已建立的连接")) {
                    log.warn("客户端连接中断: {}", httpRequest.getRequestURI());
                    return;
                }
                
                throw e;
            }
        }
    }
}