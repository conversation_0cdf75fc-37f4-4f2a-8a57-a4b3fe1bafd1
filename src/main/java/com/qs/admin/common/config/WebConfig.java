package com.qs.admin.common.config;

import com.qs.admin.common.interceptor.JwtInterceptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * Web配置类
 */
@Configuration
public class WebConfig implements WebMvcConfigurer {

    @Autowired
    private JwtInterceptor jwtInterceptor;

    /**
     * 添加拦截器
     */
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(jwtInterceptor)
                .addPathPatterns("/api/**")  // 拦截所有API请求
                .excludePathPatterns(
                        "/api/v1/auth/**",        // 认证相关接口
                        "/api/v1/agentInfo/manual-sync-bs-token",  // 手动同步白水token接口
                        "/api/v1/agentInfo/refresh-token",         // 刷新token接口
                        "/api/v1/agentInfo/save"                   // 保存代理信息接口
                );
    }

    /**
     * 跨域配置
     */
    @Override
    public void addCorsMappings(CorsRegistry registry) {
        registry.addMapping("/api/**")
                .allowedOrigins("*")
                .allowedMethods("GET", "POST", "PUT", "DELETE", "OPTIONS")
                .exposedHeaders("Content-Type", "Authorization")
                .allowedHeaders("*")
                .allowCredentials(true)  // 允许携带认证信息
                .maxAge(3600);
    }
}
