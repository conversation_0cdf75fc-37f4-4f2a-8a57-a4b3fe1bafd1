package com.qs.admin.common.domain;

import com.alibaba.fastjson.JSON;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class Area<T> {
    /**
     * 节点ID
     */
    private String unitCode;
    /**
     * 显示节点文本
     */
    private String label;

    private Integer level;

    private String pid;
    /**
     * 节点的子节点
     */
    private List<Area<T>> children = new ArrayList<Area<T>>();

    public Area(String value, String label,Integer level,String pid,
                List<Area<T>> children) {
        super();
        this. pid = pid;
        this.unitCode = value;
        this.label = label;
        this.level= level;
        this.children = children;
    }

    public Area() {
        super();
    }

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }

}
