server:
  port: 8823
  # 连接超时配置
  connection-timeout: 60000  # 60秒连接超时
  # 响应压缩配置
  compression:
    enabled: true
    mime-types: application/json,application/xml,text/html,text/xml,text/plain,application/javascript,text/css
    min-response-size: 1024  # 最小压缩响应大小
  undertow:
    io-threads: 8  # 优化为 CPU 核心数的2倍
    worker-threads: 64  # 增加工作线程数
    buffer-size: 16384  # 增加缓冲区大小到 16KB
    direct-buffers: true
    # 增加最大请求大小和连接数
    max-http-post-size: 10MB
    max-connections: 2000  # 增加最大连接数
    # 设置空闲超时
    no-request-timeout: 60000  # 60秒无请求超时

spring:
  profiles:
    active: dev,baishui
  main:
    banner-mode: off
  # 禁用动态数据源
  autoconfigure:
    exclude:
      - com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration
  # 数据库连接池优化配置
  datasource:
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000
      leak-detection-threshold: 60000
  cache:
    type: caffeine
    caffeine:
      spec: maximumSize=50000,expireAfterWrite=1800s,recordStats  # 增加缓存大小，添加统计
  # Jackson序列化优化配置
  jackson:
    serialization:
      write-dates-as-timestamps: false
      fail-on-empty-beans: false
    deserialization:
      fail-on-unknown-properties: false
    default-property-inclusion: non_null  # 忽略null值
    generator:
      write-numbers-as-strings: false
    parser:
      allow-comments: true
  # HTTP编码配置
  servlet:
    encoding:
      charset: UTF-8
      enabled: true
      force: true

# JWT配置
jwt:
  secret: qsc_jwt_secret_key_2023
  expiration: 86400  # 24小时

# Token配置
token:
  tokenExpireTime: 10080  # token过期时间，单位分钟（7天）
  refreshCheckTime: 60   # 更新令牌时间，单位分钟
  shiroCacheExpireTime: 10080  # Shiro缓存有效期，单位分钟（7天）
  secretKey: qsc_token_secret_key_2023
mybatis-plus:
  mapperLocations: classpath:mapper/*Mapper.xml,classpath:mapper/*/*Mapper.xml
  global-config:
    banner: false
    db-config:
      id-type: auto
      tableUnderline: false
      logic-delete-value: 1
      logic-not-delete-value: 0
      field-strategy: not_null
  configuration:
    map-underscore-to-camel-case: true

logging:
  level:
    root: INFO
    org.springframework: WARN
    com.baomidou: WARN
    springfox.documentation: WARN
    org.apache.shiro: WARN  # 添加 Shiro 日志控制
    com.qs.admin: INFO  # 生产环境建议使用 INFO 级别
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"


qsc:
  shiro:
    anonUrl:
      - /login
      - /logout/**
      - /register
      - /checkUserName
      - /checkUnitCode
      - /doc.html
      - /v2/api-docs/**
      - /swagger-resources/**
      - /webjars/**
      - /favicon.ico
      - /queuingsystem/**
      - /api/v1/auth/**
      - /updatePassword
    jwtTimeOut: 604800  # 7天（秒）

permission-config:
  perms:
    # 基础免认证接口
    - key: /login
      value: anon
    - key: /register
      value: anon
    - key: /checkUserName
      value: anon
    - key: /updatePassword
      value: anon
    # Knife4j API文档
    - key: /doc.html
      value: anon
    - key: /v2/api-docs/**
      value: anon
    - key: /webjars/**
      value: anon

    # 业务相关免认证接口
    - key: /queuingsystem/**
      value: anon
    - key: /api/v1/**
      value: anon

    # 需要JWT认证的接口
    - key: /**
      value: jwt

    # 登出接口
    - key: /logout
      value: logout


