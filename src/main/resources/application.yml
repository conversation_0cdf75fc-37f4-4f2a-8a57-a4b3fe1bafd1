server:
  port: 8823
  undertow:
    io-threads: 4
    worker-threads: 20
    buffer-size: 1024
    direct-buffers: true

spring:
  profiles:
    active: dev,baishui
  main:
    banner-mode: off
  cache:
    type: caffeine
    caffeine:
      spec: maximumSize=10000,expireAfterWrite=600s

# JWT配置
jwt:
  secret: qsadmin-jwt-secret-key-2024
  expire: 86400  # 24小时（秒）

# Token配置（Shiro JWT）
token:
  tokenExpireTime: 1440  # token过期时间，单位分钟（24小时）
  refreshCheckTime: 60   # 更新令牌时间，单位分钟
  shiroCacheExpireTime: 1440  # Shiro缓存有效期，单位分钟
  secretKey: qsc-admin-shiro-secret-key-2024
mybatis-plus:
  mapperLocations: classpath:mapper/*/*Mapper.xml
  global-config:
    banner: false
    db-config:
      id-type: auto
      tableUnderline: false
      logic-delete-value: 1
      logic-not-delete-value: 0
      field-strategy: not_null
  configuration:
    map-underscore-to-camel-case: true

logging:
  level:
    root: INFO
    org.springframework: WARN
    com.baomidou: WARN
    springfox.documentation: WARN
    com.qs.admin: DEBUG


qsc:
  shiro:
    anonUrl:
      - /login
      - /logout/**
      - /register
      - /checkUserName
      - /checkUnitCode
      - /doc.html
      - /v2/api-docs/**
      - /swagger-resources/**
      - /webjars/**
      - /favicon.ico
      - /queuingsystem/**
      - /api/v1/auth/**
      - /updatePassword
    jwtTimeOut: 3600

permission-config:
  perms:
    # 基础免认证接口
    - key: /login
      value: anon
    - key: /register
      value: anon
    - key: /checkUserName
      value: anon
    - key: /updatePassword
      value: anon
    # Knife4j API文档
    - key: /doc.html
      value: anon
    - key: /v2/api-docs/**
      value: anon
    - key: /webjars/**
      value: anon

    # 业务相关免认证接口
    - key: /queuingsystem/**
      value: anon
    - key: /api/v1/**
      value: anon

    # 需要JWT认证的接口
    - key: /**
      value: jwt

    # 登出接口
    - key: /logout
      value: logout


