spring:
  profiles: dev
  datasource:
    url: *********************************************************************************************************************************************************************************
    driver-class-name: com.microsoft.sqlserver.jdbc.SQLServerDriver
    username: sa
    password: windows2008R2
    hikari:
      minimum-idle: 5
      maximum-pool-size: 20
      auto-commit: true
      idle-timeout: 30000
      pool-name: DatebookHikariCP
      max-lifetime: 1800000
      connection-timeout: 30000
      connection-test-query: SELECT 1
  boot:
    admin:
      client:
        prefer-ip: true
      url: http://localhost:8082
  mvc:
    static-path-pattern: D:/queuingsystem/


logging:
  file: ./logs/qsadmin-log.log
  level:
    root: INFO
    org.springframework: DEBUG
    com.baomidou: WARN
    com.qs.admin.*.mapper: debug

# 佰税科技API接口相关配置
baishui:
  appKey: HZBSJHXT
  appSecret: 7d427891ef38f4a7b247447f715c66cd
  tokenUrl: https://etax.zhejiang.chinatax.gov.cn/nsfw/apiService/oauth/token
  apiUrl: https://etax.zhejiang.chinatax.gov.cn/nsfw/apiService/oauth/public/api