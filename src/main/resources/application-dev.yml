server:
  port: 9999
#  undertow:
#    # 设置IO线程数, 它主要执行非阻塞的任务,它们会负责多个连接, 默认设置每个CPU核心一个线程
#    io-threads: 4
#    # 阻塞任务线程池, 当执行类似servlet请求阻塞操作, undertow会从这个线程池中取得线程,它的值设置取决于系统的负载
#    worker-threads: 20
#    # 以下的配置会影响buffer,这些buffer会用于服务器连接的IO操作,有点类似netty的池化内存管理
#    # 每块buffer的空间大小,越小的空间被利用越充分
#    buffer-size: 1024
#    # 是否分配的直接内存
#    direct-buffers: true

spring:
  datasource:
#    master:
#    mysql:
#      url: **************************************************************************************************
#      driverClassName: com.mysql.jdbc.Driver
#    sqlserver:
    url: ****************************************
    driverClassName: com.microsoft.sqlserver.jdbc.SQLServerDriver

#    taxhall:


  boot:
    admin:
      client:
        prefer-ip: true
      url: http://localhost:8082

  redis:
    host: 127.0.0.1
    port: 6379
    password:
#    password: ziyun12399
    jedis:
      pool:
        min-idle: 8
        max-idle: 15
        max-active: 8
        max-wait: 3000
    timeout: 3000
  mvc:
    # windows环境下
     static-path-pattern: D:/queuingsystem/
    # linux环境下
#    static-path-pattern: /opt/queuingsystem


mybatis-plus:
  mapperLocations: classpath:mapper/*/*Mapper.xml
  global-config:
    banner: true
    db-config:
      id-type: auto
      tableUnderline: false
      # 逻辑已删除值(默认为 1)
      logic-delete-value: 1
      # 逻辑未删除值(默认为 0)
      logic-not-delete-value: 0
      field-strategy: not_null
  configuration:
    map-underscore-to-camel-case: true


logging:
  file: ./logs/qsadmin-log.log
  level:
    com.qs.admin.*.mapper: debug


qsc:
  shiro:
    # 后端免认证接口 url
    anonUrl: /login,/logout/**,/register,/checkUserName,/checkUnitCode,/doc.html,/queuingsystem/**,/qscNvr,/updatePassword
    # token有效期，单位秒
    jwtTimeOut: 3600