<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qs.admin.taxhall.mapper.TicketMapper">

    <!-- 票据VO结果映射 -->
    <resultMap id="TicketVOResultMap" type="com.qs.admin.taxhall.model.vo.TicketVO">
        <id column="uid" property="uid"/>
        <result column="TKT_ID" property="tktId"/>
        <result column="TKT_INTID" property="tktIntid"/>
        <result column="TKT_DATE" property="tktDate"/>
        <result column="TKT_TIME" property="tktTime"/>
        <result column="WIN_ID" property="winId"/>
        <result column="windowName" property="windowName"/>
        <result column="BIZ_UID" property="bizUid"/>
        <result column="businessName" property="businessName"/>
        <result column="EMP_UID" property="empUid"/>
        <result column="employeeName" property="employeeName"/>
        <result column="START_TIME" property="startTime"/>
        <result column="END_TIME" property="endTime"/>
        <result column="RANK" property="rank"/>
        <result column="STATUS" property="status"/>
        <result column="FIRST_TIME" property="firstTime"/>
        <result column="SEC_START_TIME" property="secStartTime"/>
    </resultMap>

    <!-- 分页查询票据VO -->
    <select id="selectTicketVOPage" resultMap="TicketVOResultMap">
        SELECT 
            t.uid, t.TKT_ID, t.TKT_INTID, t.TKT_DATE, t.TKT_TIME,
            t.WIN_ID, ISNULL(w.name, '窗口' + CAST(t.WIN_ID AS VARCHAR(10))) as windowName,
            t.BIZ_UID, ISNULL(b.name, '业务' + CAST(t.BIZ_UID AS VARCHAR(10))) as businessName,
            t.EMP_UID, ISNULL(e.name, '员工' + CAST(t.EMP_UID AS VARCHAR(10))) as employeeName,
            t.START_TIME, t.END_TIME, t.RANK, t.STATUS,
            t.FIRST_TIME, t.SEC_START_TIME
        FROM ticket t
        LEFT JOIN window w ON t.WIN_ID = w.uid
        LEFT JOIN business b ON t.BIZ_UID = b.uid
        LEFT JOIN employee e ON t.EMP_UID = e.uid
    </select>

    <!-- 带条件分页查询票据VO -->
    <select id="selectTicketVOPageWithCondition" resultMap="TicketVOResultMap">
        SELECT 
            t.uid, t.TKT_ID, t.TKT_INTID, t.TKT_DATE, t.TKT_TIME,
            t.WIN_ID, ISNULL(w.name, '窗口' + CAST(t.WIN_ID AS VARCHAR(10))) as windowName,
            t.BIZ_UID, ISNULL(b.name, '业务' + CAST(t.BIZ_UID AS VARCHAR(10))) as businessName,
            t.EMP_UID, ISNULL(e.name, '员工' + CAST(t.EMP_UID AS VARCHAR(10))) as employeeName,
            t.START_TIME, t.END_TIME, t.RANK, t.STATUS,
            t.FIRST_TIME, t.SEC_START_TIME
        FROM ticket t
        LEFT JOIN window w ON t.WIN_ID = w.uid
        LEFT JOIN business b ON t.BIZ_UID = b.uid
        LEFT JOIN employee e ON t.EMP_UID = e.uid
        WHERE 1=1
        <if test="condition.tktId != null and condition.tktId != ''">
            AND t.TKT_ID LIKE '%' + #{condition.tktId} + '%'
        </if>
        <if test="condition.winId != null">
            AND t.WIN_ID = #{condition.winId}
        </if>
        <if test="condition.bizUid != null">
            AND t.BIZ_UID = #{condition.bizUid}
        </if>
        <if test="condition.empUid != null">
            AND t.EMP_UID = #{condition.empUid}
        </if>
        <if test="condition.status != null">
            AND t.STATUS = #{condition.status}
        </if>
    </select>

</mapper>
