<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qs.admin.system.mapper.QscVersionsMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.qs.admin.system.model.QscVersions">
        <result column="unit_code" property="unitCode" />
        <result column="callclient_ver" property="callclientVer" />
        <result column="androidrankclient_ver" property="androidrankclientVer" />
        <result column="windowrankclient_ver" property="windowrankclientVer" />
        <result column="tvclient_ver" property="tvclientVer" />
        <result column="ticketclient_ver" property="ticketclientVer" />
        <result column="syncscreen_ver" property="syncscreenVer" />
        <result column="synczhscreen_ver" property="synczhscreenVer" />
        <result column="windowscreen_ver" property="windowscreenVer" />
        <result column="ledaudioserivce_ver" property="ledaudioserivceVer" />
        <result column="dataservice_ver" property="dataserviceVer" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        unit_code, callclient_ver, androidrankclient_ver, windowrankclient_ver, tvclient_ver, ticketclient_ver, syncscreen_ver, synczhscreen_ver, windowscreen_ver, ledaudioserivce_ver, dataservice_ver
    </sql>

</mapper>
