<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qs.admin.system.mapper.QscMenuMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.qs.admin.system.model.QscMenu">
        <result column="menu_id" property="menuId" />
        <result column="parent_id" property="parentId" />
        <result column="menu_name" property="menuName" />
        <result column="path" property="path" />
        <result column="component" property="component" />
        <result column="perms" property="perms" />
        <result column="icon" property="icon" />
        <result column="type" property="type" />
        <result column="order_num" property="orderNum" />
        <result column="create_date" property="createDate" />
        <result column="update_date" property="updateDate" />
        <result column="update_by" property="updateBy" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        menu_id, parent_id, menu_name, path, component, perms, icon, type, order_num, create_date, update_date, update_by
    </sql>

<!--    <select id="findAccountPermissions" resultMap="BaseResultMap">-->
<!--        select distinct m.perms-->
<!--        from qsc_role r-->
<!--                 left join qsc_account_role ur on (r.role_id = ur.role_id)-->
<!--                 left join qsc_account u on (u.uid = ur.account_id)-->
<!--                 left join qsc_role_menu rm on (rm.role_id = r.role_id)-->
<!--                 left join qsc_menu m on (m.menu_id = rm.menu_id)-->
<!--        where u.username = #{userName}-->
<!--          and m.perms is not null-->
<!--          and m.perms &lt;&gt; ''-->
<!--    </select>-->

<!--    <select id="findAccountMenus" resultMap="BaseResultMap">-->
<!--        select m.*-->
<!--        from qsc_menu m-->
<!--        where m.type &lt;&gt; 1-->
<!--          and m.menu_id in-->
<!--              (select distinct rm.menu_id-->
<!--               from qsc_role_menu rm-->
<!--                        left join qsc_role r on (rm.role_id = r.role_id)-->
<!--                        left join qsc_account_role ur on (ur.role_id = r.role_id)-->
<!--                        left join qsc_account u on (u.uid = ur.account_id)-->
<!--               where u.username = #{userName})-->
<!--        order by m.order_num-->
<!--    </select>-->

    <select id="findAccountIdsByMenuId" parameterType="string" resultType="string">
        SELECT
            account_id
        FROM
            t_account_role
        WHERE
            role_id IN ( SELECT rm.role_id FROM t_role_menu rm WHERE rm.menu_id = #{menuId} )
    </select>

    <!-- 递归删除菜单，findMenuChildren为自定义的 MySQL函数，作用为根据当前 menuId递归查找出其所有下级菜单或按钮-->
    <delete id="deleteMenus" parameterType="string">
        DELETE
        FROM
            qsc_menu
        WHERE
            menu_id IN ( SELECT m.menu_id FROM ( SELECT menu_id FROM t_menu WHERE FIND_IN_SET( menu_id, findMenuChildren ( #{menuId} ) ) > 0 ) m )
    </delete>

</mapper>
