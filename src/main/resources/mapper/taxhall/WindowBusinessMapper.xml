<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qs.admin.taxhall.mapper.WindowBusinessMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.qs.admin.taxhall.model.WindowBusiness">
        <result column="BIZ_UID" property="bizUid" />
        <result column="WIN_UID" property="winUid" />
        <result column="PRIORITY" property="priority" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        BIZ_UID, WIN_UID, PRIORITY
    </sql>

</mapper>
