<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qs.admin.taxhall.mapper.TicketVerifyMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.qs.admin.taxhall.model.TicketVerify">
        <result column="UID" property="uid" />
        <result column="NAME" property="name" />
        <result column="CODE" property="code" />
        <result column="COMPARECODE" property="comparecode" />
        <result column="COMPAREVALUE" property="comparevalue" />
        <result column="TKT_UID" property="tktUid" />
        <result column="DATE" property="date" />
        <result column="PHONE" property="phone" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        UID, NAME, CODE, COMPARECODE, COMPAREVALUE, TKT_UID, DATE, PHONE
    </sql>

</mapper>
