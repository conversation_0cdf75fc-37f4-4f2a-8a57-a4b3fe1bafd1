<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qs.admin.taxhall.mapper.SystemMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.qs.admin.taxhall.model.System">
        <result column="KEY" property="key" />
        <result column="VALUE" property="value" />
        <result column="MEMO" property="memo" />
    </resultMap>

    <!-- SQL Server专用查询结果映射 -->
    <resultMap id="SqlServerResultMap" type="com.qs.admin.taxhall.model.System">
        <result column="[KEY]" property="key" />
        <result column="[VALUE]" property="value" />
        <result column="[MEMO]" property="memo" />
    </resultMap>

    <!-- 通用查询结果列（MySQL/OceanBase） -->
    <sql id="Base_Column_List">
        KEY, VALUE, MEMO
    </sql>

    <!-- SQL Server查询结果列 -->
    <sql id="SqlServer_Column_List">
        [KEY], [VALUE], [MEMO]
    </sql>

</mapper>
