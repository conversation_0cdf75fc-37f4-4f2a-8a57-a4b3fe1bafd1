<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qs.admin.taxhall.mapper.WindowStatusMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.qs.admin.taxhall.model.WindowStatus">
        <result column="WIN_UID" property="winUid" />
        <result column="EMP_UID" property="empUid" />
        <result column="STATUS" property="status" />
        <result column="PC_NAME" property="pcName" />
        <result column="CLIENT_VER" property="clientVer" />
        <result column="UPDATE_TIME" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        WIN_UID, EMP_UID, STATUS, PC_NAME, CLIENT_VER, UPDATE_TIME
    </sql>

</mapper>
