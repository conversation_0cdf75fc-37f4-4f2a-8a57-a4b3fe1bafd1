<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qs.admin.taxhall.mapper.TicketBookMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.qs.admin.taxhall.model.TicketBook">
        <result column="UID" property="uid" />
        <result column="UUID" property="uuid" />
        <result column="DATE" property="date" />
        <result column="BIZ_UID" property="bizUid" />
        <result column="TIME_START" property="timeStart" />
        <result column="TIME_END" property="timeEnd" />
        <result column="TYPE" property="type" />
        <result column="NAME" property="name" />
        <result column="ID_CODE" property="idCode" />
        <result column="PHONE" property="phone" />
        <result column="TAX_CODE" property="taxCode" />
        <result column="VERIFY_CODE" property="verifyCode" />
        <result column="EXPIRE_TIME" property="expireTime" />
        <result column="NJ" property="nj" />
        <result column="TKT_UID" property="tktUid" />
        <result column="STATUS" property="status" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        UID, UUID, DATE, BIZ_UID, TIME_START, TIME_END, TYPE, NAME, ID_CODE, PHONE, TAX_CODE, VERIFY_CODE, EXPIRE_TIME, NJ, TKT_UID, STATUS
    </sql>

</mapper>
