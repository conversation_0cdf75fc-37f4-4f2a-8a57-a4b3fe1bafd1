<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qs.admin.taxhall.mapper.AgentInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.qs.admin.taxhall.model.AgentInfo">
        <id column="ID" property="id" />
        <result column="AGENT_NAME" property="agentName" />
        <result column="AGENT_ID_CARD" property="agentIdCard" />
        <result column="AGENT_PHONE" property="agentPhone" />
        <result column="TKT_DATE_TIME" property="tktDateTime" />
        <result column="TKT_UID" property="tktUid" />
        <result column="TKT_ID" property="tktId" />
        <result column="AGENT_ORG" property="agentOrg" />
    </resultMap>

    <!-- 自定义插入语句，明确指定列名 -->
    <insert id="insert" parameterType="com.qs.admin.taxhall.model.AgentInfo" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO [AGENT_INFO] (
            [AGENT_NAME],
            [AGENT_ID_CARD],
            [AGENT_PHONE],
            [TKT_DATE_TIME],
            [TKT_UID],
            [TKT_ID],
            [AGENT_ORG]
        ) VALUES (
            #{agentName, jdbcType=VARCHAR},
            #{agentIdCard, jdbcType=VARCHAR},
            #{agentPhone, jdbcType=VARCHAR},
            #{tktDateTime, jdbcType=TIMESTAMP},
            #{tktUid, jdbcType=INTEGER},
            #{tktId, jdbcType=VARCHAR},
            #{agentOrg, jdbcType=VARCHAR}
        )
    </insert>

    <!-- 自定义查询语句 -->
    <select id="selectById" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT * FROM [AGENT_INFO] WHERE [ID] = #{id}
    </select>

    <select id="selectList" resultMap="BaseResultMap">
        SELECT * FROM [AGENT_INFO]
    </select>

</mapper>
