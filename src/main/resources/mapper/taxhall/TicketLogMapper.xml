<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qs.admin.taxhall.mapper.TicketLogMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.qs.admin.taxhall.model.TicketLog">
        <result column="UID" property="uid" />
        <result column="TKT_ID" property="tktId" />
        <result column="TKT_DATE" property="tktDate" />
        <result column="TKT_TIME" property="tktTime" />
        <result column="WIN_ID" property="winId" />
        <result column="BIZ_UID" property="bizUid" />
        <result column="BIZ_PREFIX" property="bizPrefix" />
        <result column="BIZ_NAME" property="bizName" />
        <result column="EMP_UID" property="empUid" />
        <result column="EMP_ID" property="empId" />
        <result column="EMP_NAME" property="empName" />
        <result column="START_TIME" property="startTime" />
        <result column="END_TIME" property="endTime" />
        <result column="RANK" property="rank" />
        <result column="STATUS" property="status" />
        <result column="SYNC_STATUS" property="syncStatus" />
        <result column="SYNC_DATE" property="syncDate" />
        <result column="SEC_START_TIME" property="secStartTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        UID, TKT_ID, TKT_DATE, TKT_TIME, WIN_ID, BIZ_UID, BIZ_PREFIX, BIZ_NAME, EMP_UID, EMP_ID, EMP_NAME, START_TIME, END_TIME, RANK, STATUS, SYNC_STATUS, SYNC_DATE, SEC_START_TIME
    </sql>

</mapper>
