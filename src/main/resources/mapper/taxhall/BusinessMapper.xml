<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qs.admin.taxhall.mapper.BusinessMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.qs.admin.taxhall.model.Business">
        <result column="UID" property="uid" />
        <result column="PREFIX" property="prefix" />
        <result column="NAME" property="name" />
        <result column="ENABLED" property="enabled" />
        <result column="TYPE" property="type" />
        <result column="HANDLE_COUNT" property="handleCount" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        UID, PREFIX, NAME, ENABLED, TYPE, HANDLE_COUNT
    </sql>

</mapper>
