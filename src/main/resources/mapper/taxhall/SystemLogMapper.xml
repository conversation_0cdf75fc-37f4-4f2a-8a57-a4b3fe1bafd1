<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qs.admin.taxhall.mapper.SystemLogMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.qs.admin.taxhall.model.SystemLog">
        <result column="UID" property="uid" />
        <result column="LOG_TIME" property="logTime" />
        <result column="LOG_TYPE" property="logType" />
        <result column="LOG_CONTENT" property="logContent" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        UID, LOG_TIME, LOG_TYPE, LOG_CONTENT
    </sql>

</mapper>
