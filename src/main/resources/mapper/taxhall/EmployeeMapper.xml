<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qs.admin.taxhall.mapper.EmployeeMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.qs.admin.taxhall.model.Employee">
        <result column="UID" property="uid" />
        <result column="NAME" property="name" />
        <result column="ID" property="id" />
        <result column="USERNAME" property="username" />
        <result column="PASSWORD" property="password" />
        <result column="IMAGE" property="image" />
        <result column="STATUS" property="status" />
        <result column="ENABLED" property="enabled" />
        <result column="ACCESS" property="access" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        UID, NAME, ID, USERNAME, PASSWORD, IMAGE, STATUS, ENABLED, ACCESS
    </sql>

</mapper>
