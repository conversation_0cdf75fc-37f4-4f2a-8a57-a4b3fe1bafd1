<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qs.admin.taxhall.mapper.ThingsMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.qs.admin.taxhall.model.Things">
        <result column="gdslb" property="gdslb" />
        <result column="sssx_dl_dm" property="sssxDlDm" />
        <result column="sssx_dl_mc" property="sssxDlMc" />
        <result column="sssx_dm" property="sssxDm" />
        <result column="sssx_mc" property="sssxMc" />
        <result column="id" property="id" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        gdslb, sssx_dl_dm, sssx_dl_mc, sssx_dm, sssx_mc, id
    </sql>

</mapper>
