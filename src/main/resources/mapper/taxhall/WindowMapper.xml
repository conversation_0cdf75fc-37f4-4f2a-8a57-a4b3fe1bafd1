<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qs.admin.taxhall.mapper.WindowMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.qs.admin.taxhall.model.Window">
        <result column="UID" property="uid"/>
        <result column="NAME" property="name"/>
        <result column="LED_ADDRESS" property="ledAddress"/>
        <result column="LED_TEXT" property="ledText"/>
        <result column="ENABLED" property="enabled"/>
        <result column="SID" property="sid"/>
        <result column="RANK_MODE" property="rankMode" />
        <result column="RANK_ADDRESS" property="rankAddress"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        UID, NAME, LED_ADDRESS, LED_TEXT, ENABLED, SID, RANK_MODE, RANK_ADDRESS
    </sql>

    <!-- 自定义分页查询，避免方括号解析问题 -->
    <select id="selectWindowPage" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM WINDOW
        ORDER BY UID DESC
    </select>

    <!-- 使用ROW_NUMBER分页查询，兼容SQL Server 2005+ -->
    <select id="selectWindowsByPage" resultMap="BaseResultMap">
        SELECT * FROM (
            SELECT <include refid="Base_Column_List" />,
                   ROW_NUMBER() OVER (ORDER BY UID DESC) as rn
            FROM WINDOW
        ) t
        WHERE t.rn BETWEEN #{startRow} AND #{endRow}
    </select>

    <!-- 自定义count查询，避免方括号解析问题 -->
    <select id="selectWindowPageCount" resultType="java.lang.Long">
        SELECT COUNT(1)
        FROM WINDOW
    </select>

    <!-- 查询窗口选项 -->
    <select id="selectWindowOptions" resultType="java.util.Map">
        SELECT UID as value, NAME as label, NAME as text
        FROM WINDOW
        WHERE ENABLED = 1
        ORDER BY UID
    </select>

</mapper>
