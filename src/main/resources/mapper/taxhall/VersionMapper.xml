<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qs.admin.taxhall.dao.VersionMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.qs.admin.taxhall.model.Version">
        <result column="mid" property="mid" />
        <result column="version" property="version" />
        <result column="publish_date" property="publishDate" />
        <result column="update_content" property="updateContent" />
        <result column="updating_package_url" property="updatingPackageUrl" />
        <result column="deploying_packages_url" property="deployingPackagesUrl" />
        <result column="last_update_date" property="lastUpdateDate" />
        <result column="last_update_name" property="lastUpdateName" />
        <result column="plan_date" property="planDate" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        mid, version, publish_date, update_content, updating_package_url, deploying_packages_url, last_update_date, last_update_name, plan_date
    </sql>

</mapper>
