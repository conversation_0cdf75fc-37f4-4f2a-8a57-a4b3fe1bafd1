<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qs.admin.taxhall.mapper.AgentInfoEnterpriseMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.qs.admin.taxhall.model.AgentInfoEnterprise">
        <id column="ID" property="id" />
        <result column="AGENT_INFO_ID" property="agentInfoId" />
        <result column="DJXH" property="djxh" />
        <result column="NSRSBH" property="nsrsbh" />
        <result column="NSRMC" property="nsrmc" />
        <result column="ZGSWJ_DM" property="zgswjDm" />
        <result column="ZGSWKSFJ_DM" property="zgswksfjDm" />
        <result column="NSRZT_MC" property="nsrztMc" />
        <result column="SFZJ" property="sfzj" />
    </resultMap>

    <!-- 自定义插入语句，明确指定列名 -->
    <insert id="insert" parameterType="com.qs.admin.taxhall.model.AgentInfoEnterprise" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO [AGENT_INFO_ENTERPRISE] (
            [AGENT_INFO_ID],
            [DJXH],
            [NSRSBH],
            [NSRMC],
            [ZGSWJ_DM],
            [ZGSWKSFJ_DM],
            [NSRZT_MC],
            [SFZJ]
        ) VALUES (
            #{agentInfoId, jdbcType=BIGINT},
            #{djxh, jdbcType=VARCHAR},
            #{nsrsbh, jdbcType=VARCHAR},
            #{nsrmc, jdbcType=VARCHAR},
            #{zgswjDm, jdbcType=VARCHAR},
            #{zgswksfjDm, jdbcType=VARCHAR},
            #{nsrztMc, jdbcType=VARCHAR},
            #{sfzj, jdbcType=VARCHAR}
        )
    </insert>

    <!-- 自定义查询语句 -->
    <select id="selectById" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT * FROM [AGENT_INFO_ENTERPRISE] WHERE [ID] = #{id}
    </select>

    <select id="selectByAgentInfoId" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT * FROM [AGENT_INFO_ENTERPRISE] WHERE [AGENT_INFO_ID] = #{agentInfoId}
    </select>

    <select id="selectList" resultMap="BaseResultMap">
        SELECT * FROM [AGENT_INFO_ENTERPRISE]
    </select>

</mapper>
