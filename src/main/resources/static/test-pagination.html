<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>分页测试页面</title>
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
</head>
<body>
    <h1>分页接口测试</h1>
    
    <div>
        <label>页码: <input type="number" id="pageNum" value="1" min="1"></label>
        <label>每页大小: <input type="number" id="pageSize" value="10" min="1"></label>
        <button onclick="testPagination()">测试分页</button>
    </div>
    
    <div id="result" style="margin-top: 20px;">
        <h3>响应结果:</h3>
        <pre id="response"></pre>
    </div>
    
    <div id="pagination" style="margin-top: 20px;">
        <h3>分页控件:</h3>
        <button onclick="prevPage()">上一页</button>
        <span id="pageInfo">第 1 页</span>
        <button onclick="nextPage()">下一页</button>
    </div>

    <script>
        let currentPage = 1;
        let totalPages = 1;
        
        function testPagination() {
            const pageNum = document.getElementById('pageNum').value;
            const pageSize = document.getElementById('pageSize').value;
            
            console.log('Testing pagination with pageNum:', pageNum, 'pageSize:', pageSize);
            
            axios.get('/api/v1/window/page', {
                params: {
                    pageNum: pageNum,
                    pageSize: pageSize
                }
            })
            .then(function (response) {
                console.log('Success:', response.data);
                document.getElementById('response').textContent = JSON.stringify(response.data, null, 2);
                
                if (response.data.code === 200 && response.data.data) {
                    currentPage = response.data.data.current;
                    totalPages = response.data.data.pages;
                    updatePageInfo();
                }
            })
            .catch(function (error) {
                console.error('Error:', error);
                document.getElementById('response').textContent = 'Error: ' + error.message;
            });
        }
        
        function prevPage() {
            if (currentPage > 1) {
                currentPage--;
                document.getElementById('pageNum').value = currentPage;
                testPagination();
            }
        }
        
        function nextPage() {
            if (currentPage < totalPages) {
                currentPage++;
                document.getElementById('pageNum').value = currentPage;
                testPagination();
            }
        }
        
        function updatePageInfo() {
            document.getElementById('pageInfo').textContent = `第 ${currentPage} 页 / 共 ${totalPages} 页`;
        }
        
        // 页面加载时自动测试
        window.onload = function() {
            testPagination();
        };
    </script>
</body>
</html>
