package com.qs.admin;

import com.qs.admin.taxhall.model.dto.MenuDTO;
import com.qs.admin.taxhall.model.dto.LoginResponseDTO;
import com.qs.admin.taxhall.service.MenuService;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.util.List;

/**
 * Menu functionality test class
 */
public class MenuTest {
    
    public static void main(String[] args) {
        try {
            // Create menu service instance
            MenuService menuService = new MenuService();
            
            // Test get menus
            List<MenuDTO> menus = menuService.getAllMenus();
            
            // Create login response
            LoginResponseDTO response = new LoginResponseDTO(
                1L,
                "admin",
                "Administrator",
                "admin",
                "SIMPLE_TOKEN_1_" + System.currentTimeMillis(),
                menus
            );
            
            // Convert to JSON output
            ObjectMapper mapper = new ObjectMapper();
            String json = mapper.writerWithDefaultPrettyPrinter().writeValueAsString(response);
            
            System.out.println("=== Login Menu Function Test ===");
            System.out.println("Menu count: " + menus.size());
            System.out.println("\nComplete login response JSON:");
            System.out.println(json);
            
            System.out.println("\n=== Menu Structure Validation ===");
            for (MenuDTO menu : menus) {
                System.out.println("Menu: " + menu.getTitle() + " (" + menu.getIndex() + ")");
                if (menu.getSubs() != null && !menu.getSubs().isEmpty()) {
                    for (MenuDTO sub : menu.getSubs()) {
                        System.out.println("  └─ Sub-menu: " + sub.getTitle() + " (" + sub.getIndex() + ")");
                    }
                }
            }
            
            System.out.println("\n✅ Menu function test completed!");
            
        } catch (Exception e) {
            System.err.println("❌ Test failed: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
