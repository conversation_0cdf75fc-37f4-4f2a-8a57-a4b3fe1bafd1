//package com.admin.test;
//
//import com.qs.admin.system.mapper.QscAccountMapper;
//import com.qs.admin.system.model.QscAccount;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.test.context.junit4.SpringRunner;
//
//import java.util.List;
//
///**
// * @Author: ytx
// * @Date: 2020/4/16  19:48
// */
//@RunWith(SpringRunner.class)
//@SpringBootTest
//public class simpleTest {
//    @Autowired
//    private QscAccountMapper accountMapper;
//
//    @Test
//    public void selet(){
//        List<QscAccount> list = accountMapper.selectList(null);
//    }
//}
