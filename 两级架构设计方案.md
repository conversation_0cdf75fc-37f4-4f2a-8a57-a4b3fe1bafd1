# 排队系统两级架构设计方案

## 1. 架构总览

### 1.1 两级架构设计

基于实际部署需求，将原三级架构优化为两级架构，简化系统复杂度，提高部署和维护效率。

```
[省局中心服务器集群] ←→ [大厅本地服务器]
       ↑                    ↑
       ↓                    ↓
  [管理端Web应用]        [取号/呼号终端]
```

### 1.2 技术栈对照表

| 技术类别 | 省局中心服务器 | 大厅本地服务器 |
|---------|--------------|---------------|
| 开发框架 | Spring Boot X | Spring Boot X |
| 数据库 | MySQL X (主从架构) | SQLite X |
| 消息中间件 | RabbitMQ X | RabbitMQ X |
| 缓存 | Redis X | - |
| 负载均衡 | Nginx X | - |
| 安全框架 | Spring Security X | Spring Security X |
| ORM框架 | MyBatis X | MyBatis X |
| 服务发现 | Nacos X | - |
| 部署容器 | Docker X | - |
| 监控工具 | Prometheus X | - |
| 日志框架 | Logback X | Logback X |

#### 1.1.1 省局层
- **职责**：
  - 中央数据管理和存储
  - 全省业务数据聚合和分析
  - 系统配置管理和下发
  - 提供管理端Web应用
  - 负载均衡和高可用保障
  - 市/区/县级管理功能（作为逻辑角色）

- **核心组件**：
  - 中央数据库集群（主从架构）
  - 管理服务集群（负载均衡）
  - 数据同步服务（处理下级数据上传）
  - 配置管理服务（管理系统配置）
  - API网关（统一接入控制）
  - 区域管理模块（市/区/县级管理功能）

#### 1.1.2 大厅层
- **职责**：
  - 取号叫号业务处理
  - 本地数据存储和管理
  - 离线业务支持
  - 与终端设备通信
  - 数据同步（直接与省局通信）

- **核心组件**：
  - 取号服务(queuing-system-service)
  - 呼号服务(calling-system-service)
  - 本地SQLite数据库
  - 数据同步客户端
  - 消息队列(RabbitMQ)

## 2. 数据流设计

### 2.1 上行数据流（大厅→省局）

```
[大厅SQLite] → [数据同步客户端] → [省局数据同步服务] → [省局中央数据库]
```

- **同步内容**：业务数据（取号记录、呼叫记录、服务评价等）
- **同步策略**：
  - 定时批量同步（可配置间隔，默认5分钟）
  - 关键数据实时同步
  - 增量同步（仅同步变更数据）

### 2.2 下行数据流（省局→大厅）

```
[省局中央数据库] → [配置管理服务] → [数据同步客户端] → [大厅SQLite]
```

- **同步内容**：配置数据（窗口信息、人员信息、业务类型配置等）
- **同步策略**：
  - 配置变更时触发同步
  - 大厅服务启动时拉取最新配置
  - 定时检查配置更新（默认每小时）

## 3. 离线运行机制

### 3.1 网络中断处理

#### 3.1.1 大厅离线运行
- 使用本地SQLite数据库支持完整业务流程
- 本地数据添加同步状态标记
- 维护本地同步队列，记录待同步数据

#### 3.1.2 数据一致性保障
- 使用版本号和时间戳解决冲突
- 省局数据为主导，本地数据为辅助
- 关键业务数据添加唯一标识符

### 3.2 网络恢复处理

#### 3.2.1 数据同步流程
- 自动检测网络恢复
- 按优先级同步积压数据
- 拉取最新配置数据

## 4. 高可用设计

### 4.1 省局层高可用
- 使用负载均衡器（如Nginx）分发请求
- 至少部署2台以上应用服务器
- 采用主从架构的数据库集群
- 定期数据备份
- 故障自动切换机制

### 4.2 大厅层可靠性
- 本地数据定期备份
- 服务自动重启机制
- 硬件冗余（如双网卡）
- 离线运行支持

## 5. 安全设计

### 5.1 网络安全
- 使用VPN或专线连接各级系统
- 配置防火墙限制访问
- 实施网络分区隔离

### 5.2 数据安全
- 敏感数据加密存储
- 传输数据加密（HTTPS/SSL）
- 数据访问权限控制

### 5.3 身份认证与授权
- 统一身份认证系统
- 基于角色的访问控制（RBAC）
- 多因素认证（关键操作）

## 6. 市/区/县级管理

### 6.1 逻辑角色设计
- 市/区/县级作为省局系统中的逻辑角色
- 通过权限控制实现区域数据隔离
- 提供专用的区域管理界面

### 6.2 数据隔离机制

#### 6.2.1 多租户数据隔离
- **数据标识**
  - 核心业务表添加hall_id字段
  - 用户认证时关联大厅信息
  - 数据创建时自动填充hall_id

- **访问控制**
  - 基于MyBatis拦截器实现
  - 自动注入hall_id查询条件
  - 防止越权访问其他大厅数据

- **数据同步隔离**
  - 同步服务基于hall_id过滤
  - 确保数据同步的租户隔离
  - 支持增量同步机制

#### 6.2.2 配置隔离
- **配置分类**
  - 全局配置：适用所有大厅
  - 大厅私有配置：特定大厅专用
  - 使用scope字段标识配置范围

- **配置管理**
  - 大厅管理员仅可修改本厅配置
  - 省局管理员可管理全局配置
  - 配置继承与覆盖机制

- **配置同步**
  - 按大厅ID过滤配置下发
  - 支持配置版本管理
  - 增量更新配置变更

#### 6.2.3 区域数据管理
- 基于组织架构的数据过滤
- 区域管理员只能查看和管理所属区域数据
- 支持跨区域数据汇总（仅高级权限）

## 7. 部署方案

### 7.1 省局部署
- 中心数据库服务器（主从架构）
- 应用服务器集群（负载均衡）
- 文件存储服务器
- 备份服务器

### 7.2 大厅部署
- 本地服务器（取号、呼号、数据同步）
- 本地SQLite数据库
- 本地消息队列
- 终端设备（取号机、LED屏、窗口终端）

## 8. 配置管理

### 8.1 系统层级配置
- 简化为两级：CENTRAL(省局)、HALL(大厅)
- 通过配置文件指定系统角色
- 动态加载对应配置

### 8.2 同步配置
- 大厅直接连接省局同步服务
- 配置省局同步服务地址
- 设置同步间隔和批次大小

## 9. 升级路径

### 9.1 从三级架构迁移
1. 更新配置文件，移除REGIONAL层级
2. 修改大厅同步地址，直接指向省局
3. 在省局系统中添加区域管理功能
4. 迁移区域服务器数据到省局

### 9.2 兼容性保障
- 保留原有API接口
- 数据结构向后兼容
- 提供配置迁移工具

## 10. 总结

本两级架构设计方案通过优化原三级架构，简化了系统复杂度，提高了部署和维护效率。关键特性包括：

1. **简化架构**：减少中间层，降低系统复杂度
2. **数据直连**：大厅数据直接与省局同步，减少延迟
3. **离线支持**：保留完整的离线运行能力
4. **逻辑区域管理**：市/区/县级作为逻辑角色而非物理部署
5. **高可用设计**：关键节点采用集群部署，确保系统可靠性

通过本方案的实施，可以构建一个更加简洁高效、易于部署和维护的排队系统分布式架构，满足省、市、县、区各级业务办理大厅的实际需求。