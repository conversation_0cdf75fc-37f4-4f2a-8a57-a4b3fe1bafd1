# 云端数据库优化设计说明

## 1. 设计背景

原排队叫号系统采用非分布式架构，每个大厅都部署独立的数据库和服务，数据相互隔离。随着信创改革的推进，需要将部分功能迁移至云端，实现集中管理和数据共享。本设计方案旨在优化数据库结构，确保在分布式架构下，大厅服务层能够准确获取云端数据库中属于该大厅的业务配置信息，并支持多种标识方式和数据同步机制。

## 2. 设计目标

1. 在云端数据库中区分不同大厅的数据
2. 确保大厅服务层能够准确查询和获取本厅的业务配置
3. 支持数据的双向同步和版本控制
4. 保障大厅在离线状态下的业务连续性
5. 提供高效的数据查询和访问机制
6. 支持第三方接口提供的多种大厅标识信息

## 3. 数据库结构优化方案

### 3.1 大厅标识体系

为实现多大厅数据的集中管理，在云端数据库中引入大厅标识体系：

1. **创建大厅信息表(hall_info)**：存储大厅的基本信息，包括大厅ID、名称、编码等
2. **核心业务表添加大厅标识**：在业务表、窗口表、员工表、票号表等添加hall_id字段
3. **建立索引优化查询**：为包含hall_id的字段创建复合索引，提高查询效率
4. **创建大厅标识映射表(hall_identifier_mapping)**：支持多种外部标识符与内部hall_id的映射关系

#### 3.1.1 大厅标识映射实现

大厅标识映射是系统支持多种标识方式的核心机制，具体实现如下：

1. **映射表结构设计**
   - 主键ID：唯一标识每条映射记录
   - 大厅ID(hall_id)：关联到hall_info表的主键
   - 标识类型(identifier_type)：如tax_bureau_code(税务局编码)、tax_authority_id(税务机关ID)等
   - 标识值(identifier_value)：对应标识类型的具体值
   - 是否主要标识(is_primary)：标记该标识是否为主要标识
   - 描述信息(description)：标识的说明信息
   - 创建和更新时间：记录数据变更时间

2. **唯一性约束**
   - 同一大厅下不允许存在相同类型的多个标识
   - 同一标识类型和值的组合在系统中必须唯一

3. **主要标识机制**
   - 每个大厅可以设置一个主要标识，用于默认查询和显示
   - 当设置新的主要标识时，自动将原主要标识设置为非主要

4. **标识查询优化**
   - 创建(identifier_type, identifier_value)复合索引，加速标识查询
   - 创建(hall_id, is_primary)复合索引，加速主要标识查询

### 3.2 数据访问控制

为确保大厅服务层只能访问本厅数据，采用以下机制：

1. **视图隔离**：创建基于大厅ID过滤的视图(v_hall_business, v_hall_window, v_hall_employee)
2. **存储过程封装**：提供基于大厅编码的数据查询存储过程
3. **大厅编码认证**：大厅服务层通过唯一的hall_code进行身份认证和数据访问
4. **多标识查询支持**：提供基于不同标识类型的查询接口，支持第三方系统的多种标识方式

### 3.3 数据同步与版本控制

为支持云端与大厅本地数据的双向同步：

1. **版本控制表(hall_version)**：记录各大厅各类数据的版本号
2. **同步状态表(sync_status)**：跟踪数据同步状态和时间
3. **触发器机制**：自动更新数据版本号，便于增量同步
4. **同步数据表(sync_data)**：存储待同步的数据记录，支持优先级和重试机制
5. **冲突解决策略**：基于版本号和时间戳的冲突检测与解决机制

#### 3.3.1 数据同步与大厅标识映射的集成

数据同步机制与大厅标识映射紧密集成，实现了基于多种标识的数据同步：

1. **标识转换同步**
   - 同步服务在处理数据时，通过大厅标识映射服务进行标识转换
   - 支持使用任一有效标识（如税务局编码、税务机关ID等）作为同步身份标识
   - 在同步数据表中记录源系统和目标系统的标识信息

2. **多标识数据路由**
   - 基于标识映射关系，将数据准确路由到目标大厅
   - 支持一对多的数据分发，将数据同步到多个使用不同标识的系统
   - 通过唯一标识符(unique_id)防止重复同步

3. **标识映射同步**
   - 将大厅标识映射关系作为基础配置数据进行同步
   - 确保云端和本地大厅服务层保持一致的标识映射关系
   - 标识映射变更时触发相关数据的重新同步

## 4. 实现细节

### 4.1 表结构变更

1. **新增表**
   - hall_info：大厅基本信息表
   - hall_config：大厅配置表
   - sync_status：数据同步状态表
   - hall_business_relation：大厅业务关联表
   - hall_version：大厅数据版本控制表
   - hall_identifier_mapping：大厅标识映射表，存储不同系统的标识对应关系
   - sync_data：同步数据表，存储待同步的数据记录

2. **修改表**
   - business：添加hall_id字段和索引
   - window：添加hall_id字段和索引
   - employee：添加hall_id字段和索引
   - ticket：添加hall_id字段和索引
   - ticket_log：添加hall_id字段和索引

### 4.1.1 同步数据表(sync_data)设计

```sql
CREATE TABLE IF NOT EXISTS testdemo.sync_data (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    hall_id INT(10) NOT NULL COMMENT '大厅ID',
    data_type VARCHAR(50) NOT NULL COMMENT '数据类型，如QUEUE_RECORD、CALL_RECORD等',
    content TEXT NOT NULL COMMENT '数据内容（JSON格式）',
    source VARCHAR(50) COMMENT '数据来源',
    target VARCHAR(50) COMMENT '目标系统',
    version INT(10) DEFAULT 0 COMMENT '版本号',
    status TINYINT(3) DEFAULT 0 COMMENT '同步状态：0-待同步 1-已同步 -1-同步失败',
    retry_count INT(5) DEFAULT 0 COMMENT '重试次数',
    create_time DATETIME,
    update_time DATETIME,
    sync_time DATETIME COMMENT '同步时间',
    unique_id VARCHAR(100) COMMENT '唯一标识符',
    priority INT(3) DEFAULT 5 COMMENT '优先级，数值越小优先级越高',
    INDEX idx_hall_status (hall_id, status),
    INDEX idx_unique_id (unique_id),
    INDEX idx_priority (priority)
) COMMENT '同步数据表';
```

同步数据表是数据同步机制的核心，具有以下特点：

1. **多类型数据支持**：通过data_type字段区分不同类型的数据
2. **JSON格式存储**：使用content字段存储JSON格式的数据内容，支持灵活的数据结构
3. **状态跟踪**：通过status字段跟踪同步状态
4. **重试机制**：通过retry_count字段支持失败重试
5. **优先级控制**：通过priority字段控制同步顺序
6. **唯一性保障**：通过unique_id字段防止重复同步

### 4.2 数据访问机制

1. **视图**
   - v_hall_business：过滤显示特定大厅的业务信息
   - v_hall_window：过滤显示特定大厅的窗口信息
   - v_hall_employee：过滤显示特定大厅的员工信息

2. **存储过程**
   - sp_get_hall_business：根据大厅编码获取业务列表
   - sp_get_hall_windows：根据大厅编码获取窗口列表
   - sp_get_hall_employees：根据大厅编码获取员工列表
   - sp_get_hall_by_identifier：根据外部标识查询大厅信息
   - sp_get_hall_by_multiple_identifiers：根据多个标识类型查询大厅
   - sp_add_hall_identifier：添加大厅标识
   - sp_get_business_by_identifier：根据外部标识查询业务列表

3. **多租户数据隔离实现**
   - **基于hall_id的数据过滤**：所有业务查询都必须包含hall_id条件，确保只返回特定大厅的数据
   - **视图层隔离**：为每类核心业务数据创建基于hall_id过滤的视图，应用层通过视图访问数据
   - **服务层隔离**：在服务层代码中强制添加hall_id条件，防止绕过视图的直接数据访问
   - **动态数据源路由**：根据请求中的大厅标识动态确定数据源，支持跨数据库的隔离

4. **标识转换机制**
   - **标识解析**：请求入口处解析外部标识，转换为内部hall_id
   - **标识缓存**：缓存常用标识映射关系，减少数据库查询
   - **标识验证**：验证标识的有效性和权限，防止越权访问
   - **标识传递**：在微服务调用链中传递统一的hall_id，确保跨服务数据一致性

### 4.3 数据同步触发器

1. **业务数据变更触发器**
   - trg_business_update：业务表数据更新时自动更新版本号
   - trg_window_update：窗口表数据更新时自动更新版本号
   - trg_employee_update：员工表数据更新时自动更新版本号

## 5. 大厅服务层实现

### 5.1 本地数据库设计

大厅服务层使用SQLite作为本地数据库，存储结构与云端保持一致，但简化了部分字段：

```sql
-- 本地大厅信息表
CREATE TABLE hall_info (
    hall_id INTEGER PRIMARY KEY,
    hall_name TEXT NOT NULL,
    hall_code TEXT NOT NULL,
    region_code TEXT NOT NULL,
    status INTEGER DEFAULT 1,
    last_sync_time TEXT
);

-- 本地业务表
CREATE TABLE business (
    UID INTEGER PRIMARY KEY,
    hall_id INTEGER NOT NULL,
    PREFIX TEXT,
    NAME TEXT,
    ENABLED INTEGER,
    TYPE INTEGER,
    HANDLE_COUNT INTEGER,
    IS_SPECIAL INTEGER DEFAULT 0,
    local_version INTEGER DEFAULT 1
);

-- 本地数据版本表
CREATE TABLE data_version (
    table_name TEXT PRIMARY KEY,
    version INTEGER DEFAULT 1,
    update_time TEXT
);

-- 本地大厅标识映射表
CREATE TABLE hall_identifier_mapping (
    id INTEGER PRIMARY KEY,
    hall_id INTEGER NOT NULL,
    identifier_type TEXT NOT NULL,
    identifier_value TEXT NOT NULL,
    description TEXT,
    is_primary INTEGER DEFAULT 0
);
```

### 5.2 数据同步流程

1. **启动同步**
   - 大厅服务启动时，使用本地存储的hall_code或其他标识符向云端请求认证
   - 获取本厅最新配置数据和版本号

2. **增量同步**
   - 定期比对本地与云端版本号
   - 只同步版本号变更的数据

3. **离线处理**
   - 离线期间使用本地数据库提供服务
   - 记录本地数据变更
   - 网络恢复后执行双向同步

### 5.3 业务查询实现

取号端进行业务查询时，大厅服务层按以下流程处理：

1. 优先查询本地SQLite数据库中的业务配置
2. 如网络正常，同时向云端发起查询请求（使用hall_code或其他可用标识作为身份标识）
3. 比对本地与云端数据，如有差异则更新本地数据
4. 返回最新的业务配置信息给取号端

### 5.4 多标识支持实现

为支持第三方接口提供的多种大厅标识信息，大厅服务层实现以下功能：

1. **标识转换**：将第三方接口提供的标识（如税务局大厅编号、税务机关ID等）转换为内部hall_id
2. **多标识查询**：支持使用任一有效标识查询大厅信息和业务配置
3. **标识优先级**：通过is_primary字段标记主要标识，在多个标识冲突时优先使用
4. **标识同步**：将云端的标识映射关系同步到本地数据库

## 6. 安全性考虑

### 6.1 数据访问安全

1. **数据隔离**：通过hall_id严格隔离不同大厅的数据，确保每个大厅只能访问自己的数据
2. **身份认证**：大厅服务层必须提供有效的hall_code或其他标识才能访问数据
3. **最小权限**：使用视图和存储过程限制数据访问范围，按照最小权限原则分配权限
4. **数据加密**：敏感配置信息在传输和存储时进行加密，保护数据安全
5. **标识验证**：对外部标识进行有效性验证，防止非法访问和身份伪造

### 6.2 传输安全

1. **SSL/TLS加密**：所有数据传输采用SSL/TLS加密，防止数据被窃听
2. **消息签名**：关键数据传输添加数字签名，确保数据完整性和来源可验证
3. **安全通道**：大厅与云端之间建立VPN或专用网络通道，增强传输安全性
4. **传输压缩**：大批量数据传输前进行压缩，减少传输时间和风险暴露

### 6.3 数据存储安全

1. **敏感字段加密**：对用户姓名、电话等敏感信息进行加密存储
2. **数据脱敏**：在非必要场景下对敏感数据进行脱敏处理
3. **备份加密**：数据备份文件采用加密存储，防止备份数据泄露
4. **访问审计**：记录所有数据访问操作，便于安全审计和问题追溯

## 7. 性能优化

### 7.1 索引优化

1. **复合索引设计**：为hall_id和其他常用查询字段创建复合索引，如(hall_id, status)、(hall_id, create_time)
2. **标识索引优化**：为hall_identifier_mapping表创建(identifier_type, identifier_value)复合索引，加速标识查询
3. **索引覆盖**：设计索引时考虑索引覆盖，减少回表操作
4. **索引维护**：定期分析索引使用情况，优化低效索引

### 7.2 查询优化

1. **视图预计算**：使用视图预先计算常用查询结果
2. **存储过程封装**：将复杂查询封装为存储过程，减少网络传输和提高执行效率
3. **分页查询优化**：大数据量查询采用分页机制，避免一次性返回过多数据
4. **延迟加载**：非核心数据采用延迟加载策略，减轻系统负担

### 7.3 数据同步优化

1. **增量同步**：基于版本号和时间戳实现增量同步，减少数据传输量
2. **批量同步**：数据同步采用批量方式，减少网络请求次数
3. **压缩传输**：同步数据进行压缩传输，降低网络带宽占用
4. **优先级控制**：通过priority字段控制同步顺序，确保关键数据优先同步
5. **错峰同步**：非关键数据在系统负载低时进行同步，避免高峰期同步影响业务

### 7.4 缓存策略

1. **多级缓存**：实现应用层和数据库层的多级缓存机制
2. **热点数据缓存**：识别并缓存频繁访问的热点数据
3. **缓存一致性**：设计缓存更新机制，确保缓存与数据库的一致性
4. **本地缓存**：大厅服务层维护本地缓存，减少对云端的访问频率

## 8. 部署与迁移

### 8.1 数据迁移策略

1. **分批迁移**：按照大厅为单位分批迁移数据，降低风险
2. **数据清洗**：迁移前对数据进行清洗和规范化处理
3. **数据验证**：迁移后进行数据完整性和一致性验证
4. **回滚机制**：设计迁移失败的回滚方案，确保业务连续性
5. **并行运行**：新旧系统并行运行一段时间，确保平稳过渡

### 8.2 大厅接入流程

1. **大厅注册**：新大厅在云端管理平台注册并获取唯一hall_code
2. **标识映射**：建立大厅与第三方系统标识的映射关系
3. **初始配置**：云端管理员为大厅配置初始业务和窗口信息
4. **本地部署**：在大厅部署本地服务和SQLite数据库
5. **连接测试**：测试大厅服务与云端的连接和数据同步
6. **业务验证**：验证取号、叫号等核心业务功能

### 8.3 配置管理

1. **配置模板**：提供标准配置模板，简化新大厅配置过程
2. **批量配置**：支持批量导入和修改配置信息
3. **配置审核**：关键配置变更需经过审核流程
4. **版本控制**：支持配置的版本管理和历史查询
5. **配置备份**：定期备份配置信息，支持配置恢复

### 8.4 系统监控与运维

1. **健康监控**：监控各大厅服务的运行状态和健康指标
2. **同步监控**：监控数据同步状态和异常情况
3. **性能监控**：监控系统性能指标，及时发现性能瓶颈
4. **告警机制**：设置多级告警机制，及时响应异常情况
5. **运维工具**：提供运维工具，简化日常运维工作

## 9. 总结

通过在云端数据库中引入大厅标识体系和标识映射机制，结合视图、存储过程和触发器机制，可以有效确保大厅服务层在为取号端提供接口时，准确获取云端数据库中属于该大厅的业务配置信息。同时，通过本地SQLite数据库的缓存和同步机制，保障了系统在网络不稳定情况下的业务连续性。多标识支持机制使系统能够灵活适应不同第三方接口提供的各种标识方式，提高了系统的兼容性和扩展性。