@echo off
set JAVA_HOME=%~dp0jdk
if not exist "%JAVA_HOME%" (
    echo Error: JAVA_HOME directory not found at %JAVA_HOME%
    pause
    exit /b 1
)
if not exist "%JAVA_HOME%\bin\java.exe" (
    echo Error: java.exe not found in %JAVA_HOME%\bin
    pause
    exit /b 1
)
if not exist "%~dp0queue-api-1.0.0.jar" (
    echo Error: JAR file not found at %~dp0queue-api-1.0.0.jar
    pause
    exit /b 1
)
echo Starting queue-api-1.0.0.jar using JDK from %JAVA_HOME%
"%JAVA_HOME%\bin\java.exe" -jar "%~dp0queue-api-1.0.0.jar"