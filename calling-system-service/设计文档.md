# 呼号系统服务设计文档

## 1. 服务概述

呼号系统服务(calling-system-service)是排队系统的核心组件之一，负责处理窗口呼叫业务、管理LED显示和语音播报，并通过WebSocket实现实时通信。

## 2. 功能模块

### 2.1 窗口呼叫管理

- **呼叫下一位**：窗口工作人员通过界面操作呼叫下一位等待客户
- **呼叫指定号码**：支持呼叫指定票号的客户
- **重新呼叫**：对未到的客户进行重新呼叫
- **完成服务**：标记当前客户服务完成

### 2.2 LED显示控制

- **显示内容管理**：控制LED屏幕显示的内容格式和样式
- **多屏协同**：支持多个LED屏幕的协同工作
- **滚动显示**：支持信息滚动显示模式
- **状态监控**：监控LED设备的连接状态

### 2.3 语音播报服务

- **语音合成**：将呼叫信息转换为语音
- **多语言支持**：支持中英文等多语言播报
- **音量控制**：根据环境噪音自动调整音量
- **播报队列**：管理语音播报的优先级和队列

### 2.4 WebSocket通信

- **实时消息推送**：向客户端推送实时呼叫信息
- **连接管理**：管理和维护WebSocket连接
- **心跳检测**：确保连接的可靠性
- **消息格式化**：标准化消息格式

## 3. 技术实现

### 3.1 WebSocket服务实现

```java
@Component
@ServerEndpoint("/ws/calling")
public class CallingWebSocketServer {
    
    private static final Map<String, Session> clients = new ConcurrentHashMap<>();
    
    @OnOpen
    public void onOpen(Session session) {
        String clientId = session.getId();
        clients.put(clientId, session);
        log.info("新的WebSocket连接建立: {}", clientId);
    }
    
    @OnClose
    public void onClose(Session session) {
        String clientId = session.getId();
        clients.remove(clientId);
        log.info("WebSocket连接关闭: {}", clientId);
    }
    
    @OnMessage
    public void onMessage(String message, Session session) {
        CallingMessage callingMessage = JsonUtil.parseObject(message, CallingMessage.class);
        processMessage(callingMessage, session);
    }
    
    @OnError
    public void onError(Session session, Throwable error) {
        log.error("WebSocket错误: {}", error.getMessage());
    }
    
    private void processMessage(CallingMessage message, Session session) {
        // 根据消息类型处理不同的业务逻辑
        switch (message.getType()) {
            case "CALL":
                handleCallMessage(message);
                break;
            case "COMPLETE":
                handleCompleteMessage(message);
                break;
            case "RECALL":
                handleRecallMessage(message);
                break;
            default:
                log.warn("未知的消息类型: {}", message.getType());
        }
    }
    
    // 广播消息到所有连接的客户端
    public static void broadcastMessage(Object message) {
        String jsonMessage = JsonUtil.toJsonString(message);
        for (Session session : clients.values()) {
            try {
                session.getBasicRemote().sendText(jsonMessage);
            } catch (IOException e) {
                log.error("发送消息失败: {}", e.getMessage());
            }
        }
    }
}
```

### 3.2 LED控制服务

```java
@Service
public class LedControlService {
    
    @Autowired
    private LedDeviceRepository ledDeviceRepository;
    
    // 发送呼叫信息到LED显示屏
    public void sendCallInfoToLed(String windowNo, String ticketNo, String businessType) {
        List<LedDevice> devices = ledDeviceRepository.findAllByEnabled(true);
        
        for (LedDevice device : devices) {
            try {
                // 构建LED显示内容
                String displayContent = String.format("%s号窗口请%s号客户", windowNo, ticketNo);
                
                // 发送到LED设备
                sendToLedDevice(device.getIpAddress(), device.getPort(), displayContent);
                
                log.info("成功发送到LED设备: {}", device.getIpAddress());
            } catch (Exception e) {
                log.error("发送到LED设备失败: {}, 错误: {}", device.getIpAddress(), e.getMessage());
            }
        }
    }
    
    private void sendToLedDevice(String ipAddress, int port, String content) {
        // 实现具体的LED通信协议
        // 这里可以使用Socket通信或者HTTP请求等方式
    }
}
```

### 3.3 语音播报服务

```java
@Service
public class VoiceBroadcastService {
    
    private final BlockingQueue<VoiceMessage> voiceQueue = new LinkedBlockingQueue<>();
    
    @PostConstruct
    public void init() {
        // 启动语音播报线程
        new Thread(this::processVoiceQueue).start();
    }
    
    // 添加语音播报任务
    public void addVoiceTask(String windowNo, String ticketNo, String businessType) {
        VoiceMessage message = new VoiceMessage(windowNo, ticketNo, businessType);
        voiceQueue.offer(message);
    }
    
    // 处理语音播报队列
    private void processVoiceQueue() {
        while (true) {
            try {
                VoiceMessage message = voiceQueue.take();
                playVoice(message);
                // 播报间隔
                Thread.sleep(3000);
            } catch (InterruptedException e) {
                log.error("语音播报线程中断: {}", e.getMessage());
                Thread.currentThread().interrupt();
                break;
            }
        }
    }
    
    private void playVoice(VoiceMessage message) {
        // 构建语音内容
        String voiceText = String.format("%s号窗口请%s号客户", 
                message.getWindowNo(), message.getTicketNo());
        
        // 调用TTS引擎进行语音合成和播放
        // 这里可以集成第三方TTS服务或本地TTS引擎
    }
}
```

## 4. 数据模型

### 4.1 呼叫消息模型

```java
public class CallingMessage {
    private String type;          // 消息类型：CALL, COMPLETE, RECALL
    private String queueId;       // 队列ID
    private String windowNo;      // 窗口号
    private String ticketNo;      // 票号
    private String businessType;  // 业务类型
    private String userName;      // 客户姓名（可选）
    private Map<String, Object> extraData; // 扩展数据
    
    // getter和setter方法
}
```

### 4.2 LED设备模型

```java
@TableName("led_device")
public class LedDevice {
    @TableId(type = IdType.AUTO)
    private Long id;
    
    private String name;        // 设备名称
    private String ipAddress;   // IP地址
    private int port;           // 端口
    private String location;    // 安装位置
    private boolean enabled;    // 是否启用
    private String protocol;    // 通信协议
    
    // getter和setter方法
}
```

## 5. 离线操作支持

### 5.1 本地缓存机制

- 使用本地SQLite数据库缓存呼叫记录
- 实现呼叫记录的本地持久化
- 网络恢复后自动同步到云端

### 5.2 断网恢复处理

- 检测网络状态变化
- 网络恢复后重新建立WebSocket连接
- 同步离线期间的呼叫记录

## 6. 性能优化

### 6.1 WebSocket连接优化

- 实现连接池管理
- 添加心跳检测机制
- 实现断线重连策略

### 6.2 消息处理优化

- 使用消息队列削峰
- 实现消息批量处理
- 优化消息序列化/反序列化

## 7. 安全措施

### 7.1 通信安全

- 使用WSS安全WebSocket
- 实现消息签名验证
- 添加访问控制和认证

### 7.2 数据安全

- 敏感数据加密存储
- 定期数据备份
- 日志脱敏处理

## 8. 部署要求

- JDK 11+
- 2GB+ 内存
- 支持WebSocket的Web容器
- 网络带宽要求：≥2Mbps
- 支持音频输出的设备