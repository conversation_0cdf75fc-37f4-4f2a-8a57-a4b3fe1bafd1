# 科大讯飞TTS集成说明

本文档说明了如何在呼号系统中集成科大讯飞TTS引擎，实现高质量的中文语音播报功能。

## 准备工作

1. 获取科大讯飞离线TTS SDK
   - 注册科大讯飞开发者账号：https://www.xfyun.cn/
   - 创建应用并获取AppID
   - 下载离线TTS SDK（Java版）

2. 将SDK文件放入项目
   - 将`Msc.jar`放入`libs`目录
   - 将相关的本地库文件（`.dll`或`.so`）放入`libs`目录

## 配置说明

在`application.properties`中配置以下参数：

```properties
# 科大讯飞TTS配置
iflytek.appid=your_app_id_here
iflytek.tts.voice=xiaoyan
iflytek.tts.speed=50
iflytek.tts.volume=80
iflytek.tts.pitch=50
iflytek.tts.engine.type=local
iflytek.tts.audio.dir=./audio
```

参数说明：
- `iflytek.appid`: 科大讯飞应用的AppID
- `iflytek.tts.voice`: 发音人，常用的有：
  - `xiaoyan`: 青年女声（默认）
  - `aisjiuxu`: 青年男声
  - `aisxping`: 青年女声
  - `aisjinger`: 青年女声
- `iflytek.tts.speed`: 语速，范围：0-100，默认50
- `iflytek.tts.volume`: 音量，范围：0-100，默认50
- `iflytek.tts.pitch`: 音调，范围：0-100，默认50
- `iflytek.tts.engine.type`: 引擎类型，`local`表示离线引擎
- `iflytek.tts.audio.dir`: 音频文件保存目录

## 使用方法

在需要播放语音的地方，调用`VoiceBroadcastService`的`addVoiceTask`方法：

```java
@Autowired
private VoiceBroadcastService voiceBroadcastService;

// 添加语音播报任务
voiceBroadcastService.addVoiceTask("1", "A001", "普通业务");
```

## 测试接口

系统提供了一个简单的测试接口，可以用来测试TTS功能：

```
GET /api/tts/test?windowNo=1&ticketNo=A001
```

## 常见问题

1. **找不到本地库文件**
   - 确保本地库文件（`.dll`或`.so`）已正确放入`libs`目录
   - 确保`pom.xml`中的资源复制配置正确

2. **初始化失败**
   - 检查AppID是否正确
   - 检查网络连接（首次使用可能需要联网验证）

3. **没有声音输出**
   - 检查系统音量设置
   - 检查音频设备是否正常工作
   - 查看日志中是否有错误信息

## 注意事项

1. 科大讯飞离线SDK首次使用时可能需要联网验证AppID
2. 离线SDK支持的发音人数量可能少于在线版本
3. 确保系统有足够的权限访问音频设备
