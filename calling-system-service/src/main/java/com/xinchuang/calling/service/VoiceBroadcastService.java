package com.xinchuang.calling.service;

import com.xinchuang.calling.model.VoiceMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

<<<<<<< HEAD
import jakarta.annotation.PostConstruct;
import javax.sound.sampled.*;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.LinkedBlockingQueue;

/**
 * 语音播报服务
 * 模拟科大讯飞TTS功能，实际上只是打印日志
 * 在真实环境中，需要替换为实际的TTS实现
 */
=======
import javax.annotation.PostConstruct;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.LinkedBlockingQueue;

import javax.sound.sampled.*;

// TODO: 引入离线 TTS 引擎的相关依赖
import marytts.LocalMaryInterface;
import marytts.MaryInterface;
import marytts.exceptions.MaryConfigurationException;
import marytts.util.data.audio.MaryAudioUtils;
import javax.sound.sampled.AudioInputStream;

// TODO: 根据实际MaryTTS配置和需求调整以下导入

>>>>>>> a54530489a7ca219a767fe0555caa23fd417b54e
@Slf4j
@Service
public class VoiceBroadcastService {

<<<<<<< HEAD
    @Value("${tts.audio.dir:./audio}")
    private String audioDir;

=======
    // TODO: 在此处配置和初始化离线 TTS 引擎客户端
    private MaryInterface marytts;

    @PostConstruct
    public void setupOfflineTtsClient() {
        try {
            marytts = new LocalMaryInterface();
            // 根据实际可用的中文语音包进行设置，例如 "bits1-hsmm" 或其他
            // 如果不确定，可以先不设置，MaryTTS会使用默认语音
            marytts.setVoice("bits3-hsmm"); // 设置为中文语音包
            log.info("MaryTTS 初始化成功，并设置语音为 bits3-hsmm。");
        } catch (MaryConfigurationException e) {
            log.error("初始化 MaryTTS 失败: {}", e.getMessage(), e);
        }
    }


    
>>>>>>> a54530489a7ca219a767fe0555caa23fd417b54e
    private final BlockingQueue<VoiceMessage> voiceQueue = new LinkedBlockingQueue<>();

    @PostConstruct
    public void init() {
        try {
            // 确保音频目录存在
            File dir = new File(audioDir);
            if (!dir.exists()) {
                dir.mkdirs();
            }

            log.info("语音播报服务初始化完成，使用模拟TTS引擎");
            log.warn("注意：当前使用的是模拟TTS引擎，不会实际播放声音，仅打印日志");
            log.warn("在实际环境中，请替换为科大讯飞SDK或其他TTS引擎");

            // 启动语音播报线程
            new Thread(this::processVoiceQueue).start();
        } catch (Exception e) {
            log.error("初始化TTS引擎失败: {}", e.getMessage(), e);
        }
    }

    // 添加语音播报任务
    public void addVoiceTask(String windowNo, String ticketNo, String businessType) {
        VoiceMessage newMessage = new VoiceMessage(windowNo, ticketNo, businessType);
        // 检查队列中是否已存在相同的任务
        if (voiceQueue.contains(newMessage)) {
            log.info("语音任务已存在，不再重复添加: 窗口{}, 票号{}", windowNo, ticketNo);
        } else {
            boolean offered = voiceQueue.offer(newMessage);
            if (offered) {
                log.info("成功添加语音播报任务: 窗口{}, 票号{}", windowNo, ticketNo);
            } else {
                log.warn("无法添加语音播报任务到队列 (队列可能已满): 窗口{}, 票号{}", windowNo, ticketNo);
            }
        }
    }

    // 处理语音播报队列
    private void processVoiceQueue() {
        while (true) {
            try {
                VoiceMessage message = voiceQueue.take();
                playVoice(message);
                // 播报间隔
                Thread.sleep(3000);
            } catch (InterruptedException e) {
                log.error("语音播报线程中断: {}", e.getMessage());
                Thread.currentThread().interrupt();
                break;
            }
        }
    }
<<<<<<< HEAD
=======
    

>>>>>>> a54530489a7ca219a767fe0555caa23fd417b54e

    private void playVoice(VoiceMessage message) {
        // 构建语音内容
        String voiceText = String.format("%s号窗口请%s号客户",
                message.getWindowNo(), message.getTicketNo());

        log.info("准备使用离线 TTS 合成并播放语音: {}", voiceText);

        try {
<<<<<<< HEAD
            // 构建语音内容
            String voiceText = String.format("%s号窗口请%s号客户",
                    message.getWindowNo(), message.getTicketNo());

            // 模拟TTS合成和播放
            log.info("模拟播放语音: {}", voiceText);

            // 在实际环境中，这里应该调用科大讯飞SDK进行语音合成和播放
            // 以下代码仅用于演示，实际上不会播放声音
            playDummySound();

        } catch (Exception e) {
            log.error("语音播放失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 播放一个极短的无声音频，仅用于演示
     */
    private void playDummySound() {
        try {
            // 创建一个极短的无声PCM数据
            byte[] silentPcm = new byte[1024];

            // PCM音频格式 - 16位深度，16kHz采样率，单声道
            AudioFormat audioFormat = new AudioFormat(
                    AudioFormat.Encoding.PCM_SIGNED, // 编码方式
                    16000f,                         // 采样率
                    16,                             // 采样位数
                    1,                              // 声道数
                    2,                              // 每个帧的字节数 (采样位数 * 声道数 / 8)
                    16000f,                         // 每秒帧数
                    false                           // 字节序(是否大端序)
            );

            // 获取音频输入流
            AudioInputStream audioInputStream = new AudioInputStream(
                    new ByteArrayInputStream(silentPcm),
                    audioFormat,
                    silentPcm.length / audioFormat.getFrameSize()
            );

            // 获取数据行信息
            DataLine.Info dataLineInfo = new DataLine.Info(SourceDataLine.class, audioFormat);

            // 检查是否支持该格式
            if (!AudioSystem.isLineSupported(dataLineInfo)) {
                log.warn("当前系统不支持指定的音频格式，跳过播放");
                return;
            }

            // 打开并启动数据行
            try (SourceDataLine sourceDataLine = (SourceDataLine) AudioSystem.getLine(dataLineInfo)) {
                sourceDataLine.open(audioFormat);
                sourceDataLine.start();

                // 读取并播放数据
                byte[] buffer = new byte[1024];
                int bytesRead;
                while ((bytesRead = audioInputStream.read(buffer, 0, buffer.length)) != -1) {
                    sourceDataLine.write(buffer, 0, bytesRead);
                }

                // 等待播放完成
                sourceDataLine.drain();
            }
        } catch (Exception e) {
            log.warn("播放模拟音频失败: {}", e.getMessage());
=======
            if (marytts == null) {
                log.error("MaryTTS 客户端未初始化，无法合成语音。");
                return;
            }

            try (AudioInputStream audio = marytts.generateAudio(voiceText)) {
                byte[] audioData = MaryAudioUtils.getBytes(audio);
                playAudio(audioData);
                log.info("MaryTTS 语音播放成功: {}", voiceText);
            } catch (Exception synthesisException) {
                log.error("MaryTTS 语音合成失败: {}", synthesisException.getMessage(), synthesisException);
            } 


        } catch (InterruptedException e) {
            log.error("语音播放线程被中断: {}", e.getMessage(), e);
            Thread.currentThread().interrupt(); // 重新设置中断状态
        } catch (Exception e) {
            log.error("语音合成或播放时发生错误: {}", e.getMessage(), e);
        }
    }

    // 播放音频数据
    private void playAudio(byte[] audioData) throws LineUnavailableException, IOException, UnsupportedAudioFileException {
        if (audioData == null || audioData.length == 0) {
            log.warn("音频数据为空，无法播放");
            return;
        }

        try (InputStream inputStream = new ByteArrayInputStream(audioData);
             AudioInputStream audioStream = AudioSystem.getAudioInputStream(inputStream)) {

            AudioFormat format = audioStream.getFormat();
            DataLine.Info info = new DataLine.Info(SourceDataLine.class, format);

            if (!AudioSystem.isLineSupported(info)) {
                log.error("不支持的音频格式: {}", format);
                // 尝试转换格式，如果需要
                // AudioFormat targetFormat = ...;
                // audioStream = AudioSystem.getAudioInputStream(targetFormat, audioStream);
                // format = audioStream.getFormat();
                // info = new DataLine.Info(SourceDataLine.class, format);
                // if (!AudioSystem.isLineSupported(info)) {
                //     throw new LineUnavailableException("无法找到支持的音频输出线路");
                // }
                 throw new LineUnavailableException("不支持的音频输出线路 for format: " + format);
            }

            SourceDataLine line = (SourceDataLine) AudioSystem.getLine(info);
            line.open(format);
            line.start();

            log.info("开始播放音频...");
            byte[] buffer = new byte[4096];
            int bytesRead;
            while ((bytesRead = audioStream.read(buffer, 0, buffer.length)) != -1) {
                line.write(buffer, 0, bytesRead);
            }

            line.drain();
            line.stop();
            line.close();
            log.info("音频播放完毕");
>>>>>>> a54530489a7ca219a767fe0555caa23fd417b54e
        }
    }
}