<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.xinchuang</groupId>
    <artifactId>calling-system-service</artifactId>
    <version>1.0.0</version>

    <parent>
        <groupId>com.xinchuang</groupId>
        <artifactId>xinchuang-service</artifactId>
        <version>1.0.0</version>
    </parent>

    <repositories>
        <repository>
            <id>central</id>
            <name>Maven Central Repository</name>
            <url>https://repo.maven.apache.org/maven2</url>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </repository>
        <repository>
            <id>terrestris</id>
            <name>Terrestris Repository</name>
            <url>https://nexus.terrestris.de/repository/public/</url>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </repository>
    </repositories>

    <dependencies>
        <!-- Spring Boot Web (to ensure Tomcat and Jakarta WebSocket API are present) -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>

        <!-- Spring Boot WebSocket Starter -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-websocket</artifactId>
        </dependency>

<!--        &lt;!&ndash; MaryTTS &ndash;&gt;-->
<!--        <dependency>-->
<!--            <groupId>de.dfki.mary</groupId>-->
<!--            <artifactId>marytts-server</artifactId>-->
<!--            <version>5.2.1</version>-->
<!--        </dependency>-->
        <dependency>
            <groupId>de.dfki.mary</groupId>
            <artifactId>marytts-lang-en</artifactId>
            <version>5.2.1</version>
        </dependency>
        <dependency>
            <groupId>de.dfki.mary</groupId>
            <artifactId>voice-cmu-slt-hsmm</artifactId>
            <version>5.2.1</version>
        </dependency>

        <!-- MyBatis Plus -->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
        </dependency>

        <!-- SQLite -->
        <dependency>
            <groupId>org.xerial</groupId>
            <artifactId>sqlite-jdbc</artifactId>
            <version>${sqlite-jdbc.version}</version>
        </dependency>

        <!-- Lombok -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <optional>true</optional>
        </dependency>

        <!-- Test -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <!-- 科大讯飞TTS SDK 本地依赖 - 暂时注释掉，等有了SDK再启用
        <dependency>
            <groupId>com.iflytek</groupId>
            <artifactId>msc</artifactId>
            <version>1.0</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/libs/Msc.jar</systemPath>
        </dependency>
        -->
        <!-- MaryTTS Dependencies -->
<!--        <dependency>-->
<!--            <groupId>de.dfki.mary</groupId>-->
<!--            <artifactId>marytts-runtime</artifactId>-->
<!--            <version>5.2</version> &lt;!&ndash; 请确认使用合适的版本 &ndash;&gt;-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>de.dfki.mary</groupId>-->
<!--            <artifactId>marytts-lang-zh</artifactId>-->
<!--            <version>5.2</version> &lt;!&ndash; 请确认使用合适的版本 &ndash;&gt;-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>de.dfki.mary</groupId>-->
<!--            <artifactId>voice-bits3-hsmm</artifactId> &lt;!&ndash; 中文语音包 &ndash;&gt;-->
<!--            <version>5.2</version>-->
<!--        </dependency>-->
        <dependency>
            <groupId>javax.annotation</groupId>
            <artifactId>javax.annotation-api</artifactId>
            <version>1.3.2</version>
        </dependency>
    </dependencies>



    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <excludes>
                        <exclude>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                        </exclude>
                    </excludes>
                    <!-- 包含系统范围依赖 - 暂时注释掉，等有了SDK再启用
                    <includeSystemScope>true</includeSystemScope>
                    -->
                </configuration>
            </plugin>
        </plugins>

        <!-- 复制本地库文件到输出目录 - 暂时注释掉，等有了SDK再启用
        <resources>
            <resource>
                <directory>${project.basedir}/libs</directory>
                <targetPath>BOOT-INF/lib/</targetPath>
                <includes>
                    <include>**/*.dll</include>
                    <include>**/*.so</include>
                </includes>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
            </resource>
        </resources>
        -->

        <!-- 只包含资源文件 -->
        <resources>
            <resource>
                <directory>src/main/resources</directory>
            </resource>
        </resources>
    </build>
</project>