# OceanBase 数据库适配说明

## 概述

本项目已完成对 OceanBase 数据库的适配工作，包括数据库表结构优化、索引优化、多租户支持和特定功能实现。OceanBase 数据库以 MySQL 兼容模式运行，大部分 MySQL 语法和功能都可以直接使用。

## 适配内容

### 1. 数据库表结构适配

- 创建和优化了大厅信息表、大厅标识映射表等核心表结构
- 为业务表、窗口表、员工表等添加了大厅标识字段和索引
- 创建了设备与大厅映射表、数据同步状态表等支持表
- 详细内容请参考 `oceanbase_adaptation.sql` 文件

### 2. OceanBase 特定优化

- 添加了分区表支持，对票号表按日期进行分区
- 添加了全局索引，提高跨分区查询性能
- 设置了表压缩，节省存储空间
- 创建了表组，优化存储和查询
- 设置了并行度和块大小，提高查询性能
- 添加了统计信息收集，为优化器提供更准确的统计信息

### 3. 多租户支持

- 基于大厅 ID 实现了多租户数据隔离
- 通过 MyBatis-Plus 的多租户插件自动为 SQL 添加租户条件
- 配置了不需要进行多租户隔离的表

### 4. 视图和存储过程

- 创建了查询视图，方便大厅服务查询本厅业务、窗口和员工
- 创建了根据外部标识查询大厅 ID 的存储过程
- 创建了根据设备标识查询大厅 ID 的存储过程
- 创建了数据同步触发器，记录数据变更

## 使用方法

### 1. 数据源配置

在应用的配置文件中添加 OceanBase 数据源配置：

```properties
# 数据库连接URL
spring.datasource.url=jdbc:oceanbase://[HOST]:[PORT]/[DATABASE]?useUnicode=true&characterEncoding=utf8&serverTimezone=Asia/Shanghai

# 数据库用户名和密码
spring.datasource.username=root
spring.datasource.password=password

# 数据库驱动类名
spring.datasource.driver-class-name=com.oceanbase.jdbc.Driver

# OceanBase特定配置
spring.datasource.hikari.data-source-properties.ob_compatibility_mode=mysql
```

详细配置示例请参考 `oceanbase-datasource-example.properties` 文件。

### 2. 多租户配置

项目已在 `MyBatisPlusConfig` 类中添加了多租户支持，基于大厅 ID 实现数据隔离。多租户插件会自动为 SQL 添加租户条件，无需手动添加大厅 ID 过滤条件。

### 3. 执行数据库脚本

在 OceanBase 数据库中执行 `oceanbase_adaptation.sql` 脚本，创建和优化表结构。

## 注意事项

1. OceanBase 数据库以 MySQL 兼容模式运行，但部分高级特性可能有差异，如遇到问题请参考 OceanBase 官方文档。
2. 多租户功能依赖于 `UserSessionManager` 类，确保在请求处理前正确设置当前大厅 ID。
3. 部分表（如 `hall_info`、`hall_identifier_mapping` 等）被配置为不进行多租户隔离，这些表的查询不会自动添加租户条件。
4. 项目已添加 OceanBase JDBC 驱动依赖，版本为 2.4.3，如需升级请修改 `pom.xml` 文件中的 `oceanbase.version` 属性。

## 参考文档

- [OceanBase 官方文档](https://www.oceanbase.com/docs)
- [OceanBase JDBC 驱动文档](https://www.oceanbase.com/docs/connector-j)
- [MyBatis-Plus 多租户插件文档](https://baomidou.com/pages/aef2f2/)