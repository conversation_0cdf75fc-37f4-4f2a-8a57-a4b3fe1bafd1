package com.xinchuang.sync.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 大厅数据版本控制实体类
 * 用于记录各大厅各类数据的版本号
 * 对应数据库中的hall_version表
 */
@Data
@NoArgsConstructor
@TableName("hall_version")
public class HallVersion {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;
    
    /**
     * 大厅ID
     */
    @TableField("hall_id")
    private Integer hallId;
    
    /**
     * 表名
     * 记录哪个表的数据版本
     */
    @TableField("table_name")
    private String tableName;
    
    /**
     * 数据版本号
     * 每次数据更新时递增
     */
    @TableField("version")
    private Integer version;
    
    /**
     * 更新时间
     */
    @TableField("update_time")
    private LocalDateTime updateTime;
}