package com.xinchuang.sync.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 窗口实体类
 * 对应数据库中的window表
 */
@Data
@NoArgsConstructor
@TableName("window")
public class Window {
    /**
     * 主键ID
     */
    @TableId(value = "UID", type = IdType.AUTO)
    private Integer uid;
    
    /**
     * 所属大厅ID
     */
    @TableField("hall_id")
    private Integer hallId;

    /**
     * 窗口名称
     */
    @TableField("NAME")
    private String name;

    /**
     * LED地址
     */
    @TableField("LED_ADDRESS")
    private String ledAddress;

    /**
     * LED文本
     */
    @TableField("LED_TEXT")
    private String ledText;

    /**
     * 是否启用
     * 1: 启用, 0: 禁用
     */
    @TableField("ENABLED")
    private Integer enabled;

    /**
     * SID
     */
    @TableField("SID")
    private String sid;

    /**
     * 排队模式
     */
    @TableField("RANK_MODE")
    private String rankMode;

    /**
     * 排队地址
     */
    @TableField("RANK_ADDRESS")
    private String rankAddress;
}