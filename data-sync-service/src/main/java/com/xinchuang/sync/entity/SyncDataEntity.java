package com.xinchuang.sync.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 同步数据实体类
 * 用于存储需要同步的数据记录
 * 对应数据库中的sync_data表
 */
@Data
@NoArgsConstructor
@TableName("sync_data")
public class SyncDataEntity {
    
    @TableId(type = IdType.AUTO)
    private Long id;
    
    /**
     * 大厅ID
     * 标识数据所属的大厅
     */
    @TableField("hall_id")
    private Integer hallId;
    
    /**
     * 数据类型
     * 如：QUEUE_RECORD(排队记录)、CALL_RECORD(呼叫记录)等
     */
    @TableField("data_type")
    private String dataType;
    
    /**
     * 数据内容（JSON格式）
     */
    @TableField("content")
    private String content;
    
    /**
     * 数据来源
     * 标识数据的来源系统
     */
    @TableField("source")
    private String source;
    
    /**
     * 目标系统
     */
    @TableField("target")
    private String target;
    
    /**
     * 版本号
     * 用于解决数据冲突，每次更新时递增
     */
    @TableField("version")
    private Integer version = 0;
    
    /**
     * 同步状态
     * 0: 待同步
     * 1: 已同步
     * -1: 同步失败
     */
    @TableField("status")
    private Integer status = 0;
    
    /**
     * 重试次数
     */
    @TableField("retry_count")
    private Integer retryCount = 0;
    
    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    @TableField("update_time")
    private LocalDateTime updateTime;
    
    /**
     * 同步时间
     */
    @TableField("sync_time")
    private LocalDateTime syncTime;
    
    /**
     * 唯一标识符
     * 用于防止重复同步
     */
    @TableField("unique_id")
    private String uniqueId;
    
    /**
     * 优先级
     * 数值越小优先级越高
     */
    @TableField("priority")
    private Integer priority = 5;
}