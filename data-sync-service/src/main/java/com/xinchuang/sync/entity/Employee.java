package com.xinchuang.sync.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 员工实体类
 * 对应数据库中的employee表
 */
@Data
@NoArgsConstructor
@TableName("employee")
public class Employee {
    /**
     * 主键ID
     */
    @TableId(value = "UID", type = IdType.AUTO)
    private Integer uid;
    
    /**
     * 所属大厅ID
     */
    @TableField("hall_id")
    private Integer hallId;

    /**
     * 员工姓名
     */
    @TableField("NAME")
    private String name;

    /**
     * 员工ID
     */
    @TableField("ID")
    private String id;

    /**
     * 用户名
     */
    @TableField("USERNAME")
    private String username;

    /**
     * 密码
     */
    @TableField("PASSWORD")
    private String password;

    /**
     * 头像图片路径
     */
    @TableField("IMAGE")
    private String image;

    /**
     * 员工状态
     */
    @TableField("STATUS")
    private Integer status;

    /**
     * 是否启用
     * 1: 启用, 0: 禁用
     */
    @TableField("ENABLED")
    private Integer enabled;

    /**
     * 访问权限
     */
    @TableField("ACCESS")
    private Integer access;
    
    /**
     * NSFW ID
     */
    @TableField("NSFW_ID")
    private String nsfwId;
}