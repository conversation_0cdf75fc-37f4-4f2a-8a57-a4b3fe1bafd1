package com.xinchuang.sync.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 大厅信息实体类
 * 对应数据库中的hall_info表
 */
@Data
@NoArgsConstructor
@TableName("hall_info")
public class HallInfo {
    /**
     * 主键ID
     */
    @TableId(value = "hall_id", type = IdType.AUTO)
    private Integer hallId;
    
    /**
     * 大厅名称
     */
    @TableField("hall_name")
    private String hallName;
    
    /**
     * 大厅唯一编码
     */
    @TableField("hall_code")
    private String hallCode;
    
    /**
     * 区域编码
     */
    @TableField("region_code")
    private String regionCode;
    
    /**
     * 大厅地址
     */
    @TableField("address")
    private String address;
    
    /**
     * 联系人
     */
    @TableField("contact_person")
    private String contactPerson;
    
    /**
     * 联系电话
     */
    @TableField("contact_phone")
    private String contactPhone;
    
    /**
     * 状态
     * 1: 启用, 0: 禁用
     */
    @TableField("status")
    private Integer status;
    
    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    @TableField("update_time")
    private LocalDateTime updateTime;
}