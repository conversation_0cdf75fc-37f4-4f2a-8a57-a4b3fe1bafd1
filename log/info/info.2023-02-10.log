2023-02-10 16:55:53.445 ziyun [main] INFO  c.z.r.RepairSystemApplication - Starting RepairSystemApplication on DESKTOP-EAM764B with PID 36320 (started by karl in D:\project\Java Projectes\queuingsystem)
2023-02-10 16:55:53.449 ziyun [main] INFO  c.z.r.RepairSystemApplication - No active profile set, falling back to default profiles: default
2023-02-10 16:55:56.600 ziyun [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2023-02-10 16:55:56.603 ziyun [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data repositories in DEFAULT mode.
2023-02-10 16:55:56.724 ziyun [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 95ms. Found 0 repository interfaces.
2023-02-10 16:55:57.331 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$4f208c42] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-10 16:55:57.504 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.datasource.dynamic-com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-10 16:55:57.507 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration$$EnhancerBySpringCGLIB$$b8aefd13] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-10 16:55:57.513 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dsProcessor' of type [com.baomidou.dynamic.datasource.processor.DsHeaderProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-10 16:55:57.520 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dynamicDatasourceAnnotationAdvisor' of type [com.baomidou.dynamic.datasource.aop.DynamicDataSourceAnnotationAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-10 16:55:57.520 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'shiroConfig' of type [com.ziyun.repairsystem.common.authentication.ShiroConfig$$EnhancerBySpringCGLIB$$a35ad52d] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-10 16:55:57.993 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'redisConfig' of type [com.ziyun.repairsystem.common.config.RedisConfig$$EnhancerBySpringCGLIB$$e333ee81] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-10 16:55:58.011 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'redisPoolFactory' of type [redis.clients.jedis.JedisPool] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-10 16:55:58.014 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'redisService' of type [com.ziyun.repairsystem.common.service.impl.RedisServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-10 16:55:58.075 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'mybatis-plus-com.baomidou.mybatisplus.autoconfigure.MybatisPlusProperties' of type [com.baomidou.mybatisplus.autoconfigure.MybatisPlusProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-10 16:55:58.082 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'mybatisPlusConfig' of type [com.ziyun.repairsystem.common.config.MybatisPlusConfig$$EnhancerBySpringCGLIB$$de6f1813] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-10 16:55:58.093 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'paginationInterceptor' of type [com.baomidou.mybatisplus.extension.plugins.PaginationInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-10 16:55:58.099 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration' of type [com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration$$EnhancerBySpringCGLIB$$8ce8f8dc] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-10 16:55:58.107 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dynamicDataSourceCreator' of type [com.baomidou.dynamic.datasource.DynamicDataSourceCreator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-10 16:55:58.109 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dynamicDataSourceProvider' of type [com.baomidou.dynamic.datasource.provider.YmlDynamicDataSourceProvider] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-10 16:55:58.112 ziyun [main] INFO  c.b.d.d.DynamicRoutingDataSource - 动态数据源-检测到并开启了p6spy
2023-02-10 16:55:58.147 ziyun [main] INFO  com.zaxxer.hikari.HikariDataSource - primary - Starting...
2023-02-10 16:55:58.558 ziyun [main] INFO  com.zaxxer.hikari.HikariDataSource - primary - Start completed.
2023-02-10 16:55:58.558 ziyun [main] INFO  c.b.d.d.DynamicRoutingDataSource - 初始共加载 1 个数据源
2023-02-10 16:55:58.558 ziyun [main] INFO  c.b.d.d.DynamicRoutingDataSource - 动态数据源-加载 primary 成功
2023-02-10 16:55:58.558 ziyun [main] INFO  c.b.d.d.DynamicRoutingDataSource - 当前的默认数据源是单数据源，数据源名为 primary
2023-02-10 16:55:58.558 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dataSource' of type [com.baomidou.dynamic.datasource.DynamicRoutingDataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-10 16:55:58.571 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.datasource-org.springframework.boot.autoconfigure.jdbc.DataSourceProperties' of type [org.springframework.boot.autoconfigure.jdbc.DataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-10 16:55:58.586 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.jdbc.DataSourceInitializerInvoker' of type [org.springframework.boot.autoconfigure.jdbc.DataSourceInitializerInvoker] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-10 16:55:59.638 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'sqlSessionFactory' of type [org.apache.ibatis.session.defaults.DefaultSqlSessionFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-10 16:55:59.655 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'sqlSessionTemplate' of type [org.mybatis.spring.SqlSessionTemplate] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-10 16:55:59.659 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'roleMapper' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-10 16:55:59.661 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'roleMapper' of type [com.sun.proxy.$Proxy114] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-10 16:55:59.693 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'roleMenuMapper' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-10 16:55:59.700 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'roleMenuMapper' of type [com.sun.proxy.$Proxy116] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-10 16:55:59.708 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userRoleMapper' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-10 16:55:59.710 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userRoleMapper' of type [com.sun.proxy.$Proxy117] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-10 16:55:59.712 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userRoleService' of type [com.ziyun.repairsystem.system.service.impl.UserRoleServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-10 16:55:59.759 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'roleMenuService' of type [com.ziyun.repairsystem.system.service.impl.RoleMenuServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-10 16:55:59.776 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'roleService' of type [com.ziyun.repairsystem.system.service.impl.RoleServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-10 16:55:59.799 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'menuMapper' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-10 16:55:59.800 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'menuMapper' of type [com.sun.proxy.$Proxy121] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-10 16:55:59.803 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'menuService' of type [com.ziyun.repairsystem.system.service.impl.MenuServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-10 16:55:59.823 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userMapper' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-10 16:55:59.836 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userMapper' of type [com.sun.proxy.$Proxy123] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-10 16:55:59.870 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userConfigMapper' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-10 16:55:59.877 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userConfigMapper' of type [com.sun.proxy.$Proxy124] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-10 16:55:59.882 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userConfigService' of type [com.ziyun.repairsystem.system.service.impl.UserConfigServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-10 16:55:59.897 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userService' of type [com.ziyun.repairsystem.system.service.impl.UserServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-10 16:55:59.914 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperConfiguration' of type [org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperConfiguration$$EnhancerBySpringCGLIB$$aab40bd3] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-10 16:55:59.918 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperBuilderConfiguration' of type [org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperBuilderConfiguration$$EnhancerBySpringCGLIB$$5d00cdec] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-10 16:55:59.920 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$Jackson2ObjectMapperBuilderCustomizerConfiguration' of type [org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$Jackson2ObjectMapperBuilderCustomizerConfiguration$$EnhancerBySpringCGLIB$$1817b3db] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-10 16:55:59.927 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.jackson-org.springframework.boot.autoconfigure.jackson.JacksonProperties' of type [org.springframework.boot.autoconfigure.jackson.JacksonProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-10 16:55:59.930 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'standardJacksonObjectMapperBuilderCustomizer' of type [org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$Jackson2ObjectMapperBuilderCustomizerConfiguration$StandardJackson2ObjectMapperBuilderCustomizer] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-10 16:55:59.946 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$ParameterNamesModuleConfiguration' of type [org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$ParameterNamesModuleConfiguration$$EnhancerBySpringCGLIB$$3ee32b09] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-10 16:55:59.955 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'parameterNamesModule' of type [com.fasterxml.jackson.module.paramnames.ParameterNamesModule] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-10 16:55:59.957 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration' of type [org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$$EnhancerBySpringCGLIB$$8cbfa962] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-10 16:55:59.968 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'jsonComponentModule' of type [org.springframework.boot.jackson.JsonComponentModule] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-10 16:55:59.970 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.data.web.config.SpringDataJacksonConfiguration' of type [org.springframework.data.web.config.SpringDataJacksonConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-10 16:55:59.974 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'jacksonGeoModule' of type [org.springframework.data.geo.GeoModule] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-10 16:55:59.977 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'jacksonObjectMapperBuilder' of type [org.springframework.http.converter.json.Jackson2ObjectMapperBuilder] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-10 16:55:59.998 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'jacksonObjectMapper' of type [com.fasterxml.jackson.databind.ObjectMapper] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-10 16:56:00.011 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'cacheService' of type [com.ziyun.repairsystem.common.service.impl.CacheServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-10 16:56:00.023 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userManager' of type [com.ziyun.repairsystem.system.manager.UserManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-10 16:56:00.023 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'shiroRealm' of type [com.ziyun.repairsystem.common.authentication.ShiroRealm] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-10 16:56:00.031 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'securityManager' of type [org.apache.shiro.web.mgt.DefaultWebSecurityManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-10 16:56:00.051 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'authorizationAttributeSourceAdvisor' of type [org.apache.shiro.spring.security.interceptor.AuthorizationAttributeSourceAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-10 16:56:00.500 ziyun [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9528 (http)
2023-02-10 16:56:00.516 ziyun [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9528"]
2023-02-10 16:56:00.526 ziyun [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2023-02-10 16:56:00.527 ziyun [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.19]
2023-02-10 16:56:00.718 ziyun [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-02-10 16:56:00.718 ziyun [main] INFO  o.s.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 7199 ms
2023-02-10 16:56:01.795 ziyun [main] INFO  org.quartz.impl.StdSchedulerFactory - Using default implementation for ThreadExecutor
2023-02-10 16:56:01.809 ziyun [main] INFO  o.quartz.core.SchedulerSignalerImpl - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2023-02-10 16:56:01.809 ziyun [main] INFO  org.quartz.core.QuartzScheduler - Quartz Scheduler v.2.3.1 created.
2023-02-10 16:56:01.815 ziyun [main] INFO  o.s.s.quartz.LocalDataSourceJobStore - Using db table-based data access locking (synchronization).
2023-02-10 16:56:01.817 ziyun [main] INFO  o.s.s.quartz.LocalDataSourceJobStore - JobStoreCMT initialized.
2023-02-10 16:56:01.817 ziyun [main] INFO  org.quartz.core.QuartzScheduler - Scheduler meta-data: Quartz Scheduler (v2.3.1) 'MyScheduler' with instanceId 'DESKTOP-EAM764B1676019361796'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 20 threads.
  Using job-store 'org.springframework.scheduling.quartz.LocalDataSourceJobStore' - which supports persistence. and is clustered.

2023-02-10 16:56:01.817 ziyun [main] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler 'MyScheduler' initialized from an externally provided properties instance.
2023-02-10 16:56:01.817 ziyun [main] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler version: 2.3.1
2023-02-10 16:56:01.819 ziyun [main] INFO  org.quartz.core.QuartzScheduler - JobFactory set to: org.springframework.scheduling.quartz.AdaptableJobFactory@e200ae
2023-02-10 16:56:01.894 ziyun [main] INFO  p6spy - 2023-02-10 16:56:01 | 耗时 23 ms | SQL 语句：
select job_id jobId, bean_name beanName, method_name methodName, params, cron_expression cronExpression, status, remark, create_time createTime from t_job order by job_id;
2023-02-10 16:56:03.576 ziyun [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 2 endpoint(s) beneath base path '/actuator'
2023-02-10 16:56:03.733 ziyun [main] INFO  o.s.s.c.ThreadPoolTaskExecutor - Initializing ExecutorService 'taskExecutor'
2023-02-10 16:56:03.821 ziyun [main] INFO  s.d.s.w.PropertySourcedRequestMappingHandlerMapping - Mapped URL path [/v2/api-docs] onto method [public org.springframework.http.ResponseEntity<springfox.documentation.spring.web.json.Json> springfox.documentation.swagger2.web.Swagger2Controller.getDocumentation(java.lang.String,javax.servlet.http.HttpServletRequest)]
2023-02-10 16:56:04.924 ziyun [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Context refreshed
2023-02-10 16:56:04.944 ziyun [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
2023-02-10 16:56:04.988 ziyun [main] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
2023-02-10 16:56:05.183 ziyun [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_1
2023-02-10 16:56:05.195 ziyun [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_2
2023-02-10 16:56:05.205 ziyun [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_3
2023-02-10 16:56:05.212 ziyun [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_4
2023-02-10 16:56:05.227 ziyun [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_5
2023-02-10 16:56:05.271 ziyun [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: addRepairInfoUsingPOST_1
2023-02-10 16:56:05.276 ziyun [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_6
2023-02-10 16:56:05.288 ziyun [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_7
2023-02-10 16:56:05.297 ziyun [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_8
2023-02-10 16:56:05.300 ziyun [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: querySwjgRoUsingGET_1
2023-02-10 16:56:05.325 ziyun [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_9
2023-02-10 16:56:05.341 ziyun [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_10
2023-02-10 16:56:05.372 ziyun [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-9528"]
2023-02-10 16:56:05.395 ziyun [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 9528 (http) with context path ''
2023-02-10 16:56:05.397 ziyun [main] INFO  c.z.r.RepairSystemApplication - Started RepairSystemApplication in 12.945 seconds (JVM running for 23.183)
2023-02-10 16:56:05.408 ziyun [main] INFO  c.z.r.common.runner.CacheInitRunner - Redis连接中 ······
2023-02-10 16:56:05.421 ziyun [main] INFO  c.z.r.common.runner.CacheInitRunner - 缓存初始化 ······
2023-02-10 16:56:05.421 ziyun [main] INFO  c.z.r.common.runner.CacheInitRunner - 缓存用户数据 ······
2023-02-10 16:56:05.481 ziyun [main] INFO  p6spy - 2023-02-10 16:56:05 | 耗时 11 ms | SQL 语句：
SELECT USER_ID,username,password,dept_id,email,mobile,status,create_time,modify_time,last_login_time,ssex,description,REAL_NAME,avatar FROM t_user;
2023-02-10 16:56:05.509 ziyun [main] INFO  p6spy - 2023-02-10 16:56:05 | 耗时 23 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = 'ziyun' group by u.username;
2023-02-10 16:56:05.621 ziyun [main] INFO  p6spy - 2023-02-10 16:56:05 | 耗时 0 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = 'ziyun';
2023-02-10 16:56:05.632 ziyun [main] INFO  p6spy - 2023-02-10 16:56:05 | 耗时 4 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = 'ziyun' and m.perms is not null and m.perms <> '';
2023-02-10 16:56:05.647 ziyun [main] INFO  p6spy - 2023-02-10 16:56:05 | 耗时 9 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='166' ;
2023-02-10 16:56:05.650 ziyun [main] INFO  p6spy - 2023-02-10 16:56:05 | 耗时 1 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = 'test123' group by u.username;
2023-02-10 16:56:05.654 ziyun [main] INFO  p6spy - 2023-02-10 16:56:05 | 耗时 1 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = 'test123';
2023-02-10 16:56:05.656 ziyun [main] INFO  p6spy - 2023-02-10 16:56:05 | 耗时 1 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = 'test123' and m.perms is not null and m.perms <> '';
2023-02-10 16:56:05.658 ziyun [main] INFO  p6spy - 2023-02-10 16:56:05 | 耗时 0 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='167' ;
2023-02-10 16:56:05.660 ziyun [main] INFO  p6spy - 2023-02-10 16:56:05 | 耗时 1 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = 'yangyang' group by u.username;
2023-02-10 16:56:05.663 ziyun [main] INFO  p6spy - 2023-02-10 16:56:05 | 耗时 0 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = 'yangyang';
2023-02-10 16:56:05.665 ziyun [main] INFO  p6spy - 2023-02-10 16:56:05 | 耗时 1 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = 'yangyang' and m.perms is not null and m.perms <> '';
2023-02-10 16:56:05.667 ziyun [main] INFO  p6spy - 2023-02-10 16:56:05 | 耗时 0 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='171' ;
2023-02-10 16:56:05.670 ziyun [main] INFO  p6spy - 2023-02-10 16:56:05 | 耗时 1 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = '15858214004' group by u.username;
2023-02-10 16:56:05.676 ziyun [main] INFO  p6spy - 2023-02-10 16:56:05 | 耗时 0 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = '15858214004';
2023-02-10 16:56:05.678 ziyun [main] INFO  p6spy - 2023-02-10 16:56:05 | 耗时 0 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = '15858214004' and m.perms is not null and m.perms <> '';
2023-02-10 16:56:05.680 ziyun [main] INFO  p6spy - 2023-02-10 16:56:05 | 耗时 0 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='172' ;
2023-02-10 16:56:05.682 ziyun [main] INFO  p6spy - 2023-02-10 16:56:05 | 耗时 1 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = 'realwjy001' group by u.username;
2023-02-10 16:56:05.686 ziyun [main] INFO  p6spy - 2023-02-10 16:56:05 | 耗时 0 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = 'realwjy001';
2023-02-10 16:56:05.688 ziyun [main] INFO  p6spy - 2023-02-10 16:56:05 | 耗时 0 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = 'realwjy001' and m.perms is not null and m.perms <> '';
2023-02-10 16:56:05.690 ziyun [main] INFO  p6spy - 2023-02-10 16:56:05 | 耗时 0 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='174' ;
2023-02-10 16:56:05.692 ziyun [main] INFO  p6spy - 2023-02-10 16:56:05 | 耗时 1 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = '15067108130' group by u.username;
2023-02-10 16:56:05.695 ziyun [main] INFO  p6spy - 2023-02-10 16:56:05 | 耗时 0 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = '15067108130';
2023-02-10 16:56:05.697 ziyun [main] INFO  p6spy - 2023-02-10 16:56:05 | 耗时 0 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = '15067108130' and m.perms is not null and m.perms <> '';
2023-02-10 16:56:05.706 ziyun [main] INFO  p6spy - 2023-02-10 16:56:05 | 耗时 0 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='175' ;
2023-02-10 16:56:05.709 ziyun [main] INFO  p6spy - 2023-02-10 16:56:05 | 耗时 1 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = '15755998467' group by u.username;
2023-02-10 16:56:05.712 ziyun [main] INFO  p6spy - 2023-02-10 16:56:05 | 耗时 0 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = '15755998467';
2023-02-10 16:56:05.727 ziyun [main] INFO  p6spy - 2023-02-10 16:56:05 | 耗时 1 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = '15755998467' and m.perms is not null and m.perms <> '';
2023-02-10 16:56:05.728 ziyun [main] INFO  p6spy - 2023-02-10 16:56:05 | 耗时 0 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='176' ;
2023-02-10 16:56:05.731 ziyun [main] INFO  p6spy - 2023-02-10 16:56:05 | 耗时 1 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = 'qita' group by u.username;
2023-02-10 16:56:05.733 ziyun [main] INFO  p6spy - 2023-02-10 16:56:05 | 耗时 0 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = 'qita';
2023-02-10 16:56:05.735 ziyun [main] INFO  p6spy - 2023-02-10 16:56:05 | 耗时 0 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = 'qita' and m.perms is not null and m.perms <> '';
2023-02-10 16:56:05.737 ziyun [main] INFO  p6spy - 2023-02-10 16:56:05 | 耗时 0 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='177' ;
2023-02-10 16:56:05.740 ziyun [main] INFO  p6spy - 2023-02-10 16:56:05 | 耗时 1 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = '15869119455' group by u.username;
2023-02-10 16:56:05.743 ziyun [main] INFO  p6spy - 2023-02-10 16:56:05 | 耗时 0 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = '15869119455';
2023-02-10 16:56:05.745 ziyun [main] INFO  p6spy - 2023-02-10 16:56:05 | 耗时 0 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = '15869119455' and m.perms is not null and m.perms <> '';
2023-02-10 16:56:05.746 ziyun [main] INFO  p6spy - 2023-02-10 16:56:05 | 耗时 0 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='178' ;
2023-02-10 16:56:05.749 ziyun [main] INFO  p6spy - 2023-02-10 16:56:05 | 耗时 1 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = '15868125270' group by u.username;
2023-02-10 16:56:05.753 ziyun [main] INFO  p6spy - 2023-02-10 16:56:05 | 耗时 1 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = '15868125270';
2023-02-10 16:56:05.755 ziyun [main] INFO  p6spy - 2023-02-10 16:56:05 | 耗时 1 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = '15868125270' and m.perms is not null and m.perms <> '';
2023-02-10 16:56:05.757 ziyun [main] INFO  p6spy - 2023-02-10 16:56:05 | 耗时 0 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='179' ;
2023-02-10 16:56:05.760 ziyun [main] INFO  p6spy - 2023-02-10 16:56:05 | 耗时 1 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = 'wangliwei' group by u.username;
2023-02-10 16:56:05.763 ziyun [main] INFO  p6spy - 2023-02-10 16:56:05 | 耗时 0 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = 'wangliwei';
2023-02-10 16:56:05.781 ziyun [main] INFO  p6spy - 2023-02-10 16:56:05 | 耗时 12 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = 'wangliwei' and m.perms is not null and m.perms <> '';
2023-02-10 16:56:05.790 ziyun [main] INFO  p6spy - 2023-02-10 16:56:05 | 耗时 0 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='180' ;
2023-02-10 16:56:05.794 ziyun [main] INFO  p6spy - 2023-02-10 16:56:05 | 耗时 1 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = 'daiwei' group by u.username;
2023-02-10 16:56:05.797 ziyun [main] INFO  p6spy - 2023-02-10 16:56:05 | 耗时 0 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = 'daiwei';
2023-02-10 16:56:05.801 ziyun [main] INFO  p6spy - 2023-02-10 16:56:05 | 耗时 1 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = 'daiwei' and m.perms is not null and m.perms <> '';
2023-02-10 16:56:05.807 ziyun [main] INFO  p6spy - 2023-02-10 16:56:05 | 耗时 0 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='181' ;
2023-02-10 16:56:05.809 ziyun [main] INFO  c.z.r.common.runner.StartedUpRunner -  __    ___   _      ___   _     ____ _____  ____ 
2023-02-10 16:56:05.809 ziyun [main] INFO  c.z.r.common.runner.StartedUpRunner - / /`  / / \ | |\/| | |_) | |   | |_   | |  | |_  
2023-02-10 16:56:05.809 ziyun [main] INFO  c.z.r.common.runner.StartedUpRunner - \_\_, \_\_/ |_|  | |_|   |_|__ |_|__  |_|  |_|__ 
2023-02-10 16:56:05.810 ziyun [main] INFO  c.z.r.common.runner.StartedUpRunner -                                                       
2023-02-10 16:56:05.810 ziyun [main] INFO  c.z.r.common.runner.StartedUpRunner - ZiYun 启动完毕，时间：2023-02-10T16:56:05.810
2023-02-10 17:22:03.171 ziyun [Thread-3] INFO  o.s.s.c.ThreadPoolTaskExecutor - Shutting down ExecutorService 'taskExecutor'
2023-02-10 17:22:03.173 ziyun [Thread-3] INFO  o.s.s.quartz.SchedulerFactoryBean - Shutting down Quartz Scheduler
2023-02-10 17:22:03.173 ziyun [Thread-3] INFO  org.quartz.core.QuartzScheduler - Scheduler MyScheduler_$_DESKTOP-EAM764B1676019361796 shutting down.
2023-02-10 17:22:03.173 ziyun [Thread-3] INFO  org.quartz.core.QuartzScheduler - Scheduler MyScheduler_$_DESKTOP-EAM764B1676019361796 paused.
2023-02-10 17:22:03.174 ziyun [Thread-3] INFO  org.quartz.core.QuartzScheduler - Scheduler MyScheduler_$_DESKTOP-EAM764B1676019361796 shutdown complete.
2023-02-10 17:22:03.176 ziyun [Thread-3] INFO  c.b.d.d.DynamicRoutingDataSource - closing dynamicDatasource  ing....
2023-02-10 17:22:03.177 ziyun [Thread-3] INFO  com.zaxxer.hikari.HikariDataSource - primary - Shutdown initiated...
2023-02-10 17:22:03.181 ziyun [Thread-3] INFO  com.zaxxer.hikari.HikariDataSource - primary - Shutdown completed.
2023-02-10 17:22:49.401 ziyun [main] INFO  c.z.r.RepairSystemApplication - Starting RepairSystemApplication on DESKTOP-EAM764B with PID 4372 (started by karl in D:\project\Java Projectes\queuingsystem)
2023-02-10 17:22:49.404 ziyun [main] INFO  c.z.r.RepairSystemApplication - No active profile set, falling back to default profiles: default
2023-02-10 17:22:52.116 ziyun [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2023-02-10 17:22:52.118 ziyun [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data repositories in DEFAULT mode.
2023-02-10 17:22:52.233 ziyun [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 102ms. Found 0 repository interfaces.
2023-02-10 17:22:52.684 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$88e399b2] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-10 17:22:52.863 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.datasource.dynamic-com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-10 17:22:52.866 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration$$EnhancerBySpringCGLIB$$f2720a83] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-10 17:22:52.873 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dsProcessor' of type [com.baomidou.dynamic.datasource.processor.DsHeaderProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-10 17:22:52.878 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dynamicDatasourceAnnotationAdvisor' of type [com.baomidou.dynamic.datasource.aop.DynamicDataSourceAnnotationAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-10 17:22:52.878 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'shiroConfig' of type [com.ziyun.repairsystem.common.authentication.ShiroConfig$$EnhancerBySpringCGLIB$$dd1de29d] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-10 17:22:53.391 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'redisConfig' of type [com.ziyun.repairsystem.common.config.RedisConfig$$EnhancerBySpringCGLIB$$1cf6fbf1] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-10 17:22:53.407 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'redisPoolFactory' of type [redis.clients.jedis.JedisPool] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-10 17:22:53.411 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'redisService' of type [com.ziyun.repairsystem.common.service.impl.RedisServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-10 17:22:53.474 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'mybatis-plus-com.baomidou.mybatisplus.autoconfigure.MybatisPlusProperties' of type [com.baomidou.mybatisplus.autoconfigure.MybatisPlusProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-10 17:22:53.481 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'mybatisPlusConfig' of type [com.ziyun.repairsystem.common.config.MybatisPlusConfig$$EnhancerBySpringCGLIB$$18322583] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-10 17:22:53.484 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'paginationInterceptor' of type [com.baomidou.mybatisplus.extension.plugins.PaginationInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-10 17:22:53.491 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration' of type [com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration$$EnhancerBySpringCGLIB$$c6ac064c] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-10 17:22:53.499 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dynamicDataSourceCreator' of type [com.baomidou.dynamic.datasource.DynamicDataSourceCreator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-10 17:22:53.500 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dynamicDataSourceProvider' of type [com.baomidou.dynamic.datasource.provider.YmlDynamicDataSourceProvider] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-10 17:22:53.503 ziyun [main] INFO  c.b.d.d.DynamicRoutingDataSource - 动态数据源-检测到并开启了p6spy
2023-02-10 17:22:53.543 ziyun [main] INFO  com.zaxxer.hikari.HikariDataSource - primary - Starting...
2023-02-10 17:22:53.895 ziyun [main] INFO  com.zaxxer.hikari.HikariDataSource - primary - Start completed.
2023-02-10 17:22:53.896 ziyun [main] INFO  c.b.d.d.DynamicRoutingDataSource - 初始共加载 1 个数据源
2023-02-10 17:22:53.896 ziyun [main] INFO  c.b.d.d.DynamicRoutingDataSource - 动态数据源-加载 primary 成功
2023-02-10 17:22:53.896 ziyun [main] INFO  c.b.d.d.DynamicRoutingDataSource - 当前的默认数据源是单数据源，数据源名为 primary
2023-02-10 17:22:53.896 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dataSource' of type [com.baomidou.dynamic.datasource.DynamicRoutingDataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-10 17:22:53.909 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.datasource-org.springframework.boot.autoconfigure.jdbc.DataSourceProperties' of type [org.springframework.boot.autoconfigure.jdbc.DataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-10 17:22:53.922 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.jdbc.DataSourceInitializerInvoker' of type [org.springframework.boot.autoconfigure.jdbc.DataSourceInitializerInvoker] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-10 17:22:54.688 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'sqlSessionFactory' of type [org.apache.ibatis.session.defaults.DefaultSqlSessionFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-10 17:22:54.695 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'sqlSessionTemplate' of type [org.mybatis.spring.SqlSessionTemplate] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-10 17:22:54.698 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'roleMapper' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-10 17:22:54.701 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'roleMapper' of type [com.sun.proxy.$Proxy114] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-10 17:22:54.731 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'roleMenuMapper' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-10 17:22:54.732 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'roleMenuMapper' of type [com.sun.proxy.$Proxy116] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-10 17:22:54.740 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userRoleMapper' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-10 17:22:54.742 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userRoleMapper' of type [com.sun.proxy.$Proxy117] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-10 17:22:54.744 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userRoleService' of type [com.ziyun.repairsystem.system.service.impl.UserRoleServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-10 17:22:54.781 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'roleMenuService' of type [com.ziyun.repairsystem.system.service.impl.RoleMenuServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-10 17:22:54.797 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'roleService' of type [com.ziyun.repairsystem.system.service.impl.RoleServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-10 17:22:54.819 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'menuMapper' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-10 17:22:54.820 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'menuMapper' of type [com.sun.proxy.$Proxy121] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-10 17:22:54.823 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'menuService' of type [com.ziyun.repairsystem.system.service.impl.MenuServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-10 17:22:54.847 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userMapper' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-10 17:22:54.848 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userMapper' of type [com.sun.proxy.$Proxy123] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-10 17:22:54.880 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userConfigMapper' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-10 17:22:54.882 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userConfigMapper' of type [com.sun.proxy.$Proxy124] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-10 17:22:54.886 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userConfigService' of type [com.ziyun.repairsystem.system.service.impl.UserConfigServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-10 17:22:54.902 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userService' of type [com.ziyun.repairsystem.system.service.impl.UserServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-10 17:22:54.925 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperConfiguration' of type [org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperConfiguration$$EnhancerBySpringCGLIB$$e4771943] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-10 17:22:54.929 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperBuilderConfiguration' of type [org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperBuilderConfiguration$$EnhancerBySpringCGLIB$$96c3db5c] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-10 17:22:54.932 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$Jackson2ObjectMapperBuilderCustomizerConfiguration' of type [org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$Jackson2ObjectMapperBuilderCustomizerConfiguration$$EnhancerBySpringCGLIB$$51dac14b] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-10 17:22:54.939 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.jackson-org.springframework.boot.autoconfigure.jackson.JacksonProperties' of type [org.springframework.boot.autoconfigure.jackson.JacksonProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-10 17:22:54.943 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'standardJacksonObjectMapperBuilderCustomizer' of type [org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$Jackson2ObjectMapperBuilderCustomizerConfiguration$StandardJackson2ObjectMapperBuilderCustomizer] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-10 17:22:54.948 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$ParameterNamesModuleConfiguration' of type [org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$ParameterNamesModuleConfiguration$$EnhancerBySpringCGLIB$$78a63879] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-10 17:22:54.955 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'parameterNamesModule' of type [com.fasterxml.jackson.module.paramnames.ParameterNamesModule] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-10 17:22:54.958 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration' of type [org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$$EnhancerBySpringCGLIB$$c682b6d2] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-10 17:22:54.968 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'jsonComponentModule' of type [org.springframework.boot.jackson.JsonComponentModule] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-10 17:22:54.971 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.data.web.config.SpringDataJacksonConfiguration' of type [org.springframework.data.web.config.SpringDataJacksonConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-10 17:22:54.974 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'jacksonGeoModule' of type [org.springframework.data.geo.GeoModule] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-10 17:22:54.977 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'jacksonObjectMapperBuilder' of type [org.springframework.http.converter.json.Jackson2ObjectMapperBuilder] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-10 17:22:54.997 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'jacksonObjectMapper' of type [com.fasterxml.jackson.databind.ObjectMapper] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-10 17:22:55.015 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'cacheService' of type [com.ziyun.repairsystem.common.service.impl.CacheServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-10 17:22:55.019 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userManager' of type [com.ziyun.repairsystem.system.manager.UserManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-10 17:22:55.020 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'shiroRealm' of type [com.ziyun.repairsystem.common.authentication.ShiroRealm] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-10 17:22:55.027 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'securityManager' of type [org.apache.shiro.web.mgt.DefaultWebSecurityManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-10 17:22:55.039 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'authorizationAttributeSourceAdvisor' of type [org.apache.shiro.spring.security.interceptor.AuthorizationAttributeSourceAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-10 17:22:55.480 ziyun [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9528 (http)
2023-02-10 17:22:55.496 ziyun [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9528"]
2023-02-10 17:22:55.507 ziyun [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2023-02-10 17:22:55.507 ziyun [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.19]
2023-02-10 17:22:55.678 ziyun [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-02-10 17:22:55.678 ziyun [main] INFO  o.s.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 6231 ms
2023-02-10 17:22:56.362 ziyun [main] INFO  org.quartz.impl.StdSchedulerFactory - Using default implementation for ThreadExecutor
2023-02-10 17:22:56.372 ziyun [main] INFO  o.quartz.core.SchedulerSignalerImpl - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2023-02-10 17:22:56.372 ziyun [main] INFO  org.quartz.core.QuartzScheduler - Quartz Scheduler v.2.3.1 created.
2023-02-10 17:22:56.375 ziyun [main] INFO  o.s.s.quartz.LocalDataSourceJobStore - Using db table-based data access locking (synchronization).
2023-02-10 17:22:56.376 ziyun [main] INFO  o.s.s.quartz.LocalDataSourceJobStore - JobStoreCMT initialized.
2023-02-10 17:22:56.377 ziyun [main] INFO  org.quartz.core.QuartzScheduler - Scheduler meta-data: Quartz Scheduler (v2.3.1) 'MyScheduler' with instanceId 'DESKTOP-EAM764B1676020976363'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 20 threads.
  Using job-store 'org.springframework.scheduling.quartz.LocalDataSourceJobStore' - which supports persistence. and is clustered.

2023-02-10 17:22:56.377 ziyun [main] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler 'MyScheduler' initialized from an externally provided properties instance.
2023-02-10 17:22:56.377 ziyun [main] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler version: 2.3.1
2023-02-10 17:22:56.378 ziyun [main] INFO  org.quartz.core.QuartzScheduler - JobFactory set to: org.springframework.scheduling.quartz.AdaptableJobFactory@33c6c2
2023-02-10 17:22:56.430 ziyun [main] INFO  p6spy - 2023-02-10 17:22:56 | 耗时 12 ms | SQL 语句：
select job_id jobId, bean_name beanName, method_name methodName, params, cron_expression cronExpression, status, remark, create_time createTime from t_job order by job_id;
2023-02-10 17:22:57.303 ziyun [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 2 endpoint(s) beneath base path '/actuator'
2023-02-10 17:22:57.401 ziyun [main] INFO  o.s.s.c.ThreadPoolTaskExecutor - Initializing ExecutorService 'taskExecutor'
2023-02-10 17:22:57.458 ziyun [main] INFO  s.d.s.w.PropertySourcedRequestMappingHandlerMapping - Mapped URL path [/v2/api-docs] onto method [public org.springframework.http.ResponseEntity<springfox.documentation.spring.web.json.Json> springfox.documentation.swagger2.web.Swagger2Controller.getDocumentation(java.lang.String,javax.servlet.http.HttpServletRequest)]
2023-02-10 17:22:58.226 ziyun [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Context refreshed
2023-02-10 17:22:58.244 ziyun [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
2023-02-10 17:22:58.284 ziyun [main] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
2023-02-10 17:22:58.410 ziyun [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_1
2023-02-10 17:22:58.420 ziyun [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_2
2023-02-10 17:22:58.429 ziyun [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_3
2023-02-10 17:22:58.435 ziyun [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_4
2023-02-10 17:22:58.448 ziyun [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_5
2023-02-10 17:22:58.494 ziyun [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: addRepairInfoUsingPOST_1
2023-02-10 17:22:58.499 ziyun [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_6
2023-02-10 17:22:58.508 ziyun [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_7
2023-02-10 17:22:58.517 ziyun [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_8
2023-02-10 17:22:58.519 ziyun [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: querySwjgRoUsingGET_1
2023-02-10 17:22:58.526 ziyun [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_9
2023-02-10 17:22:58.540 ziyun [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_10
2023-02-10 17:22:58.567 ziyun [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-9528"]
2023-02-10 17:22:58.592 ziyun [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 9528 (http) with context path ''
2023-02-10 17:22:58.594 ziyun [main] INFO  c.z.r.RepairSystemApplication - Started RepairSystemApplication in 9.868 seconds (JVM running for 16.264)
2023-02-10 17:22:58.603 ziyun [main] INFO  c.z.r.common.runner.CacheInitRunner - Redis连接中 ······
2023-02-10 17:22:58.613 ziyun [main] INFO  c.z.r.common.runner.CacheInitRunner - 缓存初始化 ······
2023-02-10 17:22:58.614 ziyun [main] INFO  c.z.r.common.runner.CacheInitRunner - 缓存用户数据 ······
2023-02-10 17:22:58.640 ziyun [main] INFO  p6spy - 2023-02-10 17:22:58 | 耗时 1 ms | SQL 语句：
SELECT USER_ID,username,password,dept_id,email,mobile,status,create_time,modify_time,last_login_time,ssex,description,REAL_NAME,avatar FROM t_user;
2023-02-10 17:22:58.648 ziyun [main] INFO  p6spy - 2023-02-10 17:22:58 | 耗时 3 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = 'ziyun' group by u.username;
2023-02-10 17:22:58.763 ziyun [main] INFO  p6spy - 2023-02-10 17:22:58 | 耗时 0 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = 'ziyun';
2023-02-10 17:22:58.778 ziyun [main] INFO  p6spy - 2023-02-10 17:22:58 | 耗时 1 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = 'ziyun' and m.perms is not null and m.perms <> '';
2023-02-10 17:22:58.784 ziyun [main] INFO  p6spy - 2023-02-10 17:22:58 | 耗时 1 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='166' ;
2023-02-10 17:22:58.787 ziyun [main] INFO  p6spy - 2023-02-10 17:22:58 | 耗时 1 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = 'test123' group by u.username;
2023-02-10 17:22:58.790 ziyun [main] INFO  p6spy - 2023-02-10 17:22:58 | 耗时 0 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = 'test123';
2023-02-10 17:22:58.792 ziyun [main] INFO  p6spy - 2023-02-10 17:22:58 | 耗时 0 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = 'test123' and m.perms is not null and m.perms <> '';
2023-02-10 17:22:58.794 ziyun [main] INFO  p6spy - 2023-02-10 17:22:58 | 耗时 0 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='167' ;
2023-02-10 17:22:58.796 ziyun [main] INFO  p6spy - 2023-02-10 17:22:58 | 耗时 1 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = 'yangyang' group by u.username;
2023-02-10 17:22:58.799 ziyun [main] INFO  p6spy - 2023-02-10 17:22:58 | 耗时 0 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = 'yangyang';
2023-02-10 17:22:58.801 ziyun [main] INFO  p6spy - 2023-02-10 17:22:58 | 耗时 0 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = 'yangyang' and m.perms is not null and m.perms <> '';
2023-02-10 17:22:58.802 ziyun [main] INFO  p6spy - 2023-02-10 17:22:58 | 耗时 0 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='171' ;
2023-02-10 17:22:58.804 ziyun [main] INFO  p6spy - 2023-02-10 17:22:58 | 耗时 1 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = '15858214004' group by u.username;
2023-02-10 17:22:58.809 ziyun [main] INFO  p6spy - 2023-02-10 17:22:58 | 耗时 0 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = '15858214004';
2023-02-10 17:22:58.811 ziyun [main] INFO  p6spy - 2023-02-10 17:22:58 | 耗时 0 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = '15858214004' and m.perms is not null and m.perms <> '';
2023-02-10 17:22:58.813 ziyun [main] INFO  p6spy - 2023-02-10 17:22:58 | 耗时 0 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='172' ;
2023-02-10 17:22:58.815 ziyun [main] INFO  p6spy - 2023-02-10 17:22:58 | 耗时 1 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = 'realwjy001' group by u.username;
2023-02-10 17:22:58.818 ziyun [main] INFO  p6spy - 2023-02-10 17:22:58 | 耗时 0 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = 'realwjy001';
2023-02-10 17:22:58.820 ziyun [main] INFO  p6spy - 2023-02-10 17:22:58 | 耗时 0 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = 'realwjy001' and m.perms is not null and m.perms <> '';
2023-02-10 17:22:58.822 ziyun [main] INFO  p6spy - 2023-02-10 17:22:58 | 耗时 0 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='174' ;
2023-02-10 17:22:58.824 ziyun [main] INFO  p6spy - 2023-02-10 17:22:58 | 耗时 1 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = '15067108130' group by u.username;
2023-02-10 17:22:58.826 ziyun [main] INFO  p6spy - 2023-02-10 17:22:58 | 耗时 0 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = '15067108130';
2023-02-10 17:22:58.828 ziyun [main] INFO  p6spy - 2023-02-10 17:22:58 | 耗时 0 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = '15067108130' and m.perms is not null and m.perms <> '';
2023-02-10 17:22:58.830 ziyun [main] INFO  p6spy - 2023-02-10 17:22:58 | 耗时 0 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='175' ;
2023-02-10 17:22:58.832 ziyun [main] INFO  p6spy - 2023-02-10 17:22:58 | 耗时 1 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = '15755998467' group by u.username;
2023-02-10 17:22:58.835 ziyun [main] INFO  p6spy - 2023-02-10 17:22:58 | 耗时 0 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = '15755998467';
2023-02-10 17:22:58.837 ziyun [main] INFO  p6spy - 2023-02-10 17:22:58 | 耗时 0 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = '15755998467' and m.perms is not null and m.perms <> '';
2023-02-10 17:22:58.838 ziyun [main] INFO  p6spy - 2023-02-10 17:22:58 | 耗时 0 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='176' ;
2023-02-10 17:22:58.840 ziyun [main] INFO  p6spy - 2023-02-10 17:22:58 | 耗时 1 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = 'qita' group by u.username;
2023-02-10 17:22:58.843 ziyun [main] INFO  p6spy - 2023-02-10 17:22:58 | 耗时 0 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = 'qita';
2023-02-10 17:22:58.845 ziyun [main] INFO  p6spy - 2023-02-10 17:22:58 | 耗时 0 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = 'qita' and m.perms is not null and m.perms <> '';
2023-02-10 17:22:58.847 ziyun [main] INFO  p6spy - 2023-02-10 17:22:58 | 耗时 0 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='177' ;
2023-02-10 17:22:58.848 ziyun [main] INFO  p6spy - 2023-02-10 17:22:58 | 耗时 1 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = '15869119455' group by u.username;
2023-02-10 17:22:58.853 ziyun [main] INFO  p6spy - 2023-02-10 17:22:58 | 耗时 0 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = '15869119455';
2023-02-10 17:22:58.854 ziyun [main] INFO  p6spy - 2023-02-10 17:22:58 | 耗时 0 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = '15869119455' and m.perms is not null and m.perms <> '';
2023-02-10 17:22:58.856 ziyun [main] INFO  p6spy - 2023-02-10 17:22:58 | 耗时 0 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='178' ;
2023-02-10 17:22:58.858 ziyun [main] INFO  p6spy - 2023-02-10 17:22:58 | 耗时 1 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = '15868125270' group by u.username;
2023-02-10 17:22:58.862 ziyun [main] INFO  p6spy - 2023-02-10 17:22:58 | 耗时 1 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = '15868125270';
2023-02-10 17:22:58.865 ziyun [main] INFO  p6spy - 2023-02-10 17:22:58 | 耗时 1 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = '15868125270' and m.perms is not null and m.perms <> '';
2023-02-10 17:22:58.867 ziyun [main] INFO  p6spy - 2023-02-10 17:22:58 | 耗时 0 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='179' ;
2023-02-10 17:22:58.870 ziyun [main] INFO  p6spy - 2023-02-10 17:22:58 | 耗时 1 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = 'wangliwei' group by u.username;
2023-02-10 17:22:58.873 ziyun [main] INFO  p6spy - 2023-02-10 17:22:58 | 耗时 0 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = 'wangliwei';
2023-02-10 17:22:58.880 ziyun [main] INFO  p6spy - 2023-02-10 17:22:58 | 耗时 1 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = 'wangliwei' and m.perms is not null and m.perms <> '';
2023-02-10 17:22:58.888 ziyun [main] INFO  p6spy - 2023-02-10 17:22:58 | 耗时 0 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='180' ;
2023-02-10 17:22:58.894 ziyun [main] INFO  p6spy - 2023-02-10 17:22:58 | 耗时 2 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = 'daiwei' group by u.username;
2023-02-10 17:22:58.899 ziyun [main] INFO  p6spy - 2023-02-10 17:22:58 | 耗时 1 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = 'daiwei';
2023-02-10 17:22:58.904 ziyun [main] INFO  p6spy - 2023-02-10 17:22:58 | 耗时 1 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = 'daiwei' and m.perms is not null and m.perms <> '';
2023-02-10 17:22:58.910 ziyun [main] INFO  p6spy - 2023-02-10 17:22:58 | 耗时 0 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='181' ;
2023-02-10 17:22:58.911 ziyun [main] INFO  c.z.r.common.runner.StartedUpRunner -  __    ___   _      ___   _     ____ _____  ____ 
2023-02-10 17:22:58.911 ziyun [main] INFO  c.z.r.common.runner.StartedUpRunner - / /`  / / \ | |\/| | |_) | |   | |_   | |  | |_  
2023-02-10 17:22:58.911 ziyun [main] INFO  c.z.r.common.runner.StartedUpRunner - \_\_, \_\_/ |_|  | |_|   |_|__ |_|__  |_|  |_|__ 
2023-02-10 17:22:58.911 ziyun [main] INFO  c.z.r.common.runner.StartedUpRunner -                                                       
2023-02-10 17:22:58.911 ziyun [main] INFO  c.z.r.common.runner.StartedUpRunner - ZiYun 启动完毕，时间：2023-02-10T17:22:58.911
2023-02-10 17:28:26.542 ziyun [http-nio-9528-exec-5] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2023-02-10 17:28:26.542 ziyun [http-nio-9528-exec-5] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2023-02-10 17:28:26.557 ziyun [http-nio-9528-exec-5] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 15 ms
2023-02-10 17:28:26.620 ziyun [http-nio-9528-exec-2] INFO  p6spy - 2023-02-10 17:28:26 | 耗时 13 ms | SQL 语句：
SELECT district_id,pid,district,level FROM t_district;
2023-02-10 17:28:26.620 ziyun [http-nio-9528-exec-4] INFO  p6spy - 2023-02-10 17:28:26 | 耗时 19 ms | SQL 语句：
SELECT d2.district_id,d2.district FROM t_district d1 JOIN t_district d2 ON d1.district_id = d2.pid WHERE d1.district_id = 1330000;
2023-02-10 17:28:26.620 ziyun [http-nio-9528-exec-8] INFO  p6spy - 2023-02-10 17:28:26 | 耗时 27 ms | SQL 语句：
SELECT COUNT(*) as count FROM t_repair_info WHERE ADD_TIME >= DATE_FORMAT(CURDATE(), '%Y-%m-01') AND ADD_TIME <= NOW();;
2023-02-10 17:28:26.620 ziyun [http-nio-9528-exec-1] INFO  p6spy - 2023-02-10 17:28:26 | 耗时 21 ms | SQL 语句：
SELECT DATE_FORMAT(ADD_TIME, '%d') AS xAxis, count(*) as count FROM t_repair_info WHERE ADD_TIME >= DATE_FORMAT(CURDATE(), '%Y-%m-01') AND ADD_TIME <= CURDATE() GROUP BY xAxis ORDER BY xAxis;;
2023-02-10 17:28:26.620 ziyun [http-nio-9528-exec-6] INFO  p6spy - 2023-02-10 17:28:26 | 耗时 21 ms | SQL 语句：
/** 上门服务 */ (select COUNT(*) AS value, '上门处理' AS name from t_repair_info r where USER_ID = PROCESSOR_ID and r.STATE_TAG = 4) /** 电话指导 */ UNION (select COUNT(*) AS value, '电话指导处理' AS name from t_repair_info r JOIN t_user u on u.USER_ID = r.PROCESSOR_ID where r.PROCESSOR_ID = 174 and r.STATE_TAG = 4) /** 远程处理 */ UNION (select COUNT(*) AS value, '远程处理' AS name from t_repair_info r JOIN t_user u on u.USER_ID = r.PROCESSOR_ID where r.PROCESSOR_ID = 180 and r.STATE_TAG = 4) /** 代维处理 */ UNION (select COUNT(*) AS value, '代维处理' AS name from t_repair_info r JOIN t_user u on u.USER_ID = r.PROCESSOR_ID where r.PROCESSOR_ID = 181 and r.STATE_TAG = 4);;
2023-02-10 17:28:26.635 ziyun [http-nio-9528-exec-8] INFO  p6spy - 2023-02-10 17:28:26 | 耗时 2 ms | SQL 语句：
SELECT COUNT(*) as count FROM t_repair_info WHERE ADD_TIME >= CURDATE() AND ADD_TIME <= NOW();;
2023-02-10 17:28:26.635 ziyun [http-nio-9528-exec-8] INFO  p6spy - 2023-02-10 17:28:26 | 耗时 4 ms | SQL 语句：
SELECT COUNT(*) as count FROM t_repair_info WHERE STATE_TAG = '0' and ADD_TIME >= CURDATE() AND ADD_TIME <= NOW();;
2023-02-10 17:28:26.682 ziyun [http-nio-9528-exec-5] INFO  p6spy - 2023-02-10 17:28:26 | 耗时 1 ms | SQL 语句：
SELECT COUNT( 1 ) FROM t_repair_info WHERE TITLE = '0';
2023-02-10 17:28:26.682 ziyun [http-nio-9528-exec-7] INFO  p6spy - 2023-02-10 17:28:26 | 耗时 2 ms | SQL 语句：
SELECT USER_ID,username,password,dept_id,email,mobile,status,create_time,modify_time,last_login_time,ssex,description,REAL_NAME,avatar FROM t_user WHERE dept_id = '2';
2023-02-10 17:28:26.777 ziyun [http-nio-9528-exec-5] INFO  p6spy - 2023-02-10 17:28:26 | 耗时 1 ms | SQL 语句：
SELECT COUNT( 1 ) FROM t_repair_info WHERE TITLE = '1';
2023-02-10 17:28:26.777 ziyun [http-nio-9528-exec-5] INFO  p6spy - 2023-02-10 17:28:26 | 耗时 1 ms | SQL 语句：
SELECT COUNT( 1 ) FROM t_repair_info WHERE TITLE = '2';
2023-02-10 17:28:26.777 ziyun [http-nio-9528-exec-5] INFO  p6spy - 2023-02-10 17:28:26 | 耗时 1 ms | SQL 语句：
SELECT COUNT( 1 ) FROM t_repair_info WHERE TITLE = '3';
2023-02-10 17:28:26.777 ziyun [http-nio-9528-exec-5] INFO  p6spy - 2023-02-10 17:28:26 | 耗时 1 ms | SQL 语句：
SELECT COUNT( 1 ) FROM t_repair_info WHERE TITLE = '4';
2023-02-10 17:28:26.777 ziyun [http-nio-9528-exec-5] INFO  p6spy - 2023-02-10 17:28:26 | 耗时 1 ms | SQL 语句：
SELECT COUNT( 1 ) FROM t_repair_info WHERE TITLE = '5';
2023-02-10 17:28:26.793 ziyun [http-nio-9528-exec-5] INFO  p6spy - 2023-02-10 17:28:26 | 耗时 1 ms | SQL 语句：
SELECT COUNT( 1 ) FROM t_repair_info WHERE TITLE = '6';
2023-02-10 17:28:26.793 ziyun [http-nio-9528-exec-5] INFO  p6spy - 2023-02-10 17:28:26 | 耗时 1 ms | SQL 语句：
SELECT COUNT( 1 ) FROM t_repair_info WHERE TITLE = '7';
2023-02-10 17:28:26.793 ziyun [http-nio-9528-exec-5] INFO  p6spy - 2023-02-10 17:28:26 | 耗时 1 ms | SQL 语句：
SELECT COUNT( 1 ) FROM t_repair_info WHERE TITLE = '99';
2023-02-10 17:28:26.793 ziyun [http-nio-9528-exec-5] INFO  c.z.r.r.c.RepairInfoController - [{name=取号机, value=931}, {name=评价器, value=930}, {name=LED屏, value=351}, {name=综合屏, value=168}, {name=高速球, value=43}, {name=监控半球, value=295}, {name=拾音器, value=22}, {name=硬盘录像机, value=32}, {name=其他, value=219}]
2023-02-10 17:28:26.918 ziyun [http-nio-9528-exec-4] INFO  p6spy - 2023-02-10 17:28:26 | 耗时 286 ms | SQL 语句：
select COALESCE(count(STATE_TAG),0) value ,tmp_status.STATUS_TAG AS STATUS_TAG from (select REPAIR_ID,STATE_TAG,r.SWJGDM,BSDTDJXH,s.DISTRICT_ID,district from t_repair_info r JOIN t_swjg_info s ON r.SWJGDM = s.BSDTDJXH JOIN (SELECT d2.district_id,d2.district FROM t_district d1 JOIN t_district d2 ON d1.district_id = d2.pid /** 传入市的id */ WHERE d1.district_id = 1330100) all_districts on s.DISTRICT_ID = all_districts.district_id) tmp_repair RIGHT JOIN (select t_dict.KEYY AS STATUS_TAG from t_dict where FIELD_NAME = 'status_tag') tmp_status on tmp_repair.STATE_TAG = tmp_status.STATUS_TAG group by tmp_status.STATUS_TAG order by tmp_status.STATUS_TAG;;
2023-02-10 17:28:27.215 ziyun [http-nio-9528-exec-4] INFO  p6spy - 2023-02-10 17:28:27 | 耗时 304 ms | SQL 语句：
select COALESCE(count(STATE_TAG),0) value ,tmp_status.STATUS_TAG AS STATUS_TAG from (select REPAIR_ID,STATE_TAG,r.SWJGDM,BSDTDJXH,s.DISTRICT_ID,district from t_repair_info r JOIN t_swjg_info s ON r.SWJGDM = s.BSDTDJXH JOIN (SELECT d2.district_id,d2.district FROM t_district d1 JOIN t_district d2 ON d1.district_id = d2.pid /** 传入市的id */ WHERE d1.district_id = 1330300) all_districts on s.DISTRICT_ID = all_districts.district_id) tmp_repair RIGHT JOIN (select t_dict.KEYY AS STATUS_TAG from t_dict where FIELD_NAME = 'status_tag') tmp_status on tmp_repair.STATE_TAG = tmp_status.STATUS_TAG group by tmp_status.STATUS_TAG order by tmp_status.STATUS_TAG;;
2023-02-10 17:28:27.512 ziyun [http-nio-9528-exec-4] INFO  p6spy - 2023-02-10 17:28:27 | 耗时 290 ms | SQL 语句：
select COALESCE(count(STATE_TAG),0) value ,tmp_status.STATUS_TAG AS STATUS_TAG from (select REPAIR_ID,STATE_TAG,r.SWJGDM,BSDTDJXH,s.DISTRICT_ID,district from t_repair_info r JOIN t_swjg_info s ON r.SWJGDM = s.BSDTDJXH JOIN (SELECT d2.district_id,d2.district FROM t_district d1 JOIN t_district d2 ON d1.district_id = d2.pid /** 传入市的id */ WHERE d1.district_id = 1330400) all_districts on s.DISTRICT_ID = all_districts.district_id) tmp_repair RIGHT JOIN (select t_dict.KEYY AS STATUS_TAG from t_dict where FIELD_NAME = 'status_tag') tmp_status on tmp_repair.STATE_TAG = tmp_status.STATUS_TAG group by tmp_status.STATUS_TAG order by tmp_status.STATUS_TAG;;
2023-02-10 17:28:27.668 ziyun [http-nio-9528-exec-4] INFO  p6spy - 2023-02-10 17:28:27 | 耗时 157 ms | SQL 语句：
select COALESCE(count(STATE_TAG),0) value ,tmp_status.STATUS_TAG AS STATUS_TAG from (select REPAIR_ID,STATE_TAG,r.SWJGDM,BSDTDJXH,s.DISTRICT_ID,district from t_repair_info r JOIN t_swjg_info s ON r.SWJGDM = s.BSDTDJXH JOIN (SELECT d2.district_id,d2.district FROM t_district d1 JOIN t_district d2 ON d1.district_id = d2.pid /** 传入市的id */ WHERE d1.district_id = 1330500) all_districts on s.DISTRICT_ID = all_districts.district_id) tmp_repair RIGHT JOIN (select t_dict.KEYY AS STATUS_TAG from t_dict where FIELD_NAME = 'status_tag') tmp_status on tmp_repair.STATE_TAG = tmp_status.STATUS_TAG group by tmp_status.STATUS_TAG order by tmp_status.STATUS_TAG;;
2023-02-10 17:28:27.887 ziyun [http-nio-9528-exec-4] INFO  p6spy - 2023-02-10 17:28:27 | 耗时 212 ms | SQL 语句：
select COALESCE(count(STATE_TAG),0) value ,tmp_status.STATUS_TAG AS STATUS_TAG from (select REPAIR_ID,STATE_TAG,r.SWJGDM,BSDTDJXH,s.DISTRICT_ID,district from t_repair_info r JOIN t_swjg_info s ON r.SWJGDM = s.BSDTDJXH JOIN (SELECT d2.district_id,d2.district FROM t_district d1 JOIN t_district d2 ON d1.district_id = d2.pid /** 传入市的id */ WHERE d1.district_id = 1330600) all_districts on s.DISTRICT_ID = all_districts.district_id) tmp_repair RIGHT JOIN (select t_dict.KEYY AS STATUS_TAG from t_dict where FIELD_NAME = 'status_tag') tmp_status on tmp_repair.STATE_TAG = tmp_status.STATUS_TAG group by tmp_status.STATUS_TAG order by tmp_status.STATUS_TAG;;
2023-02-10 17:28:28.060 ziyun [http-nio-9528-exec-4] INFO  p6spy - 2023-02-10 17:28:28 | 耗时 180 ms | SQL 语句：
select COALESCE(count(STATE_TAG),0) value ,tmp_status.STATUS_TAG AS STATUS_TAG from (select REPAIR_ID,STATE_TAG,r.SWJGDM,BSDTDJXH,s.DISTRICT_ID,district from t_repair_info r JOIN t_swjg_info s ON r.SWJGDM = s.BSDTDJXH JOIN (SELECT d2.district_id,d2.district FROM t_district d1 JOIN t_district d2 ON d1.district_id = d2.pid /** 传入市的id */ WHERE d1.district_id = 1330700) all_districts on s.DISTRICT_ID = all_districts.district_id) tmp_repair RIGHT JOIN (select t_dict.KEYY AS STATUS_TAG from t_dict where FIELD_NAME = 'status_tag') tmp_status on tmp_repair.STATE_TAG = tmp_status.STATUS_TAG group by tmp_status.STATUS_TAG order by tmp_status.STATUS_TAG;;
2023-02-10 17:28:28.185 ziyun [http-nio-9528-exec-4] INFO  p6spy - 2023-02-10 17:28:28 | 耗时 108 ms | SQL 语句：
select COALESCE(count(STATE_TAG),0) value ,tmp_status.STATUS_TAG AS STATUS_TAG from (select REPAIR_ID,STATE_TAG,r.SWJGDM,BSDTDJXH,s.DISTRICT_ID,district from t_repair_info r JOIN t_swjg_info s ON r.SWJGDM = s.BSDTDJXH JOIN (SELECT d2.district_id,d2.district FROM t_district d1 JOIN t_district d2 ON d1.district_id = d2.pid /** 传入市的id */ WHERE d1.district_id = 1330800) all_districts on s.DISTRICT_ID = all_districts.district_id) tmp_repair RIGHT JOIN (select t_dict.KEYY AS STATUS_TAG from t_dict where FIELD_NAME = 'status_tag') tmp_status on tmp_repair.STATE_TAG = tmp_status.STATUS_TAG group by tmp_status.STATUS_TAG order by tmp_status.STATUS_TAG;;
2023-02-10 17:28:28.263 ziyun [http-nio-9528-exec-4] INFO  p6spy - 2023-02-10 17:28:28 | 耗时 91 ms | SQL 语句：
select COALESCE(count(STATE_TAG),0) value ,tmp_status.STATUS_TAG AS STATUS_TAG from (select REPAIR_ID,STATE_TAG,r.SWJGDM,BSDTDJXH,s.DISTRICT_ID,district from t_repair_info r JOIN t_swjg_info s ON r.SWJGDM = s.BSDTDJXH JOIN (SELECT d2.district_id,d2.district FROM t_district d1 JOIN t_district d2 ON d1.district_id = d2.pid /** 传入市的id */ WHERE d1.district_id = 1330900) all_districts on s.DISTRICT_ID = all_districts.district_id) tmp_repair RIGHT JOIN (select t_dict.KEYY AS STATUS_TAG from t_dict where FIELD_NAME = 'status_tag') tmp_status on tmp_repair.STATE_TAG = tmp_status.STATUS_TAG group by tmp_status.STATUS_TAG order by tmp_status.STATUS_TAG;;
2023-02-10 17:28:28.545 ziyun [http-nio-9528-exec-4] INFO  p6spy - 2023-02-10 17:28:28 | 耗时 279 ms | SQL 语句：
select COALESCE(count(STATE_TAG),0) value ,tmp_status.STATUS_TAG AS STATUS_TAG from (select REPAIR_ID,STATE_TAG,r.SWJGDM,BSDTDJXH,s.DISTRICT_ID,district from t_repair_info r JOIN t_swjg_info s ON r.SWJGDM = s.BSDTDJXH JOIN (SELECT d2.district_id,d2.district FROM t_district d1 JOIN t_district d2 ON d1.district_id = d2.pid /** 传入市的id */ WHERE d1.district_id = 1331000) all_districts on s.DISTRICT_ID = all_districts.district_id) tmp_repair RIGHT JOIN (select t_dict.KEYY AS STATUS_TAG from t_dict where FIELD_NAME = 'status_tag') tmp_status on tmp_repair.STATE_TAG = tmp_status.STATUS_TAG group by tmp_status.STATUS_TAG order by tmp_status.STATUS_TAG;;
2023-02-10 17:28:28.702 ziyun [http-nio-9528-exec-4] INFO  p6spy - 2023-02-10 17:28:28 | 耗时 143 ms | SQL 语句：
select COALESCE(count(STATE_TAG),0) value ,tmp_status.STATUS_TAG AS STATUS_TAG from (select REPAIR_ID,STATE_TAG,r.SWJGDM,BSDTDJXH,s.DISTRICT_ID,district from t_repair_info r JOIN t_swjg_info s ON r.SWJGDM = s.BSDTDJXH JOIN (SELECT d2.district_id,d2.district FROM t_district d1 JOIN t_district d2 ON d1.district_id = d2.pid /** 传入市的id */ WHERE d1.district_id = 1331100) all_districts on s.DISTRICT_ID = all_districts.district_id) tmp_repair RIGHT JOIN (select t_dict.KEYY AS STATUS_TAG from t_dict where FIELD_NAME = 'status_tag') tmp_status on tmp_repair.STATE_TAG = tmp_status.STATUS_TAG group by tmp_status.STATUS_TAG order by tmp_status.STATUS_TAG;;
2023-02-10 17:28:28.702 ziyun [http-nio-9528-exec-4] INFO  p6spy - 2023-02-10 17:28:28 | 耗时 10 ms | SQL 语句：
select COALESCE(count(STATE_TAG),0) value ,tmp_status.STATUS_TAG AS STATUS_TAG from (select REPAIR_ID,STATE_TAG,r.SWJGDM,BSDTDJXH,s.DISTRICT_ID,district from t_repair_info r JOIN t_swjg_info s ON r.SWJGDM = s.BSDTDJXH JOIN (SELECT d2.district_id,d2.district FROM t_district d1 JOIN t_district d2 ON d1.district_id = d2.pid /** 传入市的id */ WHERE d1.district_id = 1330091) all_districts on s.DISTRICT_ID = all_districts.district_id) tmp_repair RIGHT JOIN (select t_dict.KEYY AS STATUS_TAG from t_dict where FIELD_NAME = 'status_tag') tmp_status on tmp_repair.STATE_TAG = tmp_status.STATUS_TAG group by tmp_status.STATUS_TAG order by tmp_status.STATUS_TAG;;
2023-02-10 17:28:29.879 ziyun [http-nio-9528-exec-3] INFO  p6spy - 2023-02-10 17:28:29 | 耗时 2 ms | SQL 语句：
SELECT COUNT(*) as count FROM t_repair_info WHERE ADD_TIME >= DATE_FORMAT(CURDATE(), '%Y-%m-01') AND ADD_TIME <= NOW();;
2023-02-10 17:28:29.879 ziyun [http-nio-9528-exec-10] INFO  p6spy - 2023-02-10 17:28:29 | 耗时 2 ms | SQL 语句：
SELECT DATE_FORMAT(ADD_TIME, '%d') AS xAxis, count(*) as count FROM t_repair_info WHERE ADD_TIME >= DATE_FORMAT(CURDATE(), '%Y-%m-01') AND ADD_TIME <= CURDATE() GROUP BY xAxis ORDER BY xAxis;;
2023-02-10 17:28:29.879 ziyun [http-nio-9528-exec-2] INFO  p6spy - 2023-02-10 17:28:29 | 耗时 3 ms | SQL 语句：
SELECT d2.district_id,d2.district FROM t_district d1 JOIN t_district d2 ON d1.district_id = d2.pid WHERE d1.district_id = 1330000;
2023-02-10 17:28:29.879 ziyun [http-nio-9528-exec-3] INFO  p6spy - 2023-02-10 17:28:29 | 耗时 1 ms | SQL 语句：
SELECT COUNT(*) as count FROM t_repair_info WHERE ADD_TIME >= CURDATE() AND ADD_TIME <= NOW();;
2023-02-10 17:28:29.879 ziyun [http-nio-9528-exec-9] INFO  p6spy - 2023-02-10 17:28:29 | 耗时 2 ms | SQL 语句：
SELECT COUNT( 1 ) FROM t_repair_info WHERE TITLE = '0';
2023-02-10 17:28:29.879 ziyun [http-nio-9528-exec-8] INFO  p6spy - 2023-02-10 17:28:29 | 耗时 3 ms | SQL 语句：
SELECT district_id,pid,district,level FROM t_district;
2023-02-10 17:28:29.879 ziyun [http-nio-9528-exec-3] INFO  p6spy - 2023-02-10 17:28:29 | 耗时 1 ms | SQL 语句：
SELECT COUNT(*) as count FROM t_repair_info WHERE STATE_TAG = '0' and ADD_TIME >= CURDATE() AND ADD_TIME <= NOW();;
2023-02-10 17:28:29.879 ziyun [http-nio-9528-exec-9] INFO  p6spy - 2023-02-10 17:28:29 | 耗时 1 ms | SQL 语句：
SELECT COUNT( 1 ) FROM t_repair_info WHERE TITLE = '1';
2023-02-10 17:28:29.879 ziyun [http-nio-9528-exec-6] INFO  p6spy - 2023-02-10 17:28:29 | 耗时 1 ms | SQL 语句：
SELECT USER_ID,username,password,dept_id,email,mobile,status,create_time,modify_time,last_login_time,ssex,description,REAL_NAME,avatar FROM t_user WHERE dept_id = '2';
2023-02-10 17:28:29.879 ziyun [http-nio-9528-exec-1] INFO  p6spy - 2023-02-10 17:28:29 | 耗时 10 ms | SQL 语句：
/** 上门服务 */ (select COUNT(*) AS value, '上门处理' AS name from t_repair_info r where USER_ID = PROCESSOR_ID and r.STATE_TAG = 4) /** 电话指导 */ UNION (select COUNT(*) AS value, '电话指导处理' AS name from t_repair_info r JOIN t_user u on u.USER_ID = r.PROCESSOR_ID where r.PROCESSOR_ID = 174 and r.STATE_TAG = 4) /** 远程处理 */ UNION (select COUNT(*) AS value, '远程处理' AS name from t_repair_info r JOIN t_user u on u.USER_ID = r.PROCESSOR_ID where r.PROCESSOR_ID = 180 and r.STATE_TAG = 4) /** 代维处理 */ UNION (select COUNT(*) AS value, '代维处理' AS name from t_repair_info r JOIN t_user u on u.USER_ID = r.PROCESSOR_ID where r.PROCESSOR_ID = 181 and r.STATE_TAG = 4);;
2023-02-10 17:28:29.879 ziyun [http-nio-9528-exec-9] INFO  p6spy - 2023-02-10 17:28:29 | 耗时 1 ms | SQL 语句：
SELECT COUNT( 1 ) FROM t_repair_info WHERE TITLE = '2';
2023-02-10 17:28:29.894 ziyun [http-nio-9528-exec-9] INFO  p6spy - 2023-02-10 17:28:29 | 耗时 1 ms | SQL 语句：
SELECT COUNT( 1 ) FROM t_repair_info WHERE TITLE = '3';
2023-02-10 17:28:29.894 ziyun [http-nio-9528-exec-9] INFO  p6spy - 2023-02-10 17:28:29 | 耗时 1 ms | SQL 语句：
SELECT COUNT( 1 ) FROM t_repair_info WHERE TITLE = '4';
2023-02-10 17:28:29.894 ziyun [http-nio-9528-exec-9] INFO  p6spy - 2023-02-10 17:28:29 | 耗时 1 ms | SQL 语句：
SELECT COUNT( 1 ) FROM t_repair_info WHERE TITLE = '5';
2023-02-10 17:28:29.894 ziyun [http-nio-9528-exec-9] INFO  p6spy - 2023-02-10 17:28:29 | 耗时 1 ms | SQL 语句：
SELECT COUNT( 1 ) FROM t_repair_info WHERE TITLE = '6';
2023-02-10 17:28:29.894 ziyun [http-nio-9528-exec-9] INFO  p6spy - 2023-02-10 17:28:29 | 耗时 1 ms | SQL 语句：
SELECT COUNT( 1 ) FROM t_repair_info WHERE TITLE = '7';
2023-02-10 17:28:29.894 ziyun [http-nio-9528-exec-9] INFO  p6spy - 2023-02-10 17:28:29 | 耗时 1 ms | SQL 语句：
SELECT COUNT( 1 ) FROM t_repair_info WHERE TITLE = '99';
2023-02-10 17:28:29.910 ziyun [http-nio-9528-exec-9] INFO  c.z.r.r.c.RepairInfoController - [{name=取号机, value=931}, {name=评价器, value=930}, {name=LED屏, value=351}, {name=综合屏, value=168}, {name=高速球, value=43}, {name=监控半球, value=295}, {name=拾音器, value=22}, {name=硬盘录像机, value=32}, {name=其他, value=219}]
2023-02-10 17:28:30.144 ziyun [http-nio-9528-exec-2] INFO  p6spy - 2023-02-10 17:28:30 | 耗时 273 ms | SQL 语句：
select COALESCE(count(STATE_TAG),0) value ,tmp_status.STATUS_TAG AS STATUS_TAG from (select REPAIR_ID,STATE_TAG,r.SWJGDM,BSDTDJXH,s.DISTRICT_ID,district from t_repair_info r JOIN t_swjg_info s ON r.SWJGDM = s.BSDTDJXH JOIN (SELECT d2.district_id,d2.district FROM t_district d1 JOIN t_district d2 ON d1.district_id = d2.pid /** 传入市的id */ WHERE d1.district_id = 1330100) all_districts on s.DISTRICT_ID = all_districts.district_id) tmp_repair RIGHT JOIN (select t_dict.KEYY AS STATUS_TAG from t_dict where FIELD_NAME = 'status_tag') tmp_status on tmp_repair.STATE_TAG = tmp_status.STATUS_TAG group by tmp_status.STATUS_TAG order by tmp_status.STATUS_TAG;;
2023-02-10 17:28:30.457 ziyun [http-nio-9528-exec-2] INFO  p6spy - 2023-02-10 17:28:30 | 耗时 297 ms | SQL 语句：
select COALESCE(count(STATE_TAG),0) value ,tmp_status.STATUS_TAG AS STATUS_TAG from (select REPAIR_ID,STATE_TAG,r.SWJGDM,BSDTDJXH,s.DISTRICT_ID,district from t_repair_info r JOIN t_swjg_info s ON r.SWJGDM = s.BSDTDJXH JOIN (SELECT d2.district_id,d2.district FROM t_district d1 JOIN t_district d2 ON d1.district_id = d2.pid /** 传入市的id */ WHERE d1.district_id = 1330300) all_districts on s.DISTRICT_ID = all_districts.district_id) tmp_repair RIGHT JOIN (select t_dict.KEYY AS STATUS_TAG from t_dict where FIELD_NAME = 'status_tag') tmp_status on tmp_repair.STATE_TAG = tmp_status.STATUS_TAG group by tmp_status.STATUS_TAG order by tmp_status.STATUS_TAG;;
2023-02-10 17:28:30.739 ziyun [http-nio-9528-exec-2] INFO  p6spy - 2023-02-10 17:28:30 | 耗时 282 ms | SQL 语句：
select COALESCE(count(STATE_TAG),0) value ,tmp_status.STATUS_TAG AS STATUS_TAG from (select REPAIR_ID,STATE_TAG,r.SWJGDM,BSDTDJXH,s.DISTRICT_ID,district from t_repair_info r JOIN t_swjg_info s ON r.SWJGDM = s.BSDTDJXH JOIN (SELECT d2.district_id,d2.district FROM t_district d1 JOIN t_district d2 ON d1.district_id = d2.pid /** 传入市的id */ WHERE d1.district_id = 1330400) all_districts on s.DISTRICT_ID = all_districts.district_id) tmp_repair RIGHT JOIN (select t_dict.KEYY AS STATUS_TAG from t_dict where FIELD_NAME = 'status_tag') tmp_status on tmp_repair.STATE_TAG = tmp_status.STATUS_TAG group by tmp_status.STATUS_TAG order by tmp_status.STATUS_TAG;;
2023-02-10 17:28:30.895 ziyun [http-nio-9528-exec-2] INFO  p6spy - 2023-02-10 17:28:30 | 耗时 158 ms | SQL 语句：
select COALESCE(count(STATE_TAG),0) value ,tmp_status.STATUS_TAG AS STATUS_TAG from (select REPAIR_ID,STATE_TAG,r.SWJGDM,BSDTDJXH,s.DISTRICT_ID,district from t_repair_info r JOIN t_swjg_info s ON r.SWJGDM = s.BSDTDJXH JOIN (SELECT d2.district_id,d2.district FROM t_district d1 JOIN t_district d2 ON d1.district_id = d2.pid /** 传入市的id */ WHERE d1.district_id = 1330500) all_districts on s.DISTRICT_ID = all_districts.district_id) tmp_repair RIGHT JOIN (select t_dict.KEYY AS STATUS_TAG from t_dict where FIELD_NAME = 'status_tag') tmp_status on tmp_repair.STATE_TAG = tmp_status.STATUS_TAG group by tmp_status.STATUS_TAG order by tmp_status.STATUS_TAG;;
2023-02-10 17:28:31.099 ziyun [http-nio-9528-exec-2] INFO  p6spy - 2023-02-10 17:28:31 | 耗时 201 ms | SQL 语句：
select COALESCE(count(STATE_TAG),0) value ,tmp_status.STATUS_TAG AS STATUS_TAG from (select REPAIR_ID,STATE_TAG,r.SWJGDM,BSDTDJXH,s.DISTRICT_ID,district from t_repair_info r JOIN t_swjg_info s ON r.SWJGDM = s.BSDTDJXH JOIN (SELECT d2.district_id,d2.district FROM t_district d1 JOIN t_district d2 ON d1.district_id = d2.pid /** 传入市的id */ WHERE d1.district_id = 1330600) all_districts on s.DISTRICT_ID = all_districts.district_id) tmp_repair RIGHT JOIN (select t_dict.KEYY AS STATUS_TAG from t_dict where FIELD_NAME = 'status_tag') tmp_status on tmp_repair.STATE_TAG = tmp_status.STATUS_TAG group by tmp_status.STATUS_TAG order by tmp_status.STATUS_TAG;;
2023-02-10 17:28:31.271 ziyun [http-nio-9528-exec-2] INFO  p6spy - 2023-02-10 17:28:31 | 耗时 168 ms | SQL 语句：
select COALESCE(count(STATE_TAG),0) value ,tmp_status.STATUS_TAG AS STATUS_TAG from (select REPAIR_ID,STATE_TAG,r.SWJGDM,BSDTDJXH,s.DISTRICT_ID,district from t_repair_info r JOIN t_swjg_info s ON r.SWJGDM = s.BSDTDJXH JOIN (SELECT d2.district_id,d2.district FROM t_district d1 JOIN t_district d2 ON d1.district_id = d2.pid /** 传入市的id */ WHERE d1.district_id = 1330700) all_districts on s.DISTRICT_ID = all_districts.district_id) tmp_repair RIGHT JOIN (select t_dict.KEYY AS STATUS_TAG from t_dict where FIELD_NAME = 'status_tag') tmp_status on tmp_repair.STATE_TAG = tmp_status.STATUS_TAG group by tmp_status.STATUS_TAG order by tmp_status.STATUS_TAG;;
2023-02-10 17:28:31.364 ziyun [http-nio-9528-exec-2] INFO  p6spy - 2023-02-10 17:28:31 | 耗时 101 ms | SQL 语句：
select COALESCE(count(STATE_TAG),0) value ,tmp_status.STATUS_TAG AS STATUS_TAG from (select REPAIR_ID,STATE_TAG,r.SWJGDM,BSDTDJXH,s.DISTRICT_ID,district from t_repair_info r JOIN t_swjg_info s ON r.SWJGDM = s.BSDTDJXH JOIN (SELECT d2.district_id,d2.district FROM t_district d1 JOIN t_district d2 ON d1.district_id = d2.pid /** 传入市的id */ WHERE d1.district_id = 1330800) all_districts on s.DISTRICT_ID = all_districts.district_id) tmp_repair RIGHT JOIN (select t_dict.KEYY AS STATUS_TAG from t_dict where FIELD_NAME = 'status_tag') tmp_status on tmp_repair.STATE_TAG = tmp_status.STATUS_TAG group by tmp_status.STATUS_TAG order by tmp_status.STATUS_TAG;;
2023-02-10 17:28:31.461 ziyun [http-nio-9528-exec-2] INFO  p6spy - 2023-02-10 17:28:31 | 耗时 89 ms | SQL 语句：
select COALESCE(count(STATE_TAG),0) value ,tmp_status.STATUS_TAG AS STATUS_TAG from (select REPAIR_ID,STATE_TAG,r.SWJGDM,BSDTDJXH,s.DISTRICT_ID,district from t_repair_info r JOIN t_swjg_info s ON r.SWJGDM = s.BSDTDJXH JOIN (SELECT d2.district_id,d2.district FROM t_district d1 JOIN t_district d2 ON d1.district_id = d2.pid /** 传入市的id */ WHERE d1.district_id = 1330900) all_districts on s.DISTRICT_ID = all_districts.district_id) tmp_repair RIGHT JOIN (select t_dict.KEYY AS STATUS_TAG from t_dict where FIELD_NAME = 'status_tag') tmp_status on tmp_repair.STATE_TAG = tmp_status.STATUS_TAG group by tmp_status.STATUS_TAG order by tmp_status.STATUS_TAG;;
2023-02-10 17:28:31.727 ziyun [http-nio-9528-exec-2] INFO  p6spy - 2023-02-10 17:28:31 | 耗时 268 ms | SQL 语句：
select COALESCE(count(STATE_TAG),0) value ,tmp_status.STATUS_TAG AS STATUS_TAG from (select REPAIR_ID,STATE_TAG,r.SWJGDM,BSDTDJXH,s.DISTRICT_ID,district from t_repair_info r JOIN t_swjg_info s ON r.SWJGDM = s.BSDTDJXH JOIN (SELECT d2.district_id,d2.district FROM t_district d1 JOIN t_district d2 ON d1.district_id = d2.pid /** 传入市的id */ WHERE d1.district_id = 1331000) all_districts on s.DISTRICT_ID = all_districts.district_id) tmp_repair RIGHT JOIN (select t_dict.KEYY AS STATUS_TAG from t_dict where FIELD_NAME = 'status_tag') tmp_status on tmp_repair.STATE_TAG = tmp_status.STATUS_TAG group by tmp_status.STATUS_TAG order by tmp_status.STATUS_TAG;;
2023-02-10 17:28:31.867 ziyun [http-nio-9528-exec-2] INFO  p6spy - 2023-02-10 17:28:31 | 耗时 139 ms | SQL 语句：
select COALESCE(count(STATE_TAG),0) value ,tmp_status.STATUS_TAG AS STATUS_TAG from (select REPAIR_ID,STATE_TAG,r.SWJGDM,BSDTDJXH,s.DISTRICT_ID,district from t_repair_info r JOIN t_swjg_info s ON r.SWJGDM = s.BSDTDJXH JOIN (SELECT d2.district_id,d2.district FROM t_district d1 JOIN t_district d2 ON d1.district_id = d2.pid /** 传入市的id */ WHERE d1.district_id = 1331100) all_districts on s.DISTRICT_ID = all_districts.district_id) tmp_repair RIGHT JOIN (select t_dict.KEYY AS STATUS_TAG from t_dict where FIELD_NAME = 'status_tag') tmp_status on tmp_repair.STATE_TAG = tmp_status.STATUS_TAG group by tmp_status.STATUS_TAG order by tmp_status.STATUS_TAG;;
2023-02-10 17:28:31.883 ziyun [http-nio-9528-exec-2] INFO  p6spy - 2023-02-10 17:28:31 | 耗时 9 ms | SQL 语句：
select COALESCE(count(STATE_TAG),0) value ,tmp_status.STATUS_TAG AS STATUS_TAG from (select REPAIR_ID,STATE_TAG,r.SWJGDM,BSDTDJXH,s.DISTRICT_ID,district from t_repair_info r JOIN t_swjg_info s ON r.SWJGDM = s.BSDTDJXH JOIN (SELECT d2.district_id,d2.district FROM t_district d1 JOIN t_district d2 ON d1.district_id = d2.pid /** 传入市的id */ WHERE d1.district_id = 1330091) all_districts on s.DISTRICT_ID = all_districts.district_id) tmp_repair RIGHT JOIN (select t_dict.KEYY AS STATUS_TAG from t_dict where FIELD_NAME = 'status_tag') tmp_status on tmp_repair.STATE_TAG = tmp_status.STATUS_TAG group by tmp_status.STATUS_TAG order by tmp_status.STATUS_TAG;;
