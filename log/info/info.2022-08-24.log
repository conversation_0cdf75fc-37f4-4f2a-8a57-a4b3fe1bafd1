2022-08-24 15:24:45.614 ziyun [main] INFO  c.z.r.RepairSystemApplication - Starting RepairSystemApplication on DESKTOP-EAM764B with PID 19164 (started by karl in D:\project\Java Projectes\queuingsystem)
2022-08-24 15:24:45.617 ziyun [main] INFO  c.z.r.RepairSystemApplication - No active profile set, falling back to default profiles: default
2022-08-24 15:24:47.200 ziyun [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2022-08-24 15:24:47.203 ziyun [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data repositories in DEFAULT mode.
2022-08-24 15:24:47.303 ziyun [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 84ms. Found 0 repository interfaces.
2022-08-24 15:24:47.853 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$d23f652] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-08-24 15:24:48.045 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.datasource.dynamic-com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-08-24 15:24:48.048 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration$$EnhancerBySpringCGLIB$$76b26723] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-08-24 15:24:48.061 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dsProcessor' of type [com.baomidou.dynamic.datasource.processor.DsHeaderProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-08-24 15:24:48.067 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dynamicDatasourceAnnotationAdvisor' of type [com.baomidou.dynamic.datasource.aop.DynamicDataSourceAnnotationAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-08-24 15:24:48.067 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'shiroConfig' of type [com.ziyun.repairsystem.common.authentication.ShiroConfig$$EnhancerBySpringCGLIB$$615e3f3d] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-08-24 15:24:48.568 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'redisConfig' of type [com.ziyun.repairsystem.common.config.RedisConfig$$EnhancerBySpringCGLIB$$a1375891] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-08-24 15:24:48.585 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'redisPoolFactory' of type [redis.clients.jedis.JedisPool] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-08-24 15:24:48.589 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'redisService' of type [com.ziyun.repairsystem.common.service.impl.RedisServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-08-24 15:24:48.659 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'mybatis-plus-com.baomidou.mybatisplus.autoconfigure.MybatisPlusProperties' of type [com.baomidou.mybatisplus.autoconfigure.MybatisPlusProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-08-24 15:24:48.667 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'mybatisPlusConfig' of type [com.ziyun.repairsystem.common.config.MybatisPlusConfig$$EnhancerBySpringCGLIB$$9c728223] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-08-24 15:24:48.671 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'paginationInterceptor' of type [com.baomidou.mybatisplus.extension.plugins.PaginationInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-08-24 15:24:48.678 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration' of type [com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration$$EnhancerBySpringCGLIB$$4aec62ec] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-08-24 15:24:48.688 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dynamicDataSourceCreator' of type [com.baomidou.dynamic.datasource.DynamicDataSourceCreator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-08-24 15:24:48.691 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dynamicDataSourceProvider' of type [com.baomidou.dynamic.datasource.provider.YmlDynamicDataSourceProvider] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-08-24 15:24:48.694 ziyun [main] INFO  c.b.d.d.DynamicRoutingDataSource - 动态数据源-检测到并开启了p6spy
2022-08-24 15:24:48.737 ziyun [main] INFO  com.zaxxer.hikari.HikariDataSource - primary - Starting...
2022-08-24 15:24:49.230 ziyun [main] INFO  com.zaxxer.hikari.HikariDataSource - primary - Start completed.
2022-08-24 15:24:49.230 ziyun [main] INFO  c.b.d.d.DynamicRoutingDataSource - 初始共加载 1 个数据源
2022-08-24 15:24:49.230 ziyun [main] INFO  c.b.d.d.DynamicRoutingDataSource - 动态数据源-加载 primary 成功
2022-08-24 15:24:49.230 ziyun [main] INFO  c.b.d.d.DynamicRoutingDataSource - 当前的默认数据源是单数据源，数据源名为 primary
2022-08-24 15:24:49.231 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dataSource' of type [com.baomidou.dynamic.datasource.DynamicRoutingDataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-08-24 15:24:49.246 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.datasource-org.springframework.boot.autoconfigure.jdbc.DataSourceProperties' of type [org.springframework.boot.autoconfigure.jdbc.DataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-08-24 15:24:49.255 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.jdbc.DataSourceInitializerInvoker' of type [org.springframework.boot.autoconfigure.jdbc.DataSourceInitializerInvoker] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-08-24 15:24:50.004 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'sqlSessionFactory' of type [org.apache.ibatis.session.defaults.DefaultSqlSessionFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-08-24 15:24:50.011 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'sqlSessionTemplate' of type [org.mybatis.spring.SqlSessionTemplate] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-08-24 15:24:50.015 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'roleMapper' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-08-24 15:24:50.017 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'roleMapper' of type [com.sun.proxy.$Proxy114] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-08-24 15:24:50.048 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'roleMenuMapper' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-08-24 15:24:50.049 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'roleMenuMapper' of type [com.sun.proxy.$Proxy116] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-08-24 15:24:50.058 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userRoleMapper' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-08-24 15:24:50.059 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userRoleMapper' of type [com.sun.proxy.$Proxy117] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-08-24 15:24:50.066 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userRoleService' of type [com.ziyun.repairsystem.system.service.impl.UserRoleServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-08-24 15:24:50.104 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'roleMenuService' of type [com.ziyun.repairsystem.system.service.impl.RoleMenuServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-08-24 15:24:50.121 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'roleService' of type [com.ziyun.repairsystem.system.service.impl.RoleServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-08-24 15:24:50.149 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'menuMapper' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-08-24 15:24:50.150 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'menuMapper' of type [com.sun.proxy.$Proxy121] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-08-24 15:24:50.155 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'menuService' of type [com.ziyun.repairsystem.system.service.impl.MenuServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-08-24 15:24:50.176 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userMapper' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-08-24 15:24:50.177 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userMapper' of type [com.sun.proxy.$Proxy123] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-08-24 15:24:50.211 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userConfigMapper' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-08-24 15:24:50.212 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userConfigMapper' of type [com.sun.proxy.$Proxy124] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-08-24 15:24:50.217 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userConfigService' of type [com.ziyun.repairsystem.system.service.impl.UserConfigServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-08-24 15:24:50.233 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userService' of type [com.ziyun.repairsystem.system.service.impl.UserServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-08-24 15:24:50.257 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperConfiguration' of type [org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperConfiguration$$EnhancerBySpringCGLIB$$68b775e3] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-08-24 15:24:50.261 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperBuilderConfiguration' of type [org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperBuilderConfiguration$$EnhancerBySpringCGLIB$$1b0437fc] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-08-24 15:24:50.264 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$Jackson2ObjectMapperBuilderCustomizerConfiguration' of type [org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$Jackson2ObjectMapperBuilderCustomizerConfiguration$$EnhancerBySpringCGLIB$$d61b1deb] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-08-24 15:24:50.271 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.jackson-org.springframework.boot.autoconfigure.jackson.JacksonProperties' of type [org.springframework.boot.autoconfigure.jackson.JacksonProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-08-24 15:24:50.274 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'standardJacksonObjectMapperBuilderCustomizer' of type [org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$Jackson2ObjectMapperBuilderCustomizerConfiguration$StandardJackson2ObjectMapperBuilderCustomizer] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-08-24 15:24:50.280 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$ParameterNamesModuleConfiguration' of type [org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$ParameterNamesModuleConfiguration$$EnhancerBySpringCGLIB$$fce69519] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-08-24 15:24:50.285 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'parameterNamesModule' of type [com.fasterxml.jackson.module.paramnames.ParameterNamesModule] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-08-24 15:24:50.287 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration' of type [org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$$EnhancerBySpringCGLIB$$4ac31372] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-08-24 15:24:50.297 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'jsonComponentModule' of type [org.springframework.boot.jackson.JsonComponentModule] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-08-24 15:24:50.299 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.data.web.config.SpringDataJacksonConfiguration' of type [org.springframework.data.web.config.SpringDataJacksonConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-08-24 15:24:50.304 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'jacksonGeoModule' of type [org.springframework.data.geo.GeoModule] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-08-24 15:24:50.307 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'jacksonObjectMapperBuilder' of type [org.springframework.http.converter.json.Jackson2ObjectMapperBuilder] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-08-24 15:24:50.333 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'jacksonObjectMapper' of type [com.fasterxml.jackson.databind.ObjectMapper] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-08-24 15:24:50.352 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'cacheService' of type [com.ziyun.repairsystem.common.service.impl.CacheServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-08-24 15:24:50.359 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userManager' of type [com.ziyun.repairsystem.system.manager.UserManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-08-24 15:24:50.359 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'shiroRealm' of type [com.ziyun.repairsystem.common.authentication.ShiroRealm] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-08-24 15:24:50.369 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'securityManager' of type [org.apache.shiro.web.mgt.DefaultWebSecurityManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-08-24 15:24:50.382 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'authorizationAttributeSourceAdvisor' of type [org.apache.shiro.spring.security.interceptor.AuthorizationAttributeSourceAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-08-24 15:24:50.710 ziyun [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9528 (http)
2022-08-24 15:24:50.801 ziyun [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9528"]
2022-08-24 15:24:50.812 ziyun [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2022-08-24 15:24:50.812 ziyun [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.19]
2022-08-24 15:24:50.964 ziyun [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2022-08-24 15:24:50.964 ziyun [main] INFO  o.s.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 5304 ms
2022-08-24 15:24:51.724 ziyun [main] INFO  org.quartz.impl.StdSchedulerFactory - Using default implementation for ThreadExecutor
2022-08-24 15:24:51.737 ziyun [main] INFO  o.quartz.core.SchedulerSignalerImpl - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2022-08-24 15:24:51.737 ziyun [main] INFO  org.quartz.core.QuartzScheduler - Quartz Scheduler v.2.3.1 created.
2022-08-24 15:24:51.740 ziyun [main] INFO  o.s.s.quartz.LocalDataSourceJobStore - Using db table-based data access locking (synchronization).
2022-08-24 15:24:51.742 ziyun [main] INFO  o.s.s.quartz.LocalDataSourceJobStore - JobStoreCMT initialized.
2022-08-24 15:24:51.743 ziyun [main] INFO  org.quartz.core.QuartzScheduler - Scheduler meta-data: Quartz Scheduler (v2.3.1) 'MyScheduler' with instanceId 'DESKTOP-EAM764B1661325891726'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 20 threads.
  Using job-store 'org.springframework.scheduling.quartz.LocalDataSourceJobStore' - which supports persistence. and is clustered.

2022-08-24 15:24:51.743 ziyun [main] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler 'MyScheduler' initialized from an externally provided properties instance.
2022-08-24 15:24:51.743 ziyun [main] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler version: 2.3.1
2022-08-24 15:24:51.744 ziyun [main] INFO  org.quartz.core.QuartzScheduler - JobFactory set to: org.springframework.scheduling.quartz.AdaptableJobFactory@13835b6
2022-08-24 15:24:51.799 ziyun [main] INFO  p6spy - 2022-08-24 15:24:51 | 耗时 11 ms | SQL 语句：
select job_id jobId, bean_name beanName, method_name methodName, params, cron_expression cronExpression, status, remark, create_time createTime from t_job order by job_id;
2022-08-24 15:24:52.731 ziyun [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 2 endpoint(s) beneath base path '/actuator'
2022-08-24 15:24:52.851 ziyun [main] INFO  o.s.s.c.ThreadPoolTaskExecutor - Initializing ExecutorService 'taskExecutor'
2022-08-24 15:24:52.891 ziyun [main] INFO  s.d.s.w.PropertySourcedRequestMappingHandlerMapping - Mapped URL path [/v2/api-docs] onto method [public org.springframework.http.ResponseEntity<springfox.documentation.spring.web.json.Json> springfox.documentation.swagger2.web.Swagger2Controller.getDocumentation(java.lang.String,javax.servlet.http.HttpServletRequest)]
2022-08-24 15:24:53.754 ziyun [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Context refreshed
2022-08-24 15:24:53.789 ziyun [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
2022-08-24 15:24:53.838 ziyun [main] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
2022-08-24 15:24:54.011 ziyun [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_1
2022-08-24 15:24:54.025 ziyun [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_2
2022-08-24 15:24:54.038 ziyun [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_3
2022-08-24 15:24:54.046 ziyun [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_4
2022-08-24 15:24:54.065 ziyun [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_5
2022-08-24 15:24:54.120 ziyun [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: addRepairInfoUsingPOST_1
2022-08-24 15:24:54.125 ziyun [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_6
2022-08-24 15:24:54.137 ziyun [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_7
2022-08-24 15:24:54.149 ziyun [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_8
2022-08-24 15:24:54.153 ziyun [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: querySwjgRoUsingGET_1
2022-08-24 15:24:54.162 ziyun [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_9
2022-08-24 15:24:54.183 ziyun [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_10
2022-08-24 15:24:54.220 ziyun [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-9528"]
2022-08-24 15:24:54.244 ziyun [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 9528 (http) with context path ''
2022-08-24 15:24:54.247 ziyun [main] INFO  c.z.r.RepairSystemApplication - Started RepairSystemApplication in 9.255 seconds (JVM running for 16.67)
2022-08-24 15:24:54.263 ziyun [main] INFO  c.z.r.common.runner.CacheInitRunner - Redis连接中 ······
2022-08-24 15:24:54.283 ziyun [main] INFO  c.z.r.common.runner.CacheInitRunner - 缓存初始化 ······
2022-08-24 15:24:54.283 ziyun [main] INFO  c.z.r.common.runner.CacheInitRunner - 缓存用户数据 ······
2022-08-24 15:24:54.350 ziyun [main] INFO  p6spy - 2022-08-24 15:24:54 | 耗时 10 ms | SQL 语句：
SELECT USER_ID,username,password,dept_id,email,mobile,status,create_time,modify_time,last_login_time,ssex,description,REAL_NAME,avatar FROM t_user;
2022-08-24 15:24:54.368 ziyun [main] INFO  p6spy - 2022-08-24 15:24:54 | 耗时 12 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = 'ziyun' group by u.username;
2022-08-24 15:24:54.435 ziyun [main] INFO  p6spy - 2022-08-24 15:24:54 | 耗时 10 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = 'ziyun';
2022-08-24 15:24:54.453 ziyun [main] INFO  p6spy - 2022-08-24 15:24:54 | 耗时 10 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = 'ziyun' and m.perms is not null and m.perms <> '';
2022-08-24 15:24:54.468 ziyun [main] INFO  p6spy - 2022-08-24 15:24:54 | 耗时 8 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='166' ;
2022-08-24 15:24:54.483 ziyun [main] INFO  p6spy - 2022-08-24 15:24:54 | 耗时 12 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = 'test123' group by u.username;
2022-08-24 15:24:54.495 ziyun [main] INFO  p6spy - 2022-08-24 15:24:54 | 耗时 9 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = 'test123';
2022-08-24 15:24:54.510 ziyun [main] INFO  p6spy - 2022-08-24 15:24:54 | 耗时 13 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = 'test123' and m.perms is not null and m.perms <> '';
2022-08-24 15:24:54.526 ziyun [main] INFO  p6spy - 2022-08-24 15:24:54 | 耗时 15 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='167' ;
2022-08-24 15:24:54.538 ziyun [main] INFO  p6spy - 2022-08-24 15:24:54 | 耗时 10 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = 'yangyang' group by u.username;
2022-08-24 15:24:54.550 ziyun [main] INFO  p6spy - 2022-08-24 15:24:54 | 耗时 9 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = 'yangyang';
2022-08-24 15:24:54.563 ziyun [main] INFO  p6spy - 2022-08-24 15:24:54 | 耗时 10 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = 'yangyang' and m.perms is not null and m.perms <> '';
2022-08-24 15:24:54.572 ziyun [main] INFO  p6spy - 2022-08-24 15:24:54 | 耗时 7 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='171' ;
2022-08-24 15:24:54.584 ziyun [main] INFO  p6spy - 2022-08-24 15:24:54 | 耗时 10 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = '15858214004' group by u.username;
2022-08-24 15:24:54.597 ziyun [main] INFO  p6spy - 2022-08-24 15:24:54 | 耗时 9 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = '15858214004';
2022-08-24 15:24:54.609 ziyun [main] INFO  p6spy - 2022-08-24 15:24:54 | 耗时 9 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = '15858214004' and m.perms is not null and m.perms <> '';
2022-08-24 15:24:54.618 ziyun [main] INFO  p6spy - 2022-08-24 15:24:54 | 耗时 8 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='172' ;
2022-08-24 15:24:54.630 ziyun [main] INFO  p6spy - 2022-08-24 15:24:54 | 耗时 10 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = 'realwjy001' group by u.username;
2022-08-24 15:24:54.651 ziyun [main] INFO  p6spy - 2022-08-24 15:24:54 | 耗时 8 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = 'realwjy001';
2022-08-24 15:24:54.660 ziyun [main] INFO  p6spy - 2022-08-24 15:24:54 | 耗时 7 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = 'realwjy001' and m.perms is not null and m.perms <> '';
2022-08-24 15:24:54.671 ziyun [main] INFO  p6spy - 2022-08-24 15:24:54 | 耗时 9 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='174' ;
2022-08-24 15:24:54.680 ziyun [main] INFO  p6spy - 2022-08-24 15:24:54 | 耗时 7 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = '15067108130' group by u.username;
2022-08-24 15:24:54.691 ziyun [main] INFO  p6spy - 2022-08-24 15:24:54 | 耗时 7 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = '15067108130';
2022-08-24 15:24:54.703 ziyun [main] INFO  p6spy - 2022-08-24 15:24:54 | 耗时 10 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = '15067108130' and m.perms is not null and m.perms <> '';
2022-08-24 15:24:54.715 ziyun [main] INFO  p6spy - 2022-08-24 15:24:54 | 耗时 9 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='175' ;
2022-08-24 15:24:54.728 ziyun [main] INFO  p6spy - 2022-08-24 15:24:54 | 耗时 11 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = '15755998467' group by u.username;
2022-08-24 15:24:54.739 ziyun [main] INFO  p6spy - 2022-08-24 15:24:54 | 耗时 7 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = '15755998467';
2022-08-24 15:24:54.750 ziyun [main] INFO  p6spy - 2022-08-24 15:24:54 | 耗时 8 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = '15755998467' and m.perms is not null and m.perms <> '';
2022-08-24 15:24:54.759 ziyun [main] INFO  p6spy - 2022-08-24 15:24:54 | 耗时 7 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='176' ;
2022-08-24 15:24:54.771 ziyun [main] INFO  p6spy - 2022-08-24 15:24:54 | 耗时 9 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = 'qita' group by u.username;
2022-08-24 15:24:54.784 ziyun [main] INFO  p6spy - 2022-08-24 15:24:54 | 耗时 9 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = 'qita';
2022-08-24 15:24:54.794 ziyun [main] INFO  p6spy - 2022-08-24 15:24:54 | 耗时 8 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = 'qita' and m.perms is not null and m.perms <> '';
2022-08-24 15:24:54.805 ziyun [main] INFO  p6spy - 2022-08-24 15:24:54 | 耗时 8 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='177' ;
2022-08-24 15:24:54.817 ziyun [main] INFO  p6spy - 2022-08-24 15:24:54 | 耗时 10 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = '15869119455' group by u.username;
2022-08-24 15:24:54.830 ziyun [main] INFO  p6spy - 2022-08-24 15:24:54 | 耗时 9 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = '15869119455';
2022-08-24 15:24:54.842 ziyun [main] INFO  p6spy - 2022-08-24 15:24:54 | 耗时 9 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = '15869119455' and m.perms is not null and m.perms <> '';
2022-08-24 15:24:54.853 ziyun [main] INFO  p6spy - 2022-08-24 15:24:54 | 耗时 9 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='178' ;
2022-08-24 15:24:54.867 ziyun [main] INFO  p6spy - 2022-08-24 15:24:54 | 耗时 13 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = '15868125270' group by u.username;
2022-08-24 15:24:54.883 ziyun [main] INFO  p6spy - 2022-08-24 15:24:54 | 耗时 12 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = '15868125270';
2022-08-24 15:24:54.893 ziyun [main] INFO  p6spy - 2022-08-24 15:24:54 | 耗时 9 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = '15868125270' and m.perms is not null and m.perms <> '';
2022-08-24 15:24:54.904 ziyun [main] INFO  p6spy - 2022-08-24 15:24:54 | 耗时 9 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='179' ;
2022-08-24 15:24:54.916 ziyun [main] INFO  p6spy - 2022-08-24 15:24:54 | 耗时 9 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = 'wangliwei' group by u.username;
2022-08-24 15:24:54.927 ziyun [main] INFO  p6spy - 2022-08-24 15:24:54 | 耗时 8 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = 'wangliwei';
2022-08-24 15:24:54.944 ziyun [main] INFO  p6spy - 2022-08-24 15:24:54 | 耗时 10 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = 'wangliwei' and m.perms is not null and m.perms <> '';
2022-08-24 15:24:54.962 ziyun [main] INFO  p6spy - 2022-08-24 15:24:54 | 耗时 8 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='180' ;
2022-08-24 15:24:54.964 ziyun [main] INFO  c.z.r.common.runner.StartedUpRunner -  __    ___   _      ___   _     ____ _____  ____ 
2022-08-24 15:24:54.964 ziyun [main] INFO  c.z.r.common.runner.StartedUpRunner - / /`  / / \ | |\/| | |_) | |   | |_   | |  | |_  
2022-08-24 15:24:54.964 ziyun [main] INFO  c.z.r.common.runner.StartedUpRunner - \_\_, \_\_/ |_|  | |_|   |_|__ |_|__  |_|  |_|__ 
2022-08-24 15:24:54.964 ziyun [main] INFO  c.z.r.common.runner.StartedUpRunner -                                                       
2022-08-24 15:24:54.964 ziyun [main] INFO  c.z.r.common.runner.StartedUpRunner - ZiYun 启动完毕，时间：2022-08-24T15:24:54.964
2022-08-24 15:25:21.893 ziyun [http-nio-9528-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2022-08-24 15:25:21.893 ziyun [http-nio-9528-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2022-08-24 15:25:21.905 ziyun [http-nio-9528-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 12 ms
2022-08-24 15:25:21.941 ziyun [http-nio-9528-exec-1] INFO  c.z.r.w.C.MobileWebController - 杨华平
2022-08-24 15:25:22.007 ziyun [http-nio-9528-exec-1] INFO  p6spy - 2022-08-24 15:25:22 | 耗时 29 ms | SQL 语句：
SELECT REPAIR_ID,NAME,MOBILE,TITLE,ADDRESS,STATE_TAG,REPAIR_NUMBER,ADD_TIME,REVIEW_TIME,DEAL_TIME,PARTIAL_COMPLETION_TIME,COMPLETION_TIME,CONT,IMG,REVIEWS,HANDLE_CONT,RECEIPT,SWJGDM,BSDTMC,USER_ID,REAL_NAME FROM t_repair_info WHERE NAME = '杨华平' AND ADD_TIME >= '2022-08-01 00:00:001900-01-01 00:00:00' AND ADD_TIME <= '2022-08-25 00:00:002900-01-01 00:00:00';
2022-08-24 15:29:00.281 ziyun [http-nio-9528-exec-5] INFO  c.z.r.w.C.MobileWebController - 杨华平
2022-08-24 15:29:00.307 ziyun [http-nio-9528-exec-5] INFO  p6spy - 2022-08-24 15:29:00 | 耗时 14 ms | SQL 语句：
SELECT REPAIR_ID,NAME,MOBILE,TITLE,ADDRESS,STATE_TAG,REPAIR_NUMBER,ADD_TIME,REVIEW_TIME,DEAL_TIME,PARTIAL_COMPLETION_TIME,COMPLETION_TIME,CONT,IMG,REVIEWS,HANDLE_CONT,RECEIPT,SWJGDM,BSDTMC,USER_ID,REAL_NAME FROM t_repair_info WHERE NAME = '杨华平' AND ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00';
2022-08-24 15:29:20.612 ziyun [Thread-3] INFO  o.s.s.c.ThreadPoolTaskExecutor - Shutting down ExecutorService 'taskExecutor'
2022-08-24 15:29:20.614 ziyun [Thread-3] INFO  o.s.s.quartz.SchedulerFactoryBean - Shutting down Quartz Scheduler
2022-08-24 15:29:20.614 ziyun [Thread-3] INFO  org.quartz.core.QuartzScheduler - Scheduler MyScheduler_$_DESKTOP-EAM764B1661325891726 shutting down.
2022-08-24 15:29:20.614 ziyun [Thread-3] INFO  org.quartz.core.QuartzScheduler - Scheduler MyScheduler_$_DESKTOP-EAM764B1661325891726 paused.
2022-08-24 15:29:20.615 ziyun [Thread-3] INFO  org.quartz.core.QuartzScheduler - Scheduler MyScheduler_$_DESKTOP-EAM764B1661325891726 shutdown complete.
2022-08-24 15:29:20.623 ziyun [Thread-3] INFO  c.b.d.d.DynamicRoutingDataSource - closing dynamicDatasource  ing....
2022-08-24 15:29:20.624 ziyun [Thread-3] INFO  com.zaxxer.hikari.HikariDataSource - primary - Shutdown initiated...
2022-08-24 15:29:20.628 ziyun [Thread-3] INFO  com.zaxxer.hikari.HikariDataSource - primary - Shutdown completed.
