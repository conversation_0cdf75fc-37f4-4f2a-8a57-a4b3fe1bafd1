2025-07-16 15:50:48.455 [main] INFO  com.qs.admin.QscAdminApplication - Starting QscAdminApplication on MSI with PID 81708 (started by <PERSON><PERSON><PERSON> in D:\project\Java Projectes\qsadmin)
2025-07-16 15:50:48.457 [main] INFO  com.qs.admin.QscAdminApplication - The following profiles are active: oceanbase
2025-07-16 15:50:50.356 [main] WARN  io.undertow.websockets.jsr - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
2025-07-16 15:50:50.371 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-16 15:50:50.565 [main] INFO  c.q.a.common.config.FileUploadConfig - 检测到本机IP地址: **************
2025-07-16 15:50:50.565 [main] INFO  c.q.a.common.config.FileUploadConfig - 文件上传服务初始化完成，基础URL: http://**************:8823
2025-07-16 15:50:50.734 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-16 15:50:51.055 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-16 15:50:51.223 [main] WARN  c.b.m.core.injector.AbstractMethod - [com.qs.admin.taxhall.mapper.AgentInfoEnterpriseMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-07-16 15:50:51.230 [main] WARN  c.b.m.core.injector.AbstractMethod - [com.qs.admin.taxhall.mapper.AgentInfoEnterpriseMapper.selectById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectById]
2025-07-16 15:50:51.235 [main] WARN  c.b.m.core.injector.AbstractMethod - [com.qs.admin.taxhall.mapper.AgentInfoEnterpriseMapper.selectList] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectList]
2025-07-16 15:50:51.487 [main] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.qs.admin.taxhall.model.WindowBusiness".
2025-07-16 15:50:51.546 [main] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.qs.admin.taxhall.model.vo.WindowBusinessVO".
2025-07-16 15:50:51.807 [main] INFO  c.q.a.t.s.impl.BaiShuiApiServiceImpl - 获取佰税API访问令牌
2025-07-16 15:50:51.810 [main] INFO  c.q.a.t.s.impl.BaiShuiApiServiceImpl - tokenUrl=https://etax.zhejiang.chinatax.gov.cn/nsfw/apiService/oauth/token
2025-07-16 15:50:52.220 [main] INFO  c.q.a.t.s.impl.BaiShuiApiServiceImpl - 成功获取佰税API访问令牌
2025-07-16 15:50:52.280 [main] DEBUG c.q.a.t.m.SystemMapper.selectOne - ==>  Preparing: SELECT `KEY`,`VALUE`,`MEMO` FROM system WHERE (`KEY` = ?)
2025-07-16 15:50:52.296 [main] DEBUG c.q.a.t.m.SystemMapper.selectOne - ==> Parameters: BS_API_APPKEY(String)
2025-07-16 15:50:52.345 [main] DEBUG c.q.a.t.m.SystemMapper.selectOne - <==      Total: 1
2025-07-16 15:50:52.346 [main] DEBUG c.q.a.t.m.SystemMapper.selectOne - ==>  Preparing: SELECT `KEY`,`VALUE`,`MEMO` FROM system WHERE (`KEY` = ?)
2025-07-16 15:50:52.346 [main] DEBUG c.q.a.t.m.SystemMapper.selectOne - ==> Parameters: BS_API_TOKEN(String)
2025-07-16 15:50:52.369 [main] DEBUG c.q.a.t.m.SystemMapper.selectOne - <==      Total: 1
2025-07-16 15:50:52.372 [main] DEBUG c.q.a.t.mapper.SystemMapper.update - ==>  Preparing: UPDATE system SET `VALUE`=?, `MEMO`=? WHERE (`KEY` = ?)
2025-07-16 15:50:52.372 [main] DEBUG c.q.a.t.mapper.SystemMapper.update - ==> Parameters: QmVhcmVyIGV5SmhiR2NpT2lKSVV6STFOaUo5LmV5SmhjSEJyWlhraU9pSklXa0pUU2toWVZDSXNJbVY0Y0NJNk1UYzROREU0T0RJMU0zMC5XQ0RWNFp5S0x6a0I3MFFneDM2TGNSOTE0eXdLZWN5dVZtLV8zczJwY2hz(String), 佰税科技API授权令牌，自动同步于Wed Jul 16 15:50:52 CST 2025(String), BS_API_TOKEN(String)
2025-07-16 15:50:52.423 [main] DEBUG c.q.a.t.mapper.SystemMapper.update - <==    Updates: 1
2025-07-16 15:50:52.423 [main] INFO  c.q.a.t.task.BaiShuiTokenSyncTask - TOKEN已同步更新
2025-07-16 15:50:53.388 [main] INFO  io.undertow - starting server: Undertow - 2.0.27.Final
2025-07-16 15:50:53.392 [main] INFO  org.xnio - XNIO version 3.3.8.Final
2025-07-16 15:50:53.397 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.3.8.Final
2025-07-16 15:50:53.436 [main] INFO  com.qs.admin.QscAdminApplication - Started QscAdminApplication in 5.391 seconds (JVM running for 10.835)
2025-07-16 21:47:44.362 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-07-16 21:47:44.366 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-07-16 21:47:44.369 [SpringContextShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.0.27.Final
