2025-06-08 00:13:30.231 [HikariPool-1 connection adder] WARN  c.m.s.jdbc.internals.TDS.Channel - TLSv1 was negotiated. Please update server and client to use TLSv1.2 at minimum.
2025-06-08 00:13:34.603 [HikariPool-1 connection adder] WARN  c.m.s.jdbc.internals.TDS.Channel - TLSv1 was negotiated. Please update server and client to use TLSv1.2 at minimum.
2025-06-08 00:13:38.194 [HikariPool-1 connection adder] WARN  c.m.s.jdbc.internals.TDS.Channel - TLSv1 was negotiated. Please update server and client to use TLSv1.2 at minimum.
2025-06-08 00:13:47.313 [HikariPool-1 connection adder] WARN  c.m.s.jdbc.internals.TDS.Channel - TLSv1 was negotiated. Please update server and client to use TLSv1.2 at minimum.
2025-06-08 00:13:51.708 [HikariPool-1 connection adder] WARN  c.m.s.jdbc.internals.TDS.Channel - TLSv1 was negotiated. Please update server and client to use TLSv1.2 at minimum.
2025-06-08 00:13:57.034 [HikariPool-1 connection adder] WARN  c.m.s.jdbc.internals.TDS.Channel - TLSv1 was negotiated. Please update server and client to use TLSv1.2 at minimum.
2025-06-08 00:13:57.243 [HikariPool-1 connection adder] WARN  c.m.s.jdbc.internals.TDS.Channel - TLSv1 was negotiated. Please update server and client to use TLSv1.2 at minimum.
2025-06-08 00:14:01.261 [HikariPool-1 connection adder] WARN  c.m.s.jdbc.internals.TDS.Channel - TLSv1 was negotiated. Please update server and client to use TLSv1.2 at minimum.
2025-06-08 00:14:05.976 [HikariPool-1 connection adder] WARN  c.m.s.jdbc.internals.TDS.Channel - TLSv1 was negotiated. Please update server and client to use TLSv1.2 at minimum.
2025-06-08 00:14:20.646 [HikariPool-1 connection adder] WARN  c.m.s.jdbc.internals.TDS.Channel - TLSv1 was negotiated. Please update server and client to use TLSv1.2 at minimum.
2025-06-08 00:43:20.591 [HikariPool-1 connection adder] WARN  c.m.s.jdbc.internals.TDS.Channel - TLSv1 was negotiated. Please update server and client to use TLSv1.2 at minimum.
2025-06-08 00:43:20.922 [HikariPool-1 connection adder] WARN  c.m.s.jdbc.internals.TDS.Channel - TLSv1 was negotiated. Please update server and client to use TLSv1.2 at minimum.
2025-06-08 00:43:23.543 [HikariPool-1 connection adder] WARN  c.m.s.jdbc.internals.TDS.Channel - TLSv1 was negotiated. Please update server and client to use TLSv1.2 at minimum.
2025-06-08 00:43:30.035 [HikariPool-1 connection adder] WARN  c.m.s.jdbc.internals.TDS.Channel - TLSv1 was negotiated. Please update server and client to use TLSv1.2 at minimum.
2025-06-08 00:43:42.231 [HikariPool-1 connection adder] WARN  c.m.s.jdbc.internals.TDS.Channel - TLSv1 was negotiated. Please update server and client to use TLSv1.2 at minimum.
2025-06-08 00:43:44.881 [HikariPool-1 connection adder] WARN  c.m.s.jdbc.internals.TDS.Channel - TLSv1 was negotiated. Please update server and client to use TLSv1.2 at minimum.
2025-06-08 00:43:45.351 [HikariPool-1 connection adder] WARN  c.m.s.jdbc.internals.TDS.Channel - TLSv1 was negotiated. Please update server and client to use TLSv1.2 at minimum.
2025-06-08 00:43:45.428 [HikariPool-1 connection adder] WARN  c.m.s.jdbc.internals.TDS.Channel - TLSv1 was negotiated. Please update server and client to use TLSv1.2 at minimum.
2025-06-08 00:43:47.569 [HikariPool-1 connection adder] WARN  c.m.s.jdbc.internals.TDS.Channel - TLSv1 was negotiated. Please update server and client to use TLSv1.2 at minimum.
2025-06-08 00:44:09.302 [HikariPool-1 connection adder] WARN  c.m.s.jdbc.internals.TDS.Channel - TLSv1 was negotiated. Please update server and client to use TLSv1.2 at minimum.
2025-06-08 01:12:39.132 [HikariPool-1 connection adder] WARN  c.m.s.jdbc.internals.TDS.Channel - TLSv1 was negotiated. Please update server and client to use TLSv1.2 at minimum.
2025-06-08 01:13:08.574 [HikariPool-1 connection adder] WARN  c.m.s.jdbc.internals.TDS.Channel - TLSv1 was negotiated. Please update server and client to use TLSv1.2 at minimum.
2025-06-08 01:13:12.277 [HikariPool-1 connection adder] WARN  c.m.s.jdbc.internals.TDS.Channel - TLSv1 was negotiated. Please update server and client to use TLSv1.2 at minimum.
2025-06-08 01:13:12.628 [HikariPool-1 connection adder] WARN  c.m.s.jdbc.internals.TDS.Channel - TLSv1 was negotiated. Please update server and client to use TLSv1.2 at minimum.
2025-06-08 01:13:16.425 [HikariPool-1 connection adder] WARN  c.m.s.jdbc.internals.TDS.Channel - TLSv1 was negotiated. Please update server and client to use TLSv1.2 at minimum.
2025-06-08 01:13:17.054 [HikariPool-1 connection adder] WARN  c.m.s.jdbc.internals.TDS.Channel - TLSv1 was negotiated. Please update server and client to use TLSv1.2 at minimum.
2025-06-08 01:13:17.466 [HikariPool-1 connection adder] WARN  c.m.s.jdbc.internals.TDS.Channel - TLSv1 was negotiated. Please update server and client to use TLSv1.2 at minimum.
2025-06-08 01:13:31.336 [HikariPool-1 connection adder] WARN  c.m.s.jdbc.internals.TDS.Channel - TLSv1 was negotiated. Please update server and client to use TLSv1.2 at minimum.
2025-06-08 01:13:35.530 [HikariPool-1 connection adder] WARN  c.m.s.jdbc.internals.TDS.Channel - TLSv1 was negotiated. Please update server and client to use TLSv1.2 at minimum.
2025-06-08 01:14:02.590 [HikariPool-1 connection adder] WARN  c.m.s.jdbc.internals.TDS.Channel - TLSv1 was negotiated. Please update server and client to use TLSv1.2 at minimum.
2025-06-08 01:15:36.011 [SpringContextShutdownHook] DEBUG o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Closing org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@1a49800, started on Sat Jun 07 18:13:42 CST 2025
2025-06-08 01:15:36.011 [SpringContextShutdownHook] DEBUG o.s.c.e.PropertySourcesPropertyResolver - Found key 'spring.liveBeansView.mbeanDomain' in PropertySource 'systemProperties' with value of type String
2025-06-08 01:15:36.013 [SpringContextShutdownHook] DEBUG o.s.c.s.DefaultLifecycleProcessor - Stopping beans in phase 2147483647
2025-06-08 01:15:36.013 [SpringContextShutdownHook] DEBUG o.s.c.s.DefaultLifecycleProcessor - Bean 'documentationPluginsBootstrapper' completed its stop procedure
2025-06-08 01:15:36.015 [SpringContextShutdownHook] INFO  o.s.s.c.ThreadPoolTaskScheduler - Shutting down ExecutorService 'taskScheduler'
2025-06-08 01:15:36.016 [SpringContextShutdownHook] DEBUG o.s.j.e.a.AnnotationMBeanExporter - Unregistering JMX-exposed beans on shutdown
2025-06-08 01:15:36.016 [SpringContextShutdownHook] DEBUG o.s.j.e.a.AnnotationMBeanExporter - Unregistering JMX-exposed beans
2025-06-08 01:15:36.016 [SpringContextShutdownHook] INFO  o.s.s.c.ThreadPoolTaskExecutor - Shutting down ExecutorService 'applicationTaskExecutor'
2025-06-08 01:15:36.019 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-06-08 01:15:36.021 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-06-08 01:15:36.022 [SpringContextShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-06-08 01:15:36.023 [SpringContextShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.0.27.Final
