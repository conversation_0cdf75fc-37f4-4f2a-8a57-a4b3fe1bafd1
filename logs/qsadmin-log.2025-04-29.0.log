2025-04-29 15:51:16.599 [main] INFO  com.qs.admin.QscAdminApplication - Starting QscAdminApplication on MSI with PID 11448 (started by <PERSON><PERSON><PERSON> in D:\project\Java Projectes\qsadmin)
2025-04-29 15:51:16.600 [main] DEBUG com.qs.admin.QscAdminApplication - Running with Spring Boot v2.2.1.RELEASE, Spring v5.2.23.RELEASE
2025-04-29 15:51:16.601 [main] INFO  com.qs.admin.QscAdminApplication - The following profiles are active: prod
2025-04-29 15:51:19.004 [main] WARN  io.undertow.websockets.jsr - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
2025-04-29 15:51:19.015 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-04-29 15:51:19.779 [main] INFO  c.q.a.t.s.impl.BaiShuiApiServiceImpl - 获取佰税API访问令牌
2025-04-29 15:52:16.983 [main] INFO  com.qs.admin.QscAdminApplication - Starting QscAdminApplication on MSI with PID 37480 (started by KarlKyo in D:\project\Java Projectes\qsadmin)
2025-04-29 15:52:16.987 [main] DEBUG com.qs.admin.QscAdminApplication - Running with Spring Boot v2.2.1.RELEASE, Spring v5.2.23.RELEASE
2025-04-29 15:52:16.987 [main] INFO  com.qs.admin.QscAdminApplication - The following profiles are active: prod
2025-04-29 15:52:18.923 [main] WARN  io.undertow.websockets.jsr - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
2025-04-29 15:52:18.948 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-04-29 15:52:19.822 [main] INFO  c.q.a.t.s.impl.BaiShuiApiServiceImpl - 获取佰税API访问令牌
2025-04-29 15:52:40.925 [main] ERROR c.q.a.t.s.impl.BaiShuiApiServiceImpl - 获取佰税API访问令牌异常
org.springframework.web.client.ResourceAccessException: I/O error on POST request for "http://80.12.140.31:8722/oauth/token": Connection timed out: connect; nested exception is java.net.ConnectException: Connection timed out: connect
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:751)
	at org.springframework.web.client.RestTemplate.execute(RestTemplate.java:677)
	at org.springframework.web.client.RestTemplate.postForObject(RestTemplate.java:421)
	at com.qs.admin.taxhall.service.impl.BaiShuiApiServiceImpl.getToken(BaiShuiApiServiceImpl.java:72)
	at com.qs.admin.taxhall.task.BaiShuiTokenSyncTask.syncAppKeyAndToken(BaiShuiTokenSyncTask.java:49)
	at com.qs.admin.taxhall.task.BaiShuiTokenSyncTask.initSync(BaiShuiTokenSyncTask.java:29)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleElement.invoke(InitDestroyAnnotationBeanPostProcessor.java:389)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeInitMethods(InitDestroyAnnotationBeanPostProcessor.java:333)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeInitialization(InitDestroyAnnotationBeanPostProcessor.java:157)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsBeforeInitialization(AbstractAutowireCapableBeanFactory.java:416)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1795)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:595)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:517)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:323)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:222)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:321)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1287)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1207)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:636)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:116)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:397)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1429)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:594)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:517)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:323)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:222)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:321)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:879)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:879)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:551)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:141)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:747)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:397)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:315)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1226)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1215)
	at com.qs.admin.QscAdminApplication.main(QscAdminApplication.java:19)
Caused by: java.net.ConnectException: Connection timed out: connect
	at java.net.DualStackPlainSocketImpl.connect0(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:75)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:476)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:218)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:200)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:162)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:394)
	at java.net.Socket.connect(Socket.java:606)
	at java.net.Socket.connect(Socket.java:555)
	at sun.net.NetworkClient.doConnect(NetworkClient.java:180)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:463)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:558)
	at sun.net.www.http.HttpClient.<init>(HttpClient.java:242)
	at sun.net.www.http.HttpClient.New(HttpClient.java:339)
	at sun.net.www.http.HttpClient.New(HttpClient.java:357)
	at sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1240)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1167)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1061)
	at sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:995)
	at org.springframework.http.client.SimpleBufferingClientHttpRequest.executeInternal(SimpleBufferingClientHttpRequest.java:76)
	at org.springframework.http.client.AbstractBufferingClientHttpRequest.executeInternal(AbstractBufferingClientHttpRequest.java:48)
	at org.springframework.http.client.AbstractClientHttpRequest.execute(AbstractClientHttpRequest.java:53)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:742)
	... 43 common frames omitted
2025-04-29 15:52:40.926 [main] ERROR c.q.a.t.task.BaiShuiTokenSyncTask - 同步佰税API APPKEY和TOKEN异常（不影响项目运行）
java.lang.RuntimeException: 授权请求异常: I/O error on POST request for "http://80.12.140.31:8722/oauth/token": Connection timed out: connect; nested exception is java.net.ConnectException: Connection timed out: connect
	at com.qs.admin.taxhall.service.impl.BaiShuiApiServiceImpl.getToken(BaiShuiApiServiceImpl.java:84)
	at com.qs.admin.taxhall.task.BaiShuiTokenSyncTask.syncAppKeyAndToken(BaiShuiTokenSyncTask.java:49)
	at com.qs.admin.taxhall.task.BaiShuiTokenSyncTask.initSync(BaiShuiTokenSyncTask.java:29)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleElement.invoke(InitDestroyAnnotationBeanPostProcessor.java:389)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeInitMethods(InitDestroyAnnotationBeanPostProcessor.java:333)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeInitialization(InitDestroyAnnotationBeanPostProcessor.java:157)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsBeforeInitialization(AbstractAutowireCapableBeanFactory.java:416)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1795)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:595)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:517)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:323)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:222)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:321)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1287)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1207)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:636)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:116)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:397)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1429)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:594)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:517)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:323)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:222)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:321)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:879)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:879)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:551)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:141)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:747)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:397)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:315)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1226)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1215)
	at com.qs.admin.QscAdminApplication.main(QscAdminApplication.java:19)
Caused by: org.springframework.web.client.ResourceAccessException: I/O error on POST request for "http://80.12.140.31:8722/oauth/token": Connection timed out: connect; nested exception is java.net.ConnectException: Connection timed out: connect
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:751)
	at org.springframework.web.client.RestTemplate.execute(RestTemplate.java:677)
	at org.springframework.web.client.RestTemplate.postForObject(RestTemplate.java:421)
	at com.qs.admin.taxhall.service.impl.BaiShuiApiServiceImpl.getToken(BaiShuiApiServiceImpl.java:72)
	... 40 common frames omitted
Caused by: java.net.ConnectException: Connection timed out: connect
	at java.net.DualStackPlainSocketImpl.connect0(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:75)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:476)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:218)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:200)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:162)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:394)
	at java.net.Socket.connect(Socket.java:606)
	at java.net.Socket.connect(Socket.java:555)
	at sun.net.NetworkClient.doConnect(NetworkClient.java:180)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:463)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:558)
	at sun.net.www.http.HttpClient.<init>(HttpClient.java:242)
	at sun.net.www.http.HttpClient.New(HttpClient.java:339)
	at sun.net.www.http.HttpClient.New(HttpClient.java:357)
	at sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1240)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1167)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1061)
	at sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:995)
	at org.springframework.http.client.SimpleBufferingClientHttpRequest.executeInternal(SimpleBufferingClientHttpRequest.java:76)
	at org.springframework.http.client.AbstractBufferingClientHttpRequest.executeInternal(AbstractBufferingClientHttpRequest.java:48)
	at org.springframework.http.client.AbstractClientHttpRequest.execute(AbstractClientHttpRequest.java:53)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:742)
	... 43 common frames omitted
2025-04-29 15:52:41.563 [main] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.qs.admin.taxhall.model.vo.WindowBusinessVO".
2025-04-29 15:52:41.563 [main] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.qs.admin.taxhall.model.vo.WindowBusinessVO ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-04-29 15:52:42.795 [main] INFO  io.undertow - starting server: Undertow - 2.0.27.Final
2025-04-29 15:52:42.799 [main] INFO  org.xnio - XNIO version 3.3.8.Final
2025-04-29 15:52:42.804 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.3.8.Final
2025-04-29 15:52:42.842 [main] INFO  com.qs.admin.QscAdminApplication - Started QscAdminApplication in 26.307 seconds (JVM running for 31.761)
2025-04-29 15:52:57.654 [SpringContextShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.0.27.Final
2025-04-29 15:53:16.559 [main] INFO  com.qs.admin.QscAdminApplication - Starting QscAdminApplication on MSI with PID 3964 (started by KarlKyo in D:\project\Java Projectes\qsadmin)
2025-04-29 15:53:16.561 [main] DEBUG com.qs.admin.QscAdminApplication - Running with Spring Boot v2.2.1.RELEASE, Spring v5.2.23.RELEASE
2025-04-29 15:53:16.561 [main] INFO  com.qs.admin.QscAdminApplication - The following profiles are active: prod
2025-04-29 15:53:18.242 [main] WARN  io.undertow.websockets.jsr - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
2025-04-29 15:53:18.266 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-04-29 15:53:19.086 [main] INFO  c.q.a.t.s.impl.BaiShuiApiServiceImpl - 获取佰税API访问令牌
2025-04-29 15:53:19.099 [main] ERROR c.q.a.t.s.impl.BaiShuiApiServiceImpl - 获取佰税API访问令牌异常
java.lang.IllegalArgumentException: URI is not absolute
	at java.net.URI.toURL(URI.java:1088)
	at org.springframework.http.client.SimpleClientHttpRequestFactory.createRequest(SimpleClientHttpRequestFactory.java:145)
	at org.springframework.http.client.support.HttpAccessor.createRequest(HttpAccessor.java:124)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:738)
	at org.springframework.web.client.RestTemplate.execute(RestTemplate.java:677)
	at org.springframework.web.client.RestTemplate.postForObject(RestTemplate.java:421)
	at com.qs.admin.taxhall.service.impl.BaiShuiApiServiceImpl.getToken(BaiShuiApiServiceImpl.java:72)
	at com.qs.admin.taxhall.task.BaiShuiTokenSyncTask.syncAppKeyAndToken(BaiShuiTokenSyncTask.java:49)
	at com.qs.admin.taxhall.task.BaiShuiTokenSyncTask.initSync(BaiShuiTokenSyncTask.java:29)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleElement.invoke(InitDestroyAnnotationBeanPostProcessor.java:389)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeInitMethods(InitDestroyAnnotationBeanPostProcessor.java:333)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeInitialization(InitDestroyAnnotationBeanPostProcessor.java:157)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsBeforeInitialization(AbstractAutowireCapableBeanFactory.java:416)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1795)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:595)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:517)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:323)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:222)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:321)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1287)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1207)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:636)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:116)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:397)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1429)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:594)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:517)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:323)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:222)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:321)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:879)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:879)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:551)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:141)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:747)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:397)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:315)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1226)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1215)
	at com.qs.admin.QscAdminApplication.main(QscAdminApplication.java:19)
2025-04-29 15:53:19.100 [main] ERROR c.q.a.t.task.BaiShuiTokenSyncTask - 同步佰税API APPKEY和TOKEN异常（不影响项目运行）
java.lang.RuntimeException: 授权请求异常: URI is not absolute
	at com.qs.admin.taxhall.service.impl.BaiShuiApiServiceImpl.getToken(BaiShuiApiServiceImpl.java:84)
	at com.qs.admin.taxhall.task.BaiShuiTokenSyncTask.syncAppKeyAndToken(BaiShuiTokenSyncTask.java:49)
	at com.qs.admin.taxhall.task.BaiShuiTokenSyncTask.initSync(BaiShuiTokenSyncTask.java:29)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleElement.invoke(InitDestroyAnnotationBeanPostProcessor.java:389)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeInitMethods(InitDestroyAnnotationBeanPostProcessor.java:333)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeInitialization(InitDestroyAnnotationBeanPostProcessor.java:157)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsBeforeInitialization(AbstractAutowireCapableBeanFactory.java:416)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1795)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:595)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:517)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:323)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:222)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:321)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1287)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1207)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:636)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:116)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:397)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1429)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:594)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:517)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:323)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:222)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:321)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:879)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:879)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:551)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:141)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:747)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:397)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:315)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1226)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1215)
	at com.qs.admin.QscAdminApplication.main(QscAdminApplication.java:19)
Caused by: java.lang.IllegalArgumentException: URI is not absolute
	at java.net.URI.toURL(URI.java:1088)
	at org.springframework.http.client.SimpleClientHttpRequestFactory.createRequest(SimpleClientHttpRequestFactory.java:145)
	at org.springframework.http.client.support.HttpAccessor.createRequest(HttpAccessor.java:124)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:738)
	at org.springframework.web.client.RestTemplate.execute(RestTemplate.java:677)
	at org.springframework.web.client.RestTemplate.postForObject(RestTemplate.java:421)
	at com.qs.admin.taxhall.service.impl.BaiShuiApiServiceImpl.getToken(BaiShuiApiServiceImpl.java:72)
	... 40 common frames omitted
2025-04-29 15:53:19.806 [main] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.qs.admin.taxhall.model.vo.WindowBusinessVO".
2025-04-29 15:53:19.806 [main] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.qs.admin.taxhall.model.vo.WindowBusinessVO ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-04-29 15:53:21.093 [main] INFO  io.undertow - starting server: Undertow - 2.0.27.Final
2025-04-29 15:53:21.097 [main] INFO  org.xnio - XNIO version 3.3.8.Final
2025-04-29 15:53:21.111 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.3.8.Final
2025-04-29 15:53:21.145 [main] INFO  com.qs.admin.QscAdminApplication - Started QscAdminApplication in 5.01 seconds (JVM running for 10.487)
2025-04-29 15:54:40.190 [SpringContextShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.0.27.Final
2025-04-29 15:54:48.207 [main] INFO  com.qs.admin.QscAdminApplication - Starting QscAdminApplication on MSI with PID 27664 (started by KarlKyo in D:\project\Java Projectes\qsadmin)
2025-04-29 15:54:48.208 [main] DEBUG com.qs.admin.QscAdminApplication - Running with Spring Boot v2.2.1.RELEASE, Spring v5.2.23.RELEASE
2025-04-29 15:54:48.209 [main] INFO  com.qs.admin.QscAdminApplication - The following profiles are active: prod
2025-04-29 15:54:49.527 [main] WARN  io.undertow.websockets.jsr - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
2025-04-29 15:54:49.541 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-04-29 15:54:55.604 [main] INFO  c.q.a.t.s.impl.BaiShuiApiServiceImpl - 获取佰税API访问令牌
2025-04-29 15:59:12.517 [main] ERROR c.q.a.t.s.impl.BaiShuiApiServiceImpl - 获取佰税API访问令牌异常
java.lang.IllegalArgumentException: URI is not absolute
	at java.net.URI.toURL(URI.java:1088)
	at org.springframework.http.client.SimpleClientHttpRequestFactory.createRequest(SimpleClientHttpRequestFactory.java:145)
	at org.springframework.http.client.support.HttpAccessor.createRequest(HttpAccessor.java:124)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:738)
	at org.springframework.web.client.RestTemplate.execute(RestTemplate.java:677)
	at org.springframework.web.client.RestTemplate.postForObject(RestTemplate.java:421)
	at com.qs.admin.taxhall.service.impl.BaiShuiApiServiceImpl.getToken(BaiShuiApiServiceImpl.java:72)
	at com.qs.admin.taxhall.task.BaiShuiTokenSyncTask.syncAppKeyAndToken(BaiShuiTokenSyncTask.java:49)
	at com.qs.admin.taxhall.task.BaiShuiTokenSyncTask.initSync(BaiShuiTokenSyncTask.java:29)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleElement.invoke(InitDestroyAnnotationBeanPostProcessor.java:389)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeInitMethods(InitDestroyAnnotationBeanPostProcessor.java:333)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeInitialization(InitDestroyAnnotationBeanPostProcessor.java:157)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsBeforeInitialization(AbstractAutowireCapableBeanFactory.java:416)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1795)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:595)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:517)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:323)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:222)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:321)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1287)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1207)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:636)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:116)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:397)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1429)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:594)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:517)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:323)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:222)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:321)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:879)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:879)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:551)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:141)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:747)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:397)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:315)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1226)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1215)
	at com.qs.admin.QscAdminApplication.main(QscAdminApplication.java:19)
2025-04-29 15:59:12.518 [main] ERROR c.q.a.t.task.BaiShuiTokenSyncTask - 同步佰税API APPKEY和TOKEN异常（不影响项目运行）
java.lang.RuntimeException: 授权请求异常: URI is not absolute
	at com.qs.admin.taxhall.service.impl.BaiShuiApiServiceImpl.getToken(BaiShuiApiServiceImpl.java:84)
	at com.qs.admin.taxhall.task.BaiShuiTokenSyncTask.syncAppKeyAndToken(BaiShuiTokenSyncTask.java:49)
	at com.qs.admin.taxhall.task.BaiShuiTokenSyncTask.initSync(BaiShuiTokenSyncTask.java:29)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleElement.invoke(InitDestroyAnnotationBeanPostProcessor.java:389)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeInitMethods(InitDestroyAnnotationBeanPostProcessor.java:333)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeInitialization(InitDestroyAnnotationBeanPostProcessor.java:157)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsBeforeInitialization(AbstractAutowireCapableBeanFactory.java:416)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1795)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:595)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:517)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:323)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:222)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:321)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1287)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1207)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:636)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:116)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:397)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1429)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:594)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:517)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:323)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:222)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:321)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:879)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:879)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:551)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:141)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:747)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:397)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:315)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1226)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1215)
	at com.qs.admin.QscAdminApplication.main(QscAdminApplication.java:19)
Caused by: java.lang.IllegalArgumentException: URI is not absolute
	at java.net.URI.toURL(URI.java:1088)
	at org.springframework.http.client.SimpleClientHttpRequestFactory.createRequest(SimpleClientHttpRequestFactory.java:145)
	at org.springframework.http.client.support.HttpAccessor.createRequest(HttpAccessor.java:124)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:738)
	at org.springframework.web.client.RestTemplate.execute(RestTemplate.java:677)
	at org.springframework.web.client.RestTemplate.postForObject(RestTemplate.java:421)
	at com.qs.admin.taxhall.service.impl.BaiShuiApiServiceImpl.getToken(BaiShuiApiServiceImpl.java:72)
	... 40 common frames omitted
2025-04-29 15:59:26.482 [main] INFO  com.qs.admin.QscAdminApplication - Starting QscAdminApplication on MSI with PID 8268 (started by KarlKyo in D:\project\Java Projectes\qsadmin)
2025-04-29 15:59:26.484 [main] DEBUG com.qs.admin.QscAdminApplication - Running with Spring Boot v2.2.1.RELEASE, Spring v5.2.23.RELEASE
2025-04-29 15:59:26.484 [main] INFO  com.qs.admin.QscAdminApplication - The following profiles are active: prod
2025-04-29 15:59:27.757 [main] WARN  io.undertow.websockets.jsr - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
2025-04-29 15:59:27.770 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-04-29 15:59:35.414 [main] INFO  c.q.a.t.s.impl.BaiShuiApiServiceImpl - 获取佰税API访问令牌
2025-04-29 15:59:51.171 [main] INFO  c.q.a.t.s.impl.BaiShuiApiServiceImpl - tokenUrl=null
2025-04-29 16:02:31.204 [main] ERROR c.q.a.t.s.impl.BaiShuiApiServiceImpl - 获取佰税API访问令牌异常
java.lang.IllegalArgumentException: URI is not absolute
	at java.net.URI.toURL(URI.java:1088)
	at org.springframework.http.client.SimpleClientHttpRequestFactory.createRequest(SimpleClientHttpRequestFactory.java:145)
	at org.springframework.http.client.support.HttpAccessor.createRequest(HttpAccessor.java:124)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:738)
	at org.springframework.web.client.RestTemplate.execute(RestTemplate.java:677)
	at org.springframework.web.client.RestTemplate.postForObject(RestTemplate.java:421)
	at com.qs.admin.taxhall.service.impl.BaiShuiApiServiceImpl.getToken(BaiShuiApiServiceImpl.java:73)
	at com.qs.admin.taxhall.task.BaiShuiTokenSyncTask.syncAppKeyAndToken(BaiShuiTokenSyncTask.java:49)
	at com.qs.admin.taxhall.task.BaiShuiTokenSyncTask.initSync(BaiShuiTokenSyncTask.java:29)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleElement.invoke(InitDestroyAnnotationBeanPostProcessor.java:389)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeInitMethods(InitDestroyAnnotationBeanPostProcessor.java:333)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeInitialization(InitDestroyAnnotationBeanPostProcessor.java:157)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsBeforeInitialization(AbstractAutowireCapableBeanFactory.java:416)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1795)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:595)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:517)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:323)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:222)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:321)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1287)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1207)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:636)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:116)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:397)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1429)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:594)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:517)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:323)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:222)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:321)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:879)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:879)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:551)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:141)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:747)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:397)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:315)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1226)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1215)
	at com.qs.admin.QscAdminApplication.main(QscAdminApplication.java:19)
2025-04-29 16:02:31.204 [main] ERROR c.q.a.t.task.BaiShuiTokenSyncTask - 同步佰税API APPKEY和TOKEN异常（不影响项目运行）
java.lang.RuntimeException: 授权请求异常: URI is not absolute
	at com.qs.admin.taxhall.service.impl.BaiShuiApiServiceImpl.getToken(BaiShuiApiServiceImpl.java:85)
	at com.qs.admin.taxhall.task.BaiShuiTokenSyncTask.syncAppKeyAndToken(BaiShuiTokenSyncTask.java:49)
	at com.qs.admin.taxhall.task.BaiShuiTokenSyncTask.initSync(BaiShuiTokenSyncTask.java:29)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleElement.invoke(InitDestroyAnnotationBeanPostProcessor.java:389)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeInitMethods(InitDestroyAnnotationBeanPostProcessor.java:333)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeInitialization(InitDestroyAnnotationBeanPostProcessor.java:157)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsBeforeInitialization(AbstractAutowireCapableBeanFactory.java:416)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1795)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:595)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:517)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:323)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:222)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:321)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1287)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1207)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:636)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:116)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:397)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1429)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:594)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:517)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:323)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:222)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:321)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:879)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:879)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:551)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:141)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:747)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:397)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:315)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1226)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1215)
	at com.qs.admin.QscAdminApplication.main(QscAdminApplication.java:19)
Caused by: java.lang.IllegalArgumentException: URI is not absolute
	at java.net.URI.toURL(URI.java:1088)
	at org.springframework.http.client.SimpleClientHttpRequestFactory.createRequest(SimpleClientHttpRequestFactory.java:145)
	at org.springframework.http.client.support.HttpAccessor.createRequest(HttpAccessor.java:124)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:738)
	at org.springframework.web.client.RestTemplate.execute(RestTemplate.java:677)
	at org.springframework.web.client.RestTemplate.postForObject(RestTemplate.java:421)
	at com.qs.admin.taxhall.service.impl.BaiShuiApiServiceImpl.getToken(BaiShuiApiServiceImpl.java:73)
	... 40 common frames omitted
2025-04-29 16:02:38.732 [main] INFO  com.qs.admin.QscAdminApplication - Starting QscAdminApplication on MSI with PID 34060 (started by KarlKyo in D:\project\Java Projectes\qsadmin)
2025-04-29 16:02:38.733 [main] DEBUG com.qs.admin.QscAdminApplication - Running with Spring Boot v2.2.1.RELEASE, Spring v5.2.23.RELEASE
2025-04-29 16:02:38.733 [main] INFO  com.qs.admin.QscAdminApplication - The following profiles are active: prod
2025-04-29 16:02:40.002 [main] WARN  io.undertow.websockets.jsr - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
2025-04-29 16:02:40.015 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-04-29 16:02:43.944 [main] INFO  c.q.a.t.s.impl.BaiShuiApiServiceImpl - 获取佰税API访问令牌
2025-04-29 16:03:10.377 [main] INFO  c.q.a.t.s.impl.BaiShuiApiServiceImpl - tokenUrl=null
2025-04-29 16:04:24.351 [main] ERROR c.q.a.t.s.impl.BaiShuiApiServiceImpl - 获取佰税API访问令牌异常
java.lang.IllegalArgumentException: URI is not absolute
	at java.net.URI.toURL(URI.java:1088)
	at org.springframework.http.client.SimpleClientHttpRequestFactory.createRequest(SimpleClientHttpRequestFactory.java:145)
	at org.springframework.http.client.support.HttpAccessor.createRequest(HttpAccessor.java:124)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:738)
	at org.springframework.web.client.RestTemplate.execute(RestTemplate.java:677)
	at org.springframework.web.client.RestTemplate.postForObject(RestTemplate.java:421)
	at com.qs.admin.taxhall.service.impl.BaiShuiApiServiceImpl.getToken(BaiShuiApiServiceImpl.java:73)
	at com.qs.admin.taxhall.task.BaiShuiTokenSyncTask.syncAppKeyAndToken(BaiShuiTokenSyncTask.java:49)
	at com.qs.admin.taxhall.task.BaiShuiTokenSyncTask.initSync(BaiShuiTokenSyncTask.java:29)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleElement.invoke(InitDestroyAnnotationBeanPostProcessor.java:389)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeInitMethods(InitDestroyAnnotationBeanPostProcessor.java:333)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeInitialization(InitDestroyAnnotationBeanPostProcessor.java:157)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsBeforeInitialization(AbstractAutowireCapableBeanFactory.java:416)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1795)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:595)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:517)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:323)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:222)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:321)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1287)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1207)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:636)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:116)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:397)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1429)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:594)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:517)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:323)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:222)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:321)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:879)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:879)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:551)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:141)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:747)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:397)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:315)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1226)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1215)
	at com.qs.admin.QscAdminApplication.main(QscAdminApplication.java:19)
2025-04-29 16:04:24.351 [main] ERROR c.q.a.t.task.BaiShuiTokenSyncTask - 同步佰税API APPKEY和TOKEN异常（不影响项目运行）
java.lang.RuntimeException: 授权请求异常: URI is not absolute
	at com.qs.admin.taxhall.service.impl.BaiShuiApiServiceImpl.getToken(BaiShuiApiServiceImpl.java:85)
	at com.qs.admin.taxhall.task.BaiShuiTokenSyncTask.syncAppKeyAndToken(BaiShuiTokenSyncTask.java:49)
	at com.qs.admin.taxhall.task.BaiShuiTokenSyncTask.initSync(BaiShuiTokenSyncTask.java:29)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleElement.invoke(InitDestroyAnnotationBeanPostProcessor.java:389)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeInitMethods(InitDestroyAnnotationBeanPostProcessor.java:333)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeInitialization(InitDestroyAnnotationBeanPostProcessor.java:157)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsBeforeInitialization(AbstractAutowireCapableBeanFactory.java:416)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1795)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:595)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:517)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:323)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:222)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:321)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1287)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1207)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:636)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:116)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:397)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1429)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:594)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:517)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:323)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:222)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:321)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:879)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:879)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:551)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:141)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:747)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:397)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:315)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1226)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1215)
	at com.qs.admin.QscAdminApplication.main(QscAdminApplication.java:19)
Caused by: java.lang.IllegalArgumentException: URI is not absolute
	at java.net.URI.toURL(URI.java:1088)
	at org.springframework.http.client.SimpleClientHttpRequestFactory.createRequest(SimpleClientHttpRequestFactory.java:145)
	at org.springframework.http.client.support.HttpAccessor.createRequest(HttpAccessor.java:124)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:738)
	at org.springframework.web.client.RestTemplate.execute(RestTemplate.java:677)
	at org.springframework.web.client.RestTemplate.postForObject(RestTemplate.java:421)
	at com.qs.admin.taxhall.service.impl.BaiShuiApiServiceImpl.getToken(BaiShuiApiServiceImpl.java:73)
	... 40 common frames omitted
2025-04-29 16:04:32.982 [main] DEBUG o.s.b.c.l.ClasspathLoggingApplicationListener - Application started with classpath: [file:/D:/DevelopmentTools/Java/java-8u311/jre/lib/charsets.jar, file:/D:/DevelopmentTools/Java/java-8u311/jre/lib/deploy.jar, file:/D:/DevelopmentTools/Java/java-8u311/jre/lib/ext/access-bridge-32.jar, file:/D:/DevelopmentTools/Java/java-8u311/jre/lib/ext/cldrdata.jar, file:/D:/DevelopmentTools/Java/java-8u311/jre/lib/ext/dnsns.jar, file:/D:/DevelopmentTools/Java/java-8u311/jre/lib/ext/jaccess.jar, file:/D:/DevelopmentTools/Java/java-8u311/jre/lib/ext/jfxrt.jar, file:/D:/DevelopmentTools/Java/java-8u311/jre/lib/ext/localedata.jar, file:/D:/DevelopmentTools/Java/java-8u311/jre/lib/ext/nashorn.jar, file:/D:/DevelopmentTools/Java/java-8u311/jre/lib/ext/sunec.jar, file:/D:/DevelopmentTools/Java/java-8u311/jre/lib/ext/sunjce_provider.jar, file:/D:/DevelopmentTools/Java/java-8u311/jre/lib/ext/sunmscapi.jar, file:/D:/DevelopmentTools/Java/java-8u311/jre/lib/ext/sunpkcs11.jar, file:/D:/DevelopmentTools/Java/java-8u311/jre/lib/ext/zipfs.jar, file:/D:/DevelopmentTools/Java/java-8u311/jre/lib/javaws.jar, file:/D:/DevelopmentTools/Java/java-8u311/jre/lib/jce.jar, file:/D:/DevelopmentTools/Java/java-8u311/jre/lib/jfr.jar, file:/D:/DevelopmentTools/Java/java-8u311/jre/lib/jfxswt.jar, file:/D:/DevelopmentTools/Java/java-8u311/jre/lib/jsse.jar, file:/D:/DevelopmentTools/Java/java-8u311/jre/lib/management-agent.jar, file:/D:/DevelopmentTools/Java/java-8u311/jre/lib/plugin.jar, file:/D:/DevelopmentTools/Java/java-8u311/jre/lib/resources.jar, file:/D:/DevelopmentTools/Java/java-8u311/jre/lib/rt.jar, file:/D:/project/Java%20Projectes/qsadmin/target/classes/, file:/D:/DevelopmentTools/Maven_respository/com/microsoft/sqlserver/mssql-jdbc/12.6.1.jre8/mssql-jdbc-12.6.1.jre8.jar, file:/D:/DevelopmentTools/Maven_respository/org/springframework/boot/spring-boot-starter-web/2.2.1.RELEASE/spring-boot-starter-web-2.2.1.RELEASE.jar, file:/D:/DevelopmentTools/Maven_respository/org/springframework/boot/spring-boot-starter/2.2.1.RELEASE/spring-boot-starter-2.2.1.RELEASE.jar, file:/D:/DevelopmentTools/Maven_respository/org/springframework/boot/spring-boot/2.2.1.RELEASE/spring-boot-2.2.1.RELEASE.jar, file:/D:/DevelopmentTools/Maven_respository/org/springframework/boot/spring-boot-starter-logging/2.2.1.RELEASE/spring-boot-starter-logging-2.2.1.RELEASE.jar, file:/D:/DevelopmentTools/Maven_respository/ch/qos/logback/logback-classic/1.2.3/logback-classic-1.2.3.jar, file:/D:/DevelopmentTools/Maven_respository/ch/qos/logback/logback-core/1.2.3/logback-core-1.2.3.jar, file:/D:/DevelopmentTools/Maven_respository/org/apache/logging/log4j/log4j-to-slf4j/2.12.1/log4j-to-slf4j-2.12.1.jar, file:/D:/DevelopmentTools/Maven_respository/org/apache/logging/log4j/log4j-api/2.12.1/log4j-api-2.12.1.jar, file:/D:/DevelopmentTools/Maven_respository/org/slf4j/jul-to-slf4j/1.7.29/jul-to-slf4j-1.7.29.jar, file:/D:/DevelopmentTools/Maven_respository/jakarta/annotation/jakarta.annotation-api/1.3.5/jakarta.annotation-api-1.3.5.jar, file:/D:/DevelopmentTools/Maven_respository/org/yaml/snakeyaml/1.25/snakeyaml-1.25.jar, file:/D:/DevelopmentTools/Maven_respository/org/springframework/boot/spring-boot-starter-json/2.2.1.RELEASE/spring-boot-starter-json-2.2.1.RELEASE.jar, file:/D:/DevelopmentTools/Maven_respository/com/fasterxml/jackson/datatype/jackson-datatype-jdk8/2.10.0/jackson-datatype-jdk8-2.10.0.jar, file:/D:/DevelopmentTools/Maven_respository/com/fasterxml/jackson/datatype/jackson-datatype-jsr310/2.10.0/jackson-datatype-jsr310-2.10.0.jar, file:/D:/DevelopmentTools/Maven_respository/com/fasterxml/jackson/module/jackson-module-parameter-names/2.10.0/jackson-module-parameter-names-2.10.0.jar, file:/D:/DevelopmentTools/Maven_respository/org/springframework/boot/spring-boot-starter-validation/2.2.1.RELEASE/spring-boot-starter-validation-2.2.1.RELEASE.jar, file:/D:/DevelopmentTools/Maven_respository/jakarta/validation/jakarta.validation-api/2.0.1/jakarta.validation-api-2.0.1.jar, file:/D:/DevelopmentTools/Maven_respository/org/hibernate/validator/hibernate-validator/6.0.18.Final/hibernate-validator-6.0.18.Final.jar, file:/D:/DevelopmentTools/Maven_respository/org/springframework/spring-web/5.2.1.RELEASE/spring-web-5.2.1.RELEASE.jar, file:/D:/DevelopmentTools/Maven_respository/org/springframework/spring-webmvc/5.2.1.RELEASE/spring-webmvc-5.2.1.RELEASE.jar, file:/D:/DevelopmentTools/Maven_respository/org/springframework/boot/spring-boot-starter-undertow/2.2.1.RELEASE/spring-boot-starter-undertow-2.2.1.RELEASE.jar, file:/D:/DevelopmentTools/Maven_respository/io/undertow/undertow-core/2.0.27.Final/undertow-core-2.0.27.Final.jar, file:/D:/DevelopmentTools/Maven_respository/org/jboss/logging/jboss-logging/3.4.1.Final/jboss-logging-3.4.1.Final.jar, file:/D:/DevelopmentTools/Maven_respository/org/jboss/xnio/xnio-api/3.3.8.Final/xnio-api-3.3.8.Final.jar, file:/D:/DevelopmentTools/Maven_respository/org/jboss/xnio/xnio-nio/3.3.8.Final/xnio-nio-3.3.8.Final.jar, file:/D:/DevelopmentTools/Maven_respository/io/undertow/undertow-servlet/2.0.27.Final/undertow-servlet-2.0.27.Final.jar, file:/D:/DevelopmentTools/Maven_respository/org/jboss/spec/javax/annotation/jboss-annotations-api_1.2_spec/1.0.2.Final/jboss-annotations-api_1.2_spec-1.0.2.Final.jar, file:/D:/DevelopmentTools/Maven_respository/io/undertow/undertow-websockets-jsr/2.0.27.Final/undertow-websockets-jsr-2.0.27.Final.jar, file:/D:/DevelopmentTools/Maven_respository/org/jboss/spec/javax/websocket/jboss-websocket-api_1.1_spec/1.1.4.Final/jboss-websocket-api_1.1_spec-1.1.4.Final.jar, file:/D:/DevelopmentTools/Maven_respository/jakarta/servlet/jakarta.servlet-api/4.0.3/jakarta.servlet-api-4.0.3.jar, file:/D:/DevelopmentTools/Maven_respository/org/glassfish/jakarta.el/3.0.3/jakarta.el-3.0.3.jar, file:/D:/DevelopmentTools/Maven_respository/net/bytebuddy/byte-buddy/1.10.2/byte-buddy-1.10.2.jar, file:/D:/DevelopmentTools/Maven_respository/org/springframework/spring-core/5.2.1.RELEASE/spring-core-5.2.1.RELEASE.jar, file:/D:/DevelopmentTools/Maven_respository/org/springframework/spring-jcl/5.2.1.RELEASE/spring-jcl-5.2.1.RELEASE.jar, file:/D:/DevelopmentTools/Maven_respository/com/baomidou/dynamic-datasource-spring-boot-starter/3.0.0/dynamic-datasource-spring-boot-starter-3.0.0.jar, file:/D:/DevelopmentTools/Maven_respository/org/springframework/boot/spring-boot-starter-jdbc/2.2.1.RELEASE/spring-boot-starter-jdbc-2.2.1.RELEASE.jar, file:/D:/DevelopmentTools/Maven_respository/com/zaxxer/HikariCP/3.4.1/HikariCP-3.4.1.jar, file:/D:/DevelopmentTools/Maven_respository/org/springframework/spring-jdbc/5.2.1.RELEASE/spring-jdbc-5.2.1.RELEASE.jar, file:/D:/DevelopmentTools/Maven_respository/org/springframework/spring-tx/5.2.1.RELEASE/spring-tx-5.2.1.RELEASE.jar, file:/D:/DevelopmentTools/Maven_respository/org/springframework/boot/spring-boot-starter-aop/2.2.1.RELEASE/spring-boot-starter-aop-2.2.1.RELEASE.jar, file:/D:/DevelopmentTools/Maven_respository/org/aspectj/aspectjweaver/1.9.4/aspectjweaver-1.9.4.jar, file:/D:/DevelopmentTools/Maven_respository/org/apache/tomcat/tomcat-jdbc/7.0.81/tomcat-jdbc-7.0.81.jar, file:/D:/DevelopmentTools/Maven_respository/org/apache/tomcat/tomcat-juli/7.0.81/tomcat-juli-7.0.81.jar, file:/D:/DevelopmentTools/Maven_respository/com/belerweb/pinyin4j/2.5.0/pinyin4j-2.5.0.jar, file:/D:/DevelopmentTools/Maven_respository/org/apache/ant/ant/1.9.7/ant-1.9.7.jar, file:/D:/DevelopmentTools/Maven_respository/org/apache/ant/ant-launcher/1.9.7/ant-launcher-1.9.7.jar, file:/D:/DevelopmentTools/Maven_respository/net/coobird/thumbnailator/0.4.8/thumbnailator-0.4.8.jar, file:/D:/DevelopmentTools/Maven_respository/joda-time/joda-time/2.9.8/joda-time-2.9.8.jar, file:/D:/DevelopmentTools/Maven_respository/org/apache/velocity/velocity-engine-core/2.0/velocity-engine-core-2.0.jar, file:/D:/DevelopmentTools/Maven_respository/org/slf4j/slf4j-api/1.7.29/slf4j-api-1.7.29.jar, file:/D:/DevelopmentTools/Maven_respository/cn/hutool/hutool-all/4.1.2/hutool-all-4.1.2.jar, file:/D:/DevelopmentTools/Maven_respository/org/projectlombok/lombok/1.18.10/lombok-1.18.10.jar, file:/D:/DevelopmentTools/Maven_respository/com/baomidou/mybatis-plus-boot-starter/3.4.3.4/mybatis-plus-boot-starter-3.4.3.4.jar, file:/D:/DevelopmentTools/Maven_respository/com/baomidou/mybatis-plus/3.4.3.4/mybatis-plus-3.4.3.4.jar, file:/D:/DevelopmentTools/Maven_respository/com/baomidou/mybatis-plus-extension/3.4.3.4/mybatis-plus-extension-3.4.3.4.jar, file:/D:/DevelopmentTools/Maven_respository/com/baomidou/mybatis-plus-core/3.4.3.4/mybatis-plus-core-3.4.3.4.jar, file:/D:/DevelopmentTools/Maven_respository/com/baomidou/mybatis-plus-annotation/3.4.3.4/mybatis-plus-annotation-3.4.3.4.jar, file:/D:/DevelopmentTools/Maven_respository/com/github/jsqlparser/jsqlparser/4.2/jsqlparser-4.2.jar, file:/D:/DevelopmentTools/Maven_respository/org/springframework/boot/spring-boot-autoconfigure/2.2.1.RELEASE/spring-boot-autoconfigure-2.2.1.RELEASE.jar, file:/D:/DevelopmentTools/Maven_respository/org/mybatis/spring/boot/mybatis-spring-boot-starter/2.1.0/mybatis-spring-boot-starter-2.1.0.jar, file:/D:/DevelopmentTools/Maven_respository/org/mybatis/spring/boot/mybatis-spring-boot-autoconfigure/2.1.0/mybatis-spring-boot-autoconfigure-2.1.0.jar, file:/D:/DevelopmentTools/Maven_respository/org/mybatis/mybatis-spring/2.0.2/mybatis-spring-2.0.2.jar, file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-swagger2/2.9.2/springfox-swagger2-2.9.2.jar, file:/D:/DevelopmentTools/Maven_respository/io/swagger/swagger-annotations/1.5.20/swagger-annotations-1.5.20.jar, file:/D:/DevelopmentTools/Maven_respository/io/swagger/swagger-models/1.5.20/swagger-models-1.5.20.jar, file:/D:/DevelopmentTools/Maven_respository/com/fasterxml/jackson/core/jackson-annotations/2.10.0/jackson-annotations-2.10.0.jar, file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spi/2.9.2/springfox-spi-2.9.2.jar, file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-core/2.9.2/springfox-core-2.9.2.jar, file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-schema/2.9.2/springfox-schema-2.9.2.jar, file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-swagger-common/2.9.2/springfox-swagger-common-2.9.2.jar, file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar, file:/D:/DevelopmentTools/Maven_respository/com/fasterxml/classmate/1.5.1/classmate-1.5.1.jar, file:/D:/DevelopmentTools/Maven_respository/org/springframework/plugin/spring-plugin-core/1.2.0.RELEASE/spring-plugin-core-1.2.0.RELEASE.jar, file:/D:/DevelopmentTools/Maven_respository/org/springframework/plugin/spring-plugin-metadata/1.2.0.RELEASE/spring-plugin-metadata-1.2.0.RELEASE.jar, file:/D:/DevelopmentTools/Maven_respository/org/mapstruct/mapstruct/1.2.0.Final/mapstruct-1.2.0.Final.jar, file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-swagger-ui/2.9.2/springfox-swagger-ui-2.9.2.jar, file:/D:/DevelopmentTools/Maven_respository/org/apache/commons/commons-lang3/3.8.1/commons-lang3-3.8.1.jar, file:/D:/DevelopmentTools/Maven_respository/com/google/guava/guava/27.0-jre/guava-27.0-jre.jar, file:/D:/DevelopmentTools/Maven_respository/com/google/guava/failureaccess/1.0/failureaccess-1.0.jar, file:/D:/DevelopmentTools/Maven_respository/com/google/guava/listenablefuture/9999.0-empty-to-avoid-conflict-with-guava/listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar, file:/D:/DevelopmentTools/Maven_respository/com/google/code/findbugs/jsr305/3.0.2/jsr305-3.0.2.jar, file:/D:/DevelopmentTools/Maven_respository/org/checkerframework/checker-qual/2.5.2/checker-qual-2.5.2.jar, file:/D:/DevelopmentTools/Maven_respository/com/google/errorprone/error_prone_annotations/2.2.0/error_prone_annotations-2.2.0.jar, file:/D:/DevelopmentTools/Maven_respository/com/google/j2objc/j2objc-annotations/1.1/j2objc-annotations-1.1.jar, file:/D:/DevelopmentTools/Maven_respository/org/codehaus/mojo/animal-sniffer-annotations/1.17/animal-sniffer-annotations-1.17.jar, file:/D:/DevelopmentTools/Maven_respository/com/alibaba/fastjson/1.2.83/fastjson-1.2.83.jar, file:/D:/DevelopmentTools/Maven_respository/com/alibaba/druid-spring-boot-starter/1.1.10/druid-spring-boot-starter-1.1.10.jar, file:/D:/DevelopmentTools/Maven_respository/com/alibaba/druid/1.1.10/druid-1.1.10.jar, file:/D:/DevelopmentTools/Maven_respository/org/apache/shiro/shiro-spring/1.4.0/shiro-spring-1.4.0.jar, file:/D:/DevelopmentTools/Maven_respository/org/apache/shiro/shiro-core/1.4.0/shiro-core-1.4.0.jar, file:/D:/DevelopmentTools/Maven_respository/org/apache/shiro/shiro-lang/1.4.0/shiro-lang-1.4.0.jar, file:/D:/DevelopmentTools/Maven_respository/org/apache/shiro/shiro-cache/1.4.0/shiro-cache-1.4.0.jar, file:/D:/DevelopmentTools/Maven_respository/org/apache/shiro/shiro-crypto-hash/1.4.0/shiro-crypto-hash-1.4.0.jar, file:/D:/DevelopmentTools/Maven_respository/org/apache/shiro/shiro-crypto-core/1.4.0/shiro-crypto-core-1.4.0.jar, file:/D:/DevelopmentTools/Maven_respository/org/apache/shiro/shiro-crypto-cipher/1.4.0/shiro-crypto-cipher-1.4.0.jar, file:/D:/DevelopmentTools/Maven_respository/org/apache/shiro/shiro-config-core/1.4.0/shiro-config-core-1.4.0.jar, file:/D:/DevelopmentTools/Maven_respository/org/apache/shiro/shiro-config-ogdl/1.4.0/shiro-config-ogdl-1.4.0.jar, file:/D:/DevelopmentTools/Maven_respository/commons-beanutils/commons-beanutils/1.9.3/commons-beanutils-1.9.3.jar, file:/D:/DevelopmentTools/Maven_respository/commons-collections/commons-collections/3.2.2/commons-collections-3.2.2.jar, file:/D:/DevelopmentTools/Maven_respository/org/apache/shiro/shiro-event/1.4.0/shiro-event-1.4.0.jar, file:/D:/DevelopmentTools/Maven_respository/org/apache/shiro/shiro-web/1.4.0/shiro-web-1.4.0.jar, file:/D:/DevelopmentTools/Maven_respository/com/auth0/java-jwt/3.18.2/java-jwt-3.18.2.jar, file:/D:/DevelopmentTools/Maven_respository/com/fasterxml/jackson/core/jackson-databind/2.10.0/jackson-databind-2.10.0.jar, file:/D:/DevelopmentTools/Maven_respository/com/fasterxml/jackson/core/jackson-core/2.10.0/jackson-core-2.10.0.jar, file:/D:/DevelopmentTools/Maven_respository/org/springframework/spring-context/5.2.23.RELEASE/spring-context-5.2.23.RELEASE.jar, file:/D:/DevelopmentTools/Maven_respository/org/springframework/spring-aop/5.2.1.RELEASE/spring-aop-5.2.1.RELEASE.jar, file:/D:/DevelopmentTools/Maven_respository/org/springframework/spring-beans/5.2.1.RELEASE/spring-beans-5.2.1.RELEASE.jar, file:/D:/DevelopmentTools/Maven_respository/org/springframework/spring-expression/5.2.1.RELEASE/spring-expression-5.2.1.RELEASE.jar, file:/D:/DevelopmentTools/Maven_respository/org/mybatis/mybatis/3.5.7/mybatis-3.5.7.jar, file:/D:/DevelopmentTools/Maven_respository/com/github/ben-manes/caffeine/caffeine/2.8.0/caffeine-2.8.0.jar, file:/D:/DevelopmentTools/Maven_respository/com/github/xiaoymin/swagger-bootstrap-ui/1.9.3/swagger-bootstrap-ui-1.9.3.jar, file:/D:/DevelopmentTools/IntelliJ%20IDEA%202025/lib/idea_rt.jar, file:/C:/Users/<USER>/AppData/Local/JetBrains/IntelliJIdea2025.1/captureAgent/debugger-agent.jar]
2025-04-29 16:04:33.059 [main] INFO  com.qs.admin.QscAdminApplication - Starting QscAdminApplication on MSI with PID 36524 (started by KarlKyo in D:\project\Java Projectes\qsadmin)
2025-04-29 16:04:33.059 [main] DEBUG com.qs.admin.QscAdminApplication - Running with Spring Boot v2.2.1.RELEASE, Spring v5.2.23.RELEASE
2025-04-29 16:04:33.059 [main] INFO  com.qs.admin.QscAdminApplication - The following profiles are active: dev
2025-04-29 16:04:33.059 [main] DEBUG o.s.boot.SpringApplication - Loading source class com.qs.admin.QscAdminApplication
2025-04-29 16:04:33.092 [main] DEBUG o.s.b.c.c.ConfigFileApplicationListener - Activated activeProfiles dev
2025-04-29 16:04:33.092 [main] DEBUG o.s.b.c.c.ConfigFileApplicationListener - Loaded config file 'file:/D:/project/Java%20Projectes/qsadmin/target/classes/application.yml' (classpath:/application.yml)
2025-04-29 16:04:33.092 [main] DEBUG o.s.b.c.c.ConfigFileApplicationListener - Loaded config file 'file:/D:/project/Java%20Projectes/qsadmin/target/classes/application-dev.yml' (classpath:/application-dev.yml) for profile dev
2025-04-29 16:04:33.093 [main] DEBUG o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Refreshing org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@156c0a7
2025-04-29 16:04:33.111 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.context.annotation.internalConfigurationAnnotationProcessor'
2025-04-29 16:04:33.121 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.internalCachingMetadataReaderFactory'
2025-04-29 16:04:33.169 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\common\aspect\IdempotencyAspect.class]
2025-04-29 16:04:33.170 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\common\configurer\CorsConfig.class]
2025-04-29 16:04:33.174 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\common\configurer\DataSourceConfig.class]
2025-04-29 16:04:33.174 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\common\configurer\MyWebMvcConfig.class]
2025-04-29 16:04:33.175 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\common\configurer\ShiroConfig.class]
2025-04-29 16:04:33.175 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\common\configurer\Swagger2Configurer.class]
2025-04-29 16:04:33.176 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\common\core\MybatisPlusConfig.class]
2025-04-29 16:04:33.184 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\common\exception\GlobalExceptionHandler.class]
2025-04-29 16:04:33.186 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\common\service\impl\SyncCacheServiceImpl.class]
2025-04-29 16:04:33.186 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\common\shiro\ShiroFilterProperties.class]
2025-04-29 16:04:33.186 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\common\shiro\ShiroRealm.class]
2025-04-29 16:04:33.187 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\common\shiro\cache\ShiroCacheManager.class]
2025-04-29 16:04:33.189 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\common\shiro\security\JwtProperties.class]
2025-04-29 16:04:33.189 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\common\shiro\security\JwtUtil.class]
2025-04-29 16:04:33.190 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\common\utils\LocalCacheManager.class]
2025-04-29 16:04:33.191 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\common\utils\SpringContextUtil.class]
2025-04-29 16:04:33.192 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\config\BaiShuiProperties.class]
2025-04-29 16:04:33.192 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\config\RestTemplateConfig.class]
2025-04-29 16:04:33.197 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\controller\AgentInfoController.class]
2025-04-29 16:04:33.206 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\controller\AutocodeController.class]
2025-04-29 16:04:33.207 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\controller\BusinessController.class]
2025-04-29 16:04:33.207 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\controller\ClientInfoController.class]
2025-04-29 16:04:33.207 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\controller\EmployeeController.class]
2025-04-29 16:04:33.208 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\controller\NoticeController.class]
2025-04-29 16:04:33.208 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\controller\SystemController.class]
2025-04-29 16:04:33.208 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\controller\SystemLogController.class]
2025-04-29 16:04:33.209 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\controller\ThingsController.class]
2025-04-29 16:04:33.209 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\controller\TicketBookController.class]
2025-04-29 16:04:33.209 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\controller\TicketController.class]
2025-04-29 16:04:33.210 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\controller\TicketExchangeController.class]
2025-04-29 16:04:33.210 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\controller\TicketLogController.class]
2025-04-29 16:04:33.210 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\controller\TicketVerifyController.class]
2025-04-29 16:04:33.210 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\controller\WindowBusinessController.class]
2025-04-29 16:04:33.211 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\controller\WindowController.class]
2025-04-29 16:04:33.211 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\controller\WindowStatusController.class]
2025-04-29 16:04:33.233 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\service\impl\AgentInfoEnterpriseServiceImpl.class]
2025-04-29 16:04:33.234 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\service\impl\AgentInfoServiceImpl.class]
2025-04-29 16:04:33.235 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\service\impl\AutocodeServiceImpl.class]
2025-04-29 16:04:33.235 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\service\impl\BaiShuiApiServiceImpl.class]
2025-04-29 16:04:33.235 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\service\impl\BusinessServiceImpl.class]
2025-04-29 16:04:33.236 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\service\impl\ClientInfoServiceImpl.class]
2025-04-29 16:04:33.236 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\service\impl\EmployeeServiceImpl.class]
2025-04-29 16:04:33.236 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\service\impl\NoticeServiceImpl.class]
2025-04-29 16:04:33.236 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\service\impl\SystemLogServiceImpl.class]
2025-04-29 16:04:33.237 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\service\impl\SystemServiceImpl.class]
2025-04-29 16:04:33.237 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\service\impl\ThingsServiceImpl.class]
2025-04-29 16:04:33.237 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\service\impl\TicketBookServiceImpl.class]
2025-04-29 16:04:33.237 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\service\impl\TicketExchangeServiceImpl.class]
2025-04-29 16:04:33.237 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\service\impl\TicketLogServiceImpl.class]
2025-04-29 16:04:33.238 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\service\impl\TicketServiceImpl.class]
2025-04-29 16:04:33.238 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\service\impl\TicketVerifyServiceImpl.class]
2025-04-29 16:04:33.238 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\service\impl\WindowBusinessServiceImpl.class]
2025-04-29 16:04:33.238 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\service\impl\WindowServiceImpl.class]
2025-04-29 16:04:33.238 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\service\impl\WindowStatusServiceImpl.class]
2025-04-29 16:04:33.240 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\task\BaiShuiTokenSyncTask.class]
2025-04-29 16:04:33.266 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-swagger2/2.9.2/springfox-swagger2-2.9.2.jar!/springfox/documentation/swagger2/mappers/ServiceModelToSwagger2MapperImpl.class]
2025-04-29 16:04:33.266 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-swagger2/2.9.2/springfox-swagger2-2.9.2.jar!/springfox/documentation/swagger2/mappers/VendorExtensionsMapperImpl.class]
2025-04-29 16:04:33.268 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-swagger2/2.9.2/springfox-swagger2-2.9.2.jar!/springfox/documentation/swagger2/mappers/ParameterMapperImpl.class]
2025-04-29 16:04:33.269 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-swagger2/2.9.2/springfox-swagger2-2.9.2.jar!/springfox/documentation/swagger2/mappers/ModelMapperImpl.class]
2025-04-29 16:04:33.269 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-swagger2/2.9.2/springfox-swagger2-2.9.2.jar!/springfox/documentation/swagger2/mappers/LicenseMapperImpl.class]
2025-04-29 16:04:33.270 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-swagger2/2.9.2/springfox-swagger2-2.9.2.jar!/springfox/documentation/swagger2/mappers/SecurityMapperImpl.class]
2025-04-29 16:04:33.280 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/scanners/ApiListingReferenceScanner.class]
2025-04-29 16:04:33.280 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/scanners/ApiDocumentationScanner.class]
2025-04-29 16:04:33.280 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/scanners/ApiDescriptionReader.class]
2025-04-29 16:04:33.281 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/scanners/ApiListingReader.class]
2025-04-29 16:04:33.281 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/scanners/CachingOperationReader.class]
2025-04-29 16:04:33.281 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/scanners/MediaTypeReader.class]
2025-04-29 16:04:33.282 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/scanners/ApiListingScanner.class]
2025-04-29 16:04:33.283 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/scanners/ApiModelReader.class]
2025-04-29 16:04:33.284 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/scanners/ApiDescriptionLookup.class]
2025-04-29 16:04:33.285 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/readers/operation/OperationModelsProvider.class]
2025-04-29 16:04:33.285 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/readers/operation/OperationDeprecatedReader.class]
2025-04-29 16:04:33.286 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/readers/operation/ResponseMessagesReader.class]
2025-04-29 16:04:33.286 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/readers/operation/OperationParameterReader.class]
2025-04-29 16:04:33.286 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/readers/operation/DefaultTagsProvider.class]
2025-04-29 16:04:33.287 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/readers/operation/OperationTagsReader.class]
2025-04-29 16:04:33.288 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/readers/operation/ApiOperationReader.class]
2025-04-29 16:04:33.289 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/readers/operation/DefaultOperationReader.class]
2025-04-29 16:04:33.289 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/readers/operation/OperationParameterRequestConditionReader.class]
2025-04-29 16:04:33.289 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/readers/operation/OperationParameterHeadersConditionReader.class]
2025-04-29 16:04:33.290 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/readers/operation/OperationResponseClassReader.class]
2025-04-29 16:04:33.290 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/readers/operation/CachingOperationNameGenerator.class]
2025-04-29 16:04:33.292 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/readers/parameter/ParameterMultiplesReader.class]
2025-04-29 16:04:33.293 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/readers/parameter/ModelAttributeParameterExpander.class]
2025-04-29 16:04:33.293 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/readers/parameter/ParameterTypeReader.class]
2025-04-29 16:04:33.294 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/readers/parameter/ParameterRequiredReader.class]
2025-04-29 16:04:33.295 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/readers/parameter/ParameterDataTypeReader.class]
2025-04-29 16:04:33.295 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/readers/parameter/ParameterDefaultReader.class]
2025-04-29 16:04:33.296 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/readers/parameter/ParameterNameReader.class]
2025-04-29 16:04:33.296 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/readers/parameter/ExpandedParameterBuilder.class]
2025-04-29 16:04:33.300 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/plugins/DocumentationPluginsBootstrapper.class]
2025-04-29 16:04:33.301 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/plugins/WebMvcRequestHandlerProvider.class]
2025-04-29 16:04:33.301 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/plugins/DocumentationPluginsManager.class]
2025-04-29 16:04:33.303 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/paths/QueryStringUriTemplateDecorator.class]
2025-04-29 16:04:33.304 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/paths/PathMappingDecorator.class]
2025-04-29 16:04:33.305 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/paths/PathSanitizer.class]
2025-04-29 16:04:33.306 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/paths/OperationPathDecorator.class]
2025-04-29 16:04:33.324 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-schema/2.9.2/springfox-schema-2.9.2.jar!/springfox/documentation/schema/CachingModelDependencyProvider.class]
2025-04-29 16:04:33.324 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-schema/2.9.2/springfox-schema-2.9.2.jar!/springfox/documentation/schema/TypeNameExtractor.class]
2025-04-29 16:04:33.324 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-schema/2.9.2/springfox-schema-2.9.2.jar!/springfox/documentation/schema/plugins/PropertyDiscriminatorBasedInheritancePlugin.class]
2025-04-29 16:04:33.325 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-schema/2.9.2/springfox-schema-2.9.2.jar!/springfox/documentation/schema/plugins/XmlModelPlugin.class]
2025-04-29 16:04:33.326 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-schema/2.9.2/springfox-schema-2.9.2.jar!/springfox/documentation/schema/plugins/SchemaPluginsManager.class]
2025-04-29 16:04:33.326 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-schema/2.9.2/springfox-schema-2.9.2.jar!/springfox/documentation/schema/property/CachingModelPropertiesProvider.class]
2025-04-29 16:04:33.326 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-schema/2.9.2/springfox-schema-2.9.2.jar!/springfox/documentation/schema/property/ObjectMapperBeanPropertyNamingStrategy.class]
2025-04-29 16:04:33.327 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-schema/2.9.2/springfox-schema-2.9.2.jar!/springfox/documentation/schema/property/bean/AccessorsProvider.class]
2025-04-29 16:04:33.327 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-schema/2.9.2/springfox-schema-2.9.2.jar!/springfox/documentation/schema/property/field/FieldProvider.class]
2025-04-29 16:04:33.328 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-schema/2.9.2/springfox-schema-2.9.2.jar!/springfox/documentation/schema/property/XmlPropertyPlugin.class]
2025-04-29 16:04:33.328 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-schema/2.9.2/springfox-schema-2.9.2.jar!/springfox/documentation/schema/property/OptimizedModelPropertiesProvider.class]
2025-04-29 16:04:33.329 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-schema/2.9.2/springfox-schema-2.9.2.jar!/springfox/documentation/schema/property/FactoryMethodProvider.class]
2025-04-29 16:04:33.333 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-schema/2.9.2/springfox-schema-2.9.2.jar!/springfox/documentation/schema/CachingModelProvider.class]
2025-04-29 16:04:33.333 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-schema/2.9.2/springfox-schema-2.9.2.jar!/springfox/documentation/schema/DefaultModelDependencyProvider.class]
2025-04-29 16:04:33.333 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-schema/2.9.2/springfox-schema-2.9.2.jar!/springfox/documentation/schema/JacksonEnumTypeDeterminer.class]
2025-04-29 16:04:33.334 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-schema/2.9.2/springfox-schema-2.9.2.jar!/springfox/documentation/schema/DefaultModelProvider.class]
2025-04-29 16:04:33.342 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-swagger-common/2.9.2/springfox-swagger-common-2.9.2.jar!/springfox/documentation/swagger/schema/ApiModelPropertyPropertyBuilder.class]
2025-04-29 16:04:33.342 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-swagger-common/2.9.2/springfox-swagger-common-2.9.2.jar!/springfox/documentation/swagger/schema/ApiModelTypeNameProvider.class]
2025-04-29 16:04:33.343 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-swagger-common/2.9.2/springfox-swagger-common-2.9.2.jar!/springfox/documentation/swagger/schema/ApiModelBuilder.class]
2025-04-29 16:04:33.344 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-swagger-common/2.9.2/springfox-swagger-common-2.9.2.jar!/springfox/documentation/swagger/readers/operation/OperationImplicitParameterReader.class]
2025-04-29 16:04:33.346 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-swagger-common/2.9.2/springfox-swagger-common-2.9.2.jar!/springfox/documentation/swagger/readers/operation/VendorExtensionsReader.class]
2025-04-29 16:04:33.347 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-swagger-common/2.9.2/springfox-swagger-common-2.9.2.jar!/springfox/documentation/swagger/readers/operation/SwaggerOperationResponseClassReader.class]
2025-04-29 16:04:33.348 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-swagger-common/2.9.2/springfox-swagger-common-2.9.2.jar!/springfox/documentation/swagger/readers/operation/SwaggerOperationModelsProvider.class]
2025-04-29 16:04:33.348 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-swagger-common/2.9.2/springfox-swagger-common-2.9.2.jar!/springfox/documentation/swagger/readers/operation/SwaggerMediaTypeReader.class]
2025-04-29 16:04:33.348 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-swagger-common/2.9.2/springfox-swagger-common-2.9.2.jar!/springfox/documentation/swagger/readers/operation/OperationHttpMethodReader.class]
2025-04-29 16:04:33.348 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-swagger-common/2.9.2/springfox-swagger-common-2.9.2.jar!/springfox/documentation/swagger/readers/operation/OperationImplicitParametersReader.class]
2025-04-29 16:04:33.348 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-swagger-common/2.9.2/springfox-swagger-common-2.9.2.jar!/springfox/documentation/swagger/readers/operation/OperationAuthReader.class]
2025-04-29 16:04:33.350 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-swagger-common/2.9.2/springfox-swagger-common-2.9.2.jar!/springfox/documentation/swagger/readers/operation/OperationHiddenReader.class]
2025-04-29 16:04:33.350 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-swagger-common/2.9.2/springfox-swagger-common-2.9.2.jar!/springfox/documentation/swagger/readers/operation/OperationSummaryReader.class]
2025-04-29 16:04:33.350 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-swagger-common/2.9.2/springfox-swagger-common-2.9.2.jar!/springfox/documentation/swagger/readers/operation/SwaggerResponseMessageReader.class]
2025-04-29 16:04:33.351 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-swagger-common/2.9.2/springfox-swagger-common-2.9.2.jar!/springfox/documentation/swagger/readers/operation/OperationNicknameIntoUniqueIdReader.class]
2025-04-29 16:04:33.351 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-swagger-common/2.9.2/springfox-swagger-common-2.9.2.jar!/springfox/documentation/swagger/readers/operation/OperationPositionReader.class]
2025-04-29 16:04:33.351 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-swagger-common/2.9.2/springfox-swagger-common-2.9.2.jar!/springfox/documentation/swagger/readers/operation/OperationNotesReader.class]
2025-04-29 16:04:33.351 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-swagger-common/2.9.2/springfox-swagger-common-2.9.2.jar!/springfox/documentation/swagger/readers/operation/SwaggerOperationTagsReader.class]
2025-04-29 16:04:33.351 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-swagger-common/2.9.2/springfox-swagger-common-2.9.2.jar!/springfox/documentation/swagger/readers/parameter/ApiParamParameterBuilder.class]
2025-04-29 16:04:33.352 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-swagger-common/2.9.2/springfox-swagger-common-2.9.2.jar!/springfox/documentation/swagger/readers/parameter/SwaggerExpandedParameterBuilder.class]
2025-04-29 16:04:33.357 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-swagger-common/2.9.2/springfox-swagger-common-2.9.2.jar!/springfox/documentation/swagger/web/SwaggerApiListingReader.class]
2025-04-29 16:04:33.358 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-swagger-common/2.9.2/springfox-swagger-common-2.9.2.jar!/springfox/documentation/swagger/web/ClassOrApiAnnotationResourceGrouping.class]
2025-04-29 16:04:33.358 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-swagger-common/2.9.2/springfox-swagger-common-2.9.2.jar!/springfox/documentation/swagger/web/InMemorySwaggerResourcesProvider.class]
2025-04-29 16:04:33.359 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-swagger-common/2.9.2/springfox-swagger-common-2.9.2.jar!/springfox/documentation/swagger/web/ApiResourceController.class]
2025-04-29 16:04:33.542 [main] DEBUG o.s.c.e.PropertySourcesPropertyResolver - Found key 'spring.jmx.enabled' in PropertySource 'configurationProperties' with value of type String
2025-04-29 16:04:33.556 [main] DEBUG o.s.c.e.PropertySourcesPropertyResolver - Found key 'spring.jmx.enabled' in PropertySource 'configurationProperties' with value of type String
2025-04-29 16:04:33.557 [main] DEBUG o.s.c.e.PropertySourcesPropertyResolver - Found key 'spring.application.admin.enabled' in PropertySource 'configurationProperties' with value of type String
2025-04-29 16:04:33.716 [main] DEBUG o.s.c.e.PropertySourcesPropertyResolver - Found key 'spring.jmx.enabled' in PropertySource 'configurationProperties' with value of type String
2025-04-29 16:04:33.718 [main] DEBUG o.s.c.e.PropertySourcesPropertyResolver - Found key 'spring.application.admin.enabled' in PropertySource 'configurationProperties' with value of type String
2025-04-29 16:04:33.788 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'com.qs.admin.common.configurer.DataSourceConfig#MapperScannerRegistrar#0'
2025-04-29 16:04:33.795 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'propertySourcesPlaceholderConfigurer'
2025-04-29 16:04:33.855 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.context.properties.ConfigurationPropertiesBeanDefinitionValidator'
2025-04-29 16:04:33.922 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.context.event.internalEventListenerProcessor'
2025-04-29 16:04:33.922 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'preserveErrorControllerTargetClassPostProcessor'
2025-04-29 16:04:33.923 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.context.event.internalEventListenerFactory'
2025-04-29 16:04:33.923 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.transaction.config.internalTransactionalEventListenerFactory'
2025-04-29 16:04:33.925 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.context.annotation.internalAutowiredAnnotationProcessor'
2025-04-29 16:04:33.925 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.context.annotation.internalCommonAnnotationProcessor'
2025-04-29 16:04:33.927 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'lifecycleBeanPostProcessor'
2025-04-29 16:04:33.927 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'shiroConfig'
2025-04-29 16:04:33.927 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'shiroConfig' of type [com.qs.admin.common.configurer.ShiroConfig$$EnhancerBySpringCGLIB$$6009cbe2] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-04-29 16:04:33.937 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.context.properties.ConfigurationPropertiesBindingPostProcessor'
2025-04-29 16:04:33.937 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.context.internalConfigurationPropertiesBinder'
2025-04-29 16:04:33.937 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.context.internalConfigurationPropertiesBinderFactory'
2025-04-29 16:04:33.938 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'getLifecycleBeanPostProcessor'
2025-04-29 16:04:33.945 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.context.annotation.internalScheduledAnnotationProcessor'
2025-04-29 16:04:33.945 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.scheduling.annotation.SchedulingConfiguration'
2025-04-29 16:04:33.948 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'methodValidationPostProcessor'
2025-04-29 16:04:33.960 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'securityManager'
2025-04-29 16:04:33.960 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'shiroRealm'
2025-04-29 16:04:33.966 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'jwtUtil'
2025-04-29 16:04:33.967 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'jwtProperties'
2025-04-29 16:04:33.977 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'jwtProperties' of type [com.qs.admin.common.shiro.security.JwtProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-04-29 16:04:33.980 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'jwtUtil' of type [com.qs.admin.common.shiro.security.JwtUtil] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-04-29 16:04:33.980 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'shiroRealm' of type [com.qs.admin.common.shiro.ShiroRealm] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-04-29 16:04:33.980 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'shiroCacheManager'
2025-04-29 16:04:33.981 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'localCacheManager'
2025-04-29 16:04:33.997 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'localCacheManager' of type [com.qs.admin.common.utils.LocalCacheManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-04-29 16:04:33.997 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'shiroCacheManager' of type [com.qs.admin.common.shiro.cache.ShiroCacheManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-04-29 16:04:33.998 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Autowiring by type from bean name 'securityManager' via factory method to bean named 'shiroRealm'
2025-04-29 16:04:33.998 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Autowiring by type from bean name 'securityManager' via factory method to bean named 'shiroCacheManager'
2025-04-29 16:04:34.225 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'securityManager' of type [org.apache.shiro.web.mgt.DefaultWebSecurityManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-04-29 16:04:34.226 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'syncCacheServiceImpl'
2025-04-29 16:04:34.227 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'syncCacheServiceImpl' of type [com.qs.admin.common.service.impl.SyncCacheServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-04-29 16:04:34.227 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Autowiring by type from bean name 'shiroFilter' via factory method to bean named 'securityManager'
2025-04-29 16:04:34.227 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Autowiring by type from bean name 'shiroFilter' via factory method to bean named 'localCacheManager'
2025-04-29 16:04:34.227 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Autowiring by type from bean name 'shiroFilter' via factory method to bean named 'jwtProperties'
2025-04-29 16:04:34.227 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Autowiring by type from bean name 'shiroFilter' via factory method to bean named 'syncCacheServiceImpl'
2025-04-29 16:04:34.231 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'getShiroFilterProperties'
2025-04-29 16:04:34.236 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'getShiroFilterProperties' of type [com.qs.admin.common.shiro.ShiroFilterProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-04-29 16:04:34.246 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Autowiring by type from bean name 'methodValidationPostProcessor' via factory method to bean named 'environment'
2025-04-29 16:04:34.249 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'dataSourceInitializerPostProcessor'
2025-04-29 16:04:34.250 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.aop.config.internalAutoProxyCreator'
2025-04-29 16:04:34.259 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'persistenceExceptionTranslationPostProcessor'
2025-04-29 16:04:34.259 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Autowiring by type from bean name 'persistenceExceptionTranslationPostProcessor' via factory method to bean named 'environment'
2025-04-29 16:04:34.260 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'shiroFilter'
2025-04-29 16:04:34.261 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'authorizationAttributeSourceAdvisor'
2025-04-29 16:04:34.262 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Autowiring by type from bean name 'authorizationAttributeSourceAdvisor' via factory method to bean named 'securityManager'
2025-04-29 16:04:34.267 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'authorizationAttributeSourceAdvisor' of type [org.apache.shiro.spring.security.interceptor.AuthorizationAttributeSourceAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-04-29 16:04:34.268 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'dynamicDatasourceAnnotationAdvisor'
2025-04-29 16:04:34.270 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration'
2025-04-29 16:04:34.270 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.transaction.config.internalTransactionAdvisor'
2025-04-29 16:04:34.270 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration'
2025-04-29 16:04:34.281 [main] DEBUG o.s.a.a.a.ReflectiveAspectJAdvisorFactory - Found AspectJ method: public java.lang.Object com.qs.admin.common.aspect.IdempotencyAspect.around(org.aspectj.lang.ProceedingJoinPoint,org.springframework.web.bind.annotation.PostMapping) throws java.lang.Throwable
2025-04-29 16:04:34.288 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-04-29 16:04:34.336 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'transactionAttributeSource'
2025-04-29 16:04:34.341 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'transactionInterceptor'
2025-04-29 16:04:34.341 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Autowiring by type from bean name 'transactionInterceptor' via factory method to bean named 'transactionAttributeSource'
2025-04-29 16:04:34.344 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Autowiring by type from bean name 'org.springframework.transaction.config.internalTransactionAdvisor' via factory method to bean named 'transactionAttributeSource'
2025-04-29 16:04:34.344 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Autowiring by type from bean name 'org.springframework.transaction.config.internalTransactionAdvisor' via factory method to bean named 'transactionInterceptor'
2025-04-29 16:04:34.346 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'spring.datasource.dynamic-com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties'
2025-04-29 16:04:34.350 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.datasource.dynamic-com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-04-29 16:04:34.352 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration$$EnhancerBySpringCGLIB$$c9a23c78] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-04-29 16:04:34.354 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'dsProcessor'
2025-04-29 16:04:34.357 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dsProcessor' of type [com.baomidou.dynamic.datasource.processor.DsHeaderProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-04-29 16:04:34.357 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Autowiring by type from bean name 'dynamicDatasourceAnnotationAdvisor' via factory method to bean named 'dsProcessor'
2025-04-29 16:04:34.361 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dynamicDatasourceAnnotationAdvisor' of type [com.baomidou.dynamic.datasource.aop.DynamicDataSourceAnnotationAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-04-29 16:04:34.363 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'objectMapperConfigurer'
2025-04-29 16:04:34.364 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'webServerFactoryCustomizerBeanPostProcessor'
2025-04-29 16:04:34.364 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'errorPageRegistrarBeanPostProcessor'
2025-04-29 16:04:34.366 [main] DEBUG o.s.u.c.s.UiApplicationContextUtils - Unable to locate ThemeSource with name 'themeSource': using default [org.springframework.ui.context.support.ResourceBundleThemeSource@ffd3fd]
2025-04-29 16:04:34.367 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'undertowServletWebServerFactory'
2025-04-29 16:04:34.367 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.ServletWebServerFactoryConfiguration$EmbeddedUndertow'
2025-04-29 16:04:34.378 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'websocketServletWebServerCustomizer'
2025-04-29 16:04:34.378 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.websocket.servlet.WebSocketServletAutoConfiguration$UndertowWebSocketConfiguration'
2025-04-29 16:04:34.380 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'servletWebServerFactoryCustomizer'
2025-04-29 16:04:34.380 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.ServletWebServerFactoryAutoConfiguration'
2025-04-29 16:04:34.382 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'server-org.springframework.boot.autoconfigure.web.ServerProperties'
2025-04-29 16:04:34.388 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Autowiring by type from bean name 'servletWebServerFactoryCustomizer' via factory method to bean named 'server-org.springframework.boot.autoconfigure.web.ServerProperties'
2025-04-29 16:04:34.389 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'undertowWebServerFactoryCustomizer'
2025-04-29 16:04:34.389 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.embedded.EmbeddedWebServerFactoryCustomizerAutoConfiguration$UndertowWebServerFactoryCustomizerConfiguration'
2025-04-29 16:04:34.391 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Autowiring by type from bean name 'undertowWebServerFactoryCustomizer' via factory method to bean named 'environment'
2025-04-29 16:04:34.391 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Autowiring by type from bean name 'undertowWebServerFactoryCustomizer' via factory method to bean named 'server-org.springframework.boot.autoconfigure.web.ServerProperties'
2025-04-29 16:04:34.392 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'localeCharsetMappingsCustomizer'
2025-04-29 16:04:34.392 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.HttpEncodingAutoConfiguration'
2025-04-29 16:04:34.392 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'spring.http-org.springframework.boot.autoconfigure.http.HttpProperties'
2025-04-29 16:04:34.394 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.web.servlet.HttpEncodingAutoConfiguration' via constructor to bean named 'spring.http-org.springframework.boot.autoconfigure.http.HttpProperties'
2025-04-29 16:04:34.412 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'errorPageCustomizer'
2025-04-29 16:04:34.412 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.error.ErrorMvcAutoConfiguration'
2025-04-29 16:04:34.412 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.web.servlet.error.ErrorMvcAutoConfiguration' via constructor to bean named 'server-org.springframework.boot.autoconfigure.web.ServerProperties'
2025-04-29 16:04:34.414 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'dispatcherServletRegistration'
2025-04-29 16:04:34.414 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.DispatcherServletAutoConfiguration$DispatcherServletRegistrationConfiguration'
2025-04-29 16:04:34.415 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'dispatcherServlet'
2025-04-29 16:04:34.415 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.DispatcherServletAutoConfiguration$DispatcherServletConfiguration'
2025-04-29 16:04:34.419 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'spring.mvc-org.springframework.boot.autoconfigure.web.servlet.WebMvcProperties'
2025-04-29 16:04:34.425 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Autowiring by type from bean name 'dispatcherServlet' via factory method to bean named 'spring.http-org.springframework.boot.autoconfigure.http.HttpProperties'
2025-04-29 16:04:34.425 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Autowiring by type from bean name 'dispatcherServlet' via factory method to bean named 'spring.mvc-org.springframework.boot.autoconfigure.web.servlet.WebMvcProperties'
2025-04-29 16:04:34.443 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Autowiring by type from bean name 'dispatcherServletRegistration' via factory method to bean named 'dispatcherServlet'
2025-04-29 16:04:34.443 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Autowiring by type from bean name 'dispatcherServletRegistration' via factory method to bean named 'spring.mvc-org.springframework.boot.autoconfigure.web.servlet.WebMvcProperties'
2025-04-29 16:04:34.444 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'multipartConfigElement'
2025-04-29 16:04:34.444 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.MultipartAutoConfiguration'
2025-04-29 16:04:34.445 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'spring.servlet.multipart-org.springframework.boot.autoconfigure.web.servlet.MultipartProperties'
2025-04-29 16:04:34.448 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.web.servlet.MultipartAutoConfiguration' via constructor to bean named 'spring.servlet.multipart-org.springframework.boot.autoconfigure.web.servlet.MultipartProperties'
2025-04-29 16:04:34.455 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Autowiring by type from bean name 'errorPageCustomizer' via factory method to bean named 'dispatcherServletRegistration'
2025-04-29 16:04:34.478 [main] DEBUG o.s.b.w.e.u.UndertowServletWebServerFactory - Code archive: D:\DevelopmentTools\Maven_respository\org\springframework\boot\spring-boot\2.2.1.RELEASE\spring-boot-2.2.1.RELEASE.jar
2025-04-29 16:04:34.479 [main] DEBUG o.s.b.w.e.u.UndertowServletWebServerFactory - Code archive: D:\DevelopmentTools\Maven_respository\org\springframework\boot\spring-boot\2.2.1.RELEASE\spring-boot-2.2.1.RELEASE.jar
2025-04-29 16:04:34.479 [main] DEBUG o.s.b.w.e.u.UndertowServletWebServerFactory - None of the document roots [src/main/webapp, public, static] point to a directory and will be ignored.
2025-04-29 16:04:34.503 [main] WARN  io.undertow.websockets.jsr - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
2025-04-29 16:04:34.525 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-04-29 16:04:34.525 [main] DEBUG o.s.web.context.ContextLoader - Published root WebApplicationContext as ServletContext attribute with name [org.springframework.web.context.WebApplicationContext.ROOT]
2025-04-29 16:04:34.525 [main] INFO  o.s.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 1432 ms
2025-04-29 16:04:34.527 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'filterRegistrationBean'
2025-04-29 16:04:34.531 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'statViewServletRegistrationBean'
2025-04-29 16:04:34.532 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'com.alibaba.druid.spring.boot.autoconfigure.stat.DruidStatViewServletConfiguration'
2025-04-29 16:04:34.533 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'spring.datasource.druid-com.alibaba.druid.spring.boot.autoconfigure.properties.DruidStatProperties'
2025-04-29 16:04:34.535 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Autowiring by type from bean name 'statViewServletRegistrationBean' via factory method to bean named 'spring.datasource.druid-com.alibaba.druid.spring.boot.autoconfigure.properties.DruidStatProperties'
2025-04-29 16:04:34.540 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'webStatFilterRegistrationBean'
2025-04-29 16:04:34.540 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'com.alibaba.druid.spring.boot.autoconfigure.stat.DruidWebStatFilterConfiguration'
2025-04-29 16:04:34.542 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Autowiring by type from bean name 'webStatFilterRegistrationBean' via factory method to bean named 'spring.datasource.druid-com.alibaba.druid.spring.boot.autoconfigure.properties.DruidStatProperties'
2025-04-29 16:04:34.547 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'corsFilter'
2025-04-29 16:04:34.547 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'corsConfig'
2025-04-29 16:04:34.568 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'requestContextFilter'
2025-04-29 16:04:34.573 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'formContentFilter'
2025-04-29 16:04:34.573 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration'
2025-04-29 16:04:34.579 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'characterEncodingFilter'
2025-04-29 16:04:34.589 [main] DEBUG o.s.b.w.s.ServletContextInitializerBeans - Mapping filters: filterRegistrationBean urls=[/*] order=2147483647, filterRegistrationBean urls=[/*] order=2147483647, characterEncodingFilter urls=[/*] order=-2147483648, formContentFilter urls=[/*] order=-9900, requestContextFilter urls=[/*] order=-105, corsFilter urls=[/*] order=2147483647, shiroFilter urls=[/*] order=2147483647
2025-04-29 16:04:34.589 [main] DEBUG o.s.b.w.s.ServletContextInitializerBeans - Mapping servlets: dispatcherServlet urls=[/], statViewServlet urls=[/druid/*]
2025-04-29 16:04:34.612 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'qscAdminApplication'
2025-04-29 16:04:34.613 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'idempotencyAspect'
2025-04-29 16:04:34.613 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'dataSourceConfig'
2025-04-29 16:04:34.614 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'myWebMvcConfig'
2025-04-29 16:04:34.618 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'swagger2Configurer'
2025-04-29 16:04:34.621 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'mybatisPlusConfig'
2025-04-29 16:04:34.623 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'dataSource'
2025-04-29 16:04:34.678 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jdbc.DataSourceInitializerInvoker'
2025-04-29 16:04:34.678 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'spring.datasource-org.springframework.boot.autoconfigure.jdbc.DataSourceProperties'
2025-04-29 16:04:34.685 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.jdbc.DataSourceInitializerInvoker' via constructor to bean named 'spring.datasource-org.springframework.boot.autoconfigure.jdbc.DataSourceProperties'
2025-04-29 16:04:34.685 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.jdbc.DataSourceInitializerInvoker' via constructor to bean named 'org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@156c0a7'
2025-04-29 16:04:34.692 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'mybatis-org.mybatis.spring.boot.autoconfigure.MybatisProperties'
2025-04-29 16:04:34.695 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'mybatisPlusInterceptor'
2025-04-29 16:04:34.707 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'globalExceptionHandler'
2025-04-29 16:04:34.708 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'shiroFilterProperties'
2025-04-29 16:04:34.712 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'springContextUtil'
2025-04-29 16:04:34.713 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'baiShuiProperties'
2025-04-29 16:04:34.714 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'restTemplateConfig'
2025-04-29 16:04:34.715 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'agentInfoController'
2025-04-29 16:04:34.717 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'agentInfoServiceImpl'
2025-04-29 16:04:34.728 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'agentInfoMapper'
2025-04-29 16:04:34.731 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'mybatisSqlSessionFactoryBean'
2025-04-29 16:04:35.131 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'agentInfoEnterpriseMapper'
2025-04-29 16:04:35.155 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'agentInfoEnterpriseServiceImpl'
2025-04-29 16:04:35.193 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'ticketVerifyServiceImpl'
2025-04-29 16:04:35.194 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'ticketVerifyMapper'
2025-04-29 16:04:35.263 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'baiShuiApiServiceImpl'
2025-04-29 16:04:35.264 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'restTemplate'
2025-04-29 16:04:35.279 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'systemServiceImpl'
2025-04-29 16:04:35.280 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'systemMapper'
2025-04-29 16:04:35.321 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'baiShuiTokenSyncTask'
2025-04-29 16:04:40.187 [main] INFO  c.q.a.t.s.impl.BaiShuiApiServiceImpl - 获取佰税API访问令牌
2025-04-29 16:04:51.280 [main] INFO  c.q.a.t.s.impl.BaiShuiApiServiceImpl - tokenUrl=https://etax.zhejiang.chinatax.gov.cn/nsfw/apiService/oauth/token
2025-04-29 16:04:53.217 [main] DEBUG o.s.web.client.RestTemplate - HTTP POST https://etax.zhejiang.chinatax.gov.cn/nsfw/apiService/oauth/token
2025-04-29 16:04:53.250 [main] DEBUG o.s.web.client.RestTemplate - Accept=[application/json, application/*+json]
2025-04-29 16:04:53.256 [main] DEBUG o.s.web.client.RestTemplate - Writing [{appKey=[HZBSJHXT], appSecret=[7d427891ef38f4a7b247447f715c66cd]}] as "application/x-www-form-urlencoded"
2025-04-29 16:04:53.618 [main] DEBUG o.s.web.client.RestTemplate - Response 200 OK
2025-04-29 16:04:53.621 [main] DEBUG o.s.web.client.RestTemplate - Reading to [java.util.Map<?, ?>]
2025-04-29 16:04:59.021 [main] INFO  c.q.a.t.s.impl.BaiShuiApiServiceImpl - 成功获取佰税API访问令牌
2025-04-29 16:05:13.412 [main] DEBUG o.s.jdbc.datasource.DataSourceUtils - Fetching JDBC Connection from DataSource
2025-04-29 16:05:13.413 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-04-29 16:05:14.547 [main] WARN  c.m.s.jdbc.internals.TDS.Channel - TLSv1 was negotiated. Please update server and client to use TLSv1.2 at minimum.
2025-04-29 16:05:15.558 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-04-29 16:05:15.584 [main] DEBUG c.q.a.t.m.SystemMapper.selectList - ==>  Preparing: SELECT [KEY],[VALUE],[MEMO] FROM system WHERE ([KEY] = ?)
2025-04-29 16:05:15.716 [HikariPool-1 connection adder] WARN  c.m.s.jdbc.internals.TDS.Channel - TLSv1 was negotiated. Please update server and client to use TLSv1.2 at minimum.
2025-04-29 16:05:15.779 [HikariPool-1 connection adder] WARN  c.m.s.jdbc.internals.TDS.Channel - TLSv1 was negotiated. Please update server and client to use TLSv1.2 at minimum.
2025-04-29 16:05:15.833 [HikariPool-1 connection adder] WARN  c.m.s.jdbc.internals.TDS.Channel - TLSv1 was negotiated. Please update server and client to use TLSv1.2 at minimum.
2025-04-29 16:05:15.912 [HikariPool-1 connection adder] WARN  c.m.s.jdbc.internals.TDS.Channel - TLSv1 was negotiated. Please update server and client to use TLSv1.2 at minimum.
2025-04-29 16:05:15.968 [main] DEBUG c.q.a.t.m.SystemMapper.selectList - ==> Parameters: BS_API_APPKEY(String)
2025-04-29 16:05:15.975 [HikariPool-1 connection adder] WARN  c.m.s.jdbc.internals.TDS.Channel - TLSv1 was negotiated. Please update server and client to use TLSv1.2 at minimum.
2025-04-29 16:05:16.052 [HikariPool-1 connection adder] WARN  c.m.s.jdbc.internals.TDS.Channel - TLSv1 was negotiated. Please update server and client to use TLSv1.2 at minimum.
2025-04-29 16:05:16.123 [HikariPool-1 connection adder] WARN  c.m.s.jdbc.internals.TDS.Channel - TLSv1 was negotiated. Please update server and client to use TLSv1.2 at minimum.
2025-04-29 16:05:16.204 [HikariPool-1 connection adder] WARN  c.m.s.jdbc.internals.TDS.Channel - TLSv1 was negotiated. Please update server and client to use TLSv1.2 at minimum.
2025-04-29 16:05:16.247 [main] DEBUG c.q.a.t.m.SystemMapper.selectList - <==      Total: 1
2025-04-29 16:05:16.278 [HikariPool-1 connection adder] WARN  c.m.s.jdbc.internals.TDS.Channel - TLSv1 was negotiated. Please update server and client to use TLSv1.2 at minimum.
2025-04-29 16:05:20.875 [main] DEBUG o.s.jdbc.datasource.DataSourceUtils - Fetching JDBC Connection from DataSource
2025-04-29 16:05:20.883 [main] DEBUG c.q.a.t.m.SystemMapper.selectList - ==>  Preparing: SELECT [KEY],[VALUE],[MEMO] FROM system WHERE ([KEY] = ?)
2025-04-29 16:05:20.883 [main] DEBUG c.q.a.t.m.SystemMapper.selectList - ==> Parameters: BS_API_TOKEN(String)
2025-04-29 16:06:05.985 [main] DEBUG o.s.b.c.l.ClasspathLoggingApplicationListener - Application started with classpath: [file:/D:/DevelopmentTools/Java/java-8u311/jre/lib/charsets.jar, file:/D:/DevelopmentTools/Java/java-8u311/jre/lib/deploy.jar, file:/D:/DevelopmentTools/Java/java-8u311/jre/lib/ext/access-bridge-32.jar, file:/D:/DevelopmentTools/Java/java-8u311/jre/lib/ext/cldrdata.jar, file:/D:/DevelopmentTools/Java/java-8u311/jre/lib/ext/dnsns.jar, file:/D:/DevelopmentTools/Java/java-8u311/jre/lib/ext/jaccess.jar, file:/D:/DevelopmentTools/Java/java-8u311/jre/lib/ext/jfxrt.jar, file:/D:/DevelopmentTools/Java/java-8u311/jre/lib/ext/localedata.jar, file:/D:/DevelopmentTools/Java/java-8u311/jre/lib/ext/nashorn.jar, file:/D:/DevelopmentTools/Java/java-8u311/jre/lib/ext/sunec.jar, file:/D:/DevelopmentTools/Java/java-8u311/jre/lib/ext/sunjce_provider.jar, file:/D:/DevelopmentTools/Java/java-8u311/jre/lib/ext/sunmscapi.jar, file:/D:/DevelopmentTools/Java/java-8u311/jre/lib/ext/sunpkcs11.jar, file:/D:/DevelopmentTools/Java/java-8u311/jre/lib/ext/zipfs.jar, file:/D:/DevelopmentTools/Java/java-8u311/jre/lib/javaws.jar, file:/D:/DevelopmentTools/Java/java-8u311/jre/lib/jce.jar, file:/D:/DevelopmentTools/Java/java-8u311/jre/lib/jfr.jar, file:/D:/DevelopmentTools/Java/java-8u311/jre/lib/jfxswt.jar, file:/D:/DevelopmentTools/Java/java-8u311/jre/lib/jsse.jar, file:/D:/DevelopmentTools/Java/java-8u311/jre/lib/management-agent.jar, file:/D:/DevelopmentTools/Java/java-8u311/jre/lib/plugin.jar, file:/D:/DevelopmentTools/Java/java-8u311/jre/lib/resources.jar, file:/D:/DevelopmentTools/Java/java-8u311/jre/lib/rt.jar, file:/D:/project/Java%20Projectes/qsadmin/target/classes/, file:/D:/DevelopmentTools/Maven_respository/com/microsoft/sqlserver/mssql-jdbc/12.6.1.jre8/mssql-jdbc-12.6.1.jre8.jar, file:/D:/DevelopmentTools/Maven_respository/org/springframework/boot/spring-boot-starter-web/2.2.1.RELEASE/spring-boot-starter-web-2.2.1.RELEASE.jar, file:/D:/DevelopmentTools/Maven_respository/org/springframework/boot/spring-boot-starter/2.2.1.RELEASE/spring-boot-starter-2.2.1.RELEASE.jar, file:/D:/DevelopmentTools/Maven_respository/org/springframework/boot/spring-boot/2.2.1.RELEASE/spring-boot-2.2.1.RELEASE.jar, file:/D:/DevelopmentTools/Maven_respository/org/springframework/boot/spring-boot-starter-logging/2.2.1.RELEASE/spring-boot-starter-logging-2.2.1.RELEASE.jar, file:/D:/DevelopmentTools/Maven_respository/ch/qos/logback/logback-classic/1.2.3/logback-classic-1.2.3.jar, file:/D:/DevelopmentTools/Maven_respository/ch/qos/logback/logback-core/1.2.3/logback-core-1.2.3.jar, file:/D:/DevelopmentTools/Maven_respository/org/apache/logging/log4j/log4j-to-slf4j/2.12.1/log4j-to-slf4j-2.12.1.jar, file:/D:/DevelopmentTools/Maven_respository/org/apache/logging/log4j/log4j-api/2.12.1/log4j-api-2.12.1.jar, file:/D:/DevelopmentTools/Maven_respository/org/slf4j/jul-to-slf4j/1.7.29/jul-to-slf4j-1.7.29.jar, file:/D:/DevelopmentTools/Maven_respository/jakarta/annotation/jakarta.annotation-api/1.3.5/jakarta.annotation-api-1.3.5.jar, file:/D:/DevelopmentTools/Maven_respository/org/yaml/snakeyaml/1.25/snakeyaml-1.25.jar, file:/D:/DevelopmentTools/Maven_respository/org/springframework/boot/spring-boot-starter-json/2.2.1.RELEASE/spring-boot-starter-json-2.2.1.RELEASE.jar, file:/D:/DevelopmentTools/Maven_respository/com/fasterxml/jackson/datatype/jackson-datatype-jdk8/2.10.0/jackson-datatype-jdk8-2.10.0.jar, file:/D:/DevelopmentTools/Maven_respository/com/fasterxml/jackson/datatype/jackson-datatype-jsr310/2.10.0/jackson-datatype-jsr310-2.10.0.jar, file:/D:/DevelopmentTools/Maven_respository/com/fasterxml/jackson/module/jackson-module-parameter-names/2.10.0/jackson-module-parameter-names-2.10.0.jar, file:/D:/DevelopmentTools/Maven_respository/org/springframework/boot/spring-boot-starter-validation/2.2.1.RELEASE/spring-boot-starter-validation-2.2.1.RELEASE.jar, file:/D:/DevelopmentTools/Maven_respository/jakarta/validation/jakarta.validation-api/2.0.1/jakarta.validation-api-2.0.1.jar, file:/D:/DevelopmentTools/Maven_respository/org/hibernate/validator/hibernate-validator/6.0.18.Final/hibernate-validator-6.0.18.Final.jar, file:/D:/DevelopmentTools/Maven_respository/org/springframework/spring-web/5.2.1.RELEASE/spring-web-5.2.1.RELEASE.jar, file:/D:/DevelopmentTools/Maven_respository/org/springframework/spring-webmvc/5.2.1.RELEASE/spring-webmvc-5.2.1.RELEASE.jar, file:/D:/DevelopmentTools/Maven_respository/org/springframework/boot/spring-boot-starter-undertow/2.2.1.RELEASE/spring-boot-starter-undertow-2.2.1.RELEASE.jar, file:/D:/DevelopmentTools/Maven_respository/io/undertow/undertow-core/2.0.27.Final/undertow-core-2.0.27.Final.jar, file:/D:/DevelopmentTools/Maven_respository/org/jboss/logging/jboss-logging/3.4.1.Final/jboss-logging-3.4.1.Final.jar, file:/D:/DevelopmentTools/Maven_respository/org/jboss/xnio/xnio-api/3.3.8.Final/xnio-api-3.3.8.Final.jar, file:/D:/DevelopmentTools/Maven_respository/org/jboss/xnio/xnio-nio/3.3.8.Final/xnio-nio-3.3.8.Final.jar, file:/D:/DevelopmentTools/Maven_respository/io/undertow/undertow-servlet/2.0.27.Final/undertow-servlet-2.0.27.Final.jar, file:/D:/DevelopmentTools/Maven_respository/org/jboss/spec/javax/annotation/jboss-annotations-api_1.2_spec/1.0.2.Final/jboss-annotations-api_1.2_spec-1.0.2.Final.jar, file:/D:/DevelopmentTools/Maven_respository/io/undertow/undertow-websockets-jsr/2.0.27.Final/undertow-websockets-jsr-2.0.27.Final.jar, file:/D:/DevelopmentTools/Maven_respository/org/jboss/spec/javax/websocket/jboss-websocket-api_1.1_spec/1.1.4.Final/jboss-websocket-api_1.1_spec-1.1.4.Final.jar, file:/D:/DevelopmentTools/Maven_respository/jakarta/servlet/jakarta.servlet-api/4.0.3/jakarta.servlet-api-4.0.3.jar, file:/D:/DevelopmentTools/Maven_respository/org/glassfish/jakarta.el/3.0.3/jakarta.el-3.0.3.jar, file:/D:/DevelopmentTools/Maven_respository/net/bytebuddy/byte-buddy/1.10.2/byte-buddy-1.10.2.jar, file:/D:/DevelopmentTools/Maven_respository/org/springframework/spring-core/5.2.1.RELEASE/spring-core-5.2.1.RELEASE.jar, file:/D:/DevelopmentTools/Maven_respository/org/springframework/spring-jcl/5.2.1.RELEASE/spring-jcl-5.2.1.RELEASE.jar, file:/D:/DevelopmentTools/Maven_respository/com/baomidou/dynamic-datasource-spring-boot-starter/3.0.0/dynamic-datasource-spring-boot-starter-3.0.0.jar, file:/D:/DevelopmentTools/Maven_respository/org/springframework/boot/spring-boot-starter-jdbc/2.2.1.RELEASE/spring-boot-starter-jdbc-2.2.1.RELEASE.jar, file:/D:/DevelopmentTools/Maven_respository/com/zaxxer/HikariCP/3.4.1/HikariCP-3.4.1.jar, file:/D:/DevelopmentTools/Maven_respository/org/springframework/spring-jdbc/5.2.1.RELEASE/spring-jdbc-5.2.1.RELEASE.jar, file:/D:/DevelopmentTools/Maven_respository/org/springframework/spring-tx/5.2.1.RELEASE/spring-tx-5.2.1.RELEASE.jar, file:/D:/DevelopmentTools/Maven_respository/org/springframework/boot/spring-boot-starter-aop/2.2.1.RELEASE/spring-boot-starter-aop-2.2.1.RELEASE.jar, file:/D:/DevelopmentTools/Maven_respository/org/aspectj/aspectjweaver/1.9.4/aspectjweaver-1.9.4.jar, file:/D:/DevelopmentTools/Maven_respository/org/apache/tomcat/tomcat-jdbc/7.0.81/tomcat-jdbc-7.0.81.jar, file:/D:/DevelopmentTools/Maven_respository/org/apache/tomcat/tomcat-juli/7.0.81/tomcat-juli-7.0.81.jar, file:/D:/DevelopmentTools/Maven_respository/com/belerweb/pinyin4j/2.5.0/pinyin4j-2.5.0.jar, file:/D:/DevelopmentTools/Maven_respository/org/apache/ant/ant/1.9.7/ant-1.9.7.jar, file:/D:/DevelopmentTools/Maven_respository/org/apache/ant/ant-launcher/1.9.7/ant-launcher-1.9.7.jar, file:/D:/DevelopmentTools/Maven_respository/net/coobird/thumbnailator/0.4.8/thumbnailator-0.4.8.jar, file:/D:/DevelopmentTools/Maven_respository/joda-time/joda-time/2.9.8/joda-time-2.9.8.jar, file:/D:/DevelopmentTools/Maven_respository/org/apache/velocity/velocity-engine-core/2.0/velocity-engine-core-2.0.jar, file:/D:/DevelopmentTools/Maven_respository/org/slf4j/slf4j-api/1.7.29/slf4j-api-1.7.29.jar, file:/D:/DevelopmentTools/Maven_respository/cn/hutool/hutool-all/4.1.2/hutool-all-4.1.2.jar, file:/D:/DevelopmentTools/Maven_respository/org/projectlombok/lombok/1.18.10/lombok-1.18.10.jar, file:/D:/DevelopmentTools/Maven_respository/com/baomidou/mybatis-plus-boot-starter/3.4.3.4/mybatis-plus-boot-starter-3.4.3.4.jar, file:/D:/DevelopmentTools/Maven_respository/com/baomidou/mybatis-plus/3.4.3.4/mybatis-plus-3.4.3.4.jar, file:/D:/DevelopmentTools/Maven_respository/com/baomidou/mybatis-plus-extension/3.4.3.4/mybatis-plus-extension-3.4.3.4.jar, file:/D:/DevelopmentTools/Maven_respository/com/baomidou/mybatis-plus-core/3.4.3.4/mybatis-plus-core-3.4.3.4.jar, file:/D:/DevelopmentTools/Maven_respository/com/baomidou/mybatis-plus-annotation/3.4.3.4/mybatis-plus-annotation-3.4.3.4.jar, file:/D:/DevelopmentTools/Maven_respository/com/github/jsqlparser/jsqlparser/4.2/jsqlparser-4.2.jar, file:/D:/DevelopmentTools/Maven_respository/org/springframework/boot/spring-boot-autoconfigure/2.2.1.RELEASE/spring-boot-autoconfigure-2.2.1.RELEASE.jar, file:/D:/DevelopmentTools/Maven_respository/org/mybatis/spring/boot/mybatis-spring-boot-starter/2.1.0/mybatis-spring-boot-starter-2.1.0.jar, file:/D:/DevelopmentTools/Maven_respository/org/mybatis/spring/boot/mybatis-spring-boot-autoconfigure/2.1.0/mybatis-spring-boot-autoconfigure-2.1.0.jar, file:/D:/DevelopmentTools/Maven_respository/org/mybatis/mybatis-spring/2.0.2/mybatis-spring-2.0.2.jar, file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-swagger2/2.9.2/springfox-swagger2-2.9.2.jar, file:/D:/DevelopmentTools/Maven_respository/io/swagger/swagger-annotations/1.5.20/swagger-annotations-1.5.20.jar, file:/D:/DevelopmentTools/Maven_respository/io/swagger/swagger-models/1.5.20/swagger-models-1.5.20.jar, file:/D:/DevelopmentTools/Maven_respository/com/fasterxml/jackson/core/jackson-annotations/2.10.0/jackson-annotations-2.10.0.jar, file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spi/2.9.2/springfox-spi-2.9.2.jar, file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-core/2.9.2/springfox-core-2.9.2.jar, file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-schema/2.9.2/springfox-schema-2.9.2.jar, file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-swagger-common/2.9.2/springfox-swagger-common-2.9.2.jar, file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar, file:/D:/DevelopmentTools/Maven_respository/com/fasterxml/classmate/1.5.1/classmate-1.5.1.jar, file:/D:/DevelopmentTools/Maven_respository/org/springframework/plugin/spring-plugin-core/1.2.0.RELEASE/spring-plugin-core-1.2.0.RELEASE.jar, file:/D:/DevelopmentTools/Maven_respository/org/springframework/plugin/spring-plugin-metadata/1.2.0.RELEASE/spring-plugin-metadata-1.2.0.RELEASE.jar, file:/D:/DevelopmentTools/Maven_respository/org/mapstruct/mapstruct/1.2.0.Final/mapstruct-1.2.0.Final.jar, file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-swagger-ui/2.9.2/springfox-swagger-ui-2.9.2.jar, file:/D:/DevelopmentTools/Maven_respository/org/apache/commons/commons-lang3/3.8.1/commons-lang3-3.8.1.jar, file:/D:/DevelopmentTools/Maven_respository/com/google/guava/guava/27.0-jre/guava-27.0-jre.jar, file:/D:/DevelopmentTools/Maven_respository/com/google/guava/failureaccess/1.0/failureaccess-1.0.jar, file:/D:/DevelopmentTools/Maven_respository/com/google/guava/listenablefuture/9999.0-empty-to-avoid-conflict-with-guava/listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar, file:/D:/DevelopmentTools/Maven_respository/com/google/code/findbugs/jsr305/3.0.2/jsr305-3.0.2.jar, file:/D:/DevelopmentTools/Maven_respository/org/checkerframework/checker-qual/2.5.2/checker-qual-2.5.2.jar, file:/D:/DevelopmentTools/Maven_respository/com/google/errorprone/error_prone_annotations/2.2.0/error_prone_annotations-2.2.0.jar, file:/D:/DevelopmentTools/Maven_respository/com/google/j2objc/j2objc-annotations/1.1/j2objc-annotations-1.1.jar, file:/D:/DevelopmentTools/Maven_respository/org/codehaus/mojo/animal-sniffer-annotations/1.17/animal-sniffer-annotations-1.17.jar, file:/D:/DevelopmentTools/Maven_respository/com/alibaba/fastjson/1.2.83/fastjson-1.2.83.jar, file:/D:/DevelopmentTools/Maven_respository/com/alibaba/druid-spring-boot-starter/1.1.10/druid-spring-boot-starter-1.1.10.jar, file:/D:/DevelopmentTools/Maven_respository/com/alibaba/druid/1.1.10/druid-1.1.10.jar, file:/D:/DevelopmentTools/Maven_respository/org/apache/shiro/shiro-spring/1.4.0/shiro-spring-1.4.0.jar, file:/D:/DevelopmentTools/Maven_respository/org/apache/shiro/shiro-core/1.4.0/shiro-core-1.4.0.jar, file:/D:/DevelopmentTools/Maven_respository/org/apache/shiro/shiro-lang/1.4.0/shiro-lang-1.4.0.jar, file:/D:/DevelopmentTools/Maven_respository/org/apache/shiro/shiro-cache/1.4.0/shiro-cache-1.4.0.jar, file:/D:/DevelopmentTools/Maven_respository/org/apache/shiro/shiro-crypto-hash/1.4.0/shiro-crypto-hash-1.4.0.jar, file:/D:/DevelopmentTools/Maven_respository/org/apache/shiro/shiro-crypto-core/1.4.0/shiro-crypto-core-1.4.0.jar, file:/D:/DevelopmentTools/Maven_respository/org/apache/shiro/shiro-crypto-cipher/1.4.0/shiro-crypto-cipher-1.4.0.jar, file:/D:/DevelopmentTools/Maven_respository/org/apache/shiro/shiro-config-core/1.4.0/shiro-config-core-1.4.0.jar, file:/D:/DevelopmentTools/Maven_respository/org/apache/shiro/shiro-config-ogdl/1.4.0/shiro-config-ogdl-1.4.0.jar, file:/D:/DevelopmentTools/Maven_respository/commons-beanutils/commons-beanutils/1.9.3/commons-beanutils-1.9.3.jar, file:/D:/DevelopmentTools/Maven_respository/commons-collections/commons-collections/3.2.2/commons-collections-3.2.2.jar, file:/D:/DevelopmentTools/Maven_respository/org/apache/shiro/shiro-event/1.4.0/shiro-event-1.4.0.jar, file:/D:/DevelopmentTools/Maven_respository/org/apache/shiro/shiro-web/1.4.0/shiro-web-1.4.0.jar, file:/D:/DevelopmentTools/Maven_respository/com/auth0/java-jwt/3.18.2/java-jwt-3.18.2.jar, file:/D:/DevelopmentTools/Maven_respository/com/fasterxml/jackson/core/jackson-databind/2.10.0/jackson-databind-2.10.0.jar, file:/D:/DevelopmentTools/Maven_respository/com/fasterxml/jackson/core/jackson-core/2.10.0/jackson-core-2.10.0.jar, file:/D:/DevelopmentTools/Maven_respository/org/springframework/spring-context/5.2.23.RELEASE/spring-context-5.2.23.RELEASE.jar, file:/D:/DevelopmentTools/Maven_respository/org/springframework/spring-aop/5.2.1.RELEASE/spring-aop-5.2.1.RELEASE.jar, file:/D:/DevelopmentTools/Maven_respository/org/springframework/spring-beans/5.2.1.RELEASE/spring-beans-5.2.1.RELEASE.jar, file:/D:/DevelopmentTools/Maven_respository/org/springframework/spring-expression/5.2.1.RELEASE/spring-expression-5.2.1.RELEASE.jar, file:/D:/DevelopmentTools/Maven_respository/org/mybatis/mybatis/3.5.7/mybatis-3.5.7.jar, file:/D:/DevelopmentTools/Maven_respository/com/github/ben-manes/caffeine/caffeine/2.8.0/caffeine-2.8.0.jar, file:/D:/DevelopmentTools/Maven_respository/com/github/xiaoymin/swagger-bootstrap-ui/1.9.3/swagger-bootstrap-ui-1.9.3.jar, file:/D:/DevelopmentTools/IntelliJ%20IDEA%202025/lib/idea_rt.jar, file:/C:/Users/<USER>/AppData/Local/JetBrains/IntelliJIdea2025.1/captureAgent/debugger-agent.jar]
2025-04-29 16:06:06.046 [main] INFO  com.qs.admin.QscAdminApplication - Starting QscAdminApplication on MSI with PID 37076 (started by KarlKyo in D:\project\Java Projectes\qsadmin)
2025-04-29 16:06:06.046 [main] DEBUG com.qs.admin.QscAdminApplication - Running with Spring Boot v2.2.1.RELEASE, Spring v5.2.23.RELEASE
2025-04-29 16:06:06.046 [main] INFO  com.qs.admin.QscAdminApplication - The following profiles are active: dev
2025-04-29 16:06:06.046 [main] DEBUG o.s.boot.SpringApplication - Loading source class com.qs.admin.QscAdminApplication
2025-04-29 16:06:06.073 [main] DEBUG o.s.b.c.c.ConfigFileApplicationListener - Activated activeProfiles dev
2025-04-29 16:06:06.073 [main] DEBUG o.s.b.c.c.ConfigFileApplicationListener - Loaded config file 'file:/D:/project/Java%20Projectes/qsadmin/target/classes/application.yml' (classpath:/application.yml)
2025-04-29 16:06:06.073 [main] DEBUG o.s.b.c.c.ConfigFileApplicationListener - Loaded config file 'file:/D:/project/Java%20Projectes/qsadmin/target/classes/application-dev.yml' (classpath:/application-dev.yml) for profile dev
2025-04-29 16:06:06.073 [main] DEBUG o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Refreshing org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@17a1e2d
2025-04-29 16:06:06.084 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.context.annotation.internalConfigurationAnnotationProcessor'
2025-04-29 16:06:06.095 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.internalCachingMetadataReaderFactory'
2025-04-29 16:06:06.134 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\common\aspect\IdempotencyAspect.class]
2025-04-29 16:06:06.135 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\common\configurer\CorsConfig.class]
2025-04-29 16:06:06.137 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\common\configurer\DataSourceConfig.class]
2025-04-29 16:06:06.138 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\common\configurer\MyWebMvcConfig.class]
2025-04-29 16:06:06.138 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\common\configurer\ShiroConfig.class]
2025-04-29 16:06:06.138 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\common\configurer\Swagger2Configurer.class]
2025-04-29 16:06:06.140 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\common\core\MybatisPlusConfig.class]
2025-04-29 16:06:06.145 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\common\exception\GlobalExceptionHandler.class]
2025-04-29 16:06:06.147 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\common\service\impl\SyncCacheServiceImpl.class]
2025-04-29 16:06:06.147 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\common\shiro\ShiroFilterProperties.class]
2025-04-29 16:06:06.147 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\common\shiro\ShiroRealm.class]
2025-04-29 16:06:06.148 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\common\shiro\cache\ShiroCacheManager.class]
2025-04-29 16:06:06.151 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\common\shiro\security\JwtProperties.class]
2025-04-29 16:06:06.152 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\common\shiro\security\JwtUtil.class]
2025-04-29 16:06:06.153 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\common\utils\LocalCacheManager.class]
2025-04-29 16:06:06.153 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\common\utils\SpringContextUtil.class]
2025-04-29 16:06:06.154 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\config\BaiShuiProperties.class]
2025-04-29 16:06:06.154 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\config\RestTemplateConfig.class]
2025-04-29 16:06:06.158 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\controller\AgentInfoController.class]
2025-04-29 16:06:06.165 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\controller\AutocodeController.class]
2025-04-29 16:06:06.166 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\controller\BusinessController.class]
2025-04-29 16:06:06.166 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\controller\ClientInfoController.class]
2025-04-29 16:06:06.167 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\controller\EmployeeController.class]
2025-04-29 16:06:06.167 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\controller\NoticeController.class]
2025-04-29 16:06:06.167 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\controller\SystemController.class]
2025-04-29 16:06:06.168 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\controller\SystemLogController.class]
2025-04-29 16:06:06.168 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\controller\ThingsController.class]
2025-04-29 16:06:06.168 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\controller\TicketBookController.class]
2025-04-29 16:06:06.168 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\controller\TicketController.class]
2025-04-29 16:06:06.169 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\controller\TicketExchangeController.class]
2025-04-29 16:06:06.169 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\controller\TicketLogController.class]
2025-04-29 16:06:06.169 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\controller\TicketVerifyController.class]
2025-04-29 16:06:06.170 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\controller\WindowBusinessController.class]
2025-04-29 16:06:06.171 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\controller\WindowController.class]
2025-04-29 16:06:06.171 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\controller\WindowStatusController.class]
2025-04-29 16:06:06.188 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\service\impl\AgentInfoEnterpriseServiceImpl.class]
2025-04-29 16:06:06.188 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\service\impl\AgentInfoServiceImpl.class]
2025-04-29 16:06:06.189 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\service\impl\AutocodeServiceImpl.class]
2025-04-29 16:06:06.189 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\service\impl\BaiShuiApiServiceImpl.class]
2025-04-29 16:06:06.189 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\service\impl\BusinessServiceImpl.class]
2025-04-29 16:06:06.189 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\service\impl\ClientInfoServiceImpl.class]
2025-04-29 16:06:06.189 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\service\impl\EmployeeServiceImpl.class]
2025-04-29 16:06:06.190 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\service\impl\NoticeServiceImpl.class]
2025-04-29 16:06:06.190 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\service\impl\SystemLogServiceImpl.class]
2025-04-29 16:06:06.190 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\service\impl\SystemServiceImpl.class]
2025-04-29 16:06:06.190 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\service\impl\ThingsServiceImpl.class]
2025-04-29 16:06:06.190 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\service\impl\TicketBookServiceImpl.class]
2025-04-29 16:06:06.190 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\service\impl\TicketExchangeServiceImpl.class]
2025-04-29 16:06:06.191 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\service\impl\TicketLogServiceImpl.class]
2025-04-29 16:06:06.191 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\service\impl\TicketServiceImpl.class]
2025-04-29 16:06:06.191 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\service\impl\TicketVerifyServiceImpl.class]
2025-04-29 16:06:06.191 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\service\impl\WindowBusinessServiceImpl.class]
2025-04-29 16:06:06.191 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\service\impl\WindowServiceImpl.class]
2025-04-29 16:06:06.191 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\service\impl\WindowStatusServiceImpl.class]
2025-04-29 16:06:06.192 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\task\BaiShuiTokenSyncTask.class]
2025-04-29 16:06:06.218 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-swagger2/2.9.2/springfox-swagger2-2.9.2.jar!/springfox/documentation/swagger2/mappers/ServiceModelToSwagger2MapperImpl.class]
2025-04-29 16:06:06.218 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-swagger2/2.9.2/springfox-swagger2-2.9.2.jar!/springfox/documentation/swagger2/mappers/VendorExtensionsMapperImpl.class]
2025-04-29 16:06:06.220 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-swagger2/2.9.2/springfox-swagger2-2.9.2.jar!/springfox/documentation/swagger2/mappers/ParameterMapperImpl.class]
2025-04-29 16:06:06.221 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-swagger2/2.9.2/springfox-swagger2-2.9.2.jar!/springfox/documentation/swagger2/mappers/ModelMapperImpl.class]
2025-04-29 16:06:06.222 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-swagger2/2.9.2/springfox-swagger2-2.9.2.jar!/springfox/documentation/swagger2/mappers/LicenseMapperImpl.class]
2025-04-29 16:06:06.222 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-swagger2/2.9.2/springfox-swagger2-2.9.2.jar!/springfox/documentation/swagger2/mappers/SecurityMapperImpl.class]
2025-04-29 16:06:06.233 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/scanners/ApiListingReferenceScanner.class]
2025-04-29 16:06:06.234 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/scanners/ApiDocumentationScanner.class]
2025-04-29 16:06:06.234 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/scanners/ApiDescriptionReader.class]
2025-04-29 16:06:06.234 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/scanners/ApiListingReader.class]
2025-04-29 16:06:06.235 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/scanners/CachingOperationReader.class]
2025-04-29 16:06:06.235 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/scanners/MediaTypeReader.class]
2025-04-29 16:06:06.235 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/scanners/ApiListingScanner.class]
2025-04-29 16:06:06.235 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/scanners/ApiModelReader.class]
2025-04-29 16:06:06.236 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/scanners/ApiDescriptionLookup.class]
2025-04-29 16:06:06.238 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/readers/operation/OperationModelsProvider.class]
2025-04-29 16:06:06.238 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/readers/operation/OperationDeprecatedReader.class]
2025-04-29 16:06:06.238 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/readers/operation/ResponseMessagesReader.class]
2025-04-29 16:06:06.238 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/readers/operation/OperationParameterReader.class]
2025-04-29 16:06:06.238 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/readers/operation/DefaultTagsProvider.class]
2025-04-29 16:06:06.240 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/readers/operation/OperationTagsReader.class]
2025-04-29 16:06:06.240 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/readers/operation/ApiOperationReader.class]
2025-04-29 16:06:06.240 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/readers/operation/DefaultOperationReader.class]
2025-04-29 16:06:06.241 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/readers/operation/OperationParameterRequestConditionReader.class]
2025-04-29 16:06:06.241 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/readers/operation/OperationParameterHeadersConditionReader.class]
2025-04-29 16:06:06.241 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/readers/operation/OperationResponseClassReader.class]
2025-04-29 16:06:06.242 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/readers/operation/CachingOperationNameGenerator.class]
2025-04-29 16:06:06.244 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/readers/parameter/ParameterMultiplesReader.class]
2025-04-29 16:06:06.246 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/readers/parameter/ModelAttributeParameterExpander.class]
2025-04-29 16:06:06.246 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/readers/parameter/ParameterTypeReader.class]
2025-04-29 16:06:06.247 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/readers/parameter/ParameterRequiredReader.class]
2025-04-29 16:06:06.248 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/readers/parameter/ParameterDataTypeReader.class]
2025-04-29 16:06:06.248 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/readers/parameter/ParameterDefaultReader.class]
2025-04-29 16:06:06.249 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/readers/parameter/ParameterNameReader.class]
2025-04-29 16:06:06.249 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/readers/parameter/ExpandedParameterBuilder.class]
2025-04-29 16:06:06.252 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/plugins/DocumentationPluginsBootstrapper.class]
2025-04-29 16:06:06.253 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/plugins/WebMvcRequestHandlerProvider.class]
2025-04-29 16:06:06.253 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/plugins/DocumentationPluginsManager.class]
2025-04-29 16:06:06.254 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/paths/QueryStringUriTemplateDecorator.class]
2025-04-29 16:06:06.255 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/paths/PathMappingDecorator.class]
2025-04-29 16:06:06.255 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/paths/PathSanitizer.class]
2025-04-29 16:06:06.256 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/paths/OperationPathDecorator.class]
2025-04-29 16:06:06.276 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-schema/2.9.2/springfox-schema-2.9.2.jar!/springfox/documentation/schema/CachingModelDependencyProvider.class]
2025-04-29 16:06:06.277 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-schema/2.9.2/springfox-schema-2.9.2.jar!/springfox/documentation/schema/TypeNameExtractor.class]
2025-04-29 16:06:06.277 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-schema/2.9.2/springfox-schema-2.9.2.jar!/springfox/documentation/schema/plugins/PropertyDiscriminatorBasedInheritancePlugin.class]
2025-04-29 16:06:06.278 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-schema/2.9.2/springfox-schema-2.9.2.jar!/springfox/documentation/schema/plugins/XmlModelPlugin.class]
2025-04-29 16:06:06.278 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-schema/2.9.2/springfox-schema-2.9.2.jar!/springfox/documentation/schema/plugins/SchemaPluginsManager.class]
2025-04-29 16:06:06.278 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-schema/2.9.2/springfox-schema-2.9.2.jar!/springfox/documentation/schema/property/CachingModelPropertiesProvider.class]
2025-04-29 16:06:06.279 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-schema/2.9.2/springfox-schema-2.9.2.jar!/springfox/documentation/schema/property/ObjectMapperBeanPropertyNamingStrategy.class]
2025-04-29 16:06:06.279 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-schema/2.9.2/springfox-schema-2.9.2.jar!/springfox/documentation/schema/property/bean/AccessorsProvider.class]
2025-04-29 16:06:06.279 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-schema/2.9.2/springfox-schema-2.9.2.jar!/springfox/documentation/schema/property/field/FieldProvider.class]
2025-04-29 16:06:06.280 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-schema/2.9.2/springfox-schema-2.9.2.jar!/springfox/documentation/schema/property/XmlPropertyPlugin.class]
2025-04-29 16:06:06.281 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-schema/2.9.2/springfox-schema-2.9.2.jar!/springfox/documentation/schema/property/OptimizedModelPropertiesProvider.class]
2025-04-29 16:06:06.281 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-schema/2.9.2/springfox-schema-2.9.2.jar!/springfox/documentation/schema/property/FactoryMethodProvider.class]
2025-04-29 16:06:06.283 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-schema/2.9.2/springfox-schema-2.9.2.jar!/springfox/documentation/schema/CachingModelProvider.class]
2025-04-29 16:06:06.283 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-schema/2.9.2/springfox-schema-2.9.2.jar!/springfox/documentation/schema/DefaultModelDependencyProvider.class]
2025-04-29 16:06:06.284 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-schema/2.9.2/springfox-schema-2.9.2.jar!/springfox/documentation/schema/JacksonEnumTypeDeterminer.class]
2025-04-29 16:06:06.284 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-schema/2.9.2/springfox-schema-2.9.2.jar!/springfox/documentation/schema/DefaultModelProvider.class]
2025-04-29 16:06:06.293 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-swagger-common/2.9.2/springfox-swagger-common-2.9.2.jar!/springfox/documentation/swagger/schema/ApiModelPropertyPropertyBuilder.class]
2025-04-29 16:06:06.294 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-swagger-common/2.9.2/springfox-swagger-common-2.9.2.jar!/springfox/documentation/swagger/schema/ApiModelTypeNameProvider.class]
2025-04-29 16:06:06.294 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-swagger-common/2.9.2/springfox-swagger-common-2.9.2.jar!/springfox/documentation/swagger/schema/ApiModelBuilder.class]
2025-04-29 16:06:06.296 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-swagger-common/2.9.2/springfox-swagger-common-2.9.2.jar!/springfox/documentation/swagger/readers/operation/OperationImplicitParameterReader.class]
2025-04-29 16:06:06.296 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-swagger-common/2.9.2/springfox-swagger-common-2.9.2.jar!/springfox/documentation/swagger/readers/operation/VendorExtensionsReader.class]
2025-04-29 16:06:06.297 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-swagger-common/2.9.2/springfox-swagger-common-2.9.2.jar!/springfox/documentation/swagger/readers/operation/SwaggerOperationResponseClassReader.class]
2025-04-29 16:06:06.297 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-swagger-common/2.9.2/springfox-swagger-common-2.9.2.jar!/springfox/documentation/swagger/readers/operation/SwaggerOperationModelsProvider.class]
2025-04-29 16:06:06.298 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-swagger-common/2.9.2/springfox-swagger-common-2.9.2.jar!/springfox/documentation/swagger/readers/operation/SwaggerMediaTypeReader.class]
2025-04-29 16:06:06.298 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-swagger-common/2.9.2/springfox-swagger-common-2.9.2.jar!/springfox/documentation/swagger/readers/operation/OperationHttpMethodReader.class]
2025-04-29 16:06:06.298 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-swagger-common/2.9.2/springfox-swagger-common-2.9.2.jar!/springfox/documentation/swagger/readers/operation/OperationImplicitParametersReader.class]
2025-04-29 16:06:06.298 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-swagger-common/2.9.2/springfox-swagger-common-2.9.2.jar!/springfox/documentation/swagger/readers/operation/OperationAuthReader.class]
2025-04-29 16:06:06.299 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-swagger-common/2.9.2/springfox-swagger-common-2.9.2.jar!/springfox/documentation/swagger/readers/operation/OperationHiddenReader.class]
2025-04-29 16:06:06.299 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-swagger-common/2.9.2/springfox-swagger-common-2.9.2.jar!/springfox/documentation/swagger/readers/operation/OperationSummaryReader.class]
2025-04-29 16:06:06.299 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-swagger-common/2.9.2/springfox-swagger-common-2.9.2.jar!/springfox/documentation/swagger/readers/operation/SwaggerResponseMessageReader.class]
2025-04-29 16:06:06.300 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-swagger-common/2.9.2/springfox-swagger-common-2.9.2.jar!/springfox/documentation/swagger/readers/operation/OperationNicknameIntoUniqueIdReader.class]
2025-04-29 16:06:06.300 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-swagger-common/2.9.2/springfox-swagger-common-2.9.2.jar!/springfox/documentation/swagger/readers/operation/OperationPositionReader.class]
2025-04-29 16:06:06.300 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-swagger-common/2.9.2/springfox-swagger-common-2.9.2.jar!/springfox/documentation/swagger/readers/operation/OperationNotesReader.class]
2025-04-29 16:06:06.301 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-swagger-common/2.9.2/springfox-swagger-common-2.9.2.jar!/springfox/documentation/swagger/readers/operation/SwaggerOperationTagsReader.class]
2025-04-29 16:06:06.301 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-swagger-common/2.9.2/springfox-swagger-common-2.9.2.jar!/springfox/documentation/swagger/readers/parameter/ApiParamParameterBuilder.class]
2025-04-29 16:06:06.301 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-swagger-common/2.9.2/springfox-swagger-common-2.9.2.jar!/springfox/documentation/swagger/readers/parameter/SwaggerExpandedParameterBuilder.class]
2025-04-29 16:06:06.305 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-swagger-common/2.9.2/springfox-swagger-common-2.9.2.jar!/springfox/documentation/swagger/web/SwaggerApiListingReader.class]
2025-04-29 16:06:06.307 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-swagger-common/2.9.2/springfox-swagger-common-2.9.2.jar!/springfox/documentation/swagger/web/ClassOrApiAnnotationResourceGrouping.class]
2025-04-29 16:06:06.307 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-swagger-common/2.9.2/springfox-swagger-common-2.9.2.jar!/springfox/documentation/swagger/web/InMemorySwaggerResourcesProvider.class]
2025-04-29 16:06:06.307 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-swagger-common/2.9.2/springfox-swagger-common-2.9.2.jar!/springfox/documentation/swagger/web/ApiResourceController.class]
2025-04-29 16:06:06.458 [main] DEBUG o.s.c.e.PropertySourcesPropertyResolver - Found key 'spring.jmx.enabled' in PropertySource 'configurationProperties' with value of type String
2025-04-29 16:06:06.474 [main] DEBUG o.s.c.e.PropertySourcesPropertyResolver - Found key 'spring.jmx.enabled' in PropertySource 'configurationProperties' with value of type String
2025-04-29 16:06:06.475 [main] DEBUG o.s.c.e.PropertySourcesPropertyResolver - Found key 'spring.application.admin.enabled' in PropertySource 'configurationProperties' with value of type String
2025-04-29 16:06:06.634 [main] DEBUG o.s.c.e.PropertySourcesPropertyResolver - Found key 'spring.jmx.enabled' in PropertySource 'configurationProperties' with value of type String
2025-04-29 16:06:06.636 [main] DEBUG o.s.c.e.PropertySourcesPropertyResolver - Found key 'spring.application.admin.enabled' in PropertySource 'configurationProperties' with value of type String
2025-04-29 16:06:06.683 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'com.qs.admin.common.configurer.DataSourceConfig#MapperScannerRegistrar#0'
2025-04-29 16:06:06.689 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'propertySourcesPlaceholderConfigurer'
2025-04-29 16:06:06.750 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.context.properties.ConfigurationPropertiesBeanDefinitionValidator'
2025-04-29 16:06:06.820 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.context.event.internalEventListenerProcessor'
2025-04-29 16:06:06.821 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'preserveErrorControllerTargetClassPostProcessor'
2025-04-29 16:06:06.821 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.context.event.internalEventListenerFactory'
2025-04-29 16:06:06.821 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.transaction.config.internalTransactionalEventListenerFactory'
2025-04-29 16:06:06.827 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.context.annotation.internalAutowiredAnnotationProcessor'
2025-04-29 16:06:06.827 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.context.annotation.internalCommonAnnotationProcessor'
2025-04-29 16:06:06.829 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'lifecycleBeanPostProcessor'
2025-04-29 16:06:06.829 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'shiroConfig'
2025-04-29 16:06:06.829 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'shiroConfig' of type [com.qs.admin.common.configurer.ShiroConfig$$EnhancerBySpringCGLIB$$95d59ab] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-04-29 16:06:06.834 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.context.properties.ConfigurationPropertiesBindingPostProcessor'
2025-04-29 16:06:06.834 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.context.internalConfigurationPropertiesBinder'
2025-04-29 16:06:06.834 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.context.internalConfigurationPropertiesBinderFactory'
2025-04-29 16:06:06.835 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'getLifecycleBeanPostProcessor'
2025-04-29 16:06:06.841 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.context.annotation.internalScheduledAnnotationProcessor'
2025-04-29 16:06:06.841 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.scheduling.annotation.SchedulingConfiguration'
2025-04-29 16:06:06.844 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'methodValidationPostProcessor'
2025-04-29 16:06:06.856 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'securityManager'
2025-04-29 16:06:06.856 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'shiroRealm'
2025-04-29 16:06:06.865 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'jwtUtil'
2025-04-29 16:06:06.866 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'jwtProperties'
2025-04-29 16:06:06.872 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'jwtProperties' of type [com.qs.admin.common.shiro.security.JwtProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-04-29 16:06:06.872 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'jwtUtil' of type [com.qs.admin.common.shiro.security.JwtUtil] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-04-29 16:06:06.872 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'shiroRealm' of type [com.qs.admin.common.shiro.ShiroRealm] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-04-29 16:06:06.873 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'shiroCacheManager'
2025-04-29 16:06:06.873 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'localCacheManager'
2025-04-29 16:06:06.887 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'localCacheManager' of type [com.qs.admin.common.utils.LocalCacheManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-04-29 16:06:06.887 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'shiroCacheManager' of type [com.qs.admin.common.shiro.cache.ShiroCacheManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-04-29 16:06:06.888 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Autowiring by type from bean name 'securityManager' via factory method to bean named 'shiroRealm'
2025-04-29 16:06:06.888 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Autowiring by type from bean name 'securityManager' via factory method to bean named 'shiroCacheManager'
2025-04-29 16:06:07.123 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'securityManager' of type [org.apache.shiro.web.mgt.DefaultWebSecurityManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-04-29 16:06:07.124 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'syncCacheServiceImpl'
2025-04-29 16:06:07.125 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'syncCacheServiceImpl' of type [com.qs.admin.common.service.impl.SyncCacheServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-04-29 16:06:07.125 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Autowiring by type from bean name 'shiroFilter' via factory method to bean named 'securityManager'
2025-04-29 16:06:07.125 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Autowiring by type from bean name 'shiroFilter' via factory method to bean named 'localCacheManager'
2025-04-29 16:06:07.125 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Autowiring by type from bean name 'shiroFilter' via factory method to bean named 'jwtProperties'
2025-04-29 16:06:07.125 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Autowiring by type from bean name 'shiroFilter' via factory method to bean named 'syncCacheServiceImpl'
2025-04-29 16:06:07.133 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'getShiroFilterProperties'
2025-04-29 16:06:07.137 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'getShiroFilterProperties' of type [com.qs.admin.common.shiro.ShiroFilterProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-04-29 16:06:07.142 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Autowiring by type from bean name 'methodValidationPostProcessor' via factory method to bean named 'environment'
2025-04-29 16:06:07.144 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'dataSourceInitializerPostProcessor'
2025-04-29 16:06:07.144 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.aop.config.internalAutoProxyCreator'
2025-04-29 16:06:07.154 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'persistenceExceptionTranslationPostProcessor'
2025-04-29 16:06:07.155 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Autowiring by type from bean name 'persistenceExceptionTranslationPostProcessor' via factory method to bean named 'environment'
2025-04-29 16:06:07.156 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'shiroFilter'
2025-04-29 16:06:07.156 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'authorizationAttributeSourceAdvisor'
2025-04-29 16:06:07.157 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Autowiring by type from bean name 'authorizationAttributeSourceAdvisor' via factory method to bean named 'securityManager'
2025-04-29 16:06:07.164 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'authorizationAttributeSourceAdvisor' of type [org.apache.shiro.spring.security.interceptor.AuthorizationAttributeSourceAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-04-29 16:06:07.167 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'dynamicDatasourceAnnotationAdvisor'
2025-04-29 16:06:07.167 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration'
2025-04-29 16:06:07.167 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.transaction.config.internalTransactionAdvisor'
2025-04-29 16:06:07.167 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration'
2025-04-29 16:06:07.175 [main] DEBUG o.s.a.a.a.ReflectiveAspectJAdvisorFactory - Found AspectJ method: public java.lang.Object com.qs.admin.common.aspect.IdempotencyAspect.around(org.aspectj.lang.ProceedingJoinPoint,org.springframework.web.bind.annotation.PostMapping) throws java.lang.Throwable
2025-04-29 16:06:07.183 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-04-29 16:06:07.233 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'transactionAttributeSource'
2025-04-29 16:06:07.237 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'transactionInterceptor'
2025-04-29 16:06:07.237 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Autowiring by type from bean name 'transactionInterceptor' via factory method to bean named 'transactionAttributeSource'
2025-04-29 16:06:07.240 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Autowiring by type from bean name 'org.springframework.transaction.config.internalTransactionAdvisor' via factory method to bean named 'transactionAttributeSource'
2025-04-29 16:06:07.240 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Autowiring by type from bean name 'org.springframework.transaction.config.internalTransactionAdvisor' via factory method to bean named 'transactionInterceptor'
2025-04-29 16:06:07.241 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'spring.datasource.dynamic-com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties'
2025-04-29 16:06:07.245 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.datasource.dynamic-com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-04-29 16:06:07.247 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration$$EnhancerBySpringCGLIB$$72f5ca41] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-04-29 16:06:07.250 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'dsProcessor'
2025-04-29 16:06:07.252 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dsProcessor' of type [com.baomidou.dynamic.datasource.processor.DsHeaderProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-04-29 16:06:07.253 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Autowiring by type from bean name 'dynamicDatasourceAnnotationAdvisor' via factory method to bean named 'dsProcessor'
2025-04-29 16:06:07.259 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dynamicDatasourceAnnotationAdvisor' of type [com.baomidou.dynamic.datasource.aop.DynamicDataSourceAnnotationAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-04-29 16:06:07.261 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'objectMapperConfigurer'
2025-04-29 16:06:07.263 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'webServerFactoryCustomizerBeanPostProcessor'
2025-04-29 16:06:07.263 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'errorPageRegistrarBeanPostProcessor'
2025-04-29 16:06:07.265 [main] DEBUG o.s.u.c.s.UiApplicationContextUtils - Unable to locate ThemeSource with name 'themeSource': using default [org.springframework.ui.context.support.ResourceBundleThemeSource@114b60a]
2025-04-29 16:06:07.265 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'undertowServletWebServerFactory'
2025-04-29 16:06:07.265 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.ServletWebServerFactoryConfiguration$EmbeddedUndertow'
2025-04-29 16:06:07.275 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'websocketServletWebServerCustomizer'
2025-04-29 16:06:07.275 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.websocket.servlet.WebSocketServletAutoConfiguration$UndertowWebSocketConfiguration'
2025-04-29 16:06:07.276 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'servletWebServerFactoryCustomizer'
2025-04-29 16:06:07.276 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.ServletWebServerFactoryAutoConfiguration'
2025-04-29 16:06:07.278 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'server-org.springframework.boot.autoconfigure.web.ServerProperties'
2025-04-29 16:06:07.285 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Autowiring by type from bean name 'servletWebServerFactoryCustomizer' via factory method to bean named 'server-org.springframework.boot.autoconfigure.web.ServerProperties'
2025-04-29 16:06:07.285 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'undertowWebServerFactoryCustomizer'
2025-04-29 16:06:07.285 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.embedded.EmbeddedWebServerFactoryCustomizerAutoConfiguration$UndertowWebServerFactoryCustomizerConfiguration'
2025-04-29 16:06:07.286 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Autowiring by type from bean name 'undertowWebServerFactoryCustomizer' via factory method to bean named 'environment'
2025-04-29 16:06:07.286 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Autowiring by type from bean name 'undertowWebServerFactoryCustomizer' via factory method to bean named 'server-org.springframework.boot.autoconfigure.web.ServerProperties'
2025-04-29 16:06:07.287 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'localeCharsetMappingsCustomizer'
2025-04-29 16:06:07.288 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.HttpEncodingAutoConfiguration'
2025-04-29 16:06:07.288 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'spring.http-org.springframework.boot.autoconfigure.http.HttpProperties'
2025-04-29 16:06:07.290 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.web.servlet.HttpEncodingAutoConfiguration' via constructor to bean named 'spring.http-org.springframework.boot.autoconfigure.http.HttpProperties'
2025-04-29 16:06:07.310 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'errorPageCustomizer'
2025-04-29 16:06:07.310 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.error.ErrorMvcAutoConfiguration'
2025-04-29 16:06:07.311 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.web.servlet.error.ErrorMvcAutoConfiguration' via constructor to bean named 'server-org.springframework.boot.autoconfigure.web.ServerProperties'
2025-04-29 16:06:07.312 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'dispatcherServletRegistration'
2025-04-29 16:06:07.312 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.DispatcherServletAutoConfiguration$DispatcherServletRegistrationConfiguration'
2025-04-29 16:06:07.314 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'dispatcherServlet'
2025-04-29 16:06:07.314 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.DispatcherServletAutoConfiguration$DispatcherServletConfiguration'
2025-04-29 16:06:07.315 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'spring.mvc-org.springframework.boot.autoconfigure.web.servlet.WebMvcProperties'
2025-04-29 16:06:07.318 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Autowiring by type from bean name 'dispatcherServlet' via factory method to bean named 'spring.http-org.springframework.boot.autoconfigure.http.HttpProperties'
2025-04-29 16:06:07.318 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Autowiring by type from bean name 'dispatcherServlet' via factory method to bean named 'spring.mvc-org.springframework.boot.autoconfigure.web.servlet.WebMvcProperties'
2025-04-29 16:06:07.335 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Autowiring by type from bean name 'dispatcherServletRegistration' via factory method to bean named 'dispatcherServlet'
2025-04-29 16:06:07.335 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Autowiring by type from bean name 'dispatcherServletRegistration' via factory method to bean named 'spring.mvc-org.springframework.boot.autoconfigure.web.servlet.WebMvcProperties'
2025-04-29 16:06:07.337 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'multipartConfigElement'
2025-04-29 16:06:07.337 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.MultipartAutoConfiguration'
2025-04-29 16:06:07.337 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'spring.servlet.multipart-org.springframework.boot.autoconfigure.web.servlet.MultipartProperties'
2025-04-29 16:06:07.342 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.web.servlet.MultipartAutoConfiguration' via constructor to bean named 'spring.servlet.multipart-org.springframework.boot.autoconfigure.web.servlet.MultipartProperties'
2025-04-29 16:06:07.351 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Autowiring by type from bean name 'errorPageCustomizer' via factory method to bean named 'dispatcherServletRegistration'
2025-04-29 16:06:07.367 [main] DEBUG o.s.b.w.e.u.UndertowServletWebServerFactory - Code archive: D:\DevelopmentTools\Maven_respository\org\springframework\boot\spring-boot\2.2.1.RELEASE\spring-boot-2.2.1.RELEASE.jar
2025-04-29 16:06:07.367 [main] DEBUG o.s.b.w.e.u.UndertowServletWebServerFactory - Code archive: D:\DevelopmentTools\Maven_respository\org\springframework\boot\spring-boot\2.2.1.RELEASE\spring-boot-2.2.1.RELEASE.jar
2025-04-29 16:06:07.367 [main] DEBUG o.s.b.w.e.u.UndertowServletWebServerFactory - None of the document roots [src/main/webapp, public, static] point to a directory and will be ignored.
2025-04-29 16:06:07.396 [main] WARN  io.undertow.websockets.jsr - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
2025-04-29 16:06:07.412 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-04-29 16:06:07.412 [main] DEBUG o.s.web.context.ContextLoader - Published root WebApplicationContext as ServletContext attribute with name [org.springframework.web.context.WebApplicationContext.ROOT]
2025-04-29 16:06:07.412 [main] INFO  o.s.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 1339 ms
2025-04-29 16:06:07.414 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'filterRegistrationBean'
2025-04-29 16:06:07.417 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'statViewServletRegistrationBean'
2025-04-29 16:06:07.417 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'com.alibaba.druid.spring.boot.autoconfigure.stat.DruidStatViewServletConfiguration'
2025-04-29 16:06:07.419 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'spring.datasource.druid-com.alibaba.druid.spring.boot.autoconfigure.properties.DruidStatProperties'
2025-04-29 16:06:07.421 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Autowiring by type from bean name 'statViewServletRegistrationBean' via factory method to bean named 'spring.datasource.druid-com.alibaba.druid.spring.boot.autoconfigure.properties.DruidStatProperties'
2025-04-29 16:06:07.428 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'webStatFilterRegistrationBean'
2025-04-29 16:06:07.428 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'com.alibaba.druid.spring.boot.autoconfigure.stat.DruidWebStatFilterConfiguration'
2025-04-29 16:06:07.430 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Autowiring by type from bean name 'webStatFilterRegistrationBean' via factory method to bean named 'spring.datasource.druid-com.alibaba.druid.spring.boot.autoconfigure.properties.DruidStatProperties'
2025-04-29 16:06:07.438 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'corsFilter'
2025-04-29 16:06:07.438 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'corsConfig'
2025-04-29 16:06:07.454 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'requestContextFilter'
2025-04-29 16:06:07.458 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'formContentFilter'
2025-04-29 16:06:07.458 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration'
2025-04-29 16:06:07.466 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'characterEncodingFilter'
2025-04-29 16:06:07.476 [main] DEBUG o.s.b.w.s.ServletContextInitializerBeans - Mapping filters: filterRegistrationBean urls=[/*] order=2147483647, filterRegistrationBean urls=[/*] order=2147483647, characterEncodingFilter urls=[/*] order=-2147483648, formContentFilter urls=[/*] order=-9900, requestContextFilter urls=[/*] order=-105, corsFilter urls=[/*] order=2147483647, shiroFilter urls=[/*] order=2147483647
2025-04-29 16:06:07.476 [main] DEBUG o.s.b.w.s.ServletContextInitializerBeans - Mapping servlets: dispatcherServlet urls=[/], statViewServlet urls=[/druid/*]
2025-04-29 16:06:07.496 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'qscAdminApplication'
2025-04-29 16:06:07.498 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'idempotencyAspect'
2025-04-29 16:06:07.498 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'dataSourceConfig'
2025-04-29 16:06:07.498 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'myWebMvcConfig'
2025-04-29 16:06:07.507 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'swagger2Configurer'
2025-04-29 16:06:07.509 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'mybatisPlusConfig'
2025-04-29 16:06:07.511 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'dataSource'
2025-04-29 16:06:07.555 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jdbc.DataSourceInitializerInvoker'
2025-04-29 16:06:07.556 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'spring.datasource-org.springframework.boot.autoconfigure.jdbc.DataSourceProperties'
2025-04-29 16:06:07.561 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.jdbc.DataSourceInitializerInvoker' via constructor to bean named 'spring.datasource-org.springframework.boot.autoconfigure.jdbc.DataSourceProperties'
2025-04-29 16:06:07.561 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.jdbc.DataSourceInitializerInvoker' via constructor to bean named 'org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@17a1e2d'
2025-04-29 16:06:07.567 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'mybatis-org.mybatis.spring.boot.autoconfigure.MybatisProperties'
2025-04-29 16:06:07.573 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'mybatisPlusInterceptor'
2025-04-29 16:06:07.583 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'globalExceptionHandler'
2025-04-29 16:06:07.584 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'shiroFilterProperties'
2025-04-29 16:06:07.587 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'springContextUtil'
2025-04-29 16:06:07.588 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'baiShuiProperties'
2025-04-29 16:06:07.589 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'restTemplateConfig'
2025-04-29 16:06:07.590 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'agentInfoController'
2025-04-29 16:06:07.592 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'agentInfoServiceImpl'
2025-04-29 16:06:07.601 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'agentInfoMapper'
2025-04-29 16:06:07.604 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'mybatisSqlSessionFactoryBean'
2025-04-29 16:06:07.992 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'agentInfoEnterpriseMapper'
2025-04-29 16:06:08.012 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'agentInfoEnterpriseServiceImpl'
2025-04-29 16:06:08.049 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'ticketVerifyServiceImpl'
2025-04-29 16:06:08.050 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'ticketVerifyMapper'
2025-04-29 16:06:08.118 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'baiShuiApiServiceImpl'
2025-04-29 16:06:08.120 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'restTemplate'
2025-04-29 16:06:08.140 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'systemServiceImpl'
2025-04-29 16:06:08.141 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'systemMapper'
2025-04-29 16:06:08.185 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'baiShuiTokenSyncTask'
2025-04-29 16:06:11.341 [main] INFO  c.q.a.t.s.impl.BaiShuiApiServiceImpl - 获取佰税API访问令牌
2025-04-29 16:06:12.965 [main] INFO  c.q.a.t.s.impl.BaiShuiApiServiceImpl - tokenUrl=https://etax.zhejiang.chinatax.gov.cn/nsfw/apiService/oauth/token
2025-04-29 16:06:13.152 [main] DEBUG o.s.web.client.RestTemplate - HTTP POST https://etax.zhejiang.chinatax.gov.cn/nsfw/apiService/oauth/token
2025-04-29 16:06:13.182 [main] DEBUG o.s.web.client.RestTemplate - Accept=[application/json, application/*+json]
2025-04-29 16:06:13.189 [main] DEBUG o.s.web.client.RestTemplate - Writing [{appKey=[HZBSJHXT], appSecret=[7d427891ef38f4a7b247447f715c66cd]}] as "application/x-www-form-urlencoded"
2025-04-29 16:06:13.541 [main] DEBUG o.s.web.client.RestTemplate - Response 200 OK
2025-04-29 16:06:13.543 [main] DEBUG o.s.web.client.RestTemplate - Reading to [java.util.Map<?, ?>]
2025-04-29 16:06:17.340 [main] INFO  c.q.a.t.s.impl.BaiShuiApiServiceImpl - 成功获取佰税API访问令牌
2025-04-29 16:06:25.558 [main] DEBUG o.s.jdbc.datasource.DataSourceUtils - Fetching JDBC Connection from DataSource
2025-04-29 16:06:25.559 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-04-29 16:06:26.535 [main] WARN  c.m.s.jdbc.internals.TDS.Channel - TLSv1 was negotiated. Please update server and client to use TLSv1.2 at minimum.
2025-04-29 16:06:27.533 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-04-29 16:06:27.559 [main] DEBUG c.q.a.t.m.SystemMapper.selectList - ==>  Preparing: SELECT [KEY],[VALUE],[MEMO] FROM system WHERE ([KEY] = ?)
2025-04-29 16:06:27.706 [HikariPool-1 connection adder] WARN  c.m.s.jdbc.internals.TDS.Channel - TLSv1 was negotiated. Please update server and client to use TLSv1.2 at minimum.
2025-04-29 16:06:27.761 [HikariPool-1 connection adder] WARN  c.m.s.jdbc.internals.TDS.Channel - TLSv1 was negotiated. Please update server and client to use TLSv1.2 at minimum.
2025-04-29 16:06:27.822 [HikariPool-1 connection adder] WARN  c.m.s.jdbc.internals.TDS.Channel - TLSv1 was negotiated. Please update server and client to use TLSv1.2 at minimum.
2025-04-29 16:06:27.916 [HikariPool-1 connection adder] WARN  c.m.s.jdbc.internals.TDS.Channel - TLSv1 was negotiated. Please update server and client to use TLSv1.2 at minimum.
2025-04-29 16:06:27.936 [main] DEBUG c.q.a.t.m.SystemMapper.selectList - ==> Parameters: BS_API_APPKEY(String)
2025-04-29 16:06:28.026 [HikariPool-1 connection adder] WARN  c.m.s.jdbc.internals.TDS.Channel - TLSv1 was negotiated. Please update server and client to use TLSv1.2 at minimum.
2025-04-29 16:06:28.110 [HikariPool-1 connection adder] WARN  c.m.s.jdbc.internals.TDS.Channel - TLSv1 was negotiated. Please update server and client to use TLSv1.2 at minimum.
2025-04-29 16:06:28.188 [HikariPool-1 connection adder] WARN  c.m.s.jdbc.internals.TDS.Channel - TLSv1 was negotiated. Please update server and client to use TLSv1.2 at minimum.
2025-04-29 16:06:28.217 [main] DEBUG c.q.a.t.m.SystemMapper.selectList - <==      Total: 1
2025-04-29 16:06:28.255 [HikariPool-1 connection adder] WARN  c.m.s.jdbc.internals.TDS.Channel - TLSv1 was negotiated. Please update server and client to use TLSv1.2 at minimum.
2025-04-29 16:06:28.321 [HikariPool-1 connection adder] WARN  c.m.s.jdbc.internals.TDS.Channel - TLSv1 was negotiated. Please update server and client to use TLSv1.2 at minimum.
2025-04-29 16:06:52.403 [main] DEBUG o.s.jdbc.datasource.DataSourceUtils - Fetching JDBC Connection from DataSource
2025-04-29 16:06:52.596 [main] DEBUG c.q.a.t.mapper.SystemMapper.update - ==>  Preparing: UPDATE system SET [VALUE]=?, [MEMO]=? WHERE [KEY]=? AND [VALUE]=? AND [MEMO]=?
2025-04-29 16:06:52.597 [main] DEBUG c.q.a.t.mapper.SystemMapper.update - ==> Parameters: HZBSJHXT(String), 佰税科技API APPKEY，自动同步于Tue Apr 29 16:06:38 CST 2025(String), BS_API_APPKEY(String), HZBSJHXT(String), 佰税科技API APPKEY，自动同步于Tue Apr 29 16:06:38 CST 2025(String)
2025-04-29 16:06:52.611 [main] DEBUG c.q.a.t.mapper.SystemMapper.update - <==    Updates: 0
2025-04-29 16:06:55.084 [main] INFO  c.q.a.t.task.BaiShuiTokenSyncTask - APPKEY已同步更新
2025-04-29 16:07:03.369 [main] DEBUG o.s.jdbc.datasource.DataSourceUtils - Fetching JDBC Connection from DataSource
2025-04-29 16:07:03.431 [main] DEBUG c.q.a.t.m.SystemMapper.selectList - ==>  Preparing: SELECT [KEY],[VALUE],[MEMO] FROM system WHERE ([KEY] = ?)
2025-04-29 16:07:03.431 [main] DEBUG c.q.a.t.m.SystemMapper.selectList - ==> Parameters: BS_API_TOKEN(String)
2025-04-29 16:07:03.444 [main] DEBUG c.q.a.t.m.SystemMapper.selectList - <==      Total: 1
2025-04-29 16:07:06.635 [main] DEBUG o.s.jdbc.datasource.DataSourceUtils - Fetching JDBC Connection from DataSource
2025-04-29 16:07:06.648 [main] DEBUG c.q.a.t.mapper.SystemMapper.update - ==>  Preparing: UPDATE system SET [VALUE]=?, [MEMO]=? WHERE [KEY]=? AND [VALUE]=? AND [MEMO]=?
2025-04-29 16:07:06.649 [main] DEBUG c.q.a.t.mapper.SystemMapper.update - ==> Parameters: QmVhcmVyIGV5SmhiR2NpT2lKSVV6STFOaUo5LmV5SmhjSEJyWlhraU9pSklXa0pUU2toWVZDSXNJbVY0Y0NJNk1UYzNOelEwT1RrNE1uMC5UVTJVWEhzNUYzNjZqYnFVZEdFS3AzdVM0eUdwc1ZPaTg4cklkVVBubzVB(String), 佰税科技API授权令牌，自动同步于Tue Apr 29 16:07:05 CST 2025(String), BS_API_TOKEN(String), QmVhcmVyIGV5SmhiR2NpT2lKSVV6STFOaUo5LmV5SmhjSEJyWlhraU9pSklXa0pUU2toWVZDSXNJbVY0Y0NJNk1UYzNOelEwT1RrNE1uMC5UVTJVWEhzNUYzNjZqYnFVZEdFS3AzdVM0eUdwc1ZPaTg4cklkVVBubzVB(String), 佰税科技API授权令牌，自动同步于Tue Apr 29 16:07:05 CST 2025(String)
2025-04-29 16:07:06.661 [main] DEBUG c.q.a.t.mapper.SystemMapper.update - <==    Updates: 0
2025-04-29 16:07:08.363 [main] INFO  c.q.a.t.task.BaiShuiTokenSyncTask - TOKEN已同步更新
2025-04-29 16:10:10.726 [HikariPool-1 housekeeper] WARN  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=3m8s171ms811µs800ns).
2025-04-29 16:10:10.727 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'autocodeController'
2025-04-29 16:10:10.732 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'autocodeServiceImpl'
2025-04-29 16:10:10.733 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'autocodeMapper'
2025-04-29 16:10:24.014 [main] DEBUG o.s.b.c.l.ClasspathLoggingApplicationListener - Application started with classpath: [file:/D:/DevelopmentTools/Java/java-8u311/jre/lib/charsets.jar, file:/D:/DevelopmentTools/Java/java-8u311/jre/lib/deploy.jar, file:/D:/DevelopmentTools/Java/java-8u311/jre/lib/ext/access-bridge-32.jar, file:/D:/DevelopmentTools/Java/java-8u311/jre/lib/ext/cldrdata.jar, file:/D:/DevelopmentTools/Java/java-8u311/jre/lib/ext/dnsns.jar, file:/D:/DevelopmentTools/Java/java-8u311/jre/lib/ext/jaccess.jar, file:/D:/DevelopmentTools/Java/java-8u311/jre/lib/ext/jfxrt.jar, file:/D:/DevelopmentTools/Java/java-8u311/jre/lib/ext/localedata.jar, file:/D:/DevelopmentTools/Java/java-8u311/jre/lib/ext/nashorn.jar, file:/D:/DevelopmentTools/Java/java-8u311/jre/lib/ext/sunec.jar, file:/D:/DevelopmentTools/Java/java-8u311/jre/lib/ext/sunjce_provider.jar, file:/D:/DevelopmentTools/Java/java-8u311/jre/lib/ext/sunmscapi.jar, file:/D:/DevelopmentTools/Java/java-8u311/jre/lib/ext/sunpkcs11.jar, file:/D:/DevelopmentTools/Java/java-8u311/jre/lib/ext/zipfs.jar, file:/D:/DevelopmentTools/Java/java-8u311/jre/lib/javaws.jar, file:/D:/DevelopmentTools/Java/java-8u311/jre/lib/jce.jar, file:/D:/DevelopmentTools/Java/java-8u311/jre/lib/jfr.jar, file:/D:/DevelopmentTools/Java/java-8u311/jre/lib/jfxswt.jar, file:/D:/DevelopmentTools/Java/java-8u311/jre/lib/jsse.jar, file:/D:/DevelopmentTools/Java/java-8u311/jre/lib/management-agent.jar, file:/D:/DevelopmentTools/Java/java-8u311/jre/lib/plugin.jar, file:/D:/DevelopmentTools/Java/java-8u311/jre/lib/resources.jar, file:/D:/DevelopmentTools/Java/java-8u311/jre/lib/rt.jar, file:/D:/project/Java%20Projectes/qsadmin/target/classes/, file:/D:/DevelopmentTools/Maven_respository/com/microsoft/sqlserver/mssql-jdbc/12.6.1.jre8/mssql-jdbc-12.6.1.jre8.jar, file:/D:/DevelopmentTools/Maven_respository/org/springframework/boot/spring-boot-starter-web/2.2.1.RELEASE/spring-boot-starter-web-2.2.1.RELEASE.jar, file:/D:/DevelopmentTools/Maven_respository/org/springframework/boot/spring-boot-starter/2.2.1.RELEASE/spring-boot-starter-2.2.1.RELEASE.jar, file:/D:/DevelopmentTools/Maven_respository/org/springframework/boot/spring-boot/2.2.1.RELEASE/spring-boot-2.2.1.RELEASE.jar, file:/D:/DevelopmentTools/Maven_respository/org/springframework/boot/spring-boot-starter-logging/2.2.1.RELEASE/spring-boot-starter-logging-2.2.1.RELEASE.jar, file:/D:/DevelopmentTools/Maven_respository/ch/qos/logback/logback-classic/1.2.3/logback-classic-1.2.3.jar, file:/D:/DevelopmentTools/Maven_respository/ch/qos/logback/logback-core/1.2.3/logback-core-1.2.3.jar, file:/D:/DevelopmentTools/Maven_respository/org/apache/logging/log4j/log4j-to-slf4j/2.12.1/log4j-to-slf4j-2.12.1.jar, file:/D:/DevelopmentTools/Maven_respository/org/apache/logging/log4j/log4j-api/2.12.1/log4j-api-2.12.1.jar, file:/D:/DevelopmentTools/Maven_respository/org/slf4j/jul-to-slf4j/1.7.29/jul-to-slf4j-1.7.29.jar, file:/D:/DevelopmentTools/Maven_respository/jakarta/annotation/jakarta.annotation-api/1.3.5/jakarta.annotation-api-1.3.5.jar, file:/D:/DevelopmentTools/Maven_respository/org/yaml/snakeyaml/1.25/snakeyaml-1.25.jar, file:/D:/DevelopmentTools/Maven_respository/org/springframework/boot/spring-boot-starter-json/2.2.1.RELEASE/spring-boot-starter-json-2.2.1.RELEASE.jar, file:/D:/DevelopmentTools/Maven_respository/com/fasterxml/jackson/datatype/jackson-datatype-jdk8/2.10.0/jackson-datatype-jdk8-2.10.0.jar, file:/D:/DevelopmentTools/Maven_respository/com/fasterxml/jackson/datatype/jackson-datatype-jsr310/2.10.0/jackson-datatype-jsr310-2.10.0.jar, file:/D:/DevelopmentTools/Maven_respository/com/fasterxml/jackson/module/jackson-module-parameter-names/2.10.0/jackson-module-parameter-names-2.10.0.jar, file:/D:/DevelopmentTools/Maven_respository/org/springframework/boot/spring-boot-starter-validation/2.2.1.RELEASE/spring-boot-starter-validation-2.2.1.RELEASE.jar, file:/D:/DevelopmentTools/Maven_respository/jakarta/validation/jakarta.validation-api/2.0.1/jakarta.validation-api-2.0.1.jar, file:/D:/DevelopmentTools/Maven_respository/org/hibernate/validator/hibernate-validator/6.0.18.Final/hibernate-validator-6.0.18.Final.jar, file:/D:/DevelopmentTools/Maven_respository/org/springframework/spring-web/5.2.1.RELEASE/spring-web-5.2.1.RELEASE.jar, file:/D:/DevelopmentTools/Maven_respository/org/springframework/spring-webmvc/5.2.1.RELEASE/spring-webmvc-5.2.1.RELEASE.jar, file:/D:/DevelopmentTools/Maven_respository/org/springframework/boot/spring-boot-starter-undertow/2.2.1.RELEASE/spring-boot-starter-undertow-2.2.1.RELEASE.jar, file:/D:/DevelopmentTools/Maven_respository/io/undertow/undertow-core/2.0.27.Final/undertow-core-2.0.27.Final.jar, file:/D:/DevelopmentTools/Maven_respository/org/jboss/logging/jboss-logging/3.4.1.Final/jboss-logging-3.4.1.Final.jar, file:/D:/DevelopmentTools/Maven_respository/org/jboss/xnio/xnio-api/3.3.8.Final/xnio-api-3.3.8.Final.jar, file:/D:/DevelopmentTools/Maven_respository/org/jboss/xnio/xnio-nio/3.3.8.Final/xnio-nio-3.3.8.Final.jar, file:/D:/DevelopmentTools/Maven_respository/io/undertow/undertow-servlet/2.0.27.Final/undertow-servlet-2.0.27.Final.jar, file:/D:/DevelopmentTools/Maven_respository/org/jboss/spec/javax/annotation/jboss-annotations-api_1.2_spec/1.0.2.Final/jboss-annotations-api_1.2_spec-1.0.2.Final.jar, file:/D:/DevelopmentTools/Maven_respository/io/undertow/undertow-websockets-jsr/2.0.27.Final/undertow-websockets-jsr-2.0.27.Final.jar, file:/D:/DevelopmentTools/Maven_respository/org/jboss/spec/javax/websocket/jboss-websocket-api_1.1_spec/1.1.4.Final/jboss-websocket-api_1.1_spec-1.1.4.Final.jar, file:/D:/DevelopmentTools/Maven_respository/jakarta/servlet/jakarta.servlet-api/4.0.3/jakarta.servlet-api-4.0.3.jar, file:/D:/DevelopmentTools/Maven_respository/org/glassfish/jakarta.el/3.0.3/jakarta.el-3.0.3.jar, file:/D:/DevelopmentTools/Maven_respository/net/bytebuddy/byte-buddy/1.10.2/byte-buddy-1.10.2.jar, file:/D:/DevelopmentTools/Maven_respository/org/springframework/spring-core/5.2.1.RELEASE/spring-core-5.2.1.RELEASE.jar, file:/D:/DevelopmentTools/Maven_respository/org/springframework/spring-jcl/5.2.1.RELEASE/spring-jcl-5.2.1.RELEASE.jar, file:/D:/DevelopmentTools/Maven_respository/com/baomidou/dynamic-datasource-spring-boot-starter/3.0.0/dynamic-datasource-spring-boot-starter-3.0.0.jar, file:/D:/DevelopmentTools/Maven_respository/org/springframework/boot/spring-boot-starter-jdbc/2.2.1.RELEASE/spring-boot-starter-jdbc-2.2.1.RELEASE.jar, file:/D:/DevelopmentTools/Maven_respository/com/zaxxer/HikariCP/3.4.1/HikariCP-3.4.1.jar, file:/D:/DevelopmentTools/Maven_respository/org/springframework/spring-jdbc/5.2.1.RELEASE/spring-jdbc-5.2.1.RELEASE.jar, file:/D:/DevelopmentTools/Maven_respository/org/springframework/spring-tx/5.2.1.RELEASE/spring-tx-5.2.1.RELEASE.jar, file:/D:/DevelopmentTools/Maven_respository/org/springframework/boot/spring-boot-starter-aop/2.2.1.RELEASE/spring-boot-starter-aop-2.2.1.RELEASE.jar, file:/D:/DevelopmentTools/Maven_respository/org/aspectj/aspectjweaver/1.9.4/aspectjweaver-1.9.4.jar, file:/D:/DevelopmentTools/Maven_respository/org/apache/tomcat/tomcat-jdbc/7.0.81/tomcat-jdbc-7.0.81.jar, file:/D:/DevelopmentTools/Maven_respository/org/apache/tomcat/tomcat-juli/7.0.81/tomcat-juli-7.0.81.jar, file:/D:/DevelopmentTools/Maven_respository/com/belerweb/pinyin4j/2.5.0/pinyin4j-2.5.0.jar, file:/D:/DevelopmentTools/Maven_respository/org/apache/ant/ant/1.9.7/ant-1.9.7.jar, file:/D:/DevelopmentTools/Maven_respository/org/apache/ant/ant-launcher/1.9.7/ant-launcher-1.9.7.jar, file:/D:/DevelopmentTools/Maven_respository/net/coobird/thumbnailator/0.4.8/thumbnailator-0.4.8.jar, file:/D:/DevelopmentTools/Maven_respository/joda-time/joda-time/2.9.8/joda-time-2.9.8.jar, file:/D:/DevelopmentTools/Maven_respository/org/apache/velocity/velocity-engine-core/2.0/velocity-engine-core-2.0.jar, file:/D:/DevelopmentTools/Maven_respository/org/slf4j/slf4j-api/1.7.29/slf4j-api-1.7.29.jar, file:/D:/DevelopmentTools/Maven_respository/cn/hutool/hutool-all/4.1.2/hutool-all-4.1.2.jar, file:/D:/DevelopmentTools/Maven_respository/org/projectlombok/lombok/1.18.10/lombok-1.18.10.jar, file:/D:/DevelopmentTools/Maven_respository/com/baomidou/mybatis-plus-boot-starter/3.4.3.4/mybatis-plus-boot-starter-3.4.3.4.jar, file:/D:/DevelopmentTools/Maven_respository/com/baomidou/mybatis-plus/3.4.3.4/mybatis-plus-3.4.3.4.jar, file:/D:/DevelopmentTools/Maven_respository/com/baomidou/mybatis-plus-extension/3.4.3.4/mybatis-plus-extension-3.4.3.4.jar, file:/D:/DevelopmentTools/Maven_respository/com/baomidou/mybatis-plus-core/3.4.3.4/mybatis-plus-core-3.4.3.4.jar, file:/D:/DevelopmentTools/Maven_respository/com/baomidou/mybatis-plus-annotation/3.4.3.4/mybatis-plus-annotation-3.4.3.4.jar, file:/D:/DevelopmentTools/Maven_respository/com/github/jsqlparser/jsqlparser/4.2/jsqlparser-4.2.jar, file:/D:/DevelopmentTools/Maven_respository/org/springframework/boot/spring-boot-autoconfigure/2.2.1.RELEASE/spring-boot-autoconfigure-2.2.1.RELEASE.jar, file:/D:/DevelopmentTools/Maven_respository/org/mybatis/spring/boot/mybatis-spring-boot-starter/2.1.0/mybatis-spring-boot-starter-2.1.0.jar, file:/D:/DevelopmentTools/Maven_respository/org/mybatis/spring/boot/mybatis-spring-boot-autoconfigure/2.1.0/mybatis-spring-boot-autoconfigure-2.1.0.jar, file:/D:/DevelopmentTools/Maven_respository/org/mybatis/mybatis-spring/2.0.2/mybatis-spring-2.0.2.jar, file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-swagger2/2.9.2/springfox-swagger2-2.9.2.jar, file:/D:/DevelopmentTools/Maven_respository/io/swagger/swagger-annotations/1.5.20/swagger-annotations-1.5.20.jar, file:/D:/DevelopmentTools/Maven_respository/io/swagger/swagger-models/1.5.20/swagger-models-1.5.20.jar, file:/D:/DevelopmentTools/Maven_respository/com/fasterxml/jackson/core/jackson-annotations/2.10.0/jackson-annotations-2.10.0.jar, file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spi/2.9.2/springfox-spi-2.9.2.jar, file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-core/2.9.2/springfox-core-2.9.2.jar, file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-schema/2.9.2/springfox-schema-2.9.2.jar, file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-swagger-common/2.9.2/springfox-swagger-common-2.9.2.jar, file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar, file:/D:/DevelopmentTools/Maven_respository/com/fasterxml/classmate/1.5.1/classmate-1.5.1.jar, file:/D:/DevelopmentTools/Maven_respository/org/springframework/plugin/spring-plugin-core/1.2.0.RELEASE/spring-plugin-core-1.2.0.RELEASE.jar, file:/D:/DevelopmentTools/Maven_respository/org/springframework/plugin/spring-plugin-metadata/1.2.0.RELEASE/spring-plugin-metadata-1.2.0.RELEASE.jar, file:/D:/DevelopmentTools/Maven_respository/org/mapstruct/mapstruct/1.2.0.Final/mapstruct-1.2.0.Final.jar, file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-swagger-ui/2.9.2/springfox-swagger-ui-2.9.2.jar, file:/D:/DevelopmentTools/Maven_respository/org/apache/commons/commons-lang3/3.8.1/commons-lang3-3.8.1.jar, file:/D:/DevelopmentTools/Maven_respository/com/google/guava/guava/27.0-jre/guava-27.0-jre.jar, file:/D:/DevelopmentTools/Maven_respository/com/google/guava/failureaccess/1.0/failureaccess-1.0.jar, file:/D:/DevelopmentTools/Maven_respository/com/google/guava/listenablefuture/9999.0-empty-to-avoid-conflict-with-guava/listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar, file:/D:/DevelopmentTools/Maven_respository/com/google/code/findbugs/jsr305/3.0.2/jsr305-3.0.2.jar, file:/D:/DevelopmentTools/Maven_respository/org/checkerframework/checker-qual/2.5.2/checker-qual-2.5.2.jar, file:/D:/DevelopmentTools/Maven_respository/com/google/errorprone/error_prone_annotations/2.2.0/error_prone_annotations-2.2.0.jar, file:/D:/DevelopmentTools/Maven_respository/com/google/j2objc/j2objc-annotations/1.1/j2objc-annotations-1.1.jar, file:/D:/DevelopmentTools/Maven_respository/org/codehaus/mojo/animal-sniffer-annotations/1.17/animal-sniffer-annotations-1.17.jar, file:/D:/DevelopmentTools/Maven_respository/com/alibaba/fastjson/1.2.83/fastjson-1.2.83.jar, file:/D:/DevelopmentTools/Maven_respository/com/alibaba/druid-spring-boot-starter/1.1.10/druid-spring-boot-starter-1.1.10.jar, file:/D:/DevelopmentTools/Maven_respository/com/alibaba/druid/1.1.10/druid-1.1.10.jar, file:/D:/DevelopmentTools/Maven_respository/org/apache/shiro/shiro-spring/1.4.0/shiro-spring-1.4.0.jar, file:/D:/DevelopmentTools/Maven_respository/org/apache/shiro/shiro-core/1.4.0/shiro-core-1.4.0.jar, file:/D:/DevelopmentTools/Maven_respository/org/apache/shiro/shiro-lang/1.4.0/shiro-lang-1.4.0.jar, file:/D:/DevelopmentTools/Maven_respository/org/apache/shiro/shiro-cache/1.4.0/shiro-cache-1.4.0.jar, file:/D:/DevelopmentTools/Maven_respository/org/apache/shiro/shiro-crypto-hash/1.4.0/shiro-crypto-hash-1.4.0.jar, file:/D:/DevelopmentTools/Maven_respository/org/apache/shiro/shiro-crypto-core/1.4.0/shiro-crypto-core-1.4.0.jar, file:/D:/DevelopmentTools/Maven_respository/org/apache/shiro/shiro-crypto-cipher/1.4.0/shiro-crypto-cipher-1.4.0.jar, file:/D:/DevelopmentTools/Maven_respository/org/apache/shiro/shiro-config-core/1.4.0/shiro-config-core-1.4.0.jar, file:/D:/DevelopmentTools/Maven_respository/org/apache/shiro/shiro-config-ogdl/1.4.0/shiro-config-ogdl-1.4.0.jar, file:/D:/DevelopmentTools/Maven_respository/commons-beanutils/commons-beanutils/1.9.3/commons-beanutils-1.9.3.jar, file:/D:/DevelopmentTools/Maven_respository/commons-collections/commons-collections/3.2.2/commons-collections-3.2.2.jar, file:/D:/DevelopmentTools/Maven_respository/org/apache/shiro/shiro-event/1.4.0/shiro-event-1.4.0.jar, file:/D:/DevelopmentTools/Maven_respository/org/apache/shiro/shiro-web/1.4.0/shiro-web-1.4.0.jar, file:/D:/DevelopmentTools/Maven_respository/com/auth0/java-jwt/3.18.2/java-jwt-3.18.2.jar, file:/D:/DevelopmentTools/Maven_respository/com/fasterxml/jackson/core/jackson-databind/2.10.0/jackson-databind-2.10.0.jar, file:/D:/DevelopmentTools/Maven_respository/com/fasterxml/jackson/core/jackson-core/2.10.0/jackson-core-2.10.0.jar, file:/D:/DevelopmentTools/Maven_respository/org/springframework/spring-context/5.2.23.RELEASE/spring-context-5.2.23.RELEASE.jar, file:/D:/DevelopmentTools/Maven_respository/org/springframework/spring-aop/5.2.1.RELEASE/spring-aop-5.2.1.RELEASE.jar, file:/D:/DevelopmentTools/Maven_respository/org/springframework/spring-beans/5.2.1.RELEASE/spring-beans-5.2.1.RELEASE.jar, file:/D:/DevelopmentTools/Maven_respository/org/springframework/spring-expression/5.2.1.RELEASE/spring-expression-5.2.1.RELEASE.jar, file:/D:/DevelopmentTools/Maven_respository/org/mybatis/mybatis/3.5.7/mybatis-3.5.7.jar, file:/D:/DevelopmentTools/Maven_respository/com/github/ben-manes/caffeine/caffeine/2.8.0/caffeine-2.8.0.jar, file:/D:/DevelopmentTools/Maven_respository/com/github/xiaoymin/swagger-bootstrap-ui/1.9.3/swagger-bootstrap-ui-1.9.3.jar, file:/D:/DevelopmentTools/IntelliJ%20IDEA%202025/lib/idea_rt.jar, file:/C:/Users/<USER>/AppData/Local/JetBrains/IntelliJIdea2025.1/captureAgent/debugger-agent.jar]
2025-04-29 16:10:24.075 [main] INFO  com.qs.admin.QscAdminApplication - Starting QscAdminApplication on MSI with PID 37108 (started by KarlKyo in D:\project\Java Projectes\qsadmin)
2025-04-29 16:10:24.075 [main] DEBUG com.qs.admin.QscAdminApplication - Running with Spring Boot v2.2.1.RELEASE, Spring v5.2.23.RELEASE
2025-04-29 16:10:24.075 [main] INFO  com.qs.admin.QscAdminApplication - The following profiles are active: dev
2025-04-29 16:10:24.075 [main] DEBUG o.s.boot.SpringApplication - Loading source class com.qs.admin.QscAdminApplication
2025-04-29 16:10:24.105 [main] DEBUG o.s.b.c.c.ConfigFileApplicationListener - Activated activeProfiles dev
2025-04-29 16:10:24.105 [main] DEBUG o.s.b.c.c.ConfigFileApplicationListener - Loaded config file 'file:/D:/project/Java%20Projectes/qsadmin/target/classes/application.yml' (classpath:/application.yml)
2025-04-29 16:10:24.105 [main] DEBUG o.s.b.c.c.ConfigFileApplicationListener - Loaded config file 'file:/D:/project/Java%20Projectes/qsadmin/target/classes/application-dev.yml' (classpath:/application-dev.yml) for profile dev
2025-04-29 16:10:24.105 [main] DEBUG o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Refreshing org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@1b1fde8
2025-04-29 16:10:24.117 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.context.annotation.internalConfigurationAnnotationProcessor'
2025-04-29 16:10:24.126 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.internalCachingMetadataReaderFactory'
2025-04-29 16:10:24.166 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\common\aspect\IdempotencyAspect.class]
2025-04-29 16:10:24.167 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\common\configurer\CorsConfig.class]
2025-04-29 16:10:24.170 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\common\configurer\DataSourceConfig.class]
2025-04-29 16:10:24.170 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\common\configurer\MyWebMvcConfig.class]
2025-04-29 16:10:24.172 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\common\configurer\ShiroConfig.class]
2025-04-29 16:10:24.172 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\common\configurer\Swagger2Configurer.class]
2025-04-29 16:10:24.174 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\common\core\MybatisPlusConfig.class]
2025-04-29 16:10:24.180 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\common\exception\GlobalExceptionHandler.class]
2025-04-29 16:10:24.181 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\common\service\impl\SyncCacheServiceImpl.class]
2025-04-29 16:10:24.182 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\common\shiro\ShiroFilterProperties.class]
2025-04-29 16:10:24.182 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\common\shiro\ShiroRealm.class]
2025-04-29 16:10:24.182 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\common\shiro\cache\ShiroCacheManager.class]
2025-04-29 16:10:24.183 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\common\shiro\security\JwtProperties.class]
2025-04-29 16:10:24.184 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\common\shiro\security\JwtUtil.class]
2025-04-29 16:10:24.185 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\common\utils\LocalCacheManager.class]
2025-04-29 16:10:24.187 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\common\utils\SpringContextUtil.class]
2025-04-29 16:10:24.188 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\config\BaiShuiProperties.class]
2025-04-29 16:10:24.188 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\config\RestTemplateConfig.class]
2025-04-29 16:10:24.193 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\controller\AgentInfoController.class]
2025-04-29 16:10:24.200 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\controller\AutocodeController.class]
2025-04-29 16:10:24.202 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\controller\BusinessController.class]
2025-04-29 16:10:24.202 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\controller\ClientInfoController.class]
2025-04-29 16:10:24.203 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\controller\EmployeeController.class]
2025-04-29 16:10:24.203 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\controller\NoticeController.class]
2025-04-29 16:10:24.203 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\controller\SystemController.class]
2025-04-29 16:10:24.204 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\controller\SystemLogController.class]
2025-04-29 16:10:24.204 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\controller\ThingsController.class]
2025-04-29 16:10:24.204 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\controller\TicketBookController.class]
2025-04-29 16:10:24.205 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\controller\TicketController.class]
2025-04-29 16:10:24.205 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\controller\TicketExchangeController.class]
2025-04-29 16:10:24.205 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\controller\TicketLogController.class]
2025-04-29 16:10:24.206 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\controller\TicketVerifyController.class]
2025-04-29 16:10:24.206 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\controller\WindowBusinessController.class]
2025-04-29 16:10:24.206 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\controller\WindowController.class]
2025-04-29 16:10:24.207 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\controller\WindowStatusController.class]
2025-04-29 16:10:24.226 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\service\impl\AgentInfoEnterpriseServiceImpl.class]
2025-04-29 16:10:24.227 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\service\impl\AgentInfoServiceImpl.class]
2025-04-29 16:10:24.228 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\service\impl\AutocodeServiceImpl.class]
2025-04-29 16:10:24.228 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\service\impl\BaiShuiApiServiceImpl.class]
2025-04-29 16:10:24.229 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\service\impl\BusinessServiceImpl.class]
2025-04-29 16:10:24.229 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\service\impl\ClientInfoServiceImpl.class]
2025-04-29 16:10:24.229 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\service\impl\EmployeeServiceImpl.class]
2025-04-29 16:10:24.229 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\service\impl\NoticeServiceImpl.class]
2025-04-29 16:10:24.229 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\service\impl\SystemLogServiceImpl.class]
2025-04-29 16:10:24.230 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\service\impl\SystemServiceImpl.class]
2025-04-29 16:10:24.230 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\service\impl\ThingsServiceImpl.class]
2025-04-29 16:10:24.230 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\service\impl\TicketBookServiceImpl.class]
2025-04-29 16:10:24.230 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\service\impl\TicketExchangeServiceImpl.class]
2025-04-29 16:10:24.230 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\service\impl\TicketLogServiceImpl.class]
2025-04-29 16:10:24.230 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\service\impl\TicketServiceImpl.class]
2025-04-29 16:10:24.230 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\service\impl\TicketVerifyServiceImpl.class]
2025-04-29 16:10:24.230 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\service\impl\WindowBusinessServiceImpl.class]
2025-04-29 16:10:24.232 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\service\impl\WindowServiceImpl.class]
2025-04-29 16:10:24.232 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\service\impl\WindowStatusServiceImpl.class]
2025-04-29 16:10:24.246 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\task\BaiShuiTokenSyncTask.class]
2025-04-29 16:10:24.275 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-swagger2/2.9.2/springfox-swagger2-2.9.2.jar!/springfox/documentation/swagger2/mappers/ServiceModelToSwagger2MapperImpl.class]
2025-04-29 16:10:24.276 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-swagger2/2.9.2/springfox-swagger2-2.9.2.jar!/springfox/documentation/swagger2/mappers/VendorExtensionsMapperImpl.class]
2025-04-29 16:10:24.278 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-swagger2/2.9.2/springfox-swagger2-2.9.2.jar!/springfox/documentation/swagger2/mappers/ParameterMapperImpl.class]
2025-04-29 16:10:24.279 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-swagger2/2.9.2/springfox-swagger2-2.9.2.jar!/springfox/documentation/swagger2/mappers/ModelMapperImpl.class]
2025-04-29 16:10:24.279 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-swagger2/2.9.2/springfox-swagger2-2.9.2.jar!/springfox/documentation/swagger2/mappers/LicenseMapperImpl.class]
2025-04-29 16:10:24.280 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-swagger2/2.9.2/springfox-swagger2-2.9.2.jar!/springfox/documentation/swagger2/mappers/SecurityMapperImpl.class]
2025-04-29 16:10:24.290 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/scanners/ApiListingReferenceScanner.class]
2025-04-29 16:10:24.292 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/scanners/ApiDocumentationScanner.class]
2025-04-29 16:10:24.293 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/scanners/ApiDescriptionReader.class]
2025-04-29 16:10:24.293 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/scanners/ApiListingReader.class]
2025-04-29 16:10:24.294 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/scanners/CachingOperationReader.class]
2025-04-29 16:10:24.294 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/scanners/MediaTypeReader.class]
2025-04-29 16:10:24.294 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/scanners/ApiListingScanner.class]
2025-04-29 16:10:24.295 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/scanners/ApiModelReader.class]
2025-04-29 16:10:24.296 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/scanners/ApiDescriptionLookup.class]
2025-04-29 16:10:24.297 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/readers/operation/OperationModelsProvider.class]
2025-04-29 16:10:24.297 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/readers/operation/OperationDeprecatedReader.class]
2025-04-29 16:10:24.298 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/readers/operation/ResponseMessagesReader.class]
2025-04-29 16:10:24.299 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/readers/operation/OperationParameterReader.class]
2025-04-29 16:10:24.299 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/readers/operation/DefaultTagsProvider.class]
2025-04-29 16:10:24.299 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/readers/operation/OperationTagsReader.class]
2025-04-29 16:10:24.300 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/readers/operation/ApiOperationReader.class]
2025-04-29 16:10:24.300 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/readers/operation/DefaultOperationReader.class]
2025-04-29 16:10:24.301 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/readers/operation/OperationParameterRequestConditionReader.class]
2025-04-29 16:10:24.301 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/readers/operation/OperationParameterHeadersConditionReader.class]
2025-04-29 16:10:24.302 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/readers/operation/OperationResponseClassReader.class]
2025-04-29 16:10:24.302 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/readers/operation/CachingOperationNameGenerator.class]
2025-04-29 16:10:24.303 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/readers/parameter/ParameterMultiplesReader.class]
2025-04-29 16:10:24.306 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/readers/parameter/ModelAttributeParameterExpander.class]
2025-04-29 16:10:24.307 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/readers/parameter/ParameterTypeReader.class]
2025-04-29 16:10:24.307 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/readers/parameter/ParameterRequiredReader.class]
2025-04-29 16:10:24.308 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/readers/parameter/ParameterDataTypeReader.class]
2025-04-29 16:10:24.308 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/readers/parameter/ParameterDefaultReader.class]
2025-04-29 16:10:24.309 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/readers/parameter/ParameterNameReader.class]
2025-04-29 16:10:24.310 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/readers/parameter/ExpandedParameterBuilder.class]
2025-04-29 16:10:24.313 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/plugins/DocumentationPluginsBootstrapper.class]
2025-04-29 16:10:24.314 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/plugins/WebMvcRequestHandlerProvider.class]
2025-04-29 16:10:24.314 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/plugins/DocumentationPluginsManager.class]
2025-04-29 16:10:24.316 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/paths/QueryStringUriTemplateDecorator.class]
2025-04-29 16:10:24.316 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/paths/PathMappingDecorator.class]
2025-04-29 16:10:24.316 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/paths/PathSanitizer.class]
2025-04-29 16:10:24.318 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/paths/OperationPathDecorator.class]
2025-04-29 16:10:24.337 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-schema/2.9.2/springfox-schema-2.9.2.jar!/springfox/documentation/schema/CachingModelDependencyProvider.class]
2025-04-29 16:10:24.338 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-schema/2.9.2/springfox-schema-2.9.2.jar!/springfox/documentation/schema/TypeNameExtractor.class]
2025-04-29 16:10:24.338 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-schema/2.9.2/springfox-schema-2.9.2.jar!/springfox/documentation/schema/plugins/PropertyDiscriminatorBasedInheritancePlugin.class]
2025-04-29 16:10:24.339 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-schema/2.9.2/springfox-schema-2.9.2.jar!/springfox/documentation/schema/plugins/XmlModelPlugin.class]
2025-04-29 16:10:24.340 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-schema/2.9.2/springfox-schema-2.9.2.jar!/springfox/documentation/schema/plugins/SchemaPluginsManager.class]
2025-04-29 16:10:24.340 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-schema/2.9.2/springfox-schema-2.9.2.jar!/springfox/documentation/schema/property/CachingModelPropertiesProvider.class]
2025-04-29 16:10:24.340 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-schema/2.9.2/springfox-schema-2.9.2.jar!/springfox/documentation/schema/property/ObjectMapperBeanPropertyNamingStrategy.class]
2025-04-29 16:10:24.341 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-schema/2.9.2/springfox-schema-2.9.2.jar!/springfox/documentation/schema/property/bean/AccessorsProvider.class]
2025-04-29 16:10:24.341 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-schema/2.9.2/springfox-schema-2.9.2.jar!/springfox/documentation/schema/property/field/FieldProvider.class]
2025-04-29 16:10:24.342 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-schema/2.9.2/springfox-schema-2.9.2.jar!/springfox/documentation/schema/property/XmlPropertyPlugin.class]
2025-04-29 16:10:24.342 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-schema/2.9.2/springfox-schema-2.9.2.jar!/springfox/documentation/schema/property/OptimizedModelPropertiesProvider.class]
2025-04-29 16:10:24.343 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-schema/2.9.2/springfox-schema-2.9.2.jar!/springfox/documentation/schema/property/FactoryMethodProvider.class]
2025-04-29 16:10:24.344 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-schema/2.9.2/springfox-schema-2.9.2.jar!/springfox/documentation/schema/CachingModelProvider.class]
2025-04-29 16:10:24.344 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-schema/2.9.2/springfox-schema-2.9.2.jar!/springfox/documentation/schema/DefaultModelDependencyProvider.class]
2025-04-29 16:10:24.345 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-schema/2.9.2/springfox-schema-2.9.2.jar!/springfox/documentation/schema/JacksonEnumTypeDeterminer.class]
2025-04-29 16:10:24.345 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-schema/2.9.2/springfox-schema-2.9.2.jar!/springfox/documentation/schema/DefaultModelProvider.class]
2025-04-29 16:10:24.355 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-swagger-common/2.9.2/springfox-swagger-common-2.9.2.jar!/springfox/documentation/swagger/schema/ApiModelPropertyPropertyBuilder.class]
2025-04-29 16:10:24.355 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-swagger-common/2.9.2/springfox-swagger-common-2.9.2.jar!/springfox/documentation/swagger/schema/ApiModelTypeNameProvider.class]
2025-04-29 16:10:24.355 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-swagger-common/2.9.2/springfox-swagger-common-2.9.2.jar!/springfox/documentation/swagger/schema/ApiModelBuilder.class]
2025-04-29 16:10:24.357 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-swagger-common/2.9.2/springfox-swagger-common-2.9.2.jar!/springfox/documentation/swagger/readers/operation/OperationImplicitParameterReader.class]
2025-04-29 16:10:24.357 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-swagger-common/2.9.2/springfox-swagger-common-2.9.2.jar!/springfox/documentation/swagger/readers/operation/VendorExtensionsReader.class]
2025-04-29 16:10:24.357 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-swagger-common/2.9.2/springfox-swagger-common-2.9.2.jar!/springfox/documentation/swagger/readers/operation/SwaggerOperationResponseClassReader.class]
2025-04-29 16:10:24.357 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-swagger-common/2.9.2/springfox-swagger-common-2.9.2.jar!/springfox/documentation/swagger/readers/operation/SwaggerOperationModelsProvider.class]
2025-04-29 16:10:24.357 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-swagger-common/2.9.2/springfox-swagger-common-2.9.2.jar!/springfox/documentation/swagger/readers/operation/SwaggerMediaTypeReader.class]
2025-04-29 16:10:24.358 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-swagger-common/2.9.2/springfox-swagger-common-2.9.2.jar!/springfox/documentation/swagger/readers/operation/OperationHttpMethodReader.class]
2025-04-29 16:10:24.358 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-swagger-common/2.9.2/springfox-swagger-common-2.9.2.jar!/springfox/documentation/swagger/readers/operation/OperationImplicitParametersReader.class]
2025-04-29 16:10:24.358 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-swagger-common/2.9.2/springfox-swagger-common-2.9.2.jar!/springfox/documentation/swagger/readers/operation/OperationAuthReader.class]
2025-04-29 16:10:24.358 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-swagger-common/2.9.2/springfox-swagger-common-2.9.2.jar!/springfox/documentation/swagger/readers/operation/OperationHiddenReader.class]
2025-04-29 16:10:24.358 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-swagger-common/2.9.2/springfox-swagger-common-2.9.2.jar!/springfox/documentation/swagger/readers/operation/OperationSummaryReader.class]
2025-04-29 16:10:24.359 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-swagger-common/2.9.2/springfox-swagger-common-2.9.2.jar!/springfox/documentation/swagger/readers/operation/SwaggerResponseMessageReader.class]
2025-04-29 16:10:24.359 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-swagger-common/2.9.2/springfox-swagger-common-2.9.2.jar!/springfox/documentation/swagger/readers/operation/OperationNicknameIntoUniqueIdReader.class]
2025-04-29 16:10:24.359 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-swagger-common/2.9.2/springfox-swagger-common-2.9.2.jar!/springfox/documentation/swagger/readers/operation/OperationPositionReader.class]
2025-04-29 16:10:24.359 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-swagger-common/2.9.2/springfox-swagger-common-2.9.2.jar!/springfox/documentation/swagger/readers/operation/OperationNotesReader.class]
2025-04-29 16:10:24.359 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-swagger-common/2.9.2/springfox-swagger-common-2.9.2.jar!/springfox/documentation/swagger/readers/operation/SwaggerOperationTagsReader.class]
2025-04-29 16:10:24.360 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-swagger-common/2.9.2/springfox-swagger-common-2.9.2.jar!/springfox/documentation/swagger/readers/parameter/ApiParamParameterBuilder.class]
2025-04-29 16:10:24.360 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-swagger-common/2.9.2/springfox-swagger-common-2.9.2.jar!/springfox/documentation/swagger/readers/parameter/SwaggerExpandedParameterBuilder.class]
2025-04-29 16:10:24.366 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-swagger-common/2.9.2/springfox-swagger-common-2.9.2.jar!/springfox/documentation/swagger/web/SwaggerApiListingReader.class]
2025-04-29 16:10:24.367 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-swagger-common/2.9.2/springfox-swagger-common-2.9.2.jar!/springfox/documentation/swagger/web/ClassOrApiAnnotationResourceGrouping.class]
2025-04-29 16:10:24.367 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-swagger-common/2.9.2/springfox-swagger-common-2.9.2.jar!/springfox/documentation/swagger/web/InMemorySwaggerResourcesProvider.class]
2025-04-29 16:10:24.368 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-swagger-common/2.9.2/springfox-swagger-common-2.9.2.jar!/springfox/documentation/swagger/web/ApiResourceController.class]
2025-04-29 16:10:24.534 [main] DEBUG o.s.c.e.PropertySourcesPropertyResolver - Found key 'spring.jmx.enabled' in PropertySource 'configurationProperties' with value of type String
2025-04-29 16:10:24.549 [main] DEBUG o.s.c.e.PropertySourcesPropertyResolver - Found key 'spring.jmx.enabled' in PropertySource 'configurationProperties' with value of type String
2025-04-29 16:10:24.549 [main] DEBUG o.s.c.e.PropertySourcesPropertyResolver - Found key 'spring.application.admin.enabled' in PropertySource 'configurationProperties' with value of type String
2025-04-29 16:10:24.707 [main] DEBUG o.s.c.e.PropertySourcesPropertyResolver - Found key 'spring.jmx.enabled' in PropertySource 'configurationProperties' with value of type String
2025-04-29 16:10:24.709 [main] DEBUG o.s.c.e.PropertySourcesPropertyResolver - Found key 'spring.application.admin.enabled' in PropertySource 'configurationProperties' with value of type String
2025-04-29 16:10:24.753 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'com.qs.admin.common.configurer.DataSourceConfig#MapperScannerRegistrar#0'
2025-04-29 16:10:24.760 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'propertySourcesPlaceholderConfigurer'
2025-04-29 16:10:24.821 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.context.properties.ConfigurationPropertiesBeanDefinitionValidator'
2025-04-29 16:10:24.869 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.context.event.internalEventListenerProcessor'
2025-04-29 16:10:24.870 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'preserveErrorControllerTargetClassPostProcessor'
2025-04-29 16:10:24.870 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.context.event.internalEventListenerFactory'
2025-04-29 16:10:24.870 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.transaction.config.internalTransactionalEventListenerFactory'
2025-04-29 16:10:24.872 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.context.annotation.internalAutowiredAnnotationProcessor'
2025-04-29 16:10:24.872 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.context.annotation.internalCommonAnnotationProcessor'
2025-04-29 16:10:24.874 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'lifecycleBeanPostProcessor'
2025-04-29 16:10:24.874 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'shiroConfig'
2025-04-29 16:10:24.874 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'shiroConfig' of type [com.qs.admin.common.configurer.ShiroConfig$$EnhancerBySpringCGLIB$$36db62a0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-04-29 16:10:24.879 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.context.properties.ConfigurationPropertiesBindingPostProcessor'
2025-04-29 16:10:24.880 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.context.internalConfigurationPropertiesBinder'
2025-04-29 16:10:24.880 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.context.internalConfigurationPropertiesBinderFactory'
2025-04-29 16:10:24.880 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'getLifecycleBeanPostProcessor'
2025-04-29 16:10:24.891 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.context.annotation.internalScheduledAnnotationProcessor'
2025-04-29 16:10:24.891 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.scheduling.annotation.SchedulingConfiguration'
2025-04-29 16:10:24.893 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'methodValidationPostProcessor'
2025-04-29 16:10:24.904 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'securityManager'
2025-04-29 16:10:24.904 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'shiroRealm'
2025-04-29 16:10:24.909 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'jwtUtil'
2025-04-29 16:10:24.910 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'jwtProperties'
2025-04-29 16:10:24.916 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'jwtProperties' of type [com.qs.admin.common.shiro.security.JwtProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-04-29 16:10:24.917 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'jwtUtil' of type [com.qs.admin.common.shiro.security.JwtUtil] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-04-29 16:10:24.917 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'shiroRealm' of type [com.qs.admin.common.shiro.ShiroRealm] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-04-29 16:10:24.917 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'shiroCacheManager'
2025-04-29 16:10:24.918 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'localCacheManager'
2025-04-29 16:10:24.935 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'localCacheManager' of type [com.qs.admin.common.utils.LocalCacheManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-04-29 16:10:24.936 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'shiroCacheManager' of type [com.qs.admin.common.shiro.cache.ShiroCacheManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-04-29 16:10:24.936 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Autowiring by type from bean name 'securityManager' via factory method to bean named 'shiroRealm'
2025-04-29 16:10:24.936 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Autowiring by type from bean name 'securityManager' via factory method to bean named 'shiroCacheManager'
2025-04-29 16:10:25.147 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'securityManager' of type [org.apache.shiro.web.mgt.DefaultWebSecurityManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-04-29 16:10:25.148 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'syncCacheServiceImpl'
2025-04-29 16:10:25.148 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'syncCacheServiceImpl' of type [com.qs.admin.common.service.impl.SyncCacheServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-04-29 16:10:25.149 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Autowiring by type from bean name 'shiroFilter' via factory method to bean named 'securityManager'
2025-04-29 16:10:25.149 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Autowiring by type from bean name 'shiroFilter' via factory method to bean named 'localCacheManager'
2025-04-29 16:10:25.149 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Autowiring by type from bean name 'shiroFilter' via factory method to bean named 'jwtProperties'
2025-04-29 16:10:25.149 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Autowiring by type from bean name 'shiroFilter' via factory method to bean named 'syncCacheServiceImpl'
2025-04-29 16:10:25.153 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'getShiroFilterProperties'
2025-04-29 16:10:25.157 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'getShiroFilterProperties' of type [com.qs.admin.common.shiro.ShiroFilterProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-04-29 16:10:25.161 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Autowiring by type from bean name 'methodValidationPostProcessor' via factory method to bean named 'environment'
2025-04-29 16:10:25.164 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'dataSourceInitializerPostProcessor'
2025-04-29 16:10:25.164 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.aop.config.internalAutoProxyCreator'
2025-04-29 16:10:25.176 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'persistenceExceptionTranslationPostProcessor'
2025-04-29 16:10:25.177 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Autowiring by type from bean name 'persistenceExceptionTranslationPostProcessor' via factory method to bean named 'environment'
2025-04-29 16:10:25.177 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'shiroFilter'
2025-04-29 16:10:25.178 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'authorizationAttributeSourceAdvisor'
2025-04-29 16:10:25.179 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Autowiring by type from bean name 'authorizationAttributeSourceAdvisor' via factory method to bean named 'securityManager'
2025-04-29 16:10:25.183 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'authorizationAttributeSourceAdvisor' of type [org.apache.shiro.spring.security.interceptor.AuthorizationAttributeSourceAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-04-29 16:10:25.185 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'dynamicDatasourceAnnotationAdvisor'
2025-04-29 16:10:25.185 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration'
2025-04-29 16:10:25.185 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.transaction.config.internalTransactionAdvisor'
2025-04-29 16:10:25.185 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration'
2025-04-29 16:10:25.193 [main] DEBUG o.s.a.a.a.ReflectiveAspectJAdvisorFactory - Found AspectJ method: public java.lang.Object com.qs.admin.common.aspect.IdempotencyAspect.around(org.aspectj.lang.ProceedingJoinPoint,org.springframework.web.bind.annotation.PostMapping) throws java.lang.Throwable
2025-04-29 16:10:25.204 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-04-29 16:10:25.251 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'transactionAttributeSource'
2025-04-29 16:10:25.255 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'transactionInterceptor'
2025-04-29 16:10:25.255 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Autowiring by type from bean name 'transactionInterceptor' via factory method to bean named 'transactionAttributeSource'
2025-04-29 16:10:25.259 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Autowiring by type from bean name 'org.springframework.transaction.config.internalTransactionAdvisor' via factory method to bean named 'transactionAttributeSource'
2025-04-29 16:10:25.259 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Autowiring by type from bean name 'org.springframework.transaction.config.internalTransactionAdvisor' via factory method to bean named 'transactionInterceptor'
2025-04-29 16:10:25.260 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'spring.datasource.dynamic-com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties'
2025-04-29 16:10:25.268 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.datasource.dynamic-com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-04-29 16:10:25.270 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration$$EnhancerBySpringCGLIB$$a073d336] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-04-29 16:10:25.272 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'dsProcessor'
2025-04-29 16:10:25.275 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dsProcessor' of type [com.baomidou.dynamic.datasource.processor.DsHeaderProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-04-29 16:10:25.275 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Autowiring by type from bean name 'dynamicDatasourceAnnotationAdvisor' via factory method to bean named 'dsProcessor'
2025-04-29 16:10:25.278 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dynamicDatasourceAnnotationAdvisor' of type [com.baomidou.dynamic.datasource.aop.DynamicDataSourceAnnotationAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-04-29 16:10:25.281 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'objectMapperConfigurer'
2025-04-29 16:10:25.282 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'webServerFactoryCustomizerBeanPostProcessor'
2025-04-29 16:10:25.282 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'errorPageRegistrarBeanPostProcessor'
2025-04-29 16:10:25.283 [main] DEBUG o.s.u.c.s.UiApplicationContextUtils - Unable to locate ThemeSource with name 'themeSource': using default [org.springframework.ui.context.support.ResourceBundleThemeSource@237add]
2025-04-29 16:10:25.284 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'undertowServletWebServerFactory'
2025-04-29 16:10:25.284 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.ServletWebServerFactoryConfiguration$EmbeddedUndertow'
2025-04-29 16:10:25.291 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'websocketServletWebServerCustomizer'
2025-04-29 16:10:25.291 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.websocket.servlet.WebSocketServletAutoConfiguration$UndertowWebSocketConfiguration'
2025-04-29 16:10:25.293 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'servletWebServerFactoryCustomizer'
2025-04-29 16:10:25.293 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.ServletWebServerFactoryAutoConfiguration'
2025-04-29 16:10:25.295 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'server-org.springframework.boot.autoconfigure.web.ServerProperties'
2025-04-29 16:10:25.306 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Autowiring by type from bean name 'servletWebServerFactoryCustomizer' via factory method to bean named 'server-org.springframework.boot.autoconfigure.web.ServerProperties'
2025-04-29 16:10:25.307 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'undertowWebServerFactoryCustomizer'
2025-04-29 16:10:25.307 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.embedded.EmbeddedWebServerFactoryCustomizerAutoConfiguration$UndertowWebServerFactoryCustomizerConfiguration'
2025-04-29 16:10:25.308 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Autowiring by type from bean name 'undertowWebServerFactoryCustomizer' via factory method to bean named 'environment'
2025-04-29 16:10:25.308 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Autowiring by type from bean name 'undertowWebServerFactoryCustomizer' via factory method to bean named 'server-org.springframework.boot.autoconfigure.web.ServerProperties'
2025-04-29 16:10:25.309 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'localeCharsetMappingsCustomizer'
2025-04-29 16:10:25.309 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.HttpEncodingAutoConfiguration'
2025-04-29 16:10:25.310 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'spring.http-org.springframework.boot.autoconfigure.http.HttpProperties'
2025-04-29 16:10:25.311 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.web.servlet.HttpEncodingAutoConfiguration' via constructor to bean named 'spring.http-org.springframework.boot.autoconfigure.http.HttpProperties'
2025-04-29 16:10:25.327 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'errorPageCustomizer'
2025-04-29 16:10:25.327 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.error.ErrorMvcAutoConfiguration'
2025-04-29 16:10:25.327 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.web.servlet.error.ErrorMvcAutoConfiguration' via constructor to bean named 'server-org.springframework.boot.autoconfigure.web.ServerProperties'
2025-04-29 16:10:25.328 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'dispatcherServletRegistration'
2025-04-29 16:10:25.329 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.DispatcherServletAutoConfiguration$DispatcherServletRegistrationConfiguration'
2025-04-29 16:10:25.330 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'dispatcherServlet'
2025-04-29 16:10:25.330 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.DispatcherServletAutoConfiguration$DispatcherServletConfiguration'
2025-04-29 16:10:25.332 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'spring.mvc-org.springframework.boot.autoconfigure.web.servlet.WebMvcProperties'
2025-04-29 16:10:25.335 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Autowiring by type from bean name 'dispatcherServlet' via factory method to bean named 'spring.http-org.springframework.boot.autoconfigure.http.HttpProperties'
2025-04-29 16:10:25.335 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Autowiring by type from bean name 'dispatcherServlet' via factory method to bean named 'spring.mvc-org.springframework.boot.autoconfigure.web.servlet.WebMvcProperties'
2025-04-29 16:10:25.355 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Autowiring by type from bean name 'dispatcherServletRegistration' via factory method to bean named 'dispatcherServlet'
2025-04-29 16:10:25.355 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Autowiring by type from bean name 'dispatcherServletRegistration' via factory method to bean named 'spring.mvc-org.springframework.boot.autoconfigure.web.servlet.WebMvcProperties'
2025-04-29 16:10:25.356 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'multipartConfigElement'
2025-04-29 16:10:25.356 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.MultipartAutoConfiguration'
2025-04-29 16:10:25.356 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'spring.servlet.multipart-org.springframework.boot.autoconfigure.web.servlet.MultipartProperties'
2025-04-29 16:10:25.360 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.web.servlet.MultipartAutoConfiguration' via constructor to bean named 'spring.servlet.multipart-org.springframework.boot.autoconfigure.web.servlet.MultipartProperties'
2025-04-29 16:10:25.367 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Autowiring by type from bean name 'errorPageCustomizer' via factory method to bean named 'dispatcherServletRegistration'
2025-04-29 16:10:25.384 [main] DEBUG o.s.b.w.e.u.UndertowServletWebServerFactory - Code archive: D:\DevelopmentTools\Maven_respository\org\springframework\boot\spring-boot\2.2.1.RELEASE\spring-boot-2.2.1.RELEASE.jar
2025-04-29 16:10:25.384 [main] DEBUG o.s.b.w.e.u.UndertowServletWebServerFactory - Code archive: D:\DevelopmentTools\Maven_respository\org\springframework\boot\spring-boot\2.2.1.RELEASE\spring-boot-2.2.1.RELEASE.jar
2025-04-29 16:10:25.385 [main] DEBUG o.s.b.w.e.u.UndertowServletWebServerFactory - None of the document roots [src/main/webapp, public, static] point to a directory and will be ignored.
2025-04-29 16:10:25.414 [main] WARN  io.undertow.websockets.jsr - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
2025-04-29 16:10:25.427 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-04-29 16:10:25.427 [main] DEBUG o.s.web.context.ContextLoader - Published root WebApplicationContext as ServletContext attribute with name [org.springframework.web.context.WebApplicationContext.ROOT]
2025-04-29 16:10:25.427 [main] INFO  o.s.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 1322 ms
2025-04-29 16:10:25.433 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'filterRegistrationBean'
2025-04-29 16:10:25.437 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'statViewServletRegistrationBean'
2025-04-29 16:10:25.437 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'com.alibaba.druid.spring.boot.autoconfigure.stat.DruidStatViewServletConfiguration'
2025-04-29 16:10:25.439 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'spring.datasource.druid-com.alibaba.druid.spring.boot.autoconfigure.properties.DruidStatProperties'
2025-04-29 16:10:25.440 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Autowiring by type from bean name 'statViewServletRegistrationBean' via factory method to bean named 'spring.datasource.druid-com.alibaba.druid.spring.boot.autoconfigure.properties.DruidStatProperties'
2025-04-29 16:10:25.445 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'webStatFilterRegistrationBean'
2025-04-29 16:10:25.445 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'com.alibaba.druid.spring.boot.autoconfigure.stat.DruidWebStatFilterConfiguration'
2025-04-29 16:10:25.446 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Autowiring by type from bean name 'webStatFilterRegistrationBean' via factory method to bean named 'spring.datasource.druid-com.alibaba.druid.spring.boot.autoconfigure.properties.DruidStatProperties'
2025-04-29 16:10:25.451 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'corsFilter'
2025-04-29 16:10:25.452 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'corsConfig'
2025-04-29 16:10:25.471 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'requestContextFilter'
2025-04-29 16:10:25.476 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'formContentFilter'
2025-04-29 16:10:25.476 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration'
2025-04-29 16:10:25.481 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'characterEncodingFilter'
2025-04-29 16:10:25.490 [main] DEBUG o.s.b.w.s.ServletContextInitializerBeans - Mapping filters: filterRegistrationBean urls=[/*] order=2147483647, filterRegistrationBean urls=[/*] order=2147483647, characterEncodingFilter urls=[/*] order=-2147483648, formContentFilter urls=[/*] order=-9900, requestContextFilter urls=[/*] order=-105, corsFilter urls=[/*] order=2147483647, shiroFilter urls=[/*] order=2147483647
2025-04-29 16:10:25.490 [main] DEBUG o.s.b.w.s.ServletContextInitializerBeans - Mapping servlets: dispatcherServlet urls=[/], statViewServlet urls=[/druid/*]
2025-04-29 16:10:25.513 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'qscAdminApplication'
2025-04-29 16:10:25.514 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'idempotencyAspect'
2025-04-29 16:10:25.515 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'dataSourceConfig'
2025-04-29 16:10:25.516 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'myWebMvcConfig'
2025-04-29 16:10:25.519 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'swagger2Configurer'
2025-04-29 16:10:25.522 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'mybatisPlusConfig'
2025-04-29 16:10:25.524 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'dataSource'
2025-04-29 16:10:25.568 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jdbc.DataSourceInitializerInvoker'
2025-04-29 16:10:25.569 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'spring.datasource-org.springframework.boot.autoconfigure.jdbc.DataSourceProperties'
2025-04-29 16:10:25.578 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.jdbc.DataSourceInitializerInvoker' via constructor to bean named 'spring.datasource-org.springframework.boot.autoconfigure.jdbc.DataSourceProperties'
2025-04-29 16:10:25.578 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.jdbc.DataSourceInitializerInvoker' via constructor to bean named 'org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@1b1fde8'
2025-04-29 16:10:25.586 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'mybatis-org.mybatis.spring.boot.autoconfigure.MybatisProperties'
2025-04-29 16:10:25.589 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'mybatisPlusInterceptor'
2025-04-29 16:10:25.595 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'globalExceptionHandler'
2025-04-29 16:10:25.597 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'shiroFilterProperties'
2025-04-29 16:10:25.599 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'springContextUtil'
2025-04-29 16:10:25.600 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'baiShuiProperties'
2025-04-29 16:10:25.602 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'restTemplateConfig'
2025-04-29 16:10:25.602 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'agentInfoController'
2025-04-29 16:10:25.604 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'agentInfoServiceImpl'
2025-04-29 16:10:25.643 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'agentInfoMapper'
2025-04-29 16:10:25.646 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'mybatisSqlSessionFactoryBean'
2025-04-29 16:10:25.997 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'agentInfoEnterpriseMapper'
2025-04-29 16:10:26.020 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'agentInfoEnterpriseServiceImpl'
2025-04-29 16:10:26.059 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'ticketVerifyServiceImpl'
2025-04-29 16:10:26.060 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'ticketVerifyMapper'
2025-04-29 16:10:26.125 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'baiShuiApiServiceImpl'
2025-04-29 16:10:26.126 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'restTemplate'
2025-04-29 16:10:26.140 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'systemServiceImpl'
2025-04-29 16:10:26.141 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'systemMapper'
2025-04-29 16:10:26.187 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'baiShuiTokenSyncTask'
2025-04-29 16:10:28.673 [main] INFO  c.q.a.t.s.impl.BaiShuiApiServiceImpl - 获取佰税API访问令牌
2025-04-29 16:10:30.186 [main] INFO  c.q.a.t.s.impl.BaiShuiApiServiceImpl - tokenUrl=https://etax.zhejiang.chinatax.gov.cn/nsfw/apiService/oauth/token
2025-04-29 16:10:30.366 [main] DEBUG o.s.web.client.RestTemplate - HTTP POST https://etax.zhejiang.chinatax.gov.cn/nsfw/apiService/oauth/token
2025-04-29 16:10:30.402 [main] DEBUG o.s.web.client.RestTemplate - Accept=[application/json, application/*+json]
2025-04-29 16:10:30.408 [main] DEBUG o.s.web.client.RestTemplate - Writing [{appKey=[HZBSJHXT], appSecret=[7d427891ef38f4a7b247447f715c66cd]}] as "application/x-www-form-urlencoded"
2025-04-29 16:10:30.764 [main] DEBUG o.s.web.client.RestTemplate - Response 200 OK
2025-04-29 16:10:30.766 [main] DEBUG o.s.web.client.RestTemplate - Reading to [java.util.Map<?, ?>]
2025-04-29 16:10:32.742 [main] INFO  c.q.a.t.s.impl.BaiShuiApiServiceImpl - 成功获取佰税API访问令牌
2025-04-29 16:10:35.011 [main] DEBUG o.s.jdbc.datasource.DataSourceUtils - Fetching JDBC Connection from DataSource
2025-04-29 16:10:35.013 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-04-29 16:10:36.027 [main] WARN  c.m.s.jdbc.internals.TDS.Channel - TLSv1 was negotiated. Please update server and client to use TLSv1.2 at minimum.
2025-04-29 16:10:37.053 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-04-29 16:10:37.080 [main] DEBUG c.q.a.t.m.SystemMapper.selectList - ==>  Preparing: SELECT [KEY],[VALUE],[MEMO] FROM system WHERE ([KEY] = ?)
2025-04-29 16:10:37.223 [HikariPool-1 connection adder] WARN  c.m.s.jdbc.internals.TDS.Channel - TLSv1 was negotiated. Please update server and client to use TLSv1.2 at minimum.
2025-04-29 16:10:37.286 [HikariPool-1 connection adder] WARN  c.m.s.jdbc.internals.TDS.Channel - TLSv1 was negotiated. Please update server and client to use TLSv1.2 at minimum.
2025-04-29 16:10:37.347 [HikariPool-1 connection adder] WARN  c.m.s.jdbc.internals.TDS.Channel - TLSv1 was negotiated. Please update server and client to use TLSv1.2 at minimum.
2025-04-29 16:10:37.456 [HikariPool-1 connection adder] WARN  c.m.s.jdbc.internals.TDS.Channel - TLSv1 was negotiated. Please update server and client to use TLSv1.2 at minimum.
2025-04-29 16:10:37.474 [main] DEBUG c.q.a.t.m.SystemMapper.selectList - ==> Parameters: BS_API_APPKEY(String)
2025-04-29 16:10:37.515 [HikariPool-1 connection adder] WARN  c.m.s.jdbc.internals.TDS.Channel - TLSv1 was negotiated. Please update server and client to use TLSv1.2 at minimum.
2025-04-29 16:10:37.610 [HikariPool-1 connection adder] WARN  c.m.s.jdbc.internals.TDS.Channel - TLSv1 was negotiated. Please update server and client to use TLSv1.2 at minimum.
2025-04-29 16:10:37.685 [HikariPool-1 connection adder] WARN  c.m.s.jdbc.internals.TDS.Channel - TLSv1 was negotiated. Please update server and client to use TLSv1.2 at minimum.
2025-04-29 16:10:37.754 [HikariPool-1 connection adder] WARN  c.m.s.jdbc.internals.TDS.Channel - TLSv1 was negotiated. Please update server and client to use TLSv1.2 at minimum.
2025-04-29 16:10:37.771 [main] DEBUG c.q.a.t.m.SystemMapper.selectList - <==      Total: 1
2025-04-29 16:10:37.827 [HikariPool-1 connection adder] WARN  c.m.s.jdbc.internals.TDS.Channel - TLSv1 was negotiated. Please update server and client to use TLSv1.2 at minimum.
2025-04-29 16:11:17.492 [main] DEBUG o.s.jdbc.datasource.DataSourceUtils - Fetching JDBC Connection from DataSource
2025-04-29 16:11:17.505 [main] DEBUG c.q.a.t.mapper.SystemMapper.update - ==>  Preparing: UPDATE system SET [VALUE]=?, [MEMO]=? WHERE [KEY]=? AND [VALUE]=? AND [MEMO]=?
2025-04-29 16:11:17.507 [main] DEBUG c.q.a.t.mapper.SystemMapper.update - ==> Parameters: HZBSJHXT(String), 佰税科技API APPKEY，自动同步于Tue Apr 29 16:11:05 CST 2025(String), BS_API_APPKEY(String), HZBSJHXT(String), 佰税科技API APPKEY，自动同步于Tue Apr 29 16:11:05 CST 2025(String)
2025-04-29 16:11:17.519 [main] DEBUG c.q.a.t.mapper.SystemMapper.update - <==    Updates: 0
2025-04-29 16:11:26.985 [main] ERROR c.q.a.t.task.BaiShuiTokenSyncTask - APPKEY同步失败，未写入数据库！
2025-04-29 16:13:26.631 [HikariPool-1 housekeeper] WARN  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=2m16s244ms742µs200ns).
2025-04-29 16:13:26.632 [main] DEBUG o.s.jdbc.datasource.DataSourceUtils - Fetching JDBC Connection from DataSource
2025-04-29 16:13:26.644 [main] DEBUG c.q.a.t.m.SystemMapper.selectList - ==>  Preparing: SELECT [KEY],[VALUE],[MEMO] FROM system WHERE ([KEY] = ?)
2025-04-29 16:13:26.644 [main] DEBUG c.q.a.t.m.SystemMapper.selectList - ==> Parameters: BS_API_TOKEN(String)
2025-04-29 16:13:39.988 [main] DEBUG o.s.b.c.l.ClasspathLoggingApplicationListener - Application started with classpath: [file:/D:/DevelopmentTools/Java/java-8u311/jre/lib/charsets.jar, file:/D:/DevelopmentTools/Java/java-8u311/jre/lib/deploy.jar, file:/D:/DevelopmentTools/Java/java-8u311/jre/lib/ext/access-bridge-32.jar, file:/D:/DevelopmentTools/Java/java-8u311/jre/lib/ext/cldrdata.jar, file:/D:/DevelopmentTools/Java/java-8u311/jre/lib/ext/dnsns.jar, file:/D:/DevelopmentTools/Java/java-8u311/jre/lib/ext/jaccess.jar, file:/D:/DevelopmentTools/Java/java-8u311/jre/lib/ext/jfxrt.jar, file:/D:/DevelopmentTools/Java/java-8u311/jre/lib/ext/localedata.jar, file:/D:/DevelopmentTools/Java/java-8u311/jre/lib/ext/nashorn.jar, file:/D:/DevelopmentTools/Java/java-8u311/jre/lib/ext/sunec.jar, file:/D:/DevelopmentTools/Java/java-8u311/jre/lib/ext/sunjce_provider.jar, file:/D:/DevelopmentTools/Java/java-8u311/jre/lib/ext/sunmscapi.jar, file:/D:/DevelopmentTools/Java/java-8u311/jre/lib/ext/sunpkcs11.jar, file:/D:/DevelopmentTools/Java/java-8u311/jre/lib/ext/zipfs.jar, file:/D:/DevelopmentTools/Java/java-8u311/jre/lib/javaws.jar, file:/D:/DevelopmentTools/Java/java-8u311/jre/lib/jce.jar, file:/D:/DevelopmentTools/Java/java-8u311/jre/lib/jfr.jar, file:/D:/DevelopmentTools/Java/java-8u311/jre/lib/jfxswt.jar, file:/D:/DevelopmentTools/Java/java-8u311/jre/lib/jsse.jar, file:/D:/DevelopmentTools/Java/java-8u311/jre/lib/management-agent.jar, file:/D:/DevelopmentTools/Java/java-8u311/jre/lib/plugin.jar, file:/D:/DevelopmentTools/Java/java-8u311/jre/lib/resources.jar, file:/D:/DevelopmentTools/Java/java-8u311/jre/lib/rt.jar, file:/D:/project/Java%20Projectes/qsadmin/target/classes/, file:/D:/DevelopmentTools/Maven_respository/com/microsoft/sqlserver/mssql-jdbc/12.6.1.jre8/mssql-jdbc-12.6.1.jre8.jar, file:/D:/DevelopmentTools/Maven_respository/org/springframework/boot/spring-boot-starter-web/2.2.1.RELEASE/spring-boot-starter-web-2.2.1.RELEASE.jar, file:/D:/DevelopmentTools/Maven_respository/org/springframework/boot/spring-boot-starter/2.2.1.RELEASE/spring-boot-starter-2.2.1.RELEASE.jar, file:/D:/DevelopmentTools/Maven_respository/org/springframework/boot/spring-boot/2.2.1.RELEASE/spring-boot-2.2.1.RELEASE.jar, file:/D:/DevelopmentTools/Maven_respository/org/springframework/boot/spring-boot-starter-logging/2.2.1.RELEASE/spring-boot-starter-logging-2.2.1.RELEASE.jar, file:/D:/DevelopmentTools/Maven_respository/ch/qos/logback/logback-classic/1.2.3/logback-classic-1.2.3.jar, file:/D:/DevelopmentTools/Maven_respository/ch/qos/logback/logback-core/1.2.3/logback-core-1.2.3.jar, file:/D:/DevelopmentTools/Maven_respository/org/apache/logging/log4j/log4j-to-slf4j/2.12.1/log4j-to-slf4j-2.12.1.jar, file:/D:/DevelopmentTools/Maven_respository/org/apache/logging/log4j/log4j-api/2.12.1/log4j-api-2.12.1.jar, file:/D:/DevelopmentTools/Maven_respository/org/slf4j/jul-to-slf4j/1.7.29/jul-to-slf4j-1.7.29.jar, file:/D:/DevelopmentTools/Maven_respository/jakarta/annotation/jakarta.annotation-api/1.3.5/jakarta.annotation-api-1.3.5.jar, file:/D:/DevelopmentTools/Maven_respository/org/yaml/snakeyaml/1.25/snakeyaml-1.25.jar, file:/D:/DevelopmentTools/Maven_respository/org/springframework/boot/spring-boot-starter-json/2.2.1.RELEASE/spring-boot-starter-json-2.2.1.RELEASE.jar, file:/D:/DevelopmentTools/Maven_respository/com/fasterxml/jackson/datatype/jackson-datatype-jdk8/2.10.0/jackson-datatype-jdk8-2.10.0.jar, file:/D:/DevelopmentTools/Maven_respository/com/fasterxml/jackson/datatype/jackson-datatype-jsr310/2.10.0/jackson-datatype-jsr310-2.10.0.jar, file:/D:/DevelopmentTools/Maven_respository/com/fasterxml/jackson/module/jackson-module-parameter-names/2.10.0/jackson-module-parameter-names-2.10.0.jar, file:/D:/DevelopmentTools/Maven_respository/org/springframework/boot/spring-boot-starter-validation/2.2.1.RELEASE/spring-boot-starter-validation-2.2.1.RELEASE.jar, file:/D:/DevelopmentTools/Maven_respository/jakarta/validation/jakarta.validation-api/2.0.1/jakarta.validation-api-2.0.1.jar, file:/D:/DevelopmentTools/Maven_respository/org/hibernate/validator/hibernate-validator/6.0.18.Final/hibernate-validator-6.0.18.Final.jar, file:/D:/DevelopmentTools/Maven_respository/org/springframework/spring-web/5.2.1.RELEASE/spring-web-5.2.1.RELEASE.jar, file:/D:/DevelopmentTools/Maven_respository/org/springframework/spring-webmvc/5.2.1.RELEASE/spring-webmvc-5.2.1.RELEASE.jar, file:/D:/DevelopmentTools/Maven_respository/org/springframework/boot/spring-boot-starter-undertow/2.2.1.RELEASE/spring-boot-starter-undertow-2.2.1.RELEASE.jar, file:/D:/DevelopmentTools/Maven_respository/io/undertow/undertow-core/2.0.27.Final/undertow-core-2.0.27.Final.jar, file:/D:/DevelopmentTools/Maven_respository/org/jboss/logging/jboss-logging/3.4.1.Final/jboss-logging-3.4.1.Final.jar, file:/D:/DevelopmentTools/Maven_respository/org/jboss/xnio/xnio-api/3.3.8.Final/xnio-api-3.3.8.Final.jar, file:/D:/DevelopmentTools/Maven_respository/org/jboss/xnio/xnio-nio/3.3.8.Final/xnio-nio-3.3.8.Final.jar, file:/D:/DevelopmentTools/Maven_respository/io/undertow/undertow-servlet/2.0.27.Final/undertow-servlet-2.0.27.Final.jar, file:/D:/DevelopmentTools/Maven_respository/org/jboss/spec/javax/annotation/jboss-annotations-api_1.2_spec/1.0.2.Final/jboss-annotations-api_1.2_spec-1.0.2.Final.jar, file:/D:/DevelopmentTools/Maven_respository/io/undertow/undertow-websockets-jsr/2.0.27.Final/undertow-websockets-jsr-2.0.27.Final.jar, file:/D:/DevelopmentTools/Maven_respository/org/jboss/spec/javax/websocket/jboss-websocket-api_1.1_spec/1.1.4.Final/jboss-websocket-api_1.1_spec-1.1.4.Final.jar, file:/D:/DevelopmentTools/Maven_respository/jakarta/servlet/jakarta.servlet-api/4.0.3/jakarta.servlet-api-4.0.3.jar, file:/D:/DevelopmentTools/Maven_respository/org/glassfish/jakarta.el/3.0.3/jakarta.el-3.0.3.jar, file:/D:/DevelopmentTools/Maven_respository/net/bytebuddy/byte-buddy/1.10.2/byte-buddy-1.10.2.jar, file:/D:/DevelopmentTools/Maven_respository/org/springframework/spring-core/5.2.1.RELEASE/spring-core-5.2.1.RELEASE.jar, file:/D:/DevelopmentTools/Maven_respository/org/springframework/spring-jcl/5.2.1.RELEASE/spring-jcl-5.2.1.RELEASE.jar, file:/D:/DevelopmentTools/Maven_respository/com/baomidou/dynamic-datasource-spring-boot-starter/3.0.0/dynamic-datasource-spring-boot-starter-3.0.0.jar, file:/D:/DevelopmentTools/Maven_respository/org/springframework/boot/spring-boot-starter-jdbc/2.2.1.RELEASE/spring-boot-starter-jdbc-2.2.1.RELEASE.jar, file:/D:/DevelopmentTools/Maven_respository/com/zaxxer/HikariCP/3.4.1/HikariCP-3.4.1.jar, file:/D:/DevelopmentTools/Maven_respository/org/springframework/spring-jdbc/5.2.1.RELEASE/spring-jdbc-5.2.1.RELEASE.jar, file:/D:/DevelopmentTools/Maven_respository/org/springframework/spring-tx/5.2.1.RELEASE/spring-tx-5.2.1.RELEASE.jar, file:/D:/DevelopmentTools/Maven_respository/org/springframework/boot/spring-boot-starter-aop/2.2.1.RELEASE/spring-boot-starter-aop-2.2.1.RELEASE.jar, file:/D:/DevelopmentTools/Maven_respository/org/aspectj/aspectjweaver/1.9.4/aspectjweaver-1.9.4.jar, file:/D:/DevelopmentTools/Maven_respository/org/apache/tomcat/tomcat-jdbc/7.0.81/tomcat-jdbc-7.0.81.jar, file:/D:/DevelopmentTools/Maven_respository/org/apache/tomcat/tomcat-juli/7.0.81/tomcat-juli-7.0.81.jar, file:/D:/DevelopmentTools/Maven_respository/com/belerweb/pinyin4j/2.5.0/pinyin4j-2.5.0.jar, file:/D:/DevelopmentTools/Maven_respository/org/apache/ant/ant/1.9.7/ant-1.9.7.jar, file:/D:/DevelopmentTools/Maven_respository/org/apache/ant/ant-launcher/1.9.7/ant-launcher-1.9.7.jar, file:/D:/DevelopmentTools/Maven_respository/net/coobird/thumbnailator/0.4.8/thumbnailator-0.4.8.jar, file:/D:/DevelopmentTools/Maven_respository/joda-time/joda-time/2.9.8/joda-time-2.9.8.jar, file:/D:/DevelopmentTools/Maven_respository/org/apache/velocity/velocity-engine-core/2.0/velocity-engine-core-2.0.jar, file:/D:/DevelopmentTools/Maven_respository/org/slf4j/slf4j-api/1.7.29/slf4j-api-1.7.29.jar, file:/D:/DevelopmentTools/Maven_respository/cn/hutool/hutool-all/4.1.2/hutool-all-4.1.2.jar, file:/D:/DevelopmentTools/Maven_respository/org/projectlombok/lombok/1.18.10/lombok-1.18.10.jar, file:/D:/DevelopmentTools/Maven_respository/com/baomidou/mybatis-plus-boot-starter/3.4.3.4/mybatis-plus-boot-starter-3.4.3.4.jar, file:/D:/DevelopmentTools/Maven_respository/com/baomidou/mybatis-plus/3.4.3.4/mybatis-plus-3.4.3.4.jar, file:/D:/DevelopmentTools/Maven_respository/com/baomidou/mybatis-plus-extension/3.4.3.4/mybatis-plus-extension-3.4.3.4.jar, file:/D:/DevelopmentTools/Maven_respository/com/baomidou/mybatis-plus-core/3.4.3.4/mybatis-plus-core-3.4.3.4.jar, file:/D:/DevelopmentTools/Maven_respository/com/baomidou/mybatis-plus-annotation/3.4.3.4/mybatis-plus-annotation-3.4.3.4.jar, file:/D:/DevelopmentTools/Maven_respository/com/github/jsqlparser/jsqlparser/4.2/jsqlparser-4.2.jar, file:/D:/DevelopmentTools/Maven_respository/org/springframework/boot/spring-boot-autoconfigure/2.2.1.RELEASE/spring-boot-autoconfigure-2.2.1.RELEASE.jar, file:/D:/DevelopmentTools/Maven_respository/org/mybatis/spring/boot/mybatis-spring-boot-starter/2.1.0/mybatis-spring-boot-starter-2.1.0.jar, file:/D:/DevelopmentTools/Maven_respository/org/mybatis/spring/boot/mybatis-spring-boot-autoconfigure/2.1.0/mybatis-spring-boot-autoconfigure-2.1.0.jar, file:/D:/DevelopmentTools/Maven_respository/org/mybatis/mybatis-spring/2.0.2/mybatis-spring-2.0.2.jar, file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-swagger2/2.9.2/springfox-swagger2-2.9.2.jar, file:/D:/DevelopmentTools/Maven_respository/io/swagger/swagger-annotations/1.5.20/swagger-annotations-1.5.20.jar, file:/D:/DevelopmentTools/Maven_respository/io/swagger/swagger-models/1.5.20/swagger-models-1.5.20.jar, file:/D:/DevelopmentTools/Maven_respository/com/fasterxml/jackson/core/jackson-annotations/2.10.0/jackson-annotations-2.10.0.jar, file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spi/2.9.2/springfox-spi-2.9.2.jar, file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-core/2.9.2/springfox-core-2.9.2.jar, file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-schema/2.9.2/springfox-schema-2.9.2.jar, file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-swagger-common/2.9.2/springfox-swagger-common-2.9.2.jar, file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar, file:/D:/DevelopmentTools/Maven_respository/com/fasterxml/classmate/1.5.1/classmate-1.5.1.jar, file:/D:/DevelopmentTools/Maven_respository/org/springframework/plugin/spring-plugin-core/1.2.0.RELEASE/spring-plugin-core-1.2.0.RELEASE.jar, file:/D:/DevelopmentTools/Maven_respository/org/springframework/plugin/spring-plugin-metadata/1.2.0.RELEASE/spring-plugin-metadata-1.2.0.RELEASE.jar, file:/D:/DevelopmentTools/Maven_respository/org/mapstruct/mapstruct/1.2.0.Final/mapstruct-1.2.0.Final.jar, file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-swagger-ui/2.9.2/springfox-swagger-ui-2.9.2.jar, file:/D:/DevelopmentTools/Maven_respository/org/apache/commons/commons-lang3/3.8.1/commons-lang3-3.8.1.jar, file:/D:/DevelopmentTools/Maven_respository/com/google/guava/guava/27.0-jre/guava-27.0-jre.jar, file:/D:/DevelopmentTools/Maven_respository/com/google/guava/failureaccess/1.0/failureaccess-1.0.jar, file:/D:/DevelopmentTools/Maven_respository/com/google/guava/listenablefuture/9999.0-empty-to-avoid-conflict-with-guava/listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar, file:/D:/DevelopmentTools/Maven_respository/com/google/code/findbugs/jsr305/3.0.2/jsr305-3.0.2.jar, file:/D:/DevelopmentTools/Maven_respository/org/checkerframework/checker-qual/2.5.2/checker-qual-2.5.2.jar, file:/D:/DevelopmentTools/Maven_respository/com/google/errorprone/error_prone_annotations/2.2.0/error_prone_annotations-2.2.0.jar, file:/D:/DevelopmentTools/Maven_respository/com/google/j2objc/j2objc-annotations/1.1/j2objc-annotations-1.1.jar, file:/D:/DevelopmentTools/Maven_respository/org/codehaus/mojo/animal-sniffer-annotations/1.17/animal-sniffer-annotations-1.17.jar, file:/D:/DevelopmentTools/Maven_respository/com/alibaba/fastjson/1.2.83/fastjson-1.2.83.jar, file:/D:/DevelopmentTools/Maven_respository/com/alibaba/druid-spring-boot-starter/1.1.10/druid-spring-boot-starter-1.1.10.jar, file:/D:/DevelopmentTools/Maven_respository/com/alibaba/druid/1.1.10/druid-1.1.10.jar, file:/D:/DevelopmentTools/Maven_respository/org/apache/shiro/shiro-spring/1.4.0/shiro-spring-1.4.0.jar, file:/D:/DevelopmentTools/Maven_respository/org/apache/shiro/shiro-core/1.4.0/shiro-core-1.4.0.jar, file:/D:/DevelopmentTools/Maven_respository/org/apache/shiro/shiro-lang/1.4.0/shiro-lang-1.4.0.jar, file:/D:/DevelopmentTools/Maven_respository/org/apache/shiro/shiro-cache/1.4.0/shiro-cache-1.4.0.jar, file:/D:/DevelopmentTools/Maven_respository/org/apache/shiro/shiro-crypto-hash/1.4.0/shiro-crypto-hash-1.4.0.jar, file:/D:/DevelopmentTools/Maven_respository/org/apache/shiro/shiro-crypto-core/1.4.0/shiro-crypto-core-1.4.0.jar, file:/D:/DevelopmentTools/Maven_respository/org/apache/shiro/shiro-crypto-cipher/1.4.0/shiro-crypto-cipher-1.4.0.jar, file:/D:/DevelopmentTools/Maven_respository/org/apache/shiro/shiro-config-core/1.4.0/shiro-config-core-1.4.0.jar, file:/D:/DevelopmentTools/Maven_respository/org/apache/shiro/shiro-config-ogdl/1.4.0/shiro-config-ogdl-1.4.0.jar, file:/D:/DevelopmentTools/Maven_respository/commons-beanutils/commons-beanutils/1.9.3/commons-beanutils-1.9.3.jar, file:/D:/DevelopmentTools/Maven_respository/commons-collections/commons-collections/3.2.2/commons-collections-3.2.2.jar, file:/D:/DevelopmentTools/Maven_respository/org/apache/shiro/shiro-event/1.4.0/shiro-event-1.4.0.jar, file:/D:/DevelopmentTools/Maven_respository/org/apache/shiro/shiro-web/1.4.0/shiro-web-1.4.0.jar, file:/D:/DevelopmentTools/Maven_respository/com/auth0/java-jwt/3.18.2/java-jwt-3.18.2.jar, file:/D:/DevelopmentTools/Maven_respository/com/fasterxml/jackson/core/jackson-databind/2.10.0/jackson-databind-2.10.0.jar, file:/D:/DevelopmentTools/Maven_respository/com/fasterxml/jackson/core/jackson-core/2.10.0/jackson-core-2.10.0.jar, file:/D:/DevelopmentTools/Maven_respository/org/springframework/spring-context/5.2.23.RELEASE/spring-context-5.2.23.RELEASE.jar, file:/D:/DevelopmentTools/Maven_respository/org/springframework/spring-aop/5.2.1.RELEASE/spring-aop-5.2.1.RELEASE.jar, file:/D:/DevelopmentTools/Maven_respository/org/springframework/spring-beans/5.2.1.RELEASE/spring-beans-5.2.1.RELEASE.jar, file:/D:/DevelopmentTools/Maven_respository/org/springframework/spring-expression/5.2.1.RELEASE/spring-expression-5.2.1.RELEASE.jar, file:/D:/DevelopmentTools/Maven_respository/org/mybatis/mybatis/3.5.7/mybatis-3.5.7.jar, file:/D:/DevelopmentTools/Maven_respository/com/github/ben-manes/caffeine/caffeine/2.8.0/caffeine-2.8.0.jar, file:/D:/DevelopmentTools/Maven_respository/com/github/xiaoymin/swagger-bootstrap-ui/1.9.3/swagger-bootstrap-ui-1.9.3.jar, file:/D:/DevelopmentTools/IntelliJ%20IDEA%202025/lib/idea_rt.jar, file:/C:/Users/<USER>/AppData/Local/JetBrains/IntelliJIdea2025.1/captureAgent/debugger-agent.jar]
2025-04-29 16:13:40.061 [main] INFO  com.qs.admin.QscAdminApplication - Starting QscAdminApplication on MSI with PID 34664 (started by KarlKyo in D:\project\Java Projectes\qsadmin)
2025-04-29 16:13:40.062 [main] DEBUG com.qs.admin.QscAdminApplication - Running with Spring Boot v2.2.1.RELEASE, Spring v5.2.23.RELEASE
2025-04-29 16:13:40.062 [main] INFO  com.qs.admin.QscAdminApplication - The following profiles are active: dev
2025-04-29 16:13:40.062 [main] DEBUG o.s.boot.SpringApplication - Loading source class com.qs.admin.QscAdminApplication
2025-04-29 16:13:40.093 [main] DEBUG o.s.b.c.c.ConfigFileApplicationListener - Activated activeProfiles dev
2025-04-29 16:13:40.093 [main] DEBUG o.s.b.c.c.ConfigFileApplicationListener - Loaded config file 'file:/D:/project/Java%20Projectes/qsadmin/target/classes/application.yml' (classpath:/application.yml)
2025-04-29 16:13:40.093 [main] DEBUG o.s.b.c.c.ConfigFileApplicationListener - Loaded config file 'file:/D:/project/Java%20Projectes/qsadmin/target/classes/application-dev.yml' (classpath:/application-dev.yml) for profile dev
2025-04-29 16:13:40.094 [main] DEBUG o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Refreshing org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@8d6290
2025-04-29 16:13:40.109 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.context.annotation.internalConfigurationAnnotationProcessor'
2025-04-29 16:13:40.122 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.internalCachingMetadataReaderFactory'
2025-04-29 16:13:40.172 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\common\aspect\IdempotencyAspect.class]
2025-04-29 16:13:40.173 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\common\configurer\CorsConfig.class]
2025-04-29 16:13:40.177 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\common\configurer\DataSourceConfig.class]
2025-04-29 16:13:40.177 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\common\configurer\MyWebMvcConfig.class]
2025-04-29 16:13:40.178 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\common\configurer\ShiroConfig.class]
2025-04-29 16:13:40.178 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\common\configurer\Swagger2Configurer.class]
2025-04-29 16:13:40.180 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\common\core\MybatisPlusConfig.class]
2025-04-29 16:13:40.186 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\common\exception\GlobalExceptionHandler.class]
2025-04-29 16:13:40.187 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\common\service\impl\SyncCacheServiceImpl.class]
2025-04-29 16:13:40.188 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\common\shiro\ShiroFilterProperties.class]
2025-04-29 16:13:40.188 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\common\shiro\ShiroRealm.class]
2025-04-29 16:13:40.188 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\common\shiro\cache\ShiroCacheManager.class]
2025-04-29 16:13:40.190 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\common\shiro\security\JwtProperties.class]
2025-04-29 16:13:40.190 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\common\shiro\security\JwtUtil.class]
2025-04-29 16:13:40.191 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\common\utils\LocalCacheManager.class]
2025-04-29 16:13:40.192 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\common\utils\SpringContextUtil.class]
2025-04-29 16:13:40.192 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\config\BaiShuiProperties.class]
2025-04-29 16:13:40.192 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\config\RestTemplateConfig.class]
2025-04-29 16:13:40.197 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\controller\AgentInfoController.class]
2025-04-29 16:13:40.209 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\controller\AutocodeController.class]
2025-04-29 16:13:40.211 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\controller\BusinessController.class]
2025-04-29 16:13:40.212 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\controller\ClientInfoController.class]
2025-04-29 16:13:40.212 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\controller\EmployeeController.class]
2025-04-29 16:13:40.212 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\controller\NoticeController.class]
2025-04-29 16:13:40.213 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\controller\SystemController.class]
2025-04-29 16:13:40.214 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\controller\SystemLogController.class]
2025-04-29 16:13:40.214 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\controller\ThingsController.class]
2025-04-29 16:13:40.214 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\controller\TicketBookController.class]
2025-04-29 16:13:40.215 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\controller\TicketController.class]
2025-04-29 16:13:40.215 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\controller\TicketExchangeController.class]
2025-04-29 16:13:40.215 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\controller\TicketLogController.class]
2025-04-29 16:13:40.216 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\controller\TicketVerifyController.class]
2025-04-29 16:13:40.216 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\controller\WindowBusinessController.class]
2025-04-29 16:13:40.216 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\controller\WindowController.class]
2025-04-29 16:13:40.217 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\controller\WindowStatusController.class]
2025-04-29 16:13:40.237 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\service\impl\AgentInfoEnterpriseServiceImpl.class]
2025-04-29 16:13:40.237 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\service\impl\AgentInfoServiceImpl.class]
2025-04-29 16:13:40.238 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\service\impl\AutocodeServiceImpl.class]
2025-04-29 16:13:40.239 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\service\impl\BaiShuiApiServiceImpl.class]
2025-04-29 16:13:40.239 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\service\impl\BusinessServiceImpl.class]
2025-04-29 16:13:40.239 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\service\impl\ClientInfoServiceImpl.class]
2025-04-29 16:13:40.240 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\service\impl\EmployeeServiceImpl.class]
2025-04-29 16:13:40.240 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\service\impl\NoticeServiceImpl.class]
2025-04-29 16:13:40.240 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\service\impl\SystemLogServiceImpl.class]
2025-04-29 16:13:40.250 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\service\impl\SystemServiceImpl.class]
2025-04-29 16:13:40.251 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\service\impl\ThingsServiceImpl.class]
2025-04-29 16:13:40.251 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\service\impl\TicketBookServiceImpl.class]
2025-04-29 16:13:40.251 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\service\impl\TicketExchangeServiceImpl.class]
2025-04-29 16:13:40.251 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\service\impl\TicketLogServiceImpl.class]
2025-04-29 16:13:40.251 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\service\impl\TicketServiceImpl.class]
2025-04-29 16:13:40.252 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\service\impl\TicketVerifyServiceImpl.class]
2025-04-29 16:13:40.252 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\service\impl\WindowBusinessServiceImpl.class]
2025-04-29 16:13:40.252 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\service\impl\WindowServiceImpl.class]
2025-04-29 16:13:40.252 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\service\impl\WindowStatusServiceImpl.class]
2025-04-29 16:13:40.253 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\task\BaiShuiTokenSyncTask.class]
2025-04-29 16:13:40.280 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-swagger2/2.9.2/springfox-swagger2-2.9.2.jar!/springfox/documentation/swagger2/mappers/ServiceModelToSwagger2MapperImpl.class]
2025-04-29 16:13:40.281 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-swagger2/2.9.2/springfox-swagger2-2.9.2.jar!/springfox/documentation/swagger2/mappers/VendorExtensionsMapperImpl.class]
2025-04-29 16:13:40.283 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-swagger2/2.9.2/springfox-swagger2-2.9.2.jar!/springfox/documentation/swagger2/mappers/ParameterMapperImpl.class]
2025-04-29 16:13:40.284 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-swagger2/2.9.2/springfox-swagger2-2.9.2.jar!/springfox/documentation/swagger2/mappers/ModelMapperImpl.class]
2025-04-29 16:13:40.285 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-swagger2/2.9.2/springfox-swagger2-2.9.2.jar!/springfox/documentation/swagger2/mappers/LicenseMapperImpl.class]
2025-04-29 16:13:40.286 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-swagger2/2.9.2/springfox-swagger2-2.9.2.jar!/springfox/documentation/swagger2/mappers/SecurityMapperImpl.class]
2025-04-29 16:13:40.296 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/scanners/ApiListingReferenceScanner.class]
2025-04-29 16:13:40.298 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/scanners/ApiDocumentationScanner.class]
2025-04-29 16:13:40.299 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/scanners/ApiDescriptionReader.class]
2025-04-29 16:13:40.299 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/scanners/ApiListingReader.class]
2025-04-29 16:13:40.300 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/scanners/CachingOperationReader.class]
2025-04-29 16:13:40.300 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/scanners/MediaTypeReader.class]
2025-04-29 16:13:40.300 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/scanners/ApiListingScanner.class]
2025-04-29 16:13:40.301 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/scanners/ApiModelReader.class]
2025-04-29 16:13:40.303 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/scanners/ApiDescriptionLookup.class]
2025-04-29 16:13:40.304 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/readers/operation/OperationModelsProvider.class]
2025-04-29 16:13:40.304 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/readers/operation/OperationDeprecatedReader.class]
2025-04-29 16:13:40.304 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/readers/operation/ResponseMessagesReader.class]
2025-04-29 16:13:40.305 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/readers/operation/OperationParameterReader.class]
2025-04-29 16:13:40.305 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/readers/operation/DefaultTagsProvider.class]
2025-04-29 16:13:40.306 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/readers/operation/OperationTagsReader.class]
2025-04-29 16:13:40.306 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/readers/operation/ApiOperationReader.class]
2025-04-29 16:13:40.307 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/readers/operation/DefaultOperationReader.class]
2025-04-29 16:13:40.307 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/readers/operation/OperationParameterRequestConditionReader.class]
2025-04-29 16:13:40.307 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/readers/operation/OperationParameterHeadersConditionReader.class]
2025-04-29 16:13:40.308 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/readers/operation/OperationResponseClassReader.class]
2025-04-29 16:13:40.308 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/readers/operation/CachingOperationNameGenerator.class]
2025-04-29 16:13:40.310 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/readers/parameter/ParameterMultiplesReader.class]
2025-04-29 16:13:40.311 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/readers/parameter/ModelAttributeParameterExpander.class]
2025-04-29 16:13:40.311 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/readers/parameter/ParameterTypeReader.class]
2025-04-29 16:13:40.311 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/readers/parameter/ParameterRequiredReader.class]
2025-04-29 16:13:40.313 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/readers/parameter/ParameterDataTypeReader.class]
2025-04-29 16:13:40.313 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/readers/parameter/ParameterDefaultReader.class]
2025-04-29 16:13:40.316 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/readers/parameter/ParameterNameReader.class]
2025-04-29 16:13:40.316 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/readers/parameter/ExpandedParameterBuilder.class]
2025-04-29 16:13:40.319 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/plugins/DocumentationPluginsBootstrapper.class]
2025-04-29 16:13:40.320 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/plugins/WebMvcRequestHandlerProvider.class]
2025-04-29 16:13:40.320 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/plugins/DocumentationPluginsManager.class]
2025-04-29 16:13:40.321 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/paths/QueryStringUriTemplateDecorator.class]
2025-04-29 16:13:40.321 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/paths/PathMappingDecorator.class]
2025-04-29 16:13:40.322 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/paths/PathSanitizer.class]
2025-04-29 16:13:40.322 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/paths/OperationPathDecorator.class]
2025-04-29 16:13:40.340 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-schema/2.9.2/springfox-schema-2.9.2.jar!/springfox/documentation/schema/CachingModelDependencyProvider.class]
2025-04-29 16:13:40.341 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-schema/2.9.2/springfox-schema-2.9.2.jar!/springfox/documentation/schema/TypeNameExtractor.class]
2025-04-29 16:13:40.341 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-schema/2.9.2/springfox-schema-2.9.2.jar!/springfox/documentation/schema/plugins/PropertyDiscriminatorBasedInheritancePlugin.class]
2025-04-29 16:13:40.343 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-schema/2.9.2/springfox-schema-2.9.2.jar!/springfox/documentation/schema/plugins/XmlModelPlugin.class]
2025-04-29 16:13:40.343 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-schema/2.9.2/springfox-schema-2.9.2.jar!/springfox/documentation/schema/plugins/SchemaPluginsManager.class]
2025-04-29 16:13:40.344 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-schema/2.9.2/springfox-schema-2.9.2.jar!/springfox/documentation/schema/property/CachingModelPropertiesProvider.class]
2025-04-29 16:13:40.344 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-schema/2.9.2/springfox-schema-2.9.2.jar!/springfox/documentation/schema/property/ObjectMapperBeanPropertyNamingStrategy.class]
2025-04-29 16:13:40.345 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-schema/2.9.2/springfox-schema-2.9.2.jar!/springfox/documentation/schema/property/bean/AccessorsProvider.class]
2025-04-29 16:13:40.345 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-schema/2.9.2/springfox-schema-2.9.2.jar!/springfox/documentation/schema/property/field/FieldProvider.class]
2025-04-29 16:13:40.346 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-schema/2.9.2/springfox-schema-2.9.2.jar!/springfox/documentation/schema/property/XmlPropertyPlugin.class]
2025-04-29 16:13:40.346 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-schema/2.9.2/springfox-schema-2.9.2.jar!/springfox/documentation/schema/property/OptimizedModelPropertiesProvider.class]
2025-04-29 16:13:40.347 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-schema/2.9.2/springfox-schema-2.9.2.jar!/springfox/documentation/schema/property/FactoryMethodProvider.class]
2025-04-29 16:13:40.348 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-schema/2.9.2/springfox-schema-2.9.2.jar!/springfox/documentation/schema/CachingModelProvider.class]
2025-04-29 16:13:40.348 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-schema/2.9.2/springfox-schema-2.9.2.jar!/springfox/documentation/schema/DefaultModelDependencyProvider.class]
2025-04-29 16:13:40.348 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-schema/2.9.2/springfox-schema-2.9.2.jar!/springfox/documentation/schema/JacksonEnumTypeDeterminer.class]
2025-04-29 16:13:40.349 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-schema/2.9.2/springfox-schema-2.9.2.jar!/springfox/documentation/schema/DefaultModelProvider.class]
2025-04-29 16:13:40.359 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-swagger-common/2.9.2/springfox-swagger-common-2.9.2.jar!/springfox/documentation/swagger/schema/ApiModelPropertyPropertyBuilder.class]
2025-04-29 16:13:40.359 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-swagger-common/2.9.2/springfox-swagger-common-2.9.2.jar!/springfox/documentation/swagger/schema/ApiModelTypeNameProvider.class]
2025-04-29 16:13:40.359 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-swagger-common/2.9.2/springfox-swagger-common-2.9.2.jar!/springfox/documentation/swagger/schema/ApiModelBuilder.class]
2025-04-29 16:13:40.361 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-swagger-common/2.9.2/springfox-swagger-common-2.9.2.jar!/springfox/documentation/swagger/readers/operation/OperationImplicitParameterReader.class]
2025-04-29 16:13:40.361 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-swagger-common/2.9.2/springfox-swagger-common-2.9.2.jar!/springfox/documentation/swagger/readers/operation/VendorExtensionsReader.class]
2025-04-29 16:13:40.362 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-swagger-common/2.9.2/springfox-swagger-common-2.9.2.jar!/springfox/documentation/swagger/readers/operation/SwaggerOperationResponseClassReader.class]
2025-04-29 16:13:40.362 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-swagger-common/2.9.2/springfox-swagger-common-2.9.2.jar!/springfox/documentation/swagger/readers/operation/SwaggerOperationModelsProvider.class]
2025-04-29 16:13:40.362 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-swagger-common/2.9.2/springfox-swagger-common-2.9.2.jar!/springfox/documentation/swagger/readers/operation/SwaggerMediaTypeReader.class]
2025-04-29 16:13:40.363 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-swagger-common/2.9.2/springfox-swagger-common-2.9.2.jar!/springfox/documentation/swagger/readers/operation/OperationHttpMethodReader.class]
2025-04-29 16:13:40.364 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-swagger-common/2.9.2/springfox-swagger-common-2.9.2.jar!/springfox/documentation/swagger/readers/operation/OperationImplicitParametersReader.class]
2025-04-29 16:13:40.364 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-swagger-common/2.9.2/springfox-swagger-common-2.9.2.jar!/springfox/documentation/swagger/readers/operation/OperationAuthReader.class]
2025-04-29 16:13:40.364 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-swagger-common/2.9.2/springfox-swagger-common-2.9.2.jar!/springfox/documentation/swagger/readers/operation/OperationHiddenReader.class]
2025-04-29 16:13:40.364 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-swagger-common/2.9.2/springfox-swagger-common-2.9.2.jar!/springfox/documentation/swagger/readers/operation/OperationSummaryReader.class]
2025-04-29 16:13:40.364 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-swagger-common/2.9.2/springfox-swagger-common-2.9.2.jar!/springfox/documentation/swagger/readers/operation/SwaggerResponseMessageReader.class]
2025-04-29 16:13:40.365 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-swagger-common/2.9.2/springfox-swagger-common-2.9.2.jar!/springfox/documentation/swagger/readers/operation/OperationNicknameIntoUniqueIdReader.class]
2025-04-29 16:13:40.365 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-swagger-common/2.9.2/springfox-swagger-common-2.9.2.jar!/springfox/documentation/swagger/readers/operation/OperationPositionReader.class]
2025-04-29 16:13:40.365 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-swagger-common/2.9.2/springfox-swagger-common-2.9.2.jar!/springfox/documentation/swagger/readers/operation/OperationNotesReader.class]
2025-04-29 16:13:40.365 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-swagger-common/2.9.2/springfox-swagger-common-2.9.2.jar!/springfox/documentation/swagger/readers/operation/SwaggerOperationTagsReader.class]
2025-04-29 16:13:40.365 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-swagger-common/2.9.2/springfox-swagger-common-2.9.2.jar!/springfox/documentation/swagger/readers/parameter/ApiParamParameterBuilder.class]
2025-04-29 16:13:40.366 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-swagger-common/2.9.2/springfox-swagger-common-2.9.2.jar!/springfox/documentation/swagger/readers/parameter/SwaggerExpandedParameterBuilder.class]
2025-04-29 16:13:40.368 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-swagger-common/2.9.2/springfox-swagger-common-2.9.2.jar!/springfox/documentation/swagger/web/SwaggerApiListingReader.class]
2025-04-29 16:13:40.371 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-swagger-common/2.9.2/springfox-swagger-common-2.9.2.jar!/springfox/documentation/swagger/web/ClassOrApiAnnotationResourceGrouping.class]
2025-04-29 16:13:40.371 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-swagger-common/2.9.2/springfox-swagger-common-2.9.2.jar!/springfox/documentation/swagger/web/InMemorySwaggerResourcesProvider.class]
2025-04-29 16:13:40.373 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-swagger-common/2.9.2/springfox-swagger-common-2.9.2.jar!/springfox/documentation/swagger/web/ApiResourceController.class]
2025-04-29 16:13:40.544 [main] DEBUG o.s.c.e.PropertySourcesPropertyResolver - Found key 'spring.jmx.enabled' in PropertySource 'configurationProperties' with value of type String
2025-04-29 16:13:40.558 [main] DEBUG o.s.c.e.PropertySourcesPropertyResolver - Found key 'spring.jmx.enabled' in PropertySource 'configurationProperties' with value of type String
2025-04-29 16:13:40.558 [main] DEBUG o.s.c.e.PropertySourcesPropertyResolver - Found key 'spring.application.admin.enabled' in PropertySource 'configurationProperties' with value of type String
2025-04-29 16:13:40.712 [main] DEBUG o.s.c.e.PropertySourcesPropertyResolver - Found key 'spring.jmx.enabled' in PropertySource 'configurationProperties' with value of type String
2025-04-29 16:13:40.715 [main] DEBUG o.s.c.e.PropertySourcesPropertyResolver - Found key 'spring.application.admin.enabled' in PropertySource 'configurationProperties' with value of type String
2025-04-29 16:13:40.760 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'com.qs.admin.common.configurer.DataSourceConfig#MapperScannerRegistrar#0'
2025-04-29 16:13:40.766 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'propertySourcesPlaceholderConfigurer'
2025-04-29 16:13:40.828 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.context.properties.ConfigurationPropertiesBeanDefinitionValidator'
2025-04-29 16:13:40.872 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.context.event.internalEventListenerProcessor'
2025-04-29 16:13:40.873 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'preserveErrorControllerTargetClassPostProcessor'
2025-04-29 16:13:40.873 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.context.event.internalEventListenerFactory'
2025-04-29 16:13:40.873 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.transaction.config.internalTransactionalEventListenerFactory'
2025-04-29 16:13:40.875 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.context.annotation.internalAutowiredAnnotationProcessor'
2025-04-29 16:13:40.875 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.context.annotation.internalCommonAnnotationProcessor'
2025-04-29 16:13:40.877 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'lifecycleBeanPostProcessor'
2025-04-29 16:13:40.877 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'shiroConfig'
2025-04-29 16:13:40.877 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'shiroConfig' of type [com.qs.admin.common.configurer.ShiroConfig$$EnhancerBySpringCGLIB$$713c0ed5] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-04-29 16:13:40.883 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.context.properties.ConfigurationPropertiesBindingPostProcessor'
2025-04-29 16:13:40.883 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.context.internalConfigurationPropertiesBinder'
2025-04-29 16:13:40.883 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.context.internalConfigurationPropertiesBinderFactory'
2025-04-29 16:13:40.884 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'getLifecycleBeanPostProcessor'
2025-04-29 16:13:40.889 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.context.annotation.internalScheduledAnnotationProcessor'
2025-04-29 16:13:40.889 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.scheduling.annotation.SchedulingConfiguration'
2025-04-29 16:13:40.891 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'methodValidationPostProcessor'
2025-04-29 16:13:40.906 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'securityManager'
2025-04-29 16:13:40.906 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'shiroRealm'
2025-04-29 16:13:40.912 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'jwtUtil'
2025-04-29 16:13:40.912 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'jwtProperties'
2025-04-29 16:13:40.920 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'jwtProperties' of type [com.qs.admin.common.shiro.security.JwtProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-04-29 16:13:40.920 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'jwtUtil' of type [com.qs.admin.common.shiro.security.JwtUtil] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-04-29 16:13:40.920 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'shiroRealm' of type [com.qs.admin.common.shiro.ShiroRealm] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-04-29 16:13:40.921 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'shiroCacheManager'
2025-04-29 16:13:40.921 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'localCacheManager'
2025-04-29 16:13:40.939 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'localCacheManager' of type [com.qs.admin.common.utils.LocalCacheManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-04-29 16:13:40.940 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'shiroCacheManager' of type [com.qs.admin.common.shiro.cache.ShiroCacheManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-04-29 16:13:40.940 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Autowiring by type from bean name 'securityManager' via factory method to bean named 'shiroRealm'
2025-04-29 16:13:40.940 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Autowiring by type from bean name 'securityManager' via factory method to bean named 'shiroCacheManager'
2025-04-29 16:13:41.147 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'securityManager' of type [org.apache.shiro.web.mgt.DefaultWebSecurityManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-04-29 16:13:41.148 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'syncCacheServiceImpl'
2025-04-29 16:13:41.149 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'syncCacheServiceImpl' of type [com.qs.admin.common.service.impl.SyncCacheServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-04-29 16:13:41.149 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Autowiring by type from bean name 'shiroFilter' via factory method to bean named 'securityManager'
2025-04-29 16:13:41.149 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Autowiring by type from bean name 'shiroFilter' via factory method to bean named 'localCacheManager'
2025-04-29 16:13:41.149 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Autowiring by type from bean name 'shiroFilter' via factory method to bean named 'jwtProperties'
2025-04-29 16:13:41.149 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Autowiring by type from bean name 'shiroFilter' via factory method to bean named 'syncCacheServiceImpl'
2025-04-29 16:13:41.153 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'getShiroFilterProperties'
2025-04-29 16:13:41.157 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'getShiroFilterProperties' of type [com.qs.admin.common.shiro.ShiroFilterProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-04-29 16:13:41.162 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Autowiring by type from bean name 'methodValidationPostProcessor' via factory method to bean named 'environment'
2025-04-29 16:13:41.164 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'dataSourceInitializerPostProcessor'
2025-04-29 16:13:41.164 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.aop.config.internalAutoProxyCreator'
2025-04-29 16:13:41.179 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'persistenceExceptionTranslationPostProcessor'
2025-04-29 16:13:41.180 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Autowiring by type from bean name 'persistenceExceptionTranslationPostProcessor' via factory method to bean named 'environment'
2025-04-29 16:13:41.181 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'shiroFilter'
2025-04-29 16:13:41.181 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'authorizationAttributeSourceAdvisor'
2025-04-29 16:13:41.182 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Autowiring by type from bean name 'authorizationAttributeSourceAdvisor' via factory method to bean named 'securityManager'
2025-04-29 16:13:41.186 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'authorizationAttributeSourceAdvisor' of type [org.apache.shiro.spring.security.interceptor.AuthorizationAttributeSourceAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-04-29 16:13:41.189 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'dynamicDatasourceAnnotationAdvisor'
2025-04-29 16:13:41.189 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration'
2025-04-29 16:13:41.189 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.transaction.config.internalTransactionAdvisor'
2025-04-29 16:13:41.189 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration'
2025-04-29 16:13:41.197 [main] DEBUG o.s.a.a.a.ReflectiveAspectJAdvisorFactory - Found AspectJ method: public java.lang.Object com.qs.admin.common.aspect.IdempotencyAspect.around(org.aspectj.lang.ProceedingJoinPoint,org.springframework.web.bind.annotation.PostMapping) throws java.lang.Throwable
2025-04-29 16:13:41.209 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-04-29 16:13:41.251 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'transactionAttributeSource'
2025-04-29 16:13:41.256 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'transactionInterceptor'
2025-04-29 16:13:41.256 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Autowiring by type from bean name 'transactionInterceptor' via factory method to bean named 'transactionAttributeSource'
2025-04-29 16:13:41.259 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Autowiring by type from bean name 'org.springframework.transaction.config.internalTransactionAdvisor' via factory method to bean named 'transactionAttributeSource'
2025-04-29 16:13:41.259 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Autowiring by type from bean name 'org.springframework.transaction.config.internalTransactionAdvisor' via factory method to bean named 'transactionInterceptor'
2025-04-29 16:13:41.261 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'spring.datasource.dynamic-com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties'
2025-04-29 16:13:41.264 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.datasource.dynamic-com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-04-29 16:13:41.266 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration$$EnhancerBySpringCGLIB$$dad47f6b] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-04-29 16:13:41.270 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'dsProcessor'
2025-04-29 16:13:41.274 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dsProcessor' of type [com.baomidou.dynamic.datasource.processor.DsHeaderProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-04-29 16:13:41.275 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Autowiring by type from bean name 'dynamicDatasourceAnnotationAdvisor' via factory method to bean named 'dsProcessor'
2025-04-29 16:13:41.278 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dynamicDatasourceAnnotationAdvisor' of type [com.baomidou.dynamic.datasource.aop.DynamicDataSourceAnnotationAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-04-29 16:13:41.280 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'objectMapperConfigurer'
2025-04-29 16:13:41.281 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'webServerFactoryCustomizerBeanPostProcessor'
2025-04-29 16:13:41.281 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'errorPageRegistrarBeanPostProcessor'
2025-04-29 16:13:41.283 [main] DEBUG o.s.u.c.s.UiApplicationContextUtils - Unable to locate ThemeSource with name 'themeSource': using default [org.springframework.ui.context.support.ResourceBundleThemeSource@1ae2ee5]
2025-04-29 16:13:41.283 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'undertowServletWebServerFactory'
2025-04-29 16:13:41.283 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.ServletWebServerFactoryConfiguration$EmbeddedUndertow'
2025-04-29 16:13:41.292 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'websocketServletWebServerCustomizer'
2025-04-29 16:13:41.292 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.websocket.servlet.WebSocketServletAutoConfiguration$UndertowWebSocketConfiguration'
2025-04-29 16:13:41.293 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'servletWebServerFactoryCustomizer'
2025-04-29 16:13:41.293 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.ServletWebServerFactoryAutoConfiguration'
2025-04-29 16:13:41.295 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'server-org.springframework.boot.autoconfigure.web.ServerProperties'
2025-04-29 16:13:41.301 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Autowiring by type from bean name 'servletWebServerFactoryCustomizer' via factory method to bean named 'server-org.springframework.boot.autoconfigure.web.ServerProperties'
2025-04-29 16:13:41.303 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'undertowWebServerFactoryCustomizer'
2025-04-29 16:13:41.303 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.embedded.EmbeddedWebServerFactoryCustomizerAutoConfiguration$UndertowWebServerFactoryCustomizerConfiguration'
2025-04-29 16:13:41.305 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Autowiring by type from bean name 'undertowWebServerFactoryCustomizer' via factory method to bean named 'environment'
2025-04-29 16:13:41.305 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Autowiring by type from bean name 'undertowWebServerFactoryCustomizer' via factory method to bean named 'server-org.springframework.boot.autoconfigure.web.ServerProperties'
2025-04-29 16:13:41.306 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'localeCharsetMappingsCustomizer'
2025-04-29 16:13:41.306 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.HttpEncodingAutoConfiguration'
2025-04-29 16:13:41.306 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'spring.http-org.springframework.boot.autoconfigure.http.HttpProperties'
2025-04-29 16:13:41.310 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.web.servlet.HttpEncodingAutoConfiguration' via constructor to bean named 'spring.http-org.springframework.boot.autoconfigure.http.HttpProperties'
2025-04-29 16:13:41.329 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'errorPageCustomizer'
2025-04-29 16:13:41.329 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.error.ErrorMvcAutoConfiguration'
2025-04-29 16:13:41.329 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.web.servlet.error.ErrorMvcAutoConfiguration' via constructor to bean named 'server-org.springframework.boot.autoconfigure.web.ServerProperties'
2025-04-29 16:13:41.331 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'dispatcherServletRegistration'
2025-04-29 16:13:41.331 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.DispatcherServletAutoConfiguration$DispatcherServletRegistrationConfiguration'
2025-04-29 16:13:41.332 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'dispatcherServlet'
2025-04-29 16:13:41.332 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.DispatcherServletAutoConfiguration$DispatcherServletConfiguration'
2025-04-29 16:13:41.333 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'spring.mvc-org.springframework.boot.autoconfigure.web.servlet.WebMvcProperties'
2025-04-29 16:13:41.336 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Autowiring by type from bean name 'dispatcherServlet' via factory method to bean named 'spring.http-org.springframework.boot.autoconfigure.http.HttpProperties'
2025-04-29 16:13:41.336 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Autowiring by type from bean name 'dispatcherServlet' via factory method to bean named 'spring.mvc-org.springframework.boot.autoconfigure.web.servlet.WebMvcProperties'
2025-04-29 16:13:41.354 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Autowiring by type from bean name 'dispatcherServletRegistration' via factory method to bean named 'dispatcherServlet'
2025-04-29 16:13:41.354 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Autowiring by type from bean name 'dispatcherServletRegistration' via factory method to bean named 'spring.mvc-org.springframework.boot.autoconfigure.web.servlet.WebMvcProperties'
2025-04-29 16:13:41.358 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'multipartConfigElement'
2025-04-29 16:13:41.358 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.MultipartAutoConfiguration'
2025-04-29 16:13:41.358 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'spring.servlet.multipart-org.springframework.boot.autoconfigure.web.servlet.MultipartProperties'
2025-04-29 16:13:41.361 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.web.servlet.MultipartAutoConfiguration' via constructor to bean named 'spring.servlet.multipart-org.springframework.boot.autoconfigure.web.servlet.MultipartProperties'
2025-04-29 16:13:41.368 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Autowiring by type from bean name 'errorPageCustomizer' via factory method to bean named 'dispatcherServletRegistration'
2025-04-29 16:13:41.384 [main] DEBUG o.s.b.w.e.u.UndertowServletWebServerFactory - Code archive: D:\DevelopmentTools\Maven_respository\org\springframework\boot\spring-boot\2.2.1.RELEASE\spring-boot-2.2.1.RELEASE.jar
2025-04-29 16:13:41.384 [main] DEBUG o.s.b.w.e.u.UndertowServletWebServerFactory - Code archive: D:\DevelopmentTools\Maven_respository\org\springframework\boot\spring-boot\2.2.1.RELEASE\spring-boot-2.2.1.RELEASE.jar
2025-04-29 16:13:41.385 [main] DEBUG o.s.b.w.e.u.UndertowServletWebServerFactory - None of the document roots [src/main/webapp, public, static] point to a directory and will be ignored.
2025-04-29 16:13:41.415 [main] WARN  io.undertow.websockets.jsr - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
2025-04-29 16:13:41.429 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-04-29 16:13:41.429 [main] DEBUG o.s.web.context.ContextLoader - Published root WebApplicationContext as ServletContext attribute with name [org.springframework.web.context.WebApplicationContext.ROOT]
2025-04-29 16:13:41.429 [main] INFO  o.s.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 1335 ms
2025-04-29 16:13:41.431 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'filterRegistrationBean'
2025-04-29 16:13:41.436 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'statViewServletRegistrationBean'
2025-04-29 16:13:41.436 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'com.alibaba.druid.spring.boot.autoconfigure.stat.DruidStatViewServletConfiguration'
2025-04-29 16:13:41.437 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'spring.datasource.druid-com.alibaba.druid.spring.boot.autoconfigure.properties.DruidStatProperties'
2025-04-29 16:13:41.442 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Autowiring by type from bean name 'statViewServletRegistrationBean' via factory method to bean named 'spring.datasource.druid-com.alibaba.druid.spring.boot.autoconfigure.properties.DruidStatProperties'
2025-04-29 16:13:41.449 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'webStatFilterRegistrationBean'
2025-04-29 16:13:41.449 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'com.alibaba.druid.spring.boot.autoconfigure.stat.DruidWebStatFilterConfiguration'
2025-04-29 16:13:41.451 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Autowiring by type from bean name 'webStatFilterRegistrationBean' via factory method to bean named 'spring.datasource.druid-com.alibaba.druid.spring.boot.autoconfigure.properties.DruidStatProperties'
2025-04-29 16:13:41.456 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'corsFilter'
2025-04-29 16:13:41.456 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'corsConfig'
2025-04-29 16:13:41.473 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'requestContextFilter'
2025-04-29 16:13:41.478 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'formContentFilter'
2025-04-29 16:13:41.478 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration'
2025-04-29 16:13:41.484 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'characterEncodingFilter'
2025-04-29 16:13:41.494 [main] DEBUG o.s.b.w.s.ServletContextInitializerBeans - Mapping filters: filterRegistrationBean urls=[/*] order=2147483647, filterRegistrationBean urls=[/*] order=2147483647, characterEncodingFilter urls=[/*] order=-2147483648, formContentFilter urls=[/*] order=-9900, requestContextFilter urls=[/*] order=-105, corsFilter urls=[/*] order=2147483647, shiroFilter urls=[/*] order=2147483647
2025-04-29 16:13:41.494 [main] DEBUG o.s.b.w.s.ServletContextInitializerBeans - Mapping servlets: dispatcherServlet urls=[/], statViewServlet urls=[/druid/*]
2025-04-29 16:13:41.519 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'qscAdminApplication'
2025-04-29 16:13:41.521 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'idempotencyAspect'
2025-04-29 16:13:41.521 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'dataSourceConfig'
2025-04-29 16:13:41.522 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'myWebMvcConfig'
2025-04-29 16:13:41.526 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'swagger2Configurer'
2025-04-29 16:13:41.528 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'mybatisPlusConfig'
2025-04-29 16:13:41.530 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'dataSource'
2025-04-29 16:13:41.573 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jdbc.DataSourceInitializerInvoker'
2025-04-29 16:13:41.574 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'spring.datasource-org.springframework.boot.autoconfigure.jdbc.DataSourceProperties'
2025-04-29 16:13:41.580 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.jdbc.DataSourceInitializerInvoker' via constructor to bean named 'spring.datasource-org.springframework.boot.autoconfigure.jdbc.DataSourceProperties'
2025-04-29 16:13:41.580 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.jdbc.DataSourceInitializerInvoker' via constructor to bean named 'org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@8d6290'
2025-04-29 16:13:41.591 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'mybatis-org.mybatis.spring.boot.autoconfigure.MybatisProperties'
2025-04-29 16:13:41.594 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'mybatisPlusInterceptor'
2025-04-29 16:13:41.601 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'globalExceptionHandler'
2025-04-29 16:13:41.603 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'shiroFilterProperties'
2025-04-29 16:13:41.606 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'springContextUtil'
2025-04-29 16:13:41.607 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'baiShuiProperties'
2025-04-29 16:13:41.608 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'restTemplateConfig'
2025-04-29 16:13:41.608 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'agentInfoController'
2025-04-29 16:13:41.610 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'agentInfoServiceImpl'
2025-04-29 16:13:41.621 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'agentInfoMapper'
2025-04-29 16:13:41.626 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'mybatisSqlSessionFactoryBean'
2025-04-29 16:13:41.988 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'agentInfoEnterpriseMapper'
2025-04-29 16:13:42.010 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'agentInfoEnterpriseServiceImpl'
2025-04-29 16:13:42.048 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'ticketVerifyServiceImpl'
2025-04-29 16:13:42.049 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'ticketVerifyMapper'
2025-04-29 16:13:42.140 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'baiShuiApiServiceImpl'
2025-04-29 16:13:42.141 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'restTemplate'
2025-04-29 16:13:42.162 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'systemServiceImpl'
2025-04-29 16:13:42.163 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'systemMapper'
2025-04-29 16:13:42.198 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'baiShuiTokenSyncTask'
2025-04-29 16:13:44.821 [main] INFO  c.q.a.t.s.impl.BaiShuiApiServiceImpl - 获取佰税API访问令牌
2025-04-29 16:13:45.166 [main] INFO  c.q.a.t.s.impl.BaiShuiApiServiceImpl - tokenUrl=https://etax.zhejiang.chinatax.gov.cn/nsfw/apiService/oauth/token
2025-04-29 16:13:45.347 [main] DEBUG o.s.web.client.RestTemplate - HTTP POST https://etax.zhejiang.chinatax.gov.cn/nsfw/apiService/oauth/token
2025-04-29 16:13:45.375 [main] DEBUG o.s.web.client.RestTemplate - Accept=[application/json, application/*+json]
2025-04-29 16:13:45.380 [main] DEBUG o.s.web.client.RestTemplate - Writing [{appKey=[HZBSJHXT], appSecret=[7d427891ef38f4a7b247447f715c66cd]}] as "application/x-www-form-urlencoded"
2025-04-29 16:13:45.777 [main] DEBUG o.s.web.client.RestTemplate - Response 200 OK
2025-04-29 16:13:45.779 [main] DEBUG o.s.web.client.RestTemplate - Reading to [java.util.Map<?, ?>]
2025-04-29 16:13:46.970 [main] INFO  c.q.a.t.s.impl.BaiShuiApiServiceImpl - 成功获取佰税API访问令牌
2025-04-29 16:13:49.074 [main] DEBUG o.s.jdbc.datasource.DataSourceUtils - Fetching JDBC Connection from DataSource
2025-04-29 16:13:49.075 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-04-29 16:13:50.081 [main] WARN  c.m.s.jdbc.internals.TDS.Channel - TLSv1 was negotiated. Please update server and client to use TLSv1.2 at minimum.
2025-04-29 16:13:51.092 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-04-29 16:13:51.118 [main] DEBUG c.q.a.t.m.SystemMapper.selectList - ==>  Preparing: SELECT [KEY],[VALUE],[MEMO] FROM system WHERE ([KEY] = ?)
2025-04-29 16:13:51.263 [HikariPool-1 connection adder] WARN  c.m.s.jdbc.internals.TDS.Channel - TLSv1 was negotiated. Please update server and client to use TLSv1.2 at minimum.
2025-04-29 16:13:51.334 [HikariPool-1 connection adder] WARN  c.m.s.jdbc.internals.TDS.Channel - TLSv1 was negotiated. Please update server and client to use TLSv1.2 at minimum.
2025-04-29 16:13:51.404 [HikariPool-1 connection adder] WARN  c.m.s.jdbc.internals.TDS.Channel - TLSv1 was negotiated. Please update server and client to use TLSv1.2 at minimum.
2025-04-29 16:13:51.470 [HikariPool-1 connection adder] WARN  c.m.s.jdbc.internals.TDS.Channel - TLSv1 was negotiated. Please update server and client to use TLSv1.2 at minimum.
2025-04-29 16:13:51.497 [main] DEBUG c.q.a.t.m.SystemMapper.selectList - ==> Parameters: BS_API_APPKEY(String)
2025-04-29 16:13:51.545 [HikariPool-1 connection adder] WARN  c.m.s.jdbc.internals.TDS.Channel - TLSv1 was negotiated. Please update server and client to use TLSv1.2 at minimum.
2025-04-29 16:13:51.639 [HikariPool-1 connection adder] WARN  c.m.s.jdbc.internals.TDS.Channel - TLSv1 was negotiated. Please update server and client to use TLSv1.2 at minimum.
2025-04-29 16:13:51.711 [HikariPool-1 connection adder] WARN  c.m.s.jdbc.internals.TDS.Channel - TLSv1 was negotiated. Please update server and client to use TLSv1.2 at minimum.
2025-04-29 16:13:51.776 [HikariPool-1 connection adder] WARN  c.m.s.jdbc.internals.TDS.Channel - TLSv1 was negotiated. Please update server and client to use TLSv1.2 at minimum.
2025-04-29 16:13:51.783 [main] DEBUG c.q.a.t.m.SystemMapper.selectList - <==      Total: 1
2025-04-29 16:13:51.858 [HikariPool-1 connection adder] WARN  c.m.s.jdbc.internals.TDS.Channel - TLSv1 was negotiated. Please update server and client to use TLSv1.2 at minimum.
2025-04-29 16:13:54.335 [main] DEBUG o.s.jdbc.datasource.DataSourceUtils - Fetching JDBC Connection from DataSource
2025-04-29 16:13:54.348 [main] DEBUG c.q.a.t.mapper.SystemMapper.update - ==>  Preparing: UPDATE system SET [VALUE]=?, [MEMO]=? WHERE ([KEY] = ?)
2025-04-29 16:13:54.349 [main] DEBUG c.q.a.t.mapper.SystemMapper.update - ==> Parameters: HZBSJHXT(String), 佰税科技API APPKEY，自动同步于Tue Apr 29 16:13:53 CST 2025(String), BS_API_APPKEY(String)
2025-04-29 16:13:54.361 [main] DEBUG c.q.a.t.mapper.SystemMapper.update - <==    Updates: 1
2025-04-29 16:13:55.905 [main] INFO  c.q.a.t.task.BaiShuiTokenSyncTask - APPKEY已同步更新
2025-04-29 16:14:03.808 [main] DEBUG o.s.jdbc.datasource.DataSourceUtils - Fetching JDBC Connection from DataSource
2025-04-29 16:14:03.819 [main] DEBUG c.q.a.t.m.SystemMapper.selectList - ==>  Preparing: SELECT [KEY],[VALUE],[MEMO] FROM system WHERE ([KEY] = ?)
2025-04-29 16:14:03.819 [main] DEBUG c.q.a.t.m.SystemMapper.selectList - ==> Parameters: BS_API_TOKEN(String)
2025-04-29 16:26:15.310 [main] INFO  com.qs.admin.QscAdminApplication - Starting QscAdminApplication on MSI with PID 14888 (started by KarlKyo in D:\project\Java Projectes\qsadmin)
2025-04-29 16:26:15.312 [main] DEBUG com.qs.admin.QscAdminApplication - Running with Spring Boot v2.2.1.RELEASE, Spring v5.2.23.RELEASE
2025-04-29 16:26:15.312 [main] INFO  com.qs.admin.QscAdminApplication - The following profiles are active: prod
2025-04-29 16:26:17.999 [main] WARN  io.undertow.websockets.jsr - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
2025-04-29 16:26:18.019 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-04-29 16:26:21.589 [main] INFO  c.q.a.t.s.impl.BaiShuiApiServiceImpl - 获取佰税API访问令牌
2025-04-29 16:26:21.594 [main] INFO  c.q.a.t.s.impl.BaiShuiApiServiceImpl - tokenUrl=null
2025-04-29 16:26:21.602 [main] ERROR c.q.a.t.s.impl.BaiShuiApiServiceImpl - 获取佰税API访问令牌异常
java.lang.IllegalArgumentException: URI is not absolute
	at java.net.URI.toURL(URI.java:1088)
	at org.springframework.http.client.SimpleClientHttpRequestFactory.createRequest(SimpleClientHttpRequestFactory.java:145)
	at org.springframework.http.client.support.HttpAccessor.createRequest(HttpAccessor.java:124)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:738)
	at org.springframework.web.client.RestTemplate.execute(RestTemplate.java:677)
	at org.springframework.web.client.RestTemplate.postForObject(RestTemplate.java:421)
	at com.qs.admin.taxhall.service.impl.BaiShuiApiServiceImpl.getToken(BaiShuiApiServiceImpl.java:73)
	at com.qs.admin.taxhall.task.BaiShuiTokenSyncTask.syncAppKeyAndToken(BaiShuiTokenSyncTask.java:49)
	at com.qs.admin.taxhall.task.BaiShuiTokenSyncTask.initSync(BaiShuiTokenSyncTask.java:29)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleElement.invoke(InitDestroyAnnotationBeanPostProcessor.java:389)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeInitMethods(InitDestroyAnnotationBeanPostProcessor.java:333)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeInitialization(InitDestroyAnnotationBeanPostProcessor.java:157)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsBeforeInitialization(AbstractAutowireCapableBeanFactory.java:416)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1795)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:595)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:517)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:323)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:222)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:321)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1287)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1207)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:636)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:116)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:397)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1429)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:594)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:517)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:323)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:222)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:321)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:879)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:879)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:551)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:141)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:747)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:397)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:315)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1226)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1215)
	at com.qs.admin.QscAdminApplication.main(QscAdminApplication.java:19)
2025-04-29 16:26:21.602 [main] ERROR c.q.a.t.task.BaiShuiTokenSyncTask - 同步佰税API APPKEY和TOKEN异常（不影响项目运行）
java.lang.RuntimeException: 授权请求异常: URI is not absolute
	at com.qs.admin.taxhall.service.impl.BaiShuiApiServiceImpl.getToken(BaiShuiApiServiceImpl.java:85)
	at com.qs.admin.taxhall.task.BaiShuiTokenSyncTask.syncAppKeyAndToken(BaiShuiTokenSyncTask.java:49)
	at com.qs.admin.taxhall.task.BaiShuiTokenSyncTask.initSync(BaiShuiTokenSyncTask.java:29)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleElement.invoke(InitDestroyAnnotationBeanPostProcessor.java:389)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeInitMethods(InitDestroyAnnotationBeanPostProcessor.java:333)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeInitialization(InitDestroyAnnotationBeanPostProcessor.java:157)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsBeforeInitialization(AbstractAutowireCapableBeanFactory.java:416)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1795)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:595)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:517)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:323)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:222)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:321)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1287)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1207)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:636)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:116)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:397)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1429)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:594)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:517)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:323)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:222)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:321)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:879)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:879)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:551)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:141)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:747)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:397)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:315)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1226)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1215)
	at com.qs.admin.QscAdminApplication.main(QscAdminApplication.java:19)
Caused by: java.lang.IllegalArgumentException: URI is not absolute
	at java.net.URI.toURL(URI.java:1088)
	at org.springframework.http.client.SimpleClientHttpRequestFactory.createRequest(SimpleClientHttpRequestFactory.java:145)
	at org.springframework.http.client.support.HttpAccessor.createRequest(HttpAccessor.java:124)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:738)
	at org.springframework.web.client.RestTemplate.execute(RestTemplate.java:677)
	at org.springframework.web.client.RestTemplate.postForObject(RestTemplate.java:421)
	at com.qs.admin.taxhall.service.impl.BaiShuiApiServiceImpl.getToken(BaiShuiApiServiceImpl.java:73)
	... 40 common frames omitted
2025-04-29 16:26:30.724 [main] INFO  com.qs.admin.QscAdminApplication - Starting QscAdminApplication on MSI with PID 20872 (started by KarlKyo in D:\project\Java Projectes\qsadmin)
2025-04-29 16:26:30.725 [main] DEBUG com.qs.admin.QscAdminApplication - Running with Spring Boot v2.2.1.RELEASE, Spring v5.2.23.RELEASE
2025-04-29 16:26:30.725 [main] INFO  com.qs.admin.QscAdminApplication - The following profiles are active: prod
2025-04-29 16:26:32.078 [main] WARN  io.undertow.websockets.jsr - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
2025-04-29 16:26:32.096 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-04-29 16:26:37.971 [main] INFO  c.q.a.t.s.impl.BaiShuiApiServiceImpl - 获取佰税API访问令牌
2025-04-29 16:26:50.564 [main] INFO  c.q.a.t.s.impl.BaiShuiApiServiceImpl - tokenUrl=http://80.12.140.31:8722/oauth/token
