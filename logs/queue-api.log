2025-06-07 16:02:01.531 [main] DEBUG o.s.b.c.l.ClasspathLoggingApplicationListener - Application started with classpath: [file:/D:/DevelopmentTools/Java/java-8u311/jre/lib/charsets.jar, file:/D:/DevelopmentTools/Java/java-8u311/jre/lib/deploy.jar, file:/D:/DevelopmentTools/Java/java-8u311/jre/lib/ext/access-bridge-32.jar, file:/D:/DevelopmentTools/Java/java-8u311/jre/lib/ext/cldrdata.jar, file:/D:/DevelopmentTools/Java/java-8u311/jre/lib/ext/dnsns.jar, file:/D:/DevelopmentTools/Java/java-8u311/jre/lib/ext/jaccess.jar, file:/D:/DevelopmentTools/Java/java-8u311/jre/lib/ext/jfxrt.jar, file:/D:/DevelopmentTools/Java/java-8u311/jre/lib/ext/localedata.jar, file:/D:/DevelopmentTools/Java/java-8u311/jre/lib/ext/nashorn.jar, file:/D:/DevelopmentTools/Java/java-8u311/jre/lib/ext/sunec.jar, file:/D:/DevelopmentTools/Java/java-8u311/jre/lib/ext/sunjce_provider.jar, file:/D:/DevelopmentTools/Java/java-8u311/jre/lib/ext/sunmscapi.jar, file:/D:/DevelopmentTools/Java/java-8u311/jre/lib/ext/sunpkcs11.jar, file:/D:/DevelopmentTools/Java/java-8u311/jre/lib/ext/zipfs.jar, file:/D:/DevelopmentTools/Java/java-8u311/jre/lib/javaws.jar, file:/D:/DevelopmentTools/Java/java-8u311/jre/lib/jce.jar, file:/D:/DevelopmentTools/Java/java-8u311/jre/lib/jfr.jar, file:/D:/DevelopmentTools/Java/java-8u311/jre/lib/jfxswt.jar, file:/D:/DevelopmentTools/Java/java-8u311/jre/lib/jsse.jar, file:/D:/DevelopmentTools/Java/java-8u311/jre/lib/management-agent.jar, file:/D:/DevelopmentTools/Java/java-8u311/jre/lib/plugin.jar, file:/D:/DevelopmentTools/Java/java-8u311/jre/lib/resources.jar, file:/D:/DevelopmentTools/Java/java-8u311/jre/lib/rt.jar, file:/D:/project/Java%20Projectes/qsadmin/target/classes/, file:/D:/DevelopmentTools/Maven_respository/com/microsoft/sqlserver/mssql-jdbc/12.6.1.jre8/mssql-jdbc-12.6.1.jre8.jar, file:/D:/DevelopmentTools/Maven_respository/org/springframework/boot/spring-boot-starter-web/2.2.1.RELEASE/spring-boot-starter-web-2.2.1.RELEASE.jar, file:/D:/DevelopmentTools/Maven_respository/org/springframework/boot/spring-boot-starter/2.2.1.RELEASE/spring-boot-starter-2.2.1.RELEASE.jar, file:/D:/DevelopmentTools/Maven_respository/org/springframework/boot/spring-boot/2.2.1.RELEASE/spring-boot-2.2.1.RELEASE.jar, file:/D:/DevelopmentTools/Maven_respository/org/springframework/boot/spring-boot-starter-logging/2.2.1.RELEASE/spring-boot-starter-logging-2.2.1.RELEASE.jar, file:/D:/DevelopmentTools/Maven_respository/ch/qos/logback/logback-classic/1.2.3/logback-classic-1.2.3.jar, file:/D:/DevelopmentTools/Maven_respository/ch/qos/logback/logback-core/1.2.3/logback-core-1.2.3.jar, file:/D:/DevelopmentTools/Maven_respository/org/apache/logging/log4j/log4j-to-slf4j/2.12.1/log4j-to-slf4j-2.12.1.jar, file:/D:/DevelopmentTools/Maven_respository/org/apache/logging/log4j/log4j-api/2.12.1/log4j-api-2.12.1.jar, file:/D:/DevelopmentTools/Maven_respository/org/slf4j/jul-to-slf4j/1.7.29/jul-to-slf4j-1.7.29.jar, file:/D:/DevelopmentTools/Maven_respository/jakarta/annotation/jakarta.annotation-api/1.3.5/jakarta.annotation-api-1.3.5.jar, file:/D:/DevelopmentTools/Maven_respository/org/yaml/snakeyaml/1.25/snakeyaml-1.25.jar, file:/D:/DevelopmentTools/Maven_respository/org/springframework/boot/spring-boot-starter-json/2.2.1.RELEASE/spring-boot-starter-json-2.2.1.RELEASE.jar, file:/D:/DevelopmentTools/Maven_respository/com/fasterxml/jackson/datatype/jackson-datatype-jdk8/2.10.0/jackson-datatype-jdk8-2.10.0.jar, file:/D:/DevelopmentTools/Maven_respository/com/fasterxml/jackson/datatype/jackson-datatype-jsr310/2.10.0/jackson-datatype-jsr310-2.10.0.jar, file:/D:/DevelopmentTools/Maven_respository/com/fasterxml/jackson/module/jackson-module-parameter-names/2.10.0/jackson-module-parameter-names-2.10.0.jar, file:/D:/DevelopmentTools/Maven_respository/org/springframework/boot/spring-boot-starter-validation/2.2.1.RELEASE/spring-boot-starter-validation-2.2.1.RELEASE.jar, file:/D:/DevelopmentTools/Maven_respository/jakarta/validation/jakarta.validation-api/2.0.1/jakarta.validation-api-2.0.1.jar, file:/D:/DevelopmentTools/Maven_respository/org/hibernate/validator/hibernate-validator/6.0.18.Final/hibernate-validator-6.0.18.Final.jar, file:/D:/DevelopmentTools/Maven_respository/org/springframework/spring-web/5.2.1.RELEASE/spring-web-5.2.1.RELEASE.jar, file:/D:/DevelopmentTools/Maven_respository/org/springframework/spring-webmvc/5.2.1.RELEASE/spring-webmvc-5.2.1.RELEASE.jar, file:/D:/DevelopmentTools/Maven_respository/org/springframework/boot/spring-boot-starter-undertow/2.2.1.RELEASE/spring-boot-starter-undertow-2.2.1.RELEASE.jar, file:/D:/DevelopmentTools/Maven_respository/io/undertow/undertow-core/2.0.27.Final/undertow-core-2.0.27.Final.jar, file:/D:/DevelopmentTools/Maven_respository/org/jboss/logging/jboss-logging/3.4.1.Final/jboss-logging-3.4.1.Final.jar, file:/D:/DevelopmentTools/Maven_respository/org/jboss/xnio/xnio-api/3.3.8.Final/xnio-api-3.3.8.Final.jar, file:/D:/DevelopmentTools/Maven_respository/org/jboss/xnio/xnio-nio/3.3.8.Final/xnio-nio-3.3.8.Final.jar, file:/D:/DevelopmentTools/Maven_respository/io/undertow/undertow-servlet/2.0.27.Final/undertow-servlet-2.0.27.Final.jar, file:/D:/DevelopmentTools/Maven_respository/org/jboss/spec/javax/annotation/jboss-annotations-api_1.2_spec/1.0.2.Final/jboss-annotations-api_1.2_spec-1.0.2.Final.jar, file:/D:/DevelopmentTools/Maven_respository/io/undertow/undertow-websockets-jsr/2.0.27.Final/undertow-websockets-jsr-2.0.27.Final.jar, file:/D:/DevelopmentTools/Maven_respository/org/jboss/spec/javax/websocket/jboss-websocket-api_1.1_spec/1.1.4.Final/jboss-websocket-api_1.1_spec-1.1.4.Final.jar, file:/D:/DevelopmentTools/Maven_respository/jakarta/servlet/jakarta.servlet-api/4.0.3/jakarta.servlet-api-4.0.3.jar, file:/D:/DevelopmentTools/Maven_respository/org/glassfish/jakarta.el/3.0.3/jakarta.el-3.0.3.jar, file:/D:/DevelopmentTools/Maven_respository/net/bytebuddy/byte-buddy/1.10.2/byte-buddy-1.10.2.jar, file:/D:/DevelopmentTools/Maven_respository/org/springframework/spring-core/5.2.1.RELEASE/spring-core-5.2.1.RELEASE.jar, file:/D:/DevelopmentTools/Maven_respository/org/springframework/spring-jcl/5.2.1.RELEASE/spring-jcl-5.2.1.RELEASE.jar, file:/D:/DevelopmentTools/Maven_respository/com/baomidou/dynamic-datasource-spring-boot-starter/3.0.0/dynamic-datasource-spring-boot-starter-3.0.0.jar, file:/D:/DevelopmentTools/Maven_respository/org/springframework/boot/spring-boot-starter-jdbc/2.2.1.RELEASE/spring-boot-starter-jdbc-2.2.1.RELEASE.jar, file:/D:/DevelopmentTools/Maven_respository/com/zaxxer/HikariCP/3.4.1/HikariCP-3.4.1.jar, file:/D:/DevelopmentTools/Maven_respository/org/springframework/spring-jdbc/5.2.1.RELEASE/spring-jdbc-5.2.1.RELEASE.jar, file:/D:/DevelopmentTools/Maven_respository/org/springframework/spring-tx/5.2.1.RELEASE/spring-tx-5.2.1.RELEASE.jar, file:/D:/DevelopmentTools/Maven_respository/org/springframework/boot/spring-boot-starter-aop/2.2.1.RELEASE/spring-boot-starter-aop-2.2.1.RELEASE.jar, file:/D:/DevelopmentTools/Maven_respository/org/aspectj/aspectjweaver/1.9.4/aspectjweaver-1.9.4.jar, file:/D:/DevelopmentTools/Maven_respository/org/apache/tomcat/tomcat-jdbc/7.0.81/tomcat-jdbc-7.0.81.jar, file:/D:/DevelopmentTools/Maven_respository/org/apache/tomcat/tomcat-juli/7.0.81/tomcat-juli-7.0.81.jar, file:/D:/DevelopmentTools/Maven_respository/com/belerweb/pinyin4j/2.5.0/pinyin4j-2.5.0.jar, file:/D:/DevelopmentTools/Maven_respository/org/apache/ant/ant/1.9.7/ant-1.9.7.jar, file:/D:/DevelopmentTools/Maven_respository/org/apache/ant/ant-launcher/1.9.7/ant-launcher-1.9.7.jar, file:/D:/DevelopmentTools/Maven_respository/net/coobird/thumbnailator/0.4.8/thumbnailator-0.4.8.jar, file:/D:/DevelopmentTools/Maven_respository/joda-time/joda-time/2.9.8/joda-time-2.9.8.jar, file:/D:/DevelopmentTools/Maven_respository/org/apache/velocity/velocity-engine-core/2.0/velocity-engine-core-2.0.jar, file:/D:/DevelopmentTools/Maven_respository/org/slf4j/slf4j-api/1.7.29/slf4j-api-1.7.29.jar, file:/D:/DevelopmentTools/Maven_respository/cn/hutool/hutool-all/4.1.2/hutool-all-4.1.2.jar, file:/D:/DevelopmentTools/Maven_respository/org/projectlombok/lombok/1.18.10/lombok-1.18.10.jar, file:/D:/DevelopmentTools/Maven_respository/com/baomidou/mybatis-plus-boot-starter/3.4.3.4/mybatis-plus-boot-starter-3.4.3.4.jar, file:/D:/DevelopmentTools/Maven_respository/com/baomidou/mybatis-plus/3.4.3.4/mybatis-plus-3.4.3.4.jar, file:/D:/DevelopmentTools/Maven_respository/com/baomidou/mybatis-plus-extension/3.4.3.4/mybatis-plus-extension-3.4.3.4.jar, file:/D:/DevelopmentTools/Maven_respository/com/baomidou/mybatis-plus-core/3.4.3.4/mybatis-plus-core-3.4.3.4.jar, file:/D:/DevelopmentTools/Maven_respository/com/baomidou/mybatis-plus-annotation/3.4.3.4/mybatis-plus-annotation-3.4.3.4.jar, file:/D:/DevelopmentTools/Maven_respository/com/github/jsqlparser/jsqlparser/4.2/jsqlparser-4.2.jar, file:/D:/DevelopmentTools/Maven_respository/org/springframework/boot/spring-boot-autoconfigure/2.2.1.RELEASE/spring-boot-autoconfigure-2.2.1.RELEASE.jar, file:/D:/DevelopmentTools/Maven_respository/org/mybatis/spring/boot/mybatis-spring-boot-starter/2.1.0/mybatis-spring-boot-starter-2.1.0.jar, file:/D:/DevelopmentTools/Maven_respository/org/mybatis/spring/boot/mybatis-spring-boot-autoconfigure/2.1.0/mybatis-spring-boot-autoconfigure-2.1.0.jar, file:/D:/DevelopmentTools/Maven_respository/org/mybatis/mybatis-spring/2.0.2/mybatis-spring-2.0.2.jar, file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-swagger2/2.9.2/springfox-swagger2-2.9.2.jar, file:/D:/DevelopmentTools/Maven_respository/io/swagger/swagger-annotations/1.5.20/swagger-annotations-1.5.20.jar, file:/D:/DevelopmentTools/Maven_respository/io/swagger/swagger-models/1.5.20/swagger-models-1.5.20.jar, file:/D:/DevelopmentTools/Maven_respository/com/fasterxml/jackson/core/jackson-annotations/2.10.0/jackson-annotations-2.10.0.jar, file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spi/2.9.2/springfox-spi-2.9.2.jar, file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-core/2.9.2/springfox-core-2.9.2.jar, file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-schema/2.9.2/springfox-schema-2.9.2.jar, file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-swagger-common/2.9.2/springfox-swagger-common-2.9.2.jar, file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar, file:/D:/DevelopmentTools/Maven_respository/com/fasterxml/classmate/1.5.1/classmate-1.5.1.jar, file:/D:/DevelopmentTools/Maven_respository/org/springframework/plugin/spring-plugin-core/1.2.0.RELEASE/spring-plugin-core-1.2.0.RELEASE.jar, file:/D:/DevelopmentTools/Maven_respository/org/springframework/plugin/spring-plugin-metadata/1.2.0.RELEASE/spring-plugin-metadata-1.2.0.RELEASE.jar, file:/D:/DevelopmentTools/Maven_respository/org/mapstruct/mapstruct/1.2.0.Final/mapstruct-1.2.0.Final.jar, file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-swagger-ui/2.9.2/springfox-swagger-ui-2.9.2.jar, file:/D:/DevelopmentTools/Maven_respository/org/apache/commons/commons-lang3/3.8.1/commons-lang3-3.8.1.jar, file:/D:/DevelopmentTools/Maven_respository/com/google/guava/guava/27.0-jre/guava-27.0-jre.jar, file:/D:/DevelopmentTools/Maven_respository/com/google/guava/failureaccess/1.0/failureaccess-1.0.jar, file:/D:/DevelopmentTools/Maven_respository/com/google/guava/listenablefuture/9999.0-empty-to-avoid-conflict-with-guava/listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar, file:/D:/DevelopmentTools/Maven_respository/com/google/code/findbugs/jsr305/3.0.2/jsr305-3.0.2.jar, file:/D:/DevelopmentTools/Maven_respository/org/checkerframework/checker-qual/2.5.2/checker-qual-2.5.2.jar, file:/D:/DevelopmentTools/Maven_respository/com/google/errorprone/error_prone_annotations/2.2.0/error_prone_annotations-2.2.0.jar, file:/D:/DevelopmentTools/Maven_respository/com/google/j2objc/j2objc-annotations/1.1/j2objc-annotations-1.1.jar, file:/D:/DevelopmentTools/Maven_respository/org/codehaus/mojo/animal-sniffer-annotations/1.17/animal-sniffer-annotations-1.17.jar, file:/D:/DevelopmentTools/Maven_respository/com/alibaba/fastjson/1.2.83/fastjson-1.2.83.jar, file:/D:/DevelopmentTools/Maven_respository/com/alibaba/druid-spring-boot-starter/1.1.10/druid-spring-boot-starter-1.1.10.jar, file:/D:/DevelopmentTools/Maven_respository/com/alibaba/druid/1.1.10/druid-1.1.10.jar, file:/D:/DevelopmentTools/Maven_respository/org/apache/shiro/shiro-spring/1.4.0/shiro-spring-1.4.0.jar, file:/D:/DevelopmentTools/Maven_respository/org/apache/shiro/shiro-core/1.4.0/shiro-core-1.4.0.jar, file:/D:/DevelopmentTools/Maven_respository/org/apache/shiro/shiro-lang/1.4.0/shiro-lang-1.4.0.jar, file:/D:/DevelopmentTools/Maven_respository/org/apache/shiro/shiro-cache/1.4.0/shiro-cache-1.4.0.jar, file:/D:/DevelopmentTools/Maven_respository/org/apache/shiro/shiro-crypto-hash/1.4.0/shiro-crypto-hash-1.4.0.jar, file:/D:/DevelopmentTools/Maven_respository/org/apache/shiro/shiro-crypto-core/1.4.0/shiro-crypto-core-1.4.0.jar, file:/D:/DevelopmentTools/Maven_respository/org/apache/shiro/shiro-crypto-cipher/1.4.0/shiro-crypto-cipher-1.4.0.jar, file:/D:/DevelopmentTools/Maven_respository/org/apache/shiro/shiro-config-core/1.4.0/shiro-config-core-1.4.0.jar, file:/D:/DevelopmentTools/Maven_respository/org/apache/shiro/shiro-config-ogdl/1.4.0/shiro-config-ogdl-1.4.0.jar, file:/D:/DevelopmentTools/Maven_respository/commons-beanutils/commons-beanutils/1.9.3/commons-beanutils-1.9.3.jar, file:/D:/DevelopmentTools/Maven_respository/commons-collections/commons-collections/3.2.2/commons-collections-3.2.2.jar, file:/D:/DevelopmentTools/Maven_respository/org/apache/shiro/shiro-event/1.4.0/shiro-event-1.4.0.jar, file:/D:/DevelopmentTools/Maven_respository/org/apache/shiro/shiro-web/1.4.0/shiro-web-1.4.0.jar, file:/D:/DevelopmentTools/Maven_respository/com/auth0/java-jwt/3.18.2/java-jwt-3.18.2.jar, file:/D:/DevelopmentTools/Maven_respository/com/fasterxml/jackson/core/jackson-databind/2.10.0/jackson-databind-2.10.0.jar, file:/D:/DevelopmentTools/Maven_respository/com/fasterxml/jackson/core/jackson-core/2.10.0/jackson-core-2.10.0.jar, file:/D:/DevelopmentTools/Maven_respository/org/springframework/spring-context/5.2.23.RELEASE/spring-context-5.2.23.RELEASE.jar, file:/D:/DevelopmentTools/Maven_respository/org/springframework/spring-aop/5.2.1.RELEASE/spring-aop-5.2.1.RELEASE.jar, file:/D:/DevelopmentTools/Maven_respository/org/springframework/spring-beans/5.2.1.RELEASE/spring-beans-5.2.1.RELEASE.jar, file:/D:/DevelopmentTools/Maven_respository/org/springframework/spring-expression/5.2.1.RELEASE/spring-expression-5.2.1.RELEASE.jar, file:/D:/DevelopmentTools/Maven_respository/org/mybatis/mybatis/3.5.7/mybatis-3.5.7.jar, file:/D:/DevelopmentTools/Maven_respository/com/github/ben-manes/caffeine/caffeine/2.8.0/caffeine-2.8.0.jar, file:/D:/DevelopmentTools/Maven_respository/com/github/xiaoymin/swagger-bootstrap-ui/1.9.3/swagger-bootstrap-ui-1.9.3.jar, file:/D:/DevelopmentTools/IntelliJ%20IDEA%202025/lib/idea_rt.jar]
2025-06-07 16:02:01.585 [main] INFO  com.qs.admin.QscAdminApplication - Starting QscAdminApplication on MSI with PID 4300 (started by KarlKyo in D:\project\Java Projectes\qsadmin)
2025-06-07 16:02:01.586 [main] DEBUG com.qs.admin.QscAdminApplication - Running with Spring Boot v2.2.1.RELEASE, Spring v5.2.23.RELEASE
2025-06-07 16:02:01.586 [main] INFO  com.qs.admin.QscAdminApplication - The following profiles are active: dev,baishui
2025-06-07 16:02:01.586 [main] DEBUG o.s.boot.SpringApplication - Loading source class com.qs.admin.QscAdminApplication
2025-06-07 16:02:01.607 [main] DEBUG o.s.b.c.c.ConfigFileApplicationListener - Activated activeProfiles dev,baishui
2025-06-07 16:02:01.607 [main] DEBUG o.s.b.c.c.ConfigFileApplicationListener - Loaded config file 'file:/D:/project/Java%20Projectes/qsadmin/target/classes/application.yml' (classpath:/application.yml)
2025-06-07 16:02:01.607 [main] DEBUG o.s.b.c.c.ConfigFileApplicationListener - Loaded config file 'file:/D:/project/Java%20Projectes/qsadmin/target/classes/application-dev.yml' (classpath:/application-dev.yml) for profile dev
2025-06-07 16:02:01.607 [main] DEBUG o.s.b.c.c.ConfigFileApplicationListener - Loaded config file 'file:./config/application-baishui.properties' (file:./config/application-baishui.properties) for profile baishui
2025-06-07 16:02:01.607 [main] DEBUG o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Refreshing org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@1b3ee4e
2025-06-07 16:02:01.618 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.context.annotation.internalConfigurationAnnotationProcessor'
2025-06-07 16:02:01.626 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.internalCachingMetadataReaderFactory'
2025-06-07 16:02:01.674 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\common\aspect\IdempotencyAspect.class]
2025-06-07 16:02:01.679 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\common\configurer\CorsConfig.class]
2025-06-07 16:02:01.689 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\common\configurer\DataSourceConfig.class]
2025-06-07 16:02:01.695 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\common\configurer\MyWebMvcConfig.class]
2025-06-07 16:02:01.700 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\common\configurer\ShiroConfig.class]
2025-06-07 16:02:01.705 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\common\configurer\Swagger2Configurer.class]
2025-06-07 16:02:01.754 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\common\core\MybatisPlusConfig.class]
2025-06-07 16:02:01.832 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\common\exception\GlobalExceptionHandler.class]
2025-06-07 16:02:01.861 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\common\service\impl\SyncCacheServiceImpl.class]
2025-06-07 16:02:01.870 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\common\shiro\ShiroFilterProperties.class]
2025-06-07 16:02:01.879 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\common\shiro\ShiroRealm.class]
2025-06-07 16:02:01.887 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\common\shiro\cache\ShiroCacheManager.class]
2025-06-07 16:02:01.907 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\common\shiro\security\JwtProperties.class]
2025-06-07 16:02:01.918 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\common\shiro\security\JwtUtil.class]
2025-06-07 16:02:01.956 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\common\utils\LocalCacheManager.class]
2025-06-07 16:02:01.989 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\common\utils\SpringContextUtil.class]
2025-06-07 16:02:02.006 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\config\BaiShuiProperties.class]
2025-06-07 16:02:02.013 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\config\RestTemplateConfig.class]
2025-06-07 16:02:02.022 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\controller\AgentInfoController.class]
2025-06-07 16:02:02.038 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\controller\AutocodeController.class]
2025-06-07 16:02:02.049 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\controller\BusinessController.class]
2025-06-07 16:02:02.055 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\controller\ClientInfoController.class]
2025-06-07 16:02:02.062 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\controller\EmployeeController.class]
2025-06-07 16:02:02.066 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\controller\NoticeController.class]
2025-06-07 16:02:02.072 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\controller\SimpleLoginController.class]
2025-06-07 16:02:02.077 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\controller\SystemController.class]
2025-06-07 16:02:02.082 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\controller\SystemLogController.class]
2025-06-07 16:02:02.087 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\controller\ThingsController.class]
2025-06-07 16:02:02.092 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\controller\TicketBookController.class]
2025-06-07 16:02:02.096 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\controller\TicketController.class]
2025-06-07 16:02:02.102 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\controller\TicketExchangeController.class]
2025-06-07 16:02:02.107 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\controller\TicketLogController.class]
2025-06-07 16:02:02.111 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\controller\TicketVerifyController.class]
2025-06-07 16:02:02.117 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\controller\WindowBusinessController.class]
2025-06-07 16:02:02.123 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\controller\WindowController.class]
2025-06-07 16:02:02.131 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\controller\WindowStatusController.class]
2025-06-07 16:02:02.524 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\service\MenuService.class]
2025-06-07 16:02:02.569 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\service\impl\AgentInfoEnterpriseServiceImpl.class]
2025-06-07 16:02:02.575 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\service\impl\AgentInfoServiceImpl.class]
2025-06-07 16:02:02.579 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\service\impl\AutocodeServiceImpl.class]
2025-06-07 16:02:02.585 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\service\impl\BaiShuiApiServiceImpl.class]
2025-06-07 16:02:02.589 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\service\impl\BusinessServiceImpl.class]
2025-06-07 16:02:02.592 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\service\impl\ClientInfoServiceImpl.class]
2025-06-07 16:02:02.597 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\service\impl\EmployeeServiceImpl.class]
2025-06-07 16:02:02.601 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\service\impl\NoticeServiceImpl.class]
2025-06-07 16:02:02.604 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\service\impl\SystemLogServiceImpl.class]
2025-06-07 16:02:02.609 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\service\impl\SystemServiceImpl.class]
2025-06-07 16:02:02.613 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\service\impl\ThingsServiceImpl.class]
2025-06-07 16:02:02.617 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\service\impl\TicketBookServiceImpl.class]
2025-06-07 16:02:02.620 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\service\impl\TicketExchangeServiceImpl.class]
2025-06-07 16:02:02.625 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\service\impl\TicketLogServiceImpl.class]
2025-06-07 16:02:02.630 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\service\impl\TicketServiceImpl.class]
2025-06-07 16:02:02.635 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\service\impl\TicketVerifyServiceImpl.class]
2025-06-07 16:02:02.639 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\service\impl\WindowBusinessServiceImpl.class]
2025-06-07 16:02:02.645 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\service\impl\WindowServiceImpl.class]
2025-06-07 16:02:02.649 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\service\impl\WindowStatusServiceImpl.class]
2025-06-07 16:02:02.655 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: file [D:\project\Java Projectes\qsadmin\target\classes\com\qs\admin\taxhall\task\BaiShuiTokenSyncTask.class]
2025-06-07 16:02:02.678 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-swagger2/2.9.2/springfox-swagger2-2.9.2.jar!/springfox/documentation/swagger2/mappers/ServiceModelToSwagger2MapperImpl.class]
2025-06-07 16:02:02.678 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-swagger2/2.9.2/springfox-swagger2-2.9.2.jar!/springfox/documentation/swagger2/mappers/VendorExtensionsMapperImpl.class]
2025-06-07 16:02:02.680 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-swagger2/2.9.2/springfox-swagger2-2.9.2.jar!/springfox/documentation/swagger2/mappers/ParameterMapperImpl.class]
2025-06-07 16:02:02.681 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-swagger2/2.9.2/springfox-swagger2-2.9.2.jar!/springfox/documentation/swagger2/mappers/ModelMapperImpl.class]
2025-06-07 16:02:02.682 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-swagger2/2.9.2/springfox-swagger2-2.9.2.jar!/springfox/documentation/swagger2/mappers/LicenseMapperImpl.class]
2025-06-07 16:02:02.682 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-swagger2/2.9.2/springfox-swagger2-2.9.2.jar!/springfox/documentation/swagger2/mappers/SecurityMapperImpl.class]
2025-06-07 16:02:02.691 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/scanners/ApiListingReferenceScanner.class]
2025-06-07 16:02:02.692 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/scanners/ApiDocumentationScanner.class]
2025-06-07 16:02:02.692 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/scanners/ApiDescriptionReader.class]
2025-06-07 16:02:02.693 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/scanners/ApiListingReader.class]
2025-06-07 16:02:02.693 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/scanners/CachingOperationReader.class]
2025-06-07 16:02:02.693 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/scanners/MediaTypeReader.class]
2025-06-07 16:02:02.693 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/scanners/ApiListingScanner.class]
2025-06-07 16:02:02.694 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/scanners/ApiModelReader.class]
2025-06-07 16:02:02.695 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/scanners/ApiDescriptionLookup.class]
2025-06-07 16:02:02.696 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/readers/operation/OperationModelsProvider.class]
2025-06-07 16:02:02.696 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/readers/operation/OperationDeprecatedReader.class]
2025-06-07 16:02:02.696 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/readers/operation/ResponseMessagesReader.class]
2025-06-07 16:02:02.697 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/readers/operation/OperationParameterReader.class]
2025-06-07 16:02:02.697 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/readers/operation/DefaultTagsProvider.class]
2025-06-07 16:02:02.697 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/readers/operation/OperationTagsReader.class]
2025-06-07 16:02:02.698 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/readers/operation/ApiOperationReader.class]
2025-06-07 16:02:02.698 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/readers/operation/DefaultOperationReader.class]
2025-06-07 16:02:02.699 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/readers/operation/OperationParameterRequestConditionReader.class]
2025-06-07 16:02:02.699 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/readers/operation/OperationParameterHeadersConditionReader.class]
2025-06-07 16:02:02.699 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/readers/operation/OperationResponseClassReader.class]
2025-06-07 16:02:02.700 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/readers/operation/CachingOperationNameGenerator.class]
2025-06-07 16:02:02.701 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/readers/parameter/ParameterMultiplesReader.class]
2025-06-07 16:02:02.702 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/readers/parameter/ModelAttributeParameterExpander.class]
2025-06-07 16:02:02.703 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/readers/parameter/ParameterTypeReader.class]
2025-06-07 16:02:02.704 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/readers/parameter/ParameterRequiredReader.class]
2025-06-07 16:02:02.706 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/readers/parameter/ParameterDataTypeReader.class]
2025-06-07 16:02:02.706 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/readers/parameter/ParameterDefaultReader.class]
2025-06-07 16:02:02.707 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/readers/parameter/ParameterNameReader.class]
2025-06-07 16:02:02.707 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/readers/parameter/ExpandedParameterBuilder.class]
2025-06-07 16:02:02.710 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/plugins/DocumentationPluginsBootstrapper.class]
2025-06-07 16:02:02.712 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/plugins/WebMvcRequestHandlerProvider.class]
2025-06-07 16:02:02.712 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/plugins/DocumentationPluginsManager.class]
2025-06-07 16:02:02.713 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/paths/QueryStringUriTemplateDecorator.class]
2025-06-07 16:02:02.713 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/paths/PathMappingDecorator.class]
2025-06-07 16:02:02.713 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/paths/PathSanitizer.class]
2025-06-07 16:02:02.714 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar!/springfox/documentation/spring/web/paths/OperationPathDecorator.class]
2025-06-07 16:02:02.734 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-schema/2.9.2/springfox-schema-2.9.2.jar!/springfox/documentation/schema/CachingModelDependencyProvider.class]
2025-06-07 16:02:02.735 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-schema/2.9.2/springfox-schema-2.9.2.jar!/springfox/documentation/schema/TypeNameExtractor.class]
2025-06-07 16:02:02.735 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-schema/2.9.2/springfox-schema-2.9.2.jar!/springfox/documentation/schema/plugins/PropertyDiscriminatorBasedInheritancePlugin.class]
2025-06-07 16:02:02.736 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-schema/2.9.2/springfox-schema-2.9.2.jar!/springfox/documentation/schema/plugins/XmlModelPlugin.class]
2025-06-07 16:02:02.736 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-schema/2.9.2/springfox-schema-2.9.2.jar!/springfox/documentation/schema/plugins/SchemaPluginsManager.class]
2025-06-07 16:02:02.737 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-schema/2.9.2/springfox-schema-2.9.2.jar!/springfox/documentation/schema/property/CachingModelPropertiesProvider.class]
2025-06-07 16:02:02.737 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-schema/2.9.2/springfox-schema-2.9.2.jar!/springfox/documentation/schema/property/ObjectMapperBeanPropertyNamingStrategy.class]
2025-06-07 16:02:02.738 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-schema/2.9.2/springfox-schema-2.9.2.jar!/springfox/documentation/schema/property/bean/AccessorsProvider.class]
2025-06-07 16:02:02.739 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-schema/2.9.2/springfox-schema-2.9.2.jar!/springfox/documentation/schema/property/field/FieldProvider.class]
2025-06-07 16:02:02.740 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-schema/2.9.2/springfox-schema-2.9.2.jar!/springfox/documentation/schema/property/XmlPropertyPlugin.class]
2025-06-07 16:02:02.740 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-schema/2.9.2/springfox-schema-2.9.2.jar!/springfox/documentation/schema/property/OptimizedModelPropertiesProvider.class]
2025-06-07 16:02:02.740 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-schema/2.9.2/springfox-schema-2.9.2.jar!/springfox/documentation/schema/property/FactoryMethodProvider.class]
2025-06-07 16:02:02.742 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-schema/2.9.2/springfox-schema-2.9.2.jar!/springfox/documentation/schema/CachingModelProvider.class]
2025-06-07 16:02:02.743 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-schema/2.9.2/springfox-schema-2.9.2.jar!/springfox/documentation/schema/DefaultModelDependencyProvider.class]
2025-06-07 16:02:02.743 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-schema/2.9.2/springfox-schema-2.9.2.jar!/springfox/documentation/schema/JacksonEnumTypeDeterminer.class]
2025-06-07 16:02:02.743 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-schema/2.9.2/springfox-schema-2.9.2.jar!/springfox/documentation/schema/DefaultModelProvider.class]
2025-06-07 16:02:02.754 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-swagger-common/2.9.2/springfox-swagger-common-2.9.2.jar!/springfox/documentation/swagger/schema/ApiModelPropertyPropertyBuilder.class]
2025-06-07 16:02:02.754 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-swagger-common/2.9.2/springfox-swagger-common-2.9.2.jar!/springfox/documentation/swagger/schema/ApiModelTypeNameProvider.class]
2025-06-07 16:02:02.754 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-swagger-common/2.9.2/springfox-swagger-common-2.9.2.jar!/springfox/documentation/swagger/schema/ApiModelBuilder.class]
2025-06-07 16:02:02.757 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-swagger-common/2.9.2/springfox-swagger-common-2.9.2.jar!/springfox/documentation/swagger/readers/operation/OperationImplicitParameterReader.class]
2025-06-07 16:02:02.758 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-swagger-common/2.9.2/springfox-swagger-common-2.9.2.jar!/springfox/documentation/swagger/readers/operation/VendorExtensionsReader.class]
2025-06-07 16:02:02.759 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-swagger-common/2.9.2/springfox-swagger-common-2.9.2.jar!/springfox/documentation/swagger/readers/operation/SwaggerOperationResponseClassReader.class]
2025-06-07 16:02:02.759 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-swagger-common/2.9.2/springfox-swagger-common-2.9.2.jar!/springfox/documentation/swagger/readers/operation/SwaggerOperationModelsProvider.class]
2025-06-07 16:02:02.759 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-swagger-common/2.9.2/springfox-swagger-common-2.9.2.jar!/springfox/documentation/swagger/readers/operation/SwaggerMediaTypeReader.class]
2025-06-07 16:02:02.760 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-swagger-common/2.9.2/springfox-swagger-common-2.9.2.jar!/springfox/documentation/swagger/readers/operation/OperationHttpMethodReader.class]
2025-06-07 16:02:02.760 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-swagger-common/2.9.2/springfox-swagger-common-2.9.2.jar!/springfox/documentation/swagger/readers/operation/OperationImplicitParametersReader.class]
2025-06-07 16:02:02.760 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-swagger-common/2.9.2/springfox-swagger-common-2.9.2.jar!/springfox/documentation/swagger/readers/operation/OperationAuthReader.class]
2025-06-07 16:02:02.760 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-swagger-common/2.9.2/springfox-swagger-common-2.9.2.jar!/springfox/documentation/swagger/readers/operation/OperationHiddenReader.class]
2025-06-07 16:02:02.761 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-swagger-common/2.9.2/springfox-swagger-common-2.9.2.jar!/springfox/documentation/swagger/readers/operation/OperationSummaryReader.class]
2025-06-07 16:02:02.761 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-swagger-common/2.9.2/springfox-swagger-common-2.9.2.jar!/springfox/documentation/swagger/readers/operation/SwaggerResponseMessageReader.class]
2025-06-07 16:02:02.761 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-swagger-common/2.9.2/springfox-swagger-common-2.9.2.jar!/springfox/documentation/swagger/readers/operation/OperationNicknameIntoUniqueIdReader.class]
2025-06-07 16:02:02.761 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-swagger-common/2.9.2/springfox-swagger-common-2.9.2.jar!/springfox/documentation/swagger/readers/operation/OperationPositionReader.class]
2025-06-07 16:02:02.761 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-swagger-common/2.9.2/springfox-swagger-common-2.9.2.jar!/springfox/documentation/swagger/readers/operation/OperationNotesReader.class]
2025-06-07 16:02:02.762 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-swagger-common/2.9.2/springfox-swagger-common-2.9.2.jar!/springfox/documentation/swagger/readers/operation/SwaggerOperationTagsReader.class]
2025-06-07 16:02:02.762 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-swagger-common/2.9.2/springfox-swagger-common-2.9.2.jar!/springfox/documentation/swagger/readers/parameter/ApiParamParameterBuilder.class]
2025-06-07 16:02:02.762 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-swagger-common/2.9.2/springfox-swagger-common-2.9.2.jar!/springfox/documentation/swagger/readers/parameter/SwaggerExpandedParameterBuilder.class]
2025-06-07 16:02:02.767 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-swagger-common/2.9.2/springfox-swagger-common-2.9.2.jar!/springfox/documentation/swagger/web/SwaggerApiListingReader.class]
2025-06-07 16:02:02.769 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-swagger-common/2.9.2/springfox-swagger-common-2.9.2.jar!/springfox/documentation/swagger/web/ClassOrApiAnnotationResourceGrouping.class]
2025-06-07 16:02:02.769 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-swagger-common/2.9.2/springfox-swagger-common-2.9.2.jar!/springfox/documentation/swagger/web/InMemorySwaggerResourcesProvider.class]
2025-06-07 16:02:02.770 [main] DEBUG o.s.c.a.ClassPathBeanDefinitionScanner - Identified candidate component class: URL [jar:file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-swagger-common/2.9.2/springfox-swagger-common-2.9.2.jar!/springfox/documentation/swagger/web/ApiResourceController.class]
2025-06-07 16:02:02.914 [main] DEBUG o.s.c.e.PropertySourcesPropertyResolver - Found key 'spring.jmx.enabled' in PropertySource 'configurationProperties' with value of type String
2025-06-07 16:02:02.926 [main] DEBUG o.s.c.e.PropertySourcesPropertyResolver - Found key 'spring.jmx.enabled' in PropertySource 'configurationProperties' with value of type String
2025-06-07 16:02:02.926 [main] DEBUG o.s.c.e.PropertySourcesPropertyResolver - Found key 'spring.application.admin.enabled' in PropertySource 'configurationProperties' with value of type String
2025-06-07 16:02:03.086 [main] DEBUG o.s.c.e.PropertySourcesPropertyResolver - Found key 'spring.jmx.enabled' in PropertySource 'configurationProperties' with value of type String
2025-06-07 16:02:03.088 [main] DEBUG o.s.c.e.PropertySourcesPropertyResolver - Found key 'spring.application.admin.enabled' in PropertySource 'configurationProperties' with value of type String
2025-06-07 16:02:03.126 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'com.qs.admin.common.configurer.DataSourceConfig#MapperScannerRegistrar#0'
2025-06-07 16:02:03.132 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'propertySourcesPlaceholderConfigurer'
2025-06-07 16:02:03.176 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.context.properties.ConfigurationPropertiesBeanDefinitionValidator'
2025-06-07 16:02:03.204 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.context.event.internalEventListenerProcessor'
2025-06-07 16:02:03.204 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'preserveErrorControllerTargetClassPostProcessor'
2025-06-07 16:02:03.205 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.context.event.internalEventListenerFactory'
2025-06-07 16:02:03.205 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.transaction.config.internalTransactionalEventListenerFactory'
2025-06-07 16:02:03.207 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.context.annotation.internalAutowiredAnnotationProcessor'
2025-06-07 16:02:03.207 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.context.annotation.internalCommonAnnotationProcessor'
2025-06-07 16:02:03.208 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'lifecycleBeanPostProcessor'
2025-06-07 16:02:03.208 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'shiroConfig'
2025-06-07 16:02:03.209 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'shiroConfig' of type [com.qs.admin.common.configurer.ShiroConfig$$EnhancerBySpringCGLIB$$c036676b] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-07 16:02:03.212 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.context.properties.ConfigurationPropertiesBindingPostProcessor'
2025-06-07 16:02:03.212 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.context.internalConfigurationPropertiesBinder'
2025-06-07 16:02:03.212 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.context.internalConfigurationPropertiesBinderFactory'
2025-06-07 16:02:03.213 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'getLifecycleBeanPostProcessor'
2025-06-07 16:02:03.218 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.context.annotation.internalScheduledAnnotationProcessor'
2025-06-07 16:02:03.218 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.scheduling.annotation.SchedulingConfiguration'
2025-06-07 16:02:03.220 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'methodValidationPostProcessor'
2025-06-07 16:02:03.231 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'securityManager'
2025-06-07 16:02:03.231 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'shiroRealm'
2025-06-07 16:02:03.235 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'jwtUtil'
2025-06-07 16:02:03.235 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'jwtProperties'
2025-06-07 16:02:03.242 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'jwtProperties' of type [com.qs.admin.common.shiro.security.JwtProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-07 16:02:03.242 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'jwtUtil' of type [com.qs.admin.common.shiro.security.JwtUtil] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-07 16:02:03.242 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'shiroRealm' of type [com.qs.admin.common.shiro.ShiroRealm] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-07 16:02:03.242 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'shiroCacheManager'
2025-06-07 16:02:03.243 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'localCacheManager'
2025-06-07 16:02:03.260 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'localCacheManager' of type [com.qs.admin.common.utils.LocalCacheManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-07 16:02:03.260 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'shiroCacheManager' of type [com.qs.admin.common.shiro.cache.ShiroCacheManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-07 16:02:03.260 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Autowiring by type from bean name 'securityManager' via factory method to bean named 'shiroRealm'
2025-06-07 16:02:03.260 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Autowiring by type from bean name 'securityManager' via factory method to bean named 'shiroCacheManager'
2025-06-07 16:02:03.516 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'securityManager' of type [org.apache.shiro.web.mgt.DefaultWebSecurityManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-07 16:02:03.517 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'syncCacheServiceImpl'
2025-06-07 16:02:03.518 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'syncCacheServiceImpl' of type [com.qs.admin.common.service.impl.SyncCacheServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-07 16:02:03.518 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Autowiring by type from bean name 'shiroFilter' via factory method to bean named 'securityManager'
2025-06-07 16:02:03.518 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Autowiring by type from bean name 'shiroFilter' via factory method to bean named 'localCacheManager'
2025-06-07 16:02:03.518 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Autowiring by type from bean name 'shiroFilter' via factory method to bean named 'jwtProperties'
2025-06-07 16:02:03.518 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Autowiring by type from bean name 'shiroFilter' via factory method to bean named 'syncCacheServiceImpl'
2025-06-07 16:02:03.521 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'getShiroFilterProperties'
2025-06-07 16:02:03.523 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'getShiroFilterProperties' of type [com.qs.admin.common.shiro.ShiroFilterProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-07 16:02:03.528 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Autowiring by type from bean name 'methodValidationPostProcessor' via factory method to bean named 'environment'
2025-06-07 16:02:03.530 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'dataSourceInitializerPostProcessor'
2025-06-07 16:02:03.530 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.aop.config.internalAutoProxyCreator'
2025-06-07 16:02:03.539 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'persistenceExceptionTranslationPostProcessor'
2025-06-07 16:02:03.539 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Autowiring by type from bean name 'persistenceExceptionTranslationPostProcessor' via factory method to bean named 'environment'
2025-06-07 16:02:03.540 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'shiroFilter'
2025-06-07 16:02:03.540 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'authorizationAttributeSourceAdvisor'
2025-06-07 16:02:03.541 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Autowiring by type from bean name 'authorizationAttributeSourceAdvisor' via factory method to bean named 'securityManager'
2025-06-07 16:02:03.544 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'authorizationAttributeSourceAdvisor' of type [org.apache.shiro.spring.security.interceptor.AuthorizationAttributeSourceAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-07 16:02:03.545 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'dynamicDatasourceAnnotationAdvisor'
2025-06-07 16:02:03.545 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration'
2025-06-07 16:02:03.546 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.transaction.config.internalTransactionAdvisor'
2025-06-07 16:02:03.546 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration'
2025-06-07 16:02:03.553 [main] DEBUG o.s.a.a.a.ReflectiveAspectJAdvisorFactory - Found AspectJ method: public java.lang.Object com.qs.admin.common.aspect.IdempotencyAspect.around(org.aspectj.lang.ProceedingJoinPoint,org.springframework.web.bind.annotation.PostMapping) throws java.lang.Throwable
2025-06-07 16:02:03.561 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-07 16:02:03.600 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'transactionAttributeSource'
2025-06-07 16:02:03.604 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'transactionInterceptor'
2025-06-07 16:02:03.604 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Autowiring by type from bean name 'transactionInterceptor' via factory method to bean named 'transactionAttributeSource'
2025-06-07 16:02:03.606 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Autowiring by type from bean name 'org.springframework.transaction.config.internalTransactionAdvisor' via factory method to bean named 'transactionAttributeSource'
2025-06-07 16:02:03.607 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Autowiring by type from bean name 'org.springframework.transaction.config.internalTransactionAdvisor' via factory method to bean named 'transactionInterceptor'
2025-06-07 16:02:03.608 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'spring.datasource.dynamic-com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties'
2025-06-07 16:02:03.611 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.datasource.dynamic-com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-07 16:02:03.613 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration$$EnhancerBySpringCGLIB$$29ced801] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-07 16:02:03.615 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'dsProcessor'
2025-06-07 16:02:03.617 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dsProcessor' of type [com.baomidou.dynamic.datasource.processor.DsHeaderProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-07 16:02:03.618 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Autowiring by type from bean name 'dynamicDatasourceAnnotationAdvisor' via factory method to bean named 'dsProcessor'
2025-06-07 16:02:03.622 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dynamicDatasourceAnnotationAdvisor' of type [com.baomidou.dynamic.datasource.aop.DynamicDataSourceAnnotationAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-07 16:02:03.624 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'objectMapperConfigurer'
2025-06-07 16:02:03.625 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'webServerFactoryCustomizerBeanPostProcessor'
2025-06-07 16:02:03.625 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'errorPageRegistrarBeanPostProcessor'
2025-06-07 16:02:03.627 [main] DEBUG o.s.u.c.s.UiApplicationContextUtils - Unable to locate ThemeSource with name 'themeSource': using default [org.springframework.ui.context.support.ResourceBundleThemeSource@c86152]
2025-06-07 16:02:03.627 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'undertowServletWebServerFactory'
2025-06-07 16:02:03.627 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.ServletWebServerFactoryConfiguration$EmbeddedUndertow'
2025-06-07 16:02:03.636 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'websocketServletWebServerCustomizer'
2025-06-07 16:02:03.636 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.websocket.servlet.WebSocketServletAutoConfiguration$UndertowWebSocketConfiguration'
2025-06-07 16:02:03.637 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'servletWebServerFactoryCustomizer'
2025-06-07 16:02:03.637 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.ServletWebServerFactoryAutoConfiguration'
2025-06-07 16:02:03.639 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'server-org.springframework.boot.autoconfigure.web.ServerProperties'
2025-06-07 16:02:03.644 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Autowiring by type from bean name 'servletWebServerFactoryCustomizer' via factory method to bean named 'server-org.springframework.boot.autoconfigure.web.ServerProperties'
2025-06-07 16:02:03.644 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'undertowWebServerFactoryCustomizer'
2025-06-07 16:02:03.644 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.embedded.EmbeddedWebServerFactoryCustomizerAutoConfiguration$UndertowWebServerFactoryCustomizerConfiguration'
2025-06-07 16:02:03.645 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Autowiring by type from bean name 'undertowWebServerFactoryCustomizer' via factory method to bean named 'environment'
2025-06-07 16:02:03.645 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Autowiring by type from bean name 'undertowWebServerFactoryCustomizer' via factory method to bean named 'server-org.springframework.boot.autoconfigure.web.ServerProperties'
2025-06-07 16:02:03.646 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'localeCharsetMappingsCustomizer'
2025-06-07 16:02:03.647 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.HttpEncodingAutoConfiguration'
2025-06-07 16:02:03.647 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'spring.http-org.springframework.boot.autoconfigure.http.HttpProperties'
2025-06-07 16:02:03.648 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.web.servlet.HttpEncodingAutoConfiguration' via constructor to bean named 'spring.http-org.springframework.boot.autoconfigure.http.HttpProperties'
2025-06-07 16:02:03.665 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'errorPageCustomizer'
2025-06-07 16:02:03.665 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.error.ErrorMvcAutoConfiguration'
2025-06-07 16:02:03.665 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.web.servlet.error.ErrorMvcAutoConfiguration' via constructor to bean named 'server-org.springframework.boot.autoconfigure.web.ServerProperties'
2025-06-07 16:02:03.667 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'dispatcherServletRegistration'
2025-06-07 16:02:03.667 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.DispatcherServletAutoConfiguration$DispatcherServletRegistrationConfiguration'
2025-06-07 16:02:03.668 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'dispatcherServlet'
2025-06-07 16:02:03.668 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.DispatcherServletAutoConfiguration$DispatcherServletConfiguration'
2025-06-07 16:02:03.669 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'spring.mvc-org.springframework.boot.autoconfigure.web.servlet.WebMvcProperties'
2025-06-07 16:02:03.672 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Autowiring by type from bean name 'dispatcherServlet' via factory method to bean named 'spring.http-org.springframework.boot.autoconfigure.http.HttpProperties'
2025-06-07 16:02:03.672 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Autowiring by type from bean name 'dispatcherServlet' via factory method to bean named 'spring.mvc-org.springframework.boot.autoconfigure.web.servlet.WebMvcProperties'
2025-06-07 16:02:03.686 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Autowiring by type from bean name 'dispatcherServletRegistration' via factory method to bean named 'dispatcherServlet'
2025-06-07 16:02:03.686 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Autowiring by type from bean name 'dispatcherServletRegistration' via factory method to bean named 'spring.mvc-org.springframework.boot.autoconfigure.web.servlet.WebMvcProperties'
2025-06-07 16:02:03.687 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'multipartConfigElement'
2025-06-07 16:02:03.687 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.MultipartAutoConfiguration'
2025-06-07 16:02:03.688 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'spring.servlet.multipart-org.springframework.boot.autoconfigure.web.servlet.MultipartProperties'
2025-06-07 16:02:03.690 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.web.servlet.MultipartAutoConfiguration' via constructor to bean named 'spring.servlet.multipart-org.springframework.boot.autoconfigure.web.servlet.MultipartProperties'
2025-06-07 16:02:03.697 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Autowiring by type from bean name 'errorPageCustomizer' via factory method to bean named 'dispatcherServletRegistration'
2025-06-07 16:02:03.712 [main] DEBUG o.s.b.w.e.u.UndertowServletWebServerFactory - Code archive: D:\DevelopmentTools\Maven_respository\org\springframework\boot\spring-boot\2.2.1.RELEASE\spring-boot-2.2.1.RELEASE.jar
2025-06-07 16:02:03.712 [main] DEBUG o.s.b.w.e.u.UndertowServletWebServerFactory - Code archive: D:\DevelopmentTools\Maven_respository\org\springframework\boot\spring-boot\2.2.1.RELEASE\spring-boot-2.2.1.RELEASE.jar
2025-06-07 16:02:03.712 [main] DEBUG o.s.b.w.e.u.UndertowServletWebServerFactory - None of the document roots [src/main/webapp, public, static] point to a directory and will be ignored.
2025-06-07 16:02:03.739 [main] WARN  io.undertow.websockets.jsr - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
2025-06-07 16:02:03.752 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-06-07 16:02:03.752 [main] DEBUG o.s.web.context.ContextLoader - Published root WebApplicationContext as ServletContext attribute with name [org.springframework.web.context.WebApplicationContext.ROOT]
2025-06-07 16:02:03.752 [main] INFO  o.s.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 2145 ms
2025-06-07 16:02:03.754 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'statViewServletRegistrationBean'
2025-06-07 16:02:03.754 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'com.alibaba.druid.spring.boot.autoconfigure.stat.DruidStatViewServletConfiguration'
2025-06-07 16:02:03.756 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'spring.datasource.druid-com.alibaba.druid.spring.boot.autoconfigure.properties.DruidStatProperties'
2025-06-07 16:02:03.758 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Autowiring by type from bean name 'statViewServletRegistrationBean' via factory method to bean named 'spring.datasource.druid-com.alibaba.druid.spring.boot.autoconfigure.properties.DruidStatProperties'
2025-06-07 16:02:03.763 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'webStatFilterRegistrationBean'
2025-06-07 16:02:03.763 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'com.alibaba.druid.spring.boot.autoconfigure.stat.DruidWebStatFilterConfiguration'
2025-06-07 16:02:03.764 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Autowiring by type from bean name 'webStatFilterRegistrationBean' via factory method to bean named 'spring.datasource.druid-com.alibaba.druid.spring.boot.autoconfigure.properties.DruidStatProperties'
2025-06-07 16:02:03.773 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'corsFilter'
2025-06-07 16:02:03.773 [main] DEBUG o.s.b.f.s.DefaultListableBeanFactory - Creating shared instance of singleton bean 'corsConfig'
2025-06-07 16:02:03.785 [main] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Unable to start web server; nested exception is java.lang.RuntimeException: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'shiroFilter': FactoryBean threw exception on object creation; nested exception is java.lang.NullPointerException: chainDefinition cannot be null or empty.
2025-06-07 16:02:03.787 [main] DEBUG o.s.b.c.l.ClasspathLoggingApplicationListener - Application failed to start with classpath: [file:/D:/DevelopmentTools/Java/java-8u311/jre/lib/charsets.jar, file:/D:/DevelopmentTools/Java/java-8u311/jre/lib/deploy.jar, file:/D:/DevelopmentTools/Java/java-8u311/jre/lib/ext/access-bridge-32.jar, file:/D:/DevelopmentTools/Java/java-8u311/jre/lib/ext/cldrdata.jar, file:/D:/DevelopmentTools/Java/java-8u311/jre/lib/ext/dnsns.jar, file:/D:/DevelopmentTools/Java/java-8u311/jre/lib/ext/jaccess.jar, file:/D:/DevelopmentTools/Java/java-8u311/jre/lib/ext/jfxrt.jar, file:/D:/DevelopmentTools/Java/java-8u311/jre/lib/ext/localedata.jar, file:/D:/DevelopmentTools/Java/java-8u311/jre/lib/ext/nashorn.jar, file:/D:/DevelopmentTools/Java/java-8u311/jre/lib/ext/sunec.jar, file:/D:/DevelopmentTools/Java/java-8u311/jre/lib/ext/sunjce_provider.jar, file:/D:/DevelopmentTools/Java/java-8u311/jre/lib/ext/sunmscapi.jar, file:/D:/DevelopmentTools/Java/java-8u311/jre/lib/ext/sunpkcs11.jar, file:/D:/DevelopmentTools/Java/java-8u311/jre/lib/ext/zipfs.jar, file:/D:/DevelopmentTools/Java/java-8u311/jre/lib/javaws.jar, file:/D:/DevelopmentTools/Java/java-8u311/jre/lib/jce.jar, file:/D:/DevelopmentTools/Java/java-8u311/jre/lib/jfr.jar, file:/D:/DevelopmentTools/Java/java-8u311/jre/lib/jfxswt.jar, file:/D:/DevelopmentTools/Java/java-8u311/jre/lib/jsse.jar, file:/D:/DevelopmentTools/Java/java-8u311/jre/lib/management-agent.jar, file:/D:/DevelopmentTools/Java/java-8u311/jre/lib/plugin.jar, file:/D:/DevelopmentTools/Java/java-8u311/jre/lib/resources.jar, file:/D:/DevelopmentTools/Java/java-8u311/jre/lib/rt.jar, file:/D:/project/Java%20Projectes/qsadmin/target/classes/, file:/D:/DevelopmentTools/Maven_respository/com/microsoft/sqlserver/mssql-jdbc/12.6.1.jre8/mssql-jdbc-12.6.1.jre8.jar, file:/D:/DevelopmentTools/Maven_respository/org/springframework/boot/spring-boot-starter-web/2.2.1.RELEASE/spring-boot-starter-web-2.2.1.RELEASE.jar, file:/D:/DevelopmentTools/Maven_respository/org/springframework/boot/spring-boot-starter/2.2.1.RELEASE/spring-boot-starter-2.2.1.RELEASE.jar, file:/D:/DevelopmentTools/Maven_respository/org/springframework/boot/spring-boot/2.2.1.RELEASE/spring-boot-2.2.1.RELEASE.jar, file:/D:/DevelopmentTools/Maven_respository/org/springframework/boot/spring-boot-starter-logging/2.2.1.RELEASE/spring-boot-starter-logging-2.2.1.RELEASE.jar, file:/D:/DevelopmentTools/Maven_respository/ch/qos/logback/logback-classic/1.2.3/logback-classic-1.2.3.jar, file:/D:/DevelopmentTools/Maven_respository/ch/qos/logback/logback-core/1.2.3/logback-core-1.2.3.jar, file:/D:/DevelopmentTools/Maven_respository/org/apache/logging/log4j/log4j-to-slf4j/2.12.1/log4j-to-slf4j-2.12.1.jar, file:/D:/DevelopmentTools/Maven_respository/org/apache/logging/log4j/log4j-api/2.12.1/log4j-api-2.12.1.jar, file:/D:/DevelopmentTools/Maven_respository/org/slf4j/jul-to-slf4j/1.7.29/jul-to-slf4j-1.7.29.jar, file:/D:/DevelopmentTools/Maven_respository/jakarta/annotation/jakarta.annotation-api/1.3.5/jakarta.annotation-api-1.3.5.jar, file:/D:/DevelopmentTools/Maven_respository/org/yaml/snakeyaml/1.25/snakeyaml-1.25.jar, file:/D:/DevelopmentTools/Maven_respository/org/springframework/boot/spring-boot-starter-json/2.2.1.RELEASE/spring-boot-starter-json-2.2.1.RELEASE.jar, file:/D:/DevelopmentTools/Maven_respository/com/fasterxml/jackson/datatype/jackson-datatype-jdk8/2.10.0/jackson-datatype-jdk8-2.10.0.jar, file:/D:/DevelopmentTools/Maven_respository/com/fasterxml/jackson/datatype/jackson-datatype-jsr310/2.10.0/jackson-datatype-jsr310-2.10.0.jar, file:/D:/DevelopmentTools/Maven_respository/com/fasterxml/jackson/module/jackson-module-parameter-names/2.10.0/jackson-module-parameter-names-2.10.0.jar, file:/D:/DevelopmentTools/Maven_respository/org/springframework/boot/spring-boot-starter-validation/2.2.1.RELEASE/spring-boot-starter-validation-2.2.1.RELEASE.jar, file:/D:/DevelopmentTools/Maven_respository/jakarta/validation/jakarta.validation-api/2.0.1/jakarta.validation-api-2.0.1.jar, file:/D:/DevelopmentTools/Maven_respository/org/hibernate/validator/hibernate-validator/6.0.18.Final/hibernate-validator-6.0.18.Final.jar, file:/D:/DevelopmentTools/Maven_respository/org/springframework/spring-web/5.2.1.RELEASE/spring-web-5.2.1.RELEASE.jar, file:/D:/DevelopmentTools/Maven_respository/org/springframework/spring-webmvc/5.2.1.RELEASE/spring-webmvc-5.2.1.RELEASE.jar, file:/D:/DevelopmentTools/Maven_respository/org/springframework/boot/spring-boot-starter-undertow/2.2.1.RELEASE/spring-boot-starter-undertow-2.2.1.RELEASE.jar, file:/D:/DevelopmentTools/Maven_respository/io/undertow/undertow-core/2.0.27.Final/undertow-core-2.0.27.Final.jar, file:/D:/DevelopmentTools/Maven_respository/org/jboss/logging/jboss-logging/3.4.1.Final/jboss-logging-3.4.1.Final.jar, file:/D:/DevelopmentTools/Maven_respository/org/jboss/xnio/xnio-api/3.3.8.Final/xnio-api-3.3.8.Final.jar, file:/D:/DevelopmentTools/Maven_respository/org/jboss/xnio/xnio-nio/3.3.8.Final/xnio-nio-3.3.8.Final.jar, file:/D:/DevelopmentTools/Maven_respository/io/undertow/undertow-servlet/2.0.27.Final/undertow-servlet-2.0.27.Final.jar, file:/D:/DevelopmentTools/Maven_respository/org/jboss/spec/javax/annotation/jboss-annotations-api_1.2_spec/1.0.2.Final/jboss-annotations-api_1.2_spec-1.0.2.Final.jar, file:/D:/DevelopmentTools/Maven_respository/io/undertow/undertow-websockets-jsr/2.0.27.Final/undertow-websockets-jsr-2.0.27.Final.jar, file:/D:/DevelopmentTools/Maven_respository/org/jboss/spec/javax/websocket/jboss-websocket-api_1.1_spec/1.1.4.Final/jboss-websocket-api_1.1_spec-1.1.4.Final.jar, file:/D:/DevelopmentTools/Maven_respository/jakarta/servlet/jakarta.servlet-api/4.0.3/jakarta.servlet-api-4.0.3.jar, file:/D:/DevelopmentTools/Maven_respository/org/glassfish/jakarta.el/3.0.3/jakarta.el-3.0.3.jar, file:/D:/DevelopmentTools/Maven_respository/net/bytebuddy/byte-buddy/1.10.2/byte-buddy-1.10.2.jar, file:/D:/DevelopmentTools/Maven_respository/org/springframework/spring-core/5.2.1.RELEASE/spring-core-5.2.1.RELEASE.jar, file:/D:/DevelopmentTools/Maven_respository/org/springframework/spring-jcl/5.2.1.RELEASE/spring-jcl-5.2.1.RELEASE.jar, file:/D:/DevelopmentTools/Maven_respository/com/baomidou/dynamic-datasource-spring-boot-starter/3.0.0/dynamic-datasource-spring-boot-starter-3.0.0.jar, file:/D:/DevelopmentTools/Maven_respository/org/springframework/boot/spring-boot-starter-jdbc/2.2.1.RELEASE/spring-boot-starter-jdbc-2.2.1.RELEASE.jar, file:/D:/DevelopmentTools/Maven_respository/com/zaxxer/HikariCP/3.4.1/HikariCP-3.4.1.jar, file:/D:/DevelopmentTools/Maven_respository/org/springframework/spring-jdbc/5.2.1.RELEASE/spring-jdbc-5.2.1.RELEASE.jar, file:/D:/DevelopmentTools/Maven_respository/org/springframework/spring-tx/5.2.1.RELEASE/spring-tx-5.2.1.RELEASE.jar, file:/D:/DevelopmentTools/Maven_respository/org/springframework/boot/spring-boot-starter-aop/2.2.1.RELEASE/spring-boot-starter-aop-2.2.1.RELEASE.jar, file:/D:/DevelopmentTools/Maven_respository/org/aspectj/aspectjweaver/1.9.4/aspectjweaver-1.9.4.jar, file:/D:/DevelopmentTools/Maven_respository/org/apache/tomcat/tomcat-jdbc/7.0.81/tomcat-jdbc-7.0.81.jar, file:/D:/DevelopmentTools/Maven_respository/org/apache/tomcat/tomcat-juli/7.0.81/tomcat-juli-7.0.81.jar, file:/D:/DevelopmentTools/Maven_respository/com/belerweb/pinyin4j/2.5.0/pinyin4j-2.5.0.jar, file:/D:/DevelopmentTools/Maven_respository/org/apache/ant/ant/1.9.7/ant-1.9.7.jar, file:/D:/DevelopmentTools/Maven_respository/org/apache/ant/ant-launcher/1.9.7/ant-launcher-1.9.7.jar, file:/D:/DevelopmentTools/Maven_respository/net/coobird/thumbnailator/0.4.8/thumbnailator-0.4.8.jar, file:/D:/DevelopmentTools/Maven_respository/joda-time/joda-time/2.9.8/joda-time-2.9.8.jar, file:/D:/DevelopmentTools/Maven_respository/org/apache/velocity/velocity-engine-core/2.0/velocity-engine-core-2.0.jar, file:/D:/DevelopmentTools/Maven_respository/org/slf4j/slf4j-api/1.7.29/slf4j-api-1.7.29.jar, file:/D:/DevelopmentTools/Maven_respository/cn/hutool/hutool-all/4.1.2/hutool-all-4.1.2.jar, file:/D:/DevelopmentTools/Maven_respository/org/projectlombok/lombok/1.18.10/lombok-1.18.10.jar, file:/D:/DevelopmentTools/Maven_respository/com/baomidou/mybatis-plus-boot-starter/3.4.3.4/mybatis-plus-boot-starter-3.4.3.4.jar, file:/D:/DevelopmentTools/Maven_respository/com/baomidou/mybatis-plus/3.4.3.4/mybatis-plus-3.4.3.4.jar, file:/D:/DevelopmentTools/Maven_respository/com/baomidou/mybatis-plus-extension/3.4.3.4/mybatis-plus-extension-3.4.3.4.jar, file:/D:/DevelopmentTools/Maven_respository/com/baomidou/mybatis-plus-core/3.4.3.4/mybatis-plus-core-3.4.3.4.jar, file:/D:/DevelopmentTools/Maven_respository/com/baomidou/mybatis-plus-annotation/3.4.3.4/mybatis-plus-annotation-3.4.3.4.jar, file:/D:/DevelopmentTools/Maven_respository/com/github/jsqlparser/jsqlparser/4.2/jsqlparser-4.2.jar, file:/D:/DevelopmentTools/Maven_respository/org/springframework/boot/spring-boot-autoconfigure/2.2.1.RELEASE/spring-boot-autoconfigure-2.2.1.RELEASE.jar, file:/D:/DevelopmentTools/Maven_respository/org/mybatis/spring/boot/mybatis-spring-boot-starter/2.1.0/mybatis-spring-boot-starter-2.1.0.jar, file:/D:/DevelopmentTools/Maven_respository/org/mybatis/spring/boot/mybatis-spring-boot-autoconfigure/2.1.0/mybatis-spring-boot-autoconfigure-2.1.0.jar, file:/D:/DevelopmentTools/Maven_respository/org/mybatis/mybatis-spring/2.0.2/mybatis-spring-2.0.2.jar, file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-swagger2/2.9.2/springfox-swagger2-2.9.2.jar, file:/D:/DevelopmentTools/Maven_respository/io/swagger/swagger-annotations/1.5.20/swagger-annotations-1.5.20.jar, file:/D:/DevelopmentTools/Maven_respository/io/swagger/swagger-models/1.5.20/swagger-models-1.5.20.jar, file:/D:/DevelopmentTools/Maven_respository/com/fasterxml/jackson/core/jackson-annotations/2.10.0/jackson-annotations-2.10.0.jar, file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spi/2.9.2/springfox-spi-2.9.2.jar, file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-core/2.9.2/springfox-core-2.9.2.jar, file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-schema/2.9.2/springfox-schema-2.9.2.jar, file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-swagger-common/2.9.2/springfox-swagger-common-2.9.2.jar, file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar, file:/D:/DevelopmentTools/Maven_respository/com/fasterxml/classmate/1.5.1/classmate-1.5.1.jar, file:/D:/DevelopmentTools/Maven_respository/org/springframework/plugin/spring-plugin-core/1.2.0.RELEASE/spring-plugin-core-1.2.0.RELEASE.jar, file:/D:/DevelopmentTools/Maven_respository/org/springframework/plugin/spring-plugin-metadata/1.2.0.RELEASE/spring-plugin-metadata-1.2.0.RELEASE.jar, file:/D:/DevelopmentTools/Maven_respository/org/mapstruct/mapstruct/1.2.0.Final/mapstruct-1.2.0.Final.jar, file:/D:/DevelopmentTools/Maven_respository/io/springfox/springfox-swagger-ui/2.9.2/springfox-swagger-ui-2.9.2.jar, file:/D:/DevelopmentTools/Maven_respository/org/apache/commons/commons-lang3/3.8.1/commons-lang3-3.8.1.jar, file:/D:/DevelopmentTools/Maven_respository/com/google/guava/guava/27.0-jre/guava-27.0-jre.jar, file:/D:/DevelopmentTools/Maven_respository/com/google/guava/failureaccess/1.0/failureaccess-1.0.jar, file:/D:/DevelopmentTools/Maven_respository/com/google/guava/listenablefuture/9999.0-empty-to-avoid-conflict-with-guava/listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar, file:/D:/DevelopmentTools/Maven_respository/com/google/code/findbugs/jsr305/3.0.2/jsr305-3.0.2.jar, file:/D:/DevelopmentTools/Maven_respository/org/checkerframework/checker-qual/2.5.2/checker-qual-2.5.2.jar, file:/D:/DevelopmentTools/Maven_respository/com/google/errorprone/error_prone_annotations/2.2.0/error_prone_annotations-2.2.0.jar, file:/D:/DevelopmentTools/Maven_respository/com/google/j2objc/j2objc-annotations/1.1/j2objc-annotations-1.1.jar, file:/D:/DevelopmentTools/Maven_respository/org/codehaus/mojo/animal-sniffer-annotations/1.17/animal-sniffer-annotations-1.17.jar, file:/D:/DevelopmentTools/Maven_respository/com/alibaba/fastjson/1.2.83/fastjson-1.2.83.jar, file:/D:/DevelopmentTools/Maven_respository/com/alibaba/druid-spring-boot-starter/1.1.10/druid-spring-boot-starter-1.1.10.jar, file:/D:/DevelopmentTools/Maven_respository/com/alibaba/druid/1.1.10/druid-1.1.10.jar, file:/D:/DevelopmentTools/Maven_respository/org/apache/shiro/shiro-spring/1.4.0/shiro-spring-1.4.0.jar, file:/D:/DevelopmentTools/Maven_respository/org/apache/shiro/shiro-core/1.4.0/shiro-core-1.4.0.jar, file:/D:/DevelopmentTools/Maven_respository/org/apache/shiro/shiro-lang/1.4.0/shiro-lang-1.4.0.jar, file:/D:/DevelopmentTools/Maven_respository/org/apache/shiro/shiro-cache/1.4.0/shiro-cache-1.4.0.jar, file:/D:/DevelopmentTools/Maven_respository/org/apache/shiro/shiro-crypto-hash/1.4.0/shiro-crypto-hash-1.4.0.jar, file:/D:/DevelopmentTools/Maven_respository/org/apache/shiro/shiro-crypto-core/1.4.0/shiro-crypto-core-1.4.0.jar, file:/D:/DevelopmentTools/Maven_respository/org/apache/shiro/shiro-crypto-cipher/1.4.0/shiro-crypto-cipher-1.4.0.jar, file:/D:/DevelopmentTools/Maven_respository/org/apache/shiro/shiro-config-core/1.4.0/shiro-config-core-1.4.0.jar, file:/D:/DevelopmentTools/Maven_respository/org/apache/shiro/shiro-config-ogdl/1.4.0/shiro-config-ogdl-1.4.0.jar, file:/D:/DevelopmentTools/Maven_respository/commons-beanutils/commons-beanutils/1.9.3/commons-beanutils-1.9.3.jar, file:/D:/DevelopmentTools/Maven_respository/commons-collections/commons-collections/3.2.2/commons-collections-3.2.2.jar, file:/D:/DevelopmentTools/Maven_respository/org/apache/shiro/shiro-event/1.4.0/shiro-event-1.4.0.jar, file:/D:/DevelopmentTools/Maven_respository/org/apache/shiro/shiro-web/1.4.0/shiro-web-1.4.0.jar, file:/D:/DevelopmentTools/Maven_respository/com/auth0/java-jwt/3.18.2/java-jwt-3.18.2.jar, file:/D:/DevelopmentTools/Maven_respository/com/fasterxml/jackson/core/jackson-databind/2.10.0/jackson-databind-2.10.0.jar, file:/D:/DevelopmentTools/Maven_respository/com/fasterxml/jackson/core/jackson-core/2.10.0/jackson-core-2.10.0.jar, file:/D:/DevelopmentTools/Maven_respository/org/springframework/spring-context/5.2.23.RELEASE/spring-context-5.2.23.RELEASE.jar, file:/D:/DevelopmentTools/Maven_respository/org/springframework/spring-aop/5.2.1.RELEASE/spring-aop-5.2.1.RELEASE.jar, file:/D:/DevelopmentTools/Maven_respository/org/springframework/spring-beans/5.2.1.RELEASE/spring-beans-5.2.1.RELEASE.jar, file:/D:/DevelopmentTools/Maven_respository/org/springframework/spring-expression/5.2.1.RELEASE/spring-expression-5.2.1.RELEASE.jar, file:/D:/DevelopmentTools/Maven_respository/org/mybatis/mybatis/3.5.7/mybatis-3.5.7.jar, file:/D:/DevelopmentTools/Maven_respository/com/github/ben-manes/caffeine/caffeine/2.8.0/caffeine-2.8.0.jar, file:/D:/DevelopmentTools/Maven_respository/com/github/xiaoymin/swagger-bootstrap-ui/1.9.3/swagger-bootstrap-ui-1.9.3.jar, file:/D:/DevelopmentTools/IntelliJ%20IDEA%202025/lib/idea_rt.jar]
2025-06-07 16:02:03.795 [main] DEBUG o.s.b.a.l.ConditionEvaluationReportLoggingListener - 


============================
CONDITIONS EVALUATION REPORT
============================


Positive matches:
-----------------

   AopAutoConfiguration matched:
      - @ConditionalOnProperty (spring.aop.auto=true) matched (OnPropertyCondition)

   AopAutoConfiguration.AspectJAutoProxyingConfiguration matched:
      - @ConditionalOnClass found required class 'org.aspectj.weaver.Advice' (OnClassCondition)

   AopAutoConfiguration.AspectJAutoProxyingConfiguration.CglibAutoProxyConfiguration matched:
      - @ConditionalOnProperty (spring.aop.proxy-target-class=true) matched (OnPropertyCondition)

   DataSourceAutoConfiguration matched:
      - @ConditionalOnClass found required classes 'javax.sql.DataSource', 'org.springframework.jdbc.datasource.embedded.EmbeddedDatabaseType' (OnClassCondition)

   DataSourceConfiguration.Hikari matched:
      - @ConditionalOnClass found required class 'com.zaxxer.hikari.HikariDataSource' (OnClassCondition)
      - @ConditionalOnProperty (spring.datasource.type=com.zaxxer.hikari.HikariDataSource) matched (OnPropertyCondition)

   DataSourceConfiguration.Tomcat matched:
      - @ConditionalOnClass found required class 'org.apache.tomcat.jdbc.pool.DataSource' (OnClassCondition)
      - @ConditionalOnProperty (spring.datasource.type=org.apache.tomcat.jdbc.pool.DataSource) matched (OnPropertyCondition)

   DataSourceJmxConfiguration matched:
      - @ConditionalOnProperty (spring.jmx.enabled=true) matched (OnPropertyCondition)

   DataSourceJmxConfiguration.Hikari matched:
      - @ConditionalOnClass found required class 'com.zaxxer.hikari.HikariDataSource' (OnClassCondition)

   DataSourcePoolMetadataProvidersConfiguration.HikariPoolDataSourceMetadataProviderConfiguration matched:
      - @ConditionalOnClass found required class 'com.zaxxer.hikari.HikariDataSource' (OnClassCondition)

   DataSourcePoolMetadataProvidersConfiguration.TomcatDataSourcePoolMetadataProviderConfiguration matched:
      - @ConditionalOnClass found required class 'org.apache.tomcat.jdbc.pool.DataSource' (OnClassCondition)

   DataSourceTransactionManagerAutoConfiguration matched:
      - @ConditionalOnClass found required classes 'org.springframework.jdbc.core.JdbcTemplate', 'org.springframework.transaction.PlatformTransactionManager' (OnClassCondition)

   DataSourceTransactionManagerAutoConfiguration.DataSourceTransactionManagerConfiguration matched:
      - @ConditionalOnBean (types: javax.sql.DataSource; SearchStrategy: all) found a primary bean from beans 'dataSource' (OnBeanCondition)

   DataSourceTransactionManagerAutoConfiguration.DataSourceTransactionManagerConfiguration#transactionManager matched:
      - @ConditionalOnBean (types: org.springframework.transaction.PlatformTransactionManager; SearchStrategy: all) did not find any beans (OnBeanCondition)

   DispatcherServletAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.web.servlet.DispatcherServlet' (OnClassCondition)
      - found 'session' scope (OnWebApplicationCondition)

   DispatcherServletAutoConfiguration.DispatcherServletConfiguration matched:
      - @ConditionalOnClass found required class 'javax.servlet.ServletRegistration' (OnClassCondition)
      - Default DispatcherServlet did not find dispatcher servlet beans (DispatcherServletAutoConfiguration.DefaultDispatcherServletCondition)

   DispatcherServletAutoConfiguration.DispatcherServletRegistrationConfiguration matched:
      - @ConditionalOnClass found required class 'javax.servlet.ServletRegistration' (OnClassCondition)
      - DispatcherServlet Registration did not find servlet registration bean (DispatcherServletAutoConfiguration.DispatcherServletRegistrationCondition)

   DispatcherServletAutoConfiguration.DispatcherServletRegistrationConfiguration#dispatcherServletRegistration matched:
      - @ConditionalOnBean (names: dispatcherServlet types: org.springframework.web.servlet.DispatcherServlet; SearchStrategy: all) found bean 'dispatcherServlet' (OnBeanCondition)

   DruidDynamicDataSourceConfiguration matched:
      - @ConditionalOnClass found required class 'com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure' (OnClassCondition)

   DruidFilterConfiguration#statFilter matched:
      - @ConditionalOnProperty (spring.datasource.druid.filter.stat.enabled) matched (OnPropertyCondition)
      - @ConditionalOnBean (types: com.alibaba.druid.filter.stat.StatFilter; SearchStrategy: all) did not find any beans (OnBeanCondition)

   DruidStatViewServletConfiguration matched:
      - @ConditionalOnWebApplication (required) found 'session' scope (OnWebApplicationCondition)
      - @ConditionalOnProperty (spring.datasource.druid.stat-view-servlet.enabled=true) matched (OnPropertyCondition)

   DruidWebStatFilterConfiguration matched:
      - @ConditionalOnWebApplication (required) found 'session' scope (OnWebApplicationCondition)
      - @ConditionalOnProperty (spring.datasource.druid.web-stat-filter.enabled=true) matched (OnPropertyCondition)

   DynamicDataSourceAutoConfiguration matched:
      - @ConditionalOnProperty (spring.datasource.dynamic.enabled=true) matched (OnPropertyCondition)

   DynamicDataSourceAutoConfiguration#dataSourceCreator matched:
      - @ConditionalOnBean (types: com.baomidou.dynamic.datasource.creator.DataSourceCreator; SearchStrategy: all) did not find any beans (OnBeanCondition)

   DynamicDataSourceAutoConfiguration#dsProcessor matched:
      - @ConditionalOnBean (types: com.baomidou.dynamic.datasource.processor.DsProcessor; SearchStrategy: all) did not find any beans (OnBeanCondition)

   DynamicDataSourceAutoConfiguration#dynamicDataSourceProvider matched:
      - @ConditionalOnBean (types: com.baomidou.dynamic.datasource.provider.DynamicDataSourceProvider; SearchStrategy: all) did not find any beans (OnBeanCondition)

   DynamicDataSourceAutoConfiguration#dynamicDatasourceAnnotationAdvisor matched:
      - @ConditionalOnBean (types: com.baomidou.dynamic.datasource.aop.DynamicDataSourceAnnotationAdvisor; SearchStrategy: all) did not find any beans (OnBeanCondition)

   EmbeddedWebServerFactoryCustomizerAutoConfiguration matched:
      - @ConditionalOnWebApplication (required) found 'session' scope (OnWebApplicationCondition)

   EmbeddedWebServerFactoryCustomizerAutoConfiguration.UndertowWebServerFactoryCustomizerConfiguration matched:
      - @ConditionalOnClass found required classes 'io.undertow.Undertow', 'org.xnio.SslClientAuthMode' (OnClassCondition)

   ErrorMvcAutoConfiguration matched:
      - @ConditionalOnClass found required classes 'javax.servlet.Servlet', 'org.springframework.web.servlet.DispatcherServlet' (OnClassCondition)
      - found 'session' scope (OnWebApplicationCondition)

   ErrorMvcAutoConfiguration#basicErrorController matched:
      - @ConditionalOnBean (types: org.springframework.boot.web.servlet.error.ErrorController; SearchStrategy: current) did not find any beans (OnBeanCondition)

   ErrorMvcAutoConfiguration#errorAttributes matched:
      - @ConditionalOnBean (types: org.springframework.boot.web.servlet.error.ErrorAttributes; SearchStrategy: current) did not find any beans (OnBeanCondition)

   ErrorMvcAutoConfiguration.DefaultErrorViewResolverConfiguration#conventionErrorViewResolver matched:
      - @ConditionalOnBean (types: org.springframework.web.servlet.DispatcherServlet; SearchStrategy: all) found bean 'dispatcherServlet'; @ConditionalOnBean (types: org.springframework.boot.autoconfigure.web.servlet.error.ErrorViewResolver; SearchStrategy: all) did not find any beans (OnBeanCondition)

   ErrorMvcAutoConfiguration.WhitelabelErrorViewConfiguration matched:
      - @ConditionalOnProperty (server.error.whitelabel.enabled) matched (OnPropertyCondition)
      - ErrorTemplate Missing did not find error template view (ErrorMvcAutoConfiguration.ErrorTemplateMissingCondition)

   ErrorMvcAutoConfiguration.WhitelabelErrorViewConfiguration#beanNameViewResolver matched:
      - @ConditionalOnBean (types: org.springframework.web.servlet.view.BeanNameViewResolver; SearchStrategy: all) did not find any beans (OnBeanCondition)

   ErrorMvcAutoConfiguration.WhitelabelErrorViewConfiguration#defaultErrorView matched:
      - @ConditionalOnBean (names: error; SearchStrategy: all) did not find any beans (OnBeanCondition)

   HttpEncodingAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.web.filter.CharacterEncodingFilter' (OnClassCondition)
      - found 'session' scope (OnWebApplicationCondition)
      - @ConditionalOnProperty (spring.http.encoding.enabled) matched (OnPropertyCondition)

   HttpEncodingAutoConfiguration#characterEncodingFilter matched:
      - @ConditionalOnBean (types: org.springframework.web.filter.CharacterEncodingFilter; SearchStrategy: all) did not find any beans (OnBeanCondition)

   HttpMessageConvertersAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.http.converter.HttpMessageConverter' (OnClassCondition)
      - NoneNestedConditions 0 matched 1 did not; NestedCondition on HttpMessageConvertersAutoConfiguration.NotReactiveWebApplicationCondition.ReactiveWebApplication did not find reactive web application classes (HttpMessageConvertersAutoConfiguration.NotReactiveWebApplicationCondition)

   HttpMessageConvertersAutoConfiguration#messageConverters matched:
      - @ConditionalOnBean (types: org.springframework.boot.autoconfigure.http.HttpMessageConverters; SearchStrategy: all) did not find any beans (OnBeanCondition)

   HttpMessageConvertersAutoConfiguration.StringHttpMessageConverterConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.http.converter.StringHttpMessageConverter' (OnClassCondition)

   HttpMessageConvertersAutoConfiguration.StringHttpMessageConverterConfiguration#stringHttpMessageConverter matched:
      - @ConditionalOnBean (types: org.springframework.http.converter.StringHttpMessageConverter; SearchStrategy: all) did not find any beans (OnBeanCondition)

   JacksonAutoConfiguration matched:
      - @ConditionalOnClass found required class 'com.fasterxml.jackson.databind.ObjectMapper' (OnClassCondition)

   JacksonAutoConfiguration.Jackson2ObjectMapperBuilderCustomizerConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.http.converter.json.Jackson2ObjectMapperBuilder' (OnClassCondition)

   JacksonAutoConfiguration.JacksonObjectMapperBuilderConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.http.converter.json.Jackson2ObjectMapperBuilder' (OnClassCondition)

   JacksonAutoConfiguration.JacksonObjectMapperBuilderConfiguration#jacksonObjectMapperBuilder matched:
      - @ConditionalOnBean (types: org.springframework.http.converter.json.Jackson2ObjectMapperBuilder; SearchStrategy: all) did not find any beans (OnBeanCondition)

   JacksonAutoConfiguration.JacksonObjectMapperConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.http.converter.json.Jackson2ObjectMapperBuilder' (OnClassCondition)

   JacksonAutoConfiguration.JacksonObjectMapperConfiguration#jacksonObjectMapper matched:
      - @ConditionalOnBean (types: com.fasterxml.jackson.databind.ObjectMapper; SearchStrategy: all) did not find any beans (OnBeanCondition)

   JacksonAutoConfiguration.ParameterNamesModuleConfiguration matched:
      - @ConditionalOnClass found required class 'com.fasterxml.jackson.module.paramnames.ParameterNamesModule' (OnClassCondition)

   JacksonAutoConfiguration.ParameterNamesModuleConfiguration#parameterNamesModule matched:
      - @ConditionalOnBean (types: com.fasterxml.jackson.module.paramnames.ParameterNamesModule; SearchStrategy: all) did not find any beans (OnBeanCondition)

   JacksonHttpMessageConvertersConfiguration.MappingJackson2HttpMessageConverterConfiguration matched:
      - @ConditionalOnClass found required class 'com.fasterxml.jackson.databind.ObjectMapper' (OnClassCondition)
      - @ConditionalOnProperty (spring.http.converters.preferred-json-mapper=jackson) matched (OnPropertyCondition)
      - @ConditionalOnBean (types: com.fasterxml.jackson.databind.ObjectMapper; SearchStrategy: all) found bean 'jacksonObjectMapper' (OnBeanCondition)

   JacksonHttpMessageConvertersConfiguration.MappingJackson2HttpMessageConverterConfiguration#mappingJackson2HttpMessageConverter matched:
      - @ConditionalOnBean (types: org.springframework.http.converter.json.MappingJackson2HttpMessageConverter ignored: org.springframework.hateoas.server.mvc.TypeConstrainedMappingJackson2HttpMessageConverter,org.springframework.data.rest.webmvc.alps.AlpsJsonHttpMessageConverter; SearchStrategy: all) did not find any beans (OnBeanCondition)

   JdbcTemplateAutoConfiguration matched:
      - @ConditionalOnClass found required classes 'javax.sql.DataSource', 'org.springframework.jdbc.core.JdbcTemplate' (OnClassCondition)
      - @ConditionalOnBean (types: javax.sql.DataSource; SearchStrategy: all) found a primary bean from beans 'dataSource' (OnBeanCondition)

   JdbcTemplateConfiguration matched:
      - @ConditionalOnBean (types: org.springframework.jdbc.core.JdbcOperations; SearchStrategy: all) did not find any beans (OnBeanCondition)

   JmxAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.jmx.export.MBeanExporter' (OnClassCondition)
      - @ConditionalOnProperty (spring.jmx.enabled=true) matched (OnPropertyCondition)

   JmxAutoConfiguration#mbeanExporter matched:
      - @ConditionalOnBean (types: org.springframework.jmx.export.MBeanExporter; SearchStrategy: current) did not find any beans (OnBeanCondition)

   JmxAutoConfiguration#mbeanServer matched:
      - @ConditionalOnBean (types: javax.management.MBeanServer; SearchStrategy: all) did not find any beans (OnBeanCondition)

   JmxAutoConfiguration#objectNamingStrategy matched:
      - @ConditionalOnBean (types: org.springframework.jmx.export.naming.ObjectNamingStrategy; SearchStrategy: current) did not find any beans (OnBeanCondition)

   MultipartAutoConfiguration matched:
      - @ConditionalOnClass found required classes 'javax.servlet.Servlet', 'org.springframework.web.multipart.support.StandardServletMultipartResolver', 'javax.servlet.MultipartConfigElement' (OnClassCondition)
      - found 'session' scope (OnWebApplicationCondition)
      - @ConditionalOnProperty (spring.servlet.multipart.enabled) matched (OnPropertyCondition)

   MultipartAutoConfiguration#multipartConfigElement matched:
      - @ConditionalOnBean (types: javax.servlet.MultipartConfigElement,org.springframework.web.multipart.commons.CommonsMultipartResolver; SearchStrategy: all) did not find any beans (OnBeanCondition)

   MultipartAutoConfiguration#multipartResolver matched:
      - @ConditionalOnBean (types: org.springframework.web.multipart.MultipartResolver; SearchStrategy: all) did not find any beans (OnBeanCondition)

   MybatisAutoConfiguration matched:
      - @ConditionalOnClass found required classes 'org.apache.ibatis.session.SqlSessionFactory', 'org.mybatis.spring.SqlSessionFactoryBean' (OnClassCondition)
      - @ConditionalOnBean (types: javax.sql.DataSource; SearchStrategy: all) found a primary bean from beans 'dataSource' (OnBeanCondition)

   MybatisLanguageDriverAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.apache.ibatis.scripting.LanguageDriver' (OnClassCondition)

   MybatisPlusAutoConfiguration matched:
      - @ConditionalOnClass found required classes 'org.apache.ibatis.session.SqlSessionFactory', 'org.mybatis.spring.SqlSessionFactoryBean' (OnClassCondition)
      - @ConditionalOnBean (types: javax.sql.DataSource; SearchStrategy: all) found a primary bean from beans 'dataSource' (OnBeanCondition)

   MybatisPlusAutoConfiguration#sqlSessionTemplate matched:
      - @ConditionalOnBean (types: org.mybatis.spring.SqlSessionTemplate; SearchStrategy: all) did not find any beans (OnBeanCondition)

   MybatisPlusLanguageDriverAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.apache.ibatis.scripting.LanguageDriver' (OnClassCondition)

   NamedParameterJdbcTemplateConfiguration matched:
      - @ConditionalOnBean (types: org.springframework.jdbc.core.JdbcTemplate; SearchStrategy: all) found a primary bean from beans 'jdbcTemplate'; @ConditionalOnBean (types: org.springframework.jdbc.core.namedparam.NamedParameterJdbcOperations; SearchStrategy: all) did not find any beans (OnBeanCondition)

   OAuth2ResourceServerAutoConfiguration matched:
      - found 'session' scope (OnWebApplicationCondition)

   PersistenceExceptionTranslationAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.dao.annotation.PersistenceExceptionTranslationPostProcessor' (OnClassCondition)

   PersistenceExceptionTranslationAutoConfiguration#persistenceExceptionTranslationPostProcessor matched:
      - @ConditionalOnProperty (spring.dao.exceptiontranslation.enabled) matched (OnPropertyCondition)
      - @ConditionalOnBean (types: org.springframework.dao.annotation.PersistenceExceptionTranslationPostProcessor; SearchStrategy: all) did not find any beans (OnBeanCondition)

   PropertyPlaceholderAutoConfiguration#propertySourcesPlaceholderConfigurer matched:
      - @ConditionalOnBean (types: org.springframework.context.support.PropertySourcesPlaceholderConfigurer; SearchStrategy: current) did not find any beans (OnBeanCondition)

   RestTemplateAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.web.client.RestTemplate' (OnClassCondition)
      - NoneNestedConditions 0 matched 1 did not; NestedCondition on RestTemplateAutoConfiguration.NotReactiveWebApplicationCondition.ReactiveWebApplication did not find reactive web application classes (RestTemplateAutoConfiguration.NotReactiveWebApplicationCondition)

   RestTemplateAutoConfiguration#restTemplateBuilder matched:
      - @ConditionalOnBean (types: org.springframework.boot.web.client.RestTemplateBuilder; SearchStrategy: all) did not find any beans (OnBeanCondition)

   ServletWebServerFactoryAutoConfiguration matched:
      - @ConditionalOnClass found required class 'javax.servlet.ServletRequest' (OnClassCondition)
      - found 'session' scope (OnWebApplicationCondition)

   ServletWebServerFactoryConfiguration.EmbeddedUndertow matched:
      - @ConditionalOnClass found required classes 'javax.servlet.Servlet', 'io.undertow.Undertow', 'org.xnio.SslClientAuthMode' (OnClassCondition)
      - @ConditionalOnBean (types: org.springframework.boot.web.servlet.server.ServletWebServerFactory; SearchStrategy: current) did not find any beans (OnBeanCondition)

   SpringApplicationAdminJmxAutoConfiguration matched:
      - @ConditionalOnProperty (spring.application.admin.enabled=true) matched (OnPropertyCondition)

   SpringApplicationAdminJmxAutoConfiguration#springApplicationAdminRegistrar matched:
      - @ConditionalOnBean (types: org.springframework.boot.admin.SpringApplicationAdminMXBeanRegistrar; SearchStrategy: all) did not find any beans (OnBeanCondition)

   Swagger2DocumentationConfiguration matched:
      - @ConditionalOnWebApplication (required) found 'session' scope (OnWebApplicationCondition)

   TaskExecutionAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor' (OnClassCondition)

   TaskExecutionAutoConfiguration#applicationTaskExecutor matched:
      - @ConditionalOnBean (types: java.util.concurrent.Executor; SearchStrategy: all) did not find any beans (OnBeanCondition)

   TaskExecutionAutoConfiguration#taskExecutorBuilder matched:
      - @ConditionalOnBean (types: org.springframework.boot.task.TaskExecutorBuilder; SearchStrategy: all) did not find any beans (OnBeanCondition)

   TaskSchedulingAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler' (OnClassCondition)

   TaskSchedulingAutoConfiguration#taskScheduler matched:
      - @ConditionalOnBean (names: org.springframework.context.annotation.internalScheduledAnnotationProcessor; SearchStrategy: all) found bean 'org.springframework.context.annotation.internalScheduledAnnotationProcessor'; @ConditionalOnBean (types: org.springframework.scheduling.annotation.SchedulingConfigurer,org.springframework.scheduling.TaskScheduler,java.util.concurrent.ScheduledExecutorService; SearchStrategy: all) did not find any beans (OnBeanCondition)

   TaskSchedulingAutoConfiguration#taskSchedulerBuilder matched:
      - @ConditionalOnBean (types: org.springframework.boot.task.TaskSchedulerBuilder; SearchStrategy: all) did not find any beans (OnBeanCondition)

   TransactionAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.transaction.PlatformTransactionManager' (OnClassCondition)

   TransactionAutoConfiguration#platformTransactionManagerCustomizers matched:
      - @ConditionalOnBean (types: org.springframework.boot.autoconfigure.transaction.TransactionManagerCustomizers; SearchStrategy: all) did not find any beans (OnBeanCondition)

   TransactionAutoConfiguration.EnableTransactionManagementConfiguration matched:
      - @ConditionalOnBean (types: org.springframework.transaction.TransactionManager; SearchStrategy: all) found bean 'transactionManager'; @ConditionalOnBean (types: org.springframework.transaction.annotation.AbstractTransactionManagementConfiguration; SearchStrategy: all) did not find any beans (OnBeanCondition)

   TransactionAutoConfiguration.EnableTransactionManagementConfiguration.CglibAutoProxyConfiguration matched:
      - @ConditionalOnProperty (spring.aop.proxy-target-class=true) matched (OnPropertyCondition)

   TransactionAutoConfiguration.TransactionTemplateConfiguration matched:
      - @ConditionalOnBean (types: org.springframework.transaction.PlatformTransactionManager; SearchStrategy: all) found a primary bean from beans 'transactionManager' (OnBeanCondition)

   TransactionAutoConfiguration.TransactionTemplateConfiguration#transactionTemplate matched:
      - @ConditionalOnBean (types: org.springframework.transaction.support.TransactionOperations; SearchStrategy: all) did not find any beans (OnBeanCondition)

   ValidationAutoConfiguration matched:
      - @ConditionalOnClass found required class 'javax.validation.executable.ExecutableValidator' (OnClassCondition)
      - @ConditionalOnResource found location classpath:META-INF/services/javax.validation.spi.ValidationProvider (OnResourceCondition)

   ValidationAutoConfiguration#defaultValidator matched:
      - @ConditionalOnBean (types: javax.validation.Validator; SearchStrategy: all) did not find any beans (OnBeanCondition)

   ValidationAutoConfiguration#methodValidationPostProcessor matched:
      - @ConditionalOnBean (types: org.springframework.validation.beanvalidation.MethodValidationPostProcessor; SearchStrategy: all) did not find any beans (OnBeanCondition)

   WebMvcAutoConfiguration matched:
      - @ConditionalOnClass found required classes 'javax.servlet.Servlet', 'org.springframework.web.servlet.DispatcherServlet', 'org.springframework.web.servlet.config.annotation.WebMvcConfigurer' (OnClassCondition)
      - found 'session' scope (OnWebApplicationCondition)
      - @ConditionalOnBean (types: org.springframework.web.servlet.config.annotation.WebMvcConfigurationSupport; SearchStrategy: all) did not find any beans (OnBeanCondition)

   WebMvcAutoConfiguration#formContentFilter matched:
      - @ConditionalOnProperty (spring.mvc.formcontent.filter.enabled) matched (OnPropertyCondition)
      - @ConditionalOnBean (types: org.springframework.web.filter.FormContentFilter; SearchStrategy: all) did not find any beans (OnBeanCondition)

   WebMvcAutoConfiguration.WebMvcAutoConfigurationAdapter#defaultViewResolver matched:
      - @ConditionalOnBean (types: org.springframework.web.servlet.view.InternalResourceViewResolver; SearchStrategy: all) did not find any beans (OnBeanCondition)

   WebMvcAutoConfiguration.WebMvcAutoConfigurationAdapter#requestContextFilter matched:
      - @ConditionalOnBean (types: org.springframework.web.context.request.RequestContextListener,org.springframework.web.filter.RequestContextFilter; SearchStrategy: all) did not find any beans (OnBeanCondition)

   WebMvcAutoConfiguration.WebMvcAutoConfigurationAdapter#viewResolver matched:
      - @ConditionalOnBean (types: org.springframework.web.servlet.ViewResolver; SearchStrategy: all) found beans 'defaultViewResolver', 'beanNameViewResolver', 'mvcViewResolver'; @ConditionalOnBean (names: viewResolver types: org.springframework.web.servlet.view.ContentNegotiatingViewResolver; SearchStrategy: all) did not find any beans (OnBeanCondition)

   WebSocketServletAutoConfiguration matched:
      - @ConditionalOnClass found required classes 'javax.servlet.Servlet', 'javax.websocket.server.ServerContainer' (OnClassCondition)
      - found 'session' scope (OnWebApplicationCondition)

   WebSocketServletAutoConfiguration.UndertowWebSocketConfiguration matched:
      - @ConditionalOnClass found required class 'io.undertow.websockets.jsr.Bootstrap' (OnClassCondition)

   WebSocketServletAutoConfiguration.UndertowWebSocketConfiguration#websocketServletWebServerCustomizer matched:
      - @ConditionalOnBean (names: websocketServletWebServerCustomizer; SearchStrategy: all) did not find any beans (OnBeanCondition)


Negative matches:
-----------------

   ActiveMQAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'javax.jms.ConnectionFactory' (OnClassCondition)

   AopAutoConfiguration.AspectJAutoProxyingConfiguration.JdkDynamicAutoProxyConfiguration:
      Did not match:
         - @ConditionalOnProperty (spring.aop.proxy-target-class=false) did not find property 'proxy-target-class' (OnPropertyCondition)

   AopAutoConfiguration.ClassProxyingConfiguration:
      Did not match:
         - @ConditionalOnMissingClass found unwanted class 'org.aspectj.weaver.Advice' (OnClassCondition)

   ArtemisAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'javax.jms.ConnectionFactory' (OnClassCondition)

   BatchAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.batch.core.launch.JobLauncher' (OnClassCondition)

   CacheAutoConfiguration:
      Did not match:
         - @ConditionalOnBean (types: org.springframework.cache.interceptor.CacheAspectSupport; SearchStrategy: all) did not find any beans of type org.springframework.cache.interceptor.CacheAspectSupport (OnBeanCondition)
      Matched:
         - @ConditionalOnClass found required class 'org.springframework.cache.CacheManager' (OnClassCondition)

   CacheAutoConfiguration.CacheManagerEntityManagerFactoryDependsOnPostProcessor:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean' (OnClassCondition)
         - Ancestor org.springframework.boot.autoconfigure.cache.CacheAutoConfiguration did not match (ConditionEvaluationReport.AncestorsMatchedCondition)

   CaffeineCacheConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.cache.caffeine.CaffeineCacheManager' (OnClassCondition)

   CassandraAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.datastax.driver.core.Cluster' (OnClassCondition)

   CassandraDataAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.datastax.driver.core.Cluster' (OnClassCondition)

   CassandraReactiveDataAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.datastax.driver.core.Cluster' (OnClassCondition)

   CassandraReactiveRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.data.cassandra.ReactiveSession' (OnClassCondition)

   CassandraRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.datastax.driver.core.Session' (OnClassCondition)

   ClientHttpConnectorAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.web.reactive.function.client.WebClient' (OnClassCondition)

   CloudServiceConnectorsAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.cloud.config.java.CloudScanConfiguration' (OnClassCondition)

   CodecsAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.web.reactive.function.client.WebClient' (OnClassCondition)

   CouchbaseAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.couchbase.client.java.Cluster' (OnClassCondition)

   CouchbaseCacheConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required classes 'com.couchbase.client.java.Bucket', 'com.couchbase.client.spring.cache.CouchbaseCacheManager' (OnClassCondition)

   CouchbaseDataAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.couchbase.client.java.Bucket' (OnClassCondition)

   CouchbaseReactiveDataAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.couchbase.client.java.Bucket' (OnClassCondition)

   CouchbaseReactiveRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.couchbase.client.java.Bucket' (OnClassCondition)

   CouchbaseRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.couchbase.client.java.Bucket' (OnClassCondition)

   DataSourceAutoConfiguration.EmbeddedDatabaseConfiguration:
      Did not match:
         - EmbeddedDataSource found supported pooled data source (DataSourceAutoConfiguration.EmbeddedDatabaseCondition)

   DataSourceAutoConfiguration.PooledDataSourceConfiguration:
      Did not match:
         - @ConditionalOnBean (types: javax.sql.DataSource,javax.sql.XADataSource; SearchStrategy: all) found beans of type 'javax.sql.DataSource' dataSource (OnBeanCondition)
      Matched:
         - AnyNestedCondition 1 matched 1 did not; NestedCondition on DataSourceAutoConfiguration.PooledDataSourceCondition.PooledDataSourceAvailable PooledDataSource found supported DataSource; NestedCondition on DataSourceAutoConfiguration.PooledDataSourceCondition.ExplicitType @ConditionalOnProperty (spring.datasource.type) did not find property 'type' (DataSourceAutoConfiguration.PooledDataSourceCondition)

   DataSourceConfiguration.Dbcp2:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.apache.commons.dbcp2.BasicDataSource' (OnClassCondition)

   DataSourceConfiguration.Generic:
      Did not match:
         - @ConditionalOnProperty (spring.datasource.type) did not find property 'spring.datasource.type' (OnPropertyCondition)

   DataSourceJmxConfiguration.TomcatDataSourceJmxConfiguration:
      Did not match:
         - @ConditionalOnProperty (spring.datasource.tomcat.jmx-enabled) did not find property 'jmx-enabled' (OnPropertyCondition)
      Matched:
         - @ConditionalOnClass found required class 'org.apache.tomcat.jdbc.pool.DataSourceProxy' (OnClassCondition)

   DataSourcePoolMetadataProvidersConfiguration.CommonsDbcp2PoolDataSourceMetadataProviderConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.apache.commons.dbcp2.BasicDataSource' (OnClassCondition)

   DispatcherServletAutoConfiguration.DispatcherServletConfiguration#multipartResolver:
      Did not match:
         - @ConditionalOnBean (types: org.springframework.web.multipart.MultipartResolver; SearchStrategy: all) did not find any beans of type org.springframework.web.multipart.MultipartResolver (OnBeanCondition)

   DruidFilterConfiguration#commonsLogFilter:
      Did not match:
         - @ConditionalOnProperty (spring.datasource.druid.filter.commons-log.enabled) did not find property 'enabled' (OnPropertyCondition)

   DruidFilterConfiguration#configFilter:
      Did not match:
         - @ConditionalOnProperty (spring.datasource.druid.filter.config.enabled) did not find property 'enabled' (OnPropertyCondition)

   DruidFilterConfiguration#encodingConvertFilter:
      Did not match:
         - @ConditionalOnProperty (spring.datasource.druid.filter.encoding.enabled) did not find property 'enabled' (OnPropertyCondition)

   DruidFilterConfiguration#log4j2Filter:
      Did not match:
         - @ConditionalOnProperty (spring.datasource.druid.filter.log4j2.enabled) did not find property 'enabled' (OnPropertyCondition)

   DruidFilterConfiguration#log4jFilter:
      Did not match:
         - @ConditionalOnProperty (spring.datasource.druid.filter.log4j.enabled) did not find property 'enabled' (OnPropertyCondition)

   DruidFilterConfiguration#slf4jLogFilter:
      Did not match:
         - @ConditionalOnProperty (spring.datasource.druid.filter.slf4j.enabled) did not find property 'enabled' (OnPropertyCondition)

   DruidFilterConfiguration#wallConfig:
      Did not match:
         - @ConditionalOnProperty (spring.datasource.druid.filter.wall.enabled) did not find property 'enabled' (OnPropertyCondition)

   DruidFilterConfiguration#wallFilter:
      Did not match:
         - @ConditionalOnProperty (spring.datasource.druid.filter.wall.enabled) did not find property 'enabled' (OnPropertyCondition)

   DruidSpringAopConfiguration:
      Did not match:
         - @ConditionalOnProperty (spring.datasource.druid.aop-patterns) did not find property 'spring.datasource.druid.aop-patterns' (OnPropertyCondition)

   DynamicDataSourceAutoConfiguration#dataSource:
      Did not match:
         - @ConditionalOnBean (types: javax.sql.DataSource; SearchStrategy: all) found beans of type 'javax.sql.DataSource' dataSource (OnBeanCondition)

   DynamicDataSourceAutoConfiguration#dynamicAdvisor:
      Did not match:
         - @ConditionalOnBean (types: com.baomidou.dynamic.datasource.DynamicDataSourceConfigure; SearchStrategy: all) did not find any beans of type com.baomidou.dynamic.datasource.DynamicDataSourceConfigure (OnBeanCondition)

   EhCacheCacheConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required classes 'net.sf.ehcache.Cache', 'org.springframework.cache.ehcache.EhCacheCacheManager' (OnClassCondition)

   ElasticsearchAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.elasticsearch.client.Client' (OnClassCondition)

   ElasticsearchDataAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.data.elasticsearch.core.ElasticsearchTemplate' (OnClassCondition)

   ElasticsearchRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.elasticsearch.client.Client' (OnClassCondition)

   EmbeddedLdapAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.unboundid.ldap.listener.InMemoryDirectoryServer' (OnClassCondition)

   EmbeddedMongoAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.mongodb.MongoClient' (OnClassCondition)

   EmbeddedWebServerFactoryCustomizerAutoConfiguration.JettyWebServerFactoryCustomizerConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required classes 'org.eclipse.jetty.server.Server', 'org.eclipse.jetty.util.Loader', 'org.eclipse.jetty.webapp.WebAppContext' (OnClassCondition)

   EmbeddedWebServerFactoryCustomizerAutoConfiguration.NettyWebServerFactoryCustomizerConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'reactor.netty.http.server.HttpServer' (OnClassCondition)

   EmbeddedWebServerFactoryCustomizerAutoConfiguration.TomcatWebServerFactoryCustomizerConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required classes 'org.apache.catalina.startup.Tomcat', 'org.apache.coyote.UpgradeProtocol' (OnClassCondition)

   ErrorWebFluxAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.web.reactive.config.WebFluxConfigurer' (OnClassCondition)

   FlywayAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.flywaydb.core.Flyway' (OnClassCondition)

   FreeMarkerAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'freemarker.template.Configuration' (OnClassCondition)

   GenericCacheConfiguration:
      Did not match:
         - Cache org.springframework.boot.autoconfigure.cache.GenericCacheConfiguration unknown cache type (CacheCondition)

   GroovyTemplateAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'groovy.text.markup.MarkupTemplateEngine' (OnClassCondition)

   GsonAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.google.gson.Gson' (OnClassCondition)

   GsonHttpMessageConvertersConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.google.gson.Gson' (OnClassCondition)

   H2ConsoleAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.h2.server.web.WebServlet' (OnClassCondition)

   HazelcastAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.hazelcast.core.HazelcastInstance' (OnClassCondition)

   HazelcastCacheConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required classes 'com.hazelcast.core.HazelcastInstance', 'com.hazelcast.spring.cache.HazelcastCacheManager' (OnClassCondition)

   HazelcastJpaDependencyAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.hazelcast.core.HazelcastInstance' (OnClassCondition)

   HibernateJpaAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'javax.persistence.EntityManager' (OnClassCondition)

   HttpHandlerAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.web.reactive.DispatcherHandler' (OnClassCondition)

   HypermediaAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.hateoas.EntityModel' (OnClassCondition)

   IdentifierGeneratorAutoConfiguration.InetUtilsAutoConfig:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.cloud.commons.util.InetUtils' (OnClassCondition)

   InfinispanCacheConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.infinispan.spring.embedded.provider.SpringEmbeddedCacheManager' (OnClassCondition)

   InfluxDbAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.influxdb.InfluxDB' (OnClassCondition)

   IntegrationAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.integration.config.EnableIntegration' (OnClassCondition)

   JCacheCacheConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required classes 'javax.cache.Caching', 'org.springframework.cache.jcache.JCacheCacheManager' (OnClassCondition)

   JacksonAutoConfiguration.JodaDateTimeJacksonConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required classes 'com.fasterxml.jackson.datatype.joda.ser.DateTimeSerializer', 'com.fasterxml.jackson.datatype.joda.cfg.JacksonJodaDateFormat' (OnClassCondition)

   JacksonHttpMessageConvertersConfiguration.MappingJackson2XmlHttpMessageConverterConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.fasterxml.jackson.dataformat.xml.XmlMapper' (OnClassCondition)

   JdbcRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.data.jdbc.repository.config.AbstractJdbcConfiguration' (OnClassCondition)

   JerseyAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.glassfish.jersey.server.spring.SpringComponentProvider' (OnClassCondition)

   JestAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.searchbox.client.JestClient' (OnClassCondition)

   JmsAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'javax.jms.Message' (OnClassCondition)

   JndiConnectionFactoryAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.jms.core.JmsTemplate' (OnClassCondition)

   JndiDataSourceAutoConfiguration:
      Did not match:
         - @ConditionalOnProperty (spring.datasource.jndi-name) did not find property 'jndi-name' (OnPropertyCondition)
      Matched:
         - @ConditionalOnClass found required classes 'javax.sql.DataSource', 'org.springframework.jdbc.datasource.embedded.EmbeddedDatabaseType' (OnClassCondition)

   JooqAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.jooq.DSLContext' (OnClassCondition)

   JpaRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.data.jpa.repository.JpaRepository' (OnClassCondition)

   JsonbAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'javax.json.bind.Jsonb' (OnClassCondition)

   JsonbHttpMessageConvertersConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'javax.json.bind.Jsonb' (OnClassCondition)

   JtaAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'javax.transaction.Transaction' (OnClassCondition)

   KafkaAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.kafka.core.KafkaTemplate' (OnClassCondition)

   LdapAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.ldap.core.ContextSource' (OnClassCondition)

   LdapRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.data.ldap.repository.LdapRepository' (OnClassCondition)

   LiquibaseAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'liquibase.change.DatabaseChange' (OnClassCondition)

   MailSenderAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'javax.mail.internet.MimeMessage' (OnClassCondition)

   MailSenderValidatorAutoConfiguration:
      Did not match:
         - @ConditionalOnSingleCandidate did not find required type 'org.springframework.mail.javamail.JavaMailSenderImpl' (OnBeanCondition)

   MessageSourceAutoConfiguration:
      Did not match:
         - ResourceBundle did not find bundle with basename messages (MessageSourceAutoConfiguration.ResourceBundleCondition)

   MongoAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.mongodb.MongoClient' (OnClassCondition)

   MongoDataAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.mongodb.client.MongoClient' (OnClassCondition)

   MongoReactiveAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.mongodb.reactivestreams.client.MongoClient' (OnClassCondition)

   MongoReactiveDataAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.mongodb.reactivestreams.client.MongoClient' (OnClassCondition)

   MongoReactiveRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.mongodb.reactivestreams.client.MongoClient' (OnClassCondition)

   MongoRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.mongodb.MongoClient' (OnClassCondition)

   MustacheAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.samskivert.mustache.Mustache' (OnClassCondition)

   MybatisAutoConfiguration#sqlSessionFactory:
      Did not match:
         - @ConditionalOnBean (types: org.apache.ibatis.session.SqlSessionFactory; SearchStrategy: all) found beans of type 'org.apache.ibatis.session.SqlSessionFactory' mybatisSqlSessionFactoryBean (OnBeanCondition)

   MybatisAutoConfiguration#sqlSessionTemplate:
      Did not match:
         - @ConditionalOnBean (types: org.mybatis.spring.SqlSessionTemplate; SearchStrategy: all) found beans of type 'org.mybatis.spring.SqlSessionTemplate' sqlSessionTemplate (OnBeanCondition)

   MybatisAutoConfiguration.MapperScannerRegistrarNotFoundConfiguration:
      Did not match:
         - @ConditionalOnBean (types: org.mybatis.spring.mapper.MapperFactoryBean,org.mybatis.spring.mapper.MapperScannerConfigurer; SearchStrategy: all) found beans of type 'org.mybatis.spring.mapper.MapperScannerConfigurer' com.qs.admin.common.configurer.DataSourceConfig#MapperScannerRegistrar#0 (OnBeanCondition)

   MybatisLanguageDriverAutoConfiguration.FreeMarkerConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required classes 'org.mybatis.scripting.freemarker.FreeMarkerLanguageDriver', 'org.mybatis.scripting.freemarker.FreeMarkerLanguageDriverConfig' (OnClassCondition)

   MybatisLanguageDriverAutoConfiguration.LegacyFreeMarkerConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.mybatis.scripting.freemarker.FreeMarkerLanguageDriver' (OnClassCondition)

   MybatisLanguageDriverAutoConfiguration.LegacyVelocityConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.mybatis.scripting.velocity.Driver' (OnClassCondition)

   MybatisLanguageDriverAutoConfiguration.ThymeleafConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.mybatis.scripting.thymeleaf.ThymeleafLanguageDriver' (OnClassCondition)

   MybatisLanguageDriverAutoConfiguration.VelocityConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required classes 'org.mybatis.scripting.velocity.VelocityLanguageDriver', 'org.mybatis.scripting.velocity.VelocityLanguageDriverConfig' (OnClassCondition)

   MybatisPlusAutoConfiguration#sqlSessionFactory:
      Did not match:
         - @ConditionalOnBean (types: org.apache.ibatis.session.SqlSessionFactory; SearchStrategy: all) found beans of type 'org.apache.ibatis.session.SqlSessionFactory' mybatisSqlSessionFactoryBean (OnBeanCondition)

   MybatisPlusAutoConfiguration.MapperScannerRegistrarNotFoundConfiguration:
      Did not match:
         - @ConditionalOnBean (types: org.mybatis.spring.mapper.MapperFactoryBean,org.mybatis.spring.mapper.MapperScannerConfigurer; SearchStrategy: all) found beans of type 'org.mybatis.spring.mapper.MapperScannerConfigurer' com.qs.admin.common.configurer.DataSourceConfig#MapperScannerRegistrar#0 (OnBeanCondition)

   MybatisPlusLanguageDriverAutoConfiguration.FreeMarkerConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required classes 'org.mybatis.scripting.freemarker.FreeMarkerLanguageDriver', 'org.mybatis.scripting.freemarker.FreeMarkerLanguageDriverConfig' (OnClassCondition)

   MybatisPlusLanguageDriverAutoConfiguration.LegacyFreeMarkerConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.mybatis.scripting.freemarker.FreeMarkerLanguageDriver' (OnClassCondition)

   MybatisPlusLanguageDriverAutoConfiguration.ThymeleafConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.mybatis.scripting.thymeleaf.ThymeleafLanguageDriver' (OnClassCondition)

   MybatisPlusLanguageDriverAutoConfiguration.VelocityConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required classes 'org.mybatis.scripting.velocity.VelocityLanguageDriver', 'org.mybatis.scripting.velocity.VelocityLanguageDriverConfig' (OnClassCondition)

   Neo4jDataAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.neo4j.ogm.session.SessionFactory' (OnClassCondition)

   Neo4jRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.neo4j.ogm.session.Neo4jSession' (OnClassCondition)

   NoOpCacheConfiguration:
      Did not match:
         - Cache org.springframework.boot.autoconfigure.cache.NoOpCacheConfiguration unknown cache type (CacheCondition)

   OAuth2ClientAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.security.config.annotation.web.configuration.EnableWebSecurity' (OnClassCondition)

   OAuth2ResourceServerAutoConfiguration.JwtConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required classes 'org.springframework.security.oauth2.server.resource.authentication.JwtAuthenticationToken', 'org.springframework.security.oauth2.jwt.JwtDecoder' (OnClassCondition)

   OAuth2ResourceServerAutoConfiguration.OpaqueTokenConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required classes 'org.springframework.security.oauth2.server.resource.BearerTokenAuthenticationToken', 'org.springframework.security.oauth2.server.resource.introspection.OpaqueTokenIntrospector' (OnClassCondition)

   ProjectInfoAutoConfiguration#buildProperties:
      Did not match:
         - @ConditionalOnResource did not find resource '${spring.info.build.location:classpath:META-INF/build-info.properties}' (OnResourceCondition)

   ProjectInfoAutoConfiguration#gitProperties:
      Did not match:
         - GitResource did not find git info at classpath:git.properties (ProjectInfoAutoConfiguration.GitResourceAvailableCondition)

   QuartzAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.quartz.Scheduler' (OnClassCondition)

   RSocketMessagingAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.rsocket.RSocketFactory' (OnClassCondition)

   RSocketRequesterAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.rsocket.RSocketFactory' (OnClassCondition)

   RSocketSecurityAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.security.rsocket.core.SecuritySocketAcceptorInterceptor' (OnClassCondition)

   RSocketServerAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.rsocket.RSocketFactory' (OnClassCondition)

   RSocketStrategiesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.netty.buffer.PooledByteBufAllocator' (OnClassCondition)

   RabbitAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.rabbitmq.client.Channel' (OnClassCondition)

   ReactiveElasticsearchRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.data.elasticsearch.client.reactive.ReactiveElasticsearchClient' (OnClassCondition)

   ReactiveOAuth2ClientAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'reactor.core.publisher.Flux' (OnClassCondition)

   ReactiveOAuth2ResourceServerAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.security.config.annotation.web.reactive.EnableWebFluxSecurity' (OnClassCondition)

   ReactiveRestClientAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'reactor.netty.http.client.HttpClient' (OnClassCondition)

   ReactiveSecurityAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'reactor.core.publisher.Flux' (OnClassCondition)

   ReactiveUserDetailsServiceAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.security.authentication.ReactiveAuthenticationManager' (OnClassCondition)

   ReactiveWebServerFactoryAutoConfiguration:
      Did not match:
         - @ConditionalOnWebApplication did not find reactive web application classes (OnWebApplicationCondition)

   RedisAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.data.redis.core.RedisOperations' (OnClassCondition)

   RedisCacheConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.data.redis.connection.RedisConnectionFactory' (OnClassCondition)

   RedisReactiveAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'reactor.core.publisher.Flux' (OnClassCondition)

   RedisRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.data.redis.repository.configuration.EnableRedisRepositories' (OnClassCondition)

   RepositoryRestMvcAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.data.rest.webmvc.config.RepositoryRestMvcConfiguration' (OnClassCondition)

   RestClientAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.elasticsearch.client.RestClient' (OnClassCondition)

   Saml2RelyingPartyAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.security.saml2.provider.service.registration.RelyingPartyRegistrationRepository' (OnClassCondition)

   SecurityAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.security.authentication.DefaultAuthenticationEventPublisher' (OnClassCondition)

   SecurityFilterAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.security.config.http.SessionCreationPolicy' (OnClassCondition)

   SendGridAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.sendgrid.SendGrid' (OnClassCondition)

   ServletWebServerFactoryAutoConfiguration#forwardedHeaderFilter:
      Did not match:
         - @ConditionalOnProperty (server.forward-headers-strategy=framework) did not find property 'server.forward-headers-strategy' (OnPropertyCondition)

   ServletWebServerFactoryAutoConfiguration#tomcatServletWebServerFactoryCustomizer:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.apache.catalina.startup.Tomcat' (OnClassCondition)

   ServletWebServerFactoryConfiguration.EmbeddedJetty:
      Did not match:
         - @ConditionalOnClass did not find required classes 'org.eclipse.jetty.server.Server', 'org.eclipse.jetty.util.Loader', 'org.eclipse.jetty.webapp.WebAppContext' (OnClassCondition)

   ServletWebServerFactoryConfiguration.EmbeddedTomcat:
      Did not match:
         - @ConditionalOnClass did not find required classes 'org.apache.catalina.startup.Tomcat', 'org.apache.coyote.UpgradeProtocol' (OnClassCondition)

   SessionAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.session.Session' (OnClassCondition)

   SimpleCacheConfiguration:
      Did not match:
         - Cache org.springframework.boot.autoconfigure.cache.SimpleCacheConfiguration unknown cache type (CacheCondition)

   SolrAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.apache.solr.client.solrj.impl.CloudSolrClient' (OnClassCondition)

   SolrRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.apache.solr.client.solrj.SolrClient' (OnClassCondition)

   SpringDataWebAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.data.web.PageableHandlerMethodArgumentResolver' (OnClassCondition)

   ThymeleafAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.thymeleaf.spring5.SpringTemplateEngine' (OnClassCondition)

   TransactionAutoConfiguration#transactionalOperator:
      Did not match:
         - @ConditionalOnBean (types: org.springframework.transaction.ReactiveTransactionManager; SearchStrategy: all) did not find any beans (OnBeanCondition)

   TransactionAutoConfiguration.EnableTransactionManagementConfiguration.JdkDynamicAutoProxyConfiguration:
      Did not match:
         - @ConditionalOnProperty (spring.aop.proxy-target-class=false) did not find property 'proxy-target-class' (OnPropertyCondition)

   UserDetailsServiceAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.security.authentication.AuthenticationManager' (OnClassCondition)

   WebClientAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.web.reactive.function.client.WebClient' (OnClassCondition)

   WebFluxAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.web.reactive.config.WebFluxConfigurer' (OnClassCondition)

   WebMvcAutoConfiguration#hiddenHttpMethodFilter:
      Did not match:
         - @ConditionalOnProperty (spring.mvc.hiddenmethod.filter.enabled) did not find property 'enabled' (OnPropertyCondition)

   WebMvcAutoConfiguration.ResourceChainCustomizerConfiguration:
      Did not match:
         - @ConditionalOnEnabledResourceChain did not find class org.webjars.WebJarAssetLocator (OnEnabledResourceChainCondition)

   WebMvcAutoConfiguration.WebMvcAutoConfigurationAdapter#beanNameViewResolver:
      Did not match:
         - @ConditionalOnBean (types: org.springframework.web.servlet.view.BeanNameViewResolver; SearchStrategy: all) found beans of type 'org.springframework.web.servlet.view.BeanNameViewResolver' beanNameViewResolver (OnBeanCondition)

   WebMvcAutoConfiguration.WebMvcAutoConfigurationAdapter#localeResolver:
      Did not match:
         - @ConditionalOnProperty (spring.mvc.locale) did not find property 'locale' (OnPropertyCondition)

   WebServiceTemplateAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.oxm.Marshaller' (OnClassCondition)

   WebServicesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.ws.transport.http.MessageDispatcherServlet' (OnClassCondition)

   WebSocketMessagingAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.web.socket.config.annotation.WebSocketMessageBrokerConfigurer' (OnClassCondition)

   WebSocketReactiveAutoConfiguration:
      Did not match:
         - @ConditionalOnWebApplication did not find reactive web application classes (OnWebApplicationCondition)

   WebSocketServletAutoConfiguration.JettyWebSocketConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.eclipse.jetty.websocket.jsr356.server.deploy.WebSocketServerContainerInitializer' (OnClassCondition)

   WebSocketServletAutoConfiguration.TomcatWebSocketConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required classes 'org.apache.catalina.startup.Tomcat', 'org.apache.tomcat.websocket.server.WsSci' (OnClassCondition)

   XADataSourceAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'javax.transaction.TransactionManager' (OnClassCondition)


Exclusions:
-----------

    com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure


Unconditional classes:
----------------------

    org.springframework.boot.autoconfigure.context.ConfigurationPropertiesAutoConfiguration

    org.springframework.boot.autoconfigure.context.PropertyPlaceholderAutoConfiguration

    com.baomidou.mybatisplus.autoconfigure.IdentifierGeneratorAutoConfiguration

    org.springframework.boot.autoconfigure.info.ProjectInfoAutoConfiguration



2025-06-07 16:02:03.803 [main] ERROR o.s.boot.SpringApplication - Application run failed
org.springframework.context.ApplicationContextException: Unable to start web server; nested exception is java.lang.RuntimeException: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'shiroFilter': FactoryBean threw exception on object creation; nested exception is java.lang.NullPointerException: chainDefinition cannot be null or empty.
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.onRefresh(ServletWebServerApplicationContext.java:156)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:545)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:141)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:747)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:397)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:315)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1226)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1215)
	at com.qs.admin.QscAdminApplication.main(QscAdminApplication.java:19)
Caused by: java.lang.RuntimeException: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'shiroFilter': FactoryBean threw exception on object creation; nested exception is java.lang.NullPointerException: chainDefinition cannot be null or empty.
	at io.undertow.servlet.core.DeploymentManagerImpl.deploy(DeploymentManagerImpl.java:254)
	at org.springframework.boot.web.embedded.undertow.UndertowServletWebServerFactory.createDeploymentManager(UndertowServletWebServerFactory.java:292)
	at org.springframework.boot.web.embedded.undertow.UndertowServletWebServerFactory.getWebServer(UndertowServletWebServerFactory.java:218)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.createWebServer(ServletWebServerApplicationContext.java:180)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.onRefresh(ServletWebServerApplicationContext.java:153)
	... 8 common frames omitted
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'shiroFilter': FactoryBean threw exception on object creation; nested exception is java.lang.NullPointerException: chainDefinition cannot be null or empty.
	at org.springframework.beans.factory.support.FactoryBeanRegistrySupport.doGetObjectFromFactoryBean(FactoryBeanRegistrySupport.java:178)
	at org.springframework.beans.factory.support.FactoryBeanRegistrySupport.getObjectFromFactoryBean(FactoryBeanRegistrySupport.java:101)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getObjectForBeanInstance(AbstractBeanFactory.java:1818)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.getObjectForBeanInstance(AbstractAutowireCapableBeanFactory.java:1266)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:260)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:207)
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.getOrderedBeansOfType(ServletContextInitializerBeans.java:211)
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.addAsRegistrationBean(ServletContextInitializerBeans.java:174)
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.addAsRegistrationBean(ServletContextInitializerBeans.java:169)
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.addAdaptableBeans(ServletContextInitializerBeans.java:154)
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.<init>(ServletContextInitializerBeans.java:86)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.getServletContextInitializerBeans(ServletWebServerApplicationContext.java:253)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.selfInitialize(ServletWebServerApplicationContext.java:227)
	at org.springframework.boot.web.embedded.undertow.UndertowServletWebServerFactory$Initializer.onStartup(UndertowServletWebServerFactory.java:619)
	at io.undertow.servlet.core.DeploymentManagerImpl$1.call(DeploymentManagerImpl.java:204)
	at io.undertow.servlet.core.DeploymentManagerImpl$1.call(DeploymentManagerImpl.java:186)
	at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:42)
	at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43)
	at io.undertow.servlet.core.DeploymentManagerImpl.deploy(DeploymentManagerImpl.java:252)
	... 12 common frames omitted
Caused by: java.lang.NullPointerException: chainDefinition cannot be null or empty.
	at org.apache.shiro.web.filter.mgt.DefaultFilterChainManager.createChain(DefaultFilterChainManager.java:123)
	at org.apache.shiro.spring.web.ShiroFilterFactoryBean.createFilterChainManager(ShiroFilterFactoryBean.java:397)
	at org.apache.shiro.spring.web.ShiroFilterFactoryBean.createInstance(ShiroFilterFactoryBean.java:437)
	at org.apache.shiro.spring.web.ShiroFilterFactoryBean.getObject(ShiroFilterFactoryBean.java:343)
	at org.springframework.beans.factory.support.FactoryBeanRegistrySupport.doGetObjectFromFactoryBean(FactoryBeanRegistrySupport.java:171)
	... 30 common frames omitted
