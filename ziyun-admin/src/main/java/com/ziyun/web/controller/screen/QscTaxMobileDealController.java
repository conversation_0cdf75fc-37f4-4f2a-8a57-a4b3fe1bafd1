package com.ziyun.web.controller.screen;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.ziyun.common.annotation.Log;
import com.ziyun.common.annotation.RepeatSubmit;
import com.ziyun.common.core.controller.BaseController;
import com.ziyun.common.core.domain.PageQuery;
import com.ziyun.common.core.domain.R;
import com.ziyun.common.core.page.TableDataInfo;
import com.ziyun.common.core.validate.AddGroup;
import com.ziyun.common.core.validate.EditGroup;
import com.ziyun.common.enums.BusinessType;
import com.ziyun.common.utils.poi.ExcelUtil;
import com.ziyun.screen.domain.bo.QscTaxMobileDealBo;
import com.ziyun.screen.domain.vo.QscTaxMobileDealVo;
import com.ziyun.screen.service.IQscTaxMobileDealService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.*;
import java.util.Arrays;
import java.util.List;

/**
 * 掌上办
 *
 * <AUTHOR>
 * @date 2022-12-04
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/screen/taxMobileDeal")
public class QscTaxMobileDealController extends BaseController {

    private final IQscTaxMobileDealService iQscTaxMobileDealService;

    /**
     * 查询掌上办列表
     */
    @SaCheckPermission("screen:taxMobileDeal:list")
    @GetMapping("/list")
    public TableDataInfo<QscTaxMobileDealVo> list(QscTaxMobileDealBo bo, PageQuery pageQuery) {
        return iQscTaxMobileDealService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出掌上办列表
     */
    @SaCheckPermission("screen:taxMobileDeal:export")
    @Log(title = "掌上办", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(QscTaxMobileDealBo bo, HttpServletResponse response) {
        List<QscTaxMobileDealVo> list = iQscTaxMobileDealService.queryList(bo);
        ExcelUtil.exportExcel(list, "掌上办", QscTaxMobileDealVo.class, response);
    }

    /**
     * 获取掌上办详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("screen:taxMobileDeal:query")
    @GetMapping("/{id}")
    public R<QscTaxMobileDealVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(iQscTaxMobileDealService.queryById(id));
    }

    /**
     * 新增掌上办
     */
    @SaCheckPermission("screen:taxMobileDeal:add")
    @Log(title = "掌上办", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody QscTaxMobileDealBo bo) {
        return toAjax(iQscTaxMobileDealService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 修改掌上办
     */
    @SaCheckPermission("screen:taxMobileDeal:edit")
    @Log(title = "掌上办", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody QscTaxMobileDealBo bo) {
        return toAjax(iQscTaxMobileDealService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除掌上办
     *
     * @param ids 主键串
     */
    @SaCheckPermission("screen:taxMobileDeal:remove")
    @Log(title = "掌上办", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(iQscTaxMobileDealService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }
}
