package com.ziyun.web.controller.screen;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.ziyun.common.annotation.Log;
import com.ziyun.common.annotation.RepeatSubmit;
import com.ziyun.common.core.controller.BaseController;
import com.ziyun.common.core.domain.PageQuery;
import com.ziyun.common.core.domain.R;
import com.ziyun.common.core.page.TableDataInfo;
import com.ziyun.common.core.validate.AddGroup;
import com.ziyun.common.core.validate.EditGroup;
import com.ziyun.common.enums.BusinessType;
import com.ziyun.common.utils.poi.ExcelUtil;
import com.ziyun.screen.domain.bo.QscPhoneAnalysisBo;
import com.ziyun.screen.domain.vo.QscPhoneAnalysisVo;
import com.ziyun.screen.service.IQscPhoneAnalysisService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.*;
import java.util.Arrays;
import java.util.List;

/**
 * 来电情况分析
 *
 * <AUTHOR>
 * @date 2022-12-04
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/screen/phoneAnalysis")
public class QscPhoneAnalysisController extends BaseController {

    private final IQscPhoneAnalysisService iQscPhoneAnalysisService;

    /**
     * 查询来电情况分析列表
     */
    @SaCheckPermission("screen:phoneAnalysis:list")
    @GetMapping("/list")
    public TableDataInfo<QscPhoneAnalysisVo> list(QscPhoneAnalysisBo bo, PageQuery pageQuery) {
        return iQscPhoneAnalysisService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出来电情况分析列表
     */
    @SaCheckPermission("screen:phoneAnalysis:export")
    @Log(title = "来电情况分析", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(QscPhoneAnalysisBo bo, HttpServletResponse response) {
        List<QscPhoneAnalysisVo> list = iQscPhoneAnalysisService.queryList(bo);
        ExcelUtil.exportExcel(list, "来电情况分析", QscPhoneAnalysisVo.class, response);
    }

    /**
     * 获取来电情况分析详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("screen:phoneAnalysis:query")
    @GetMapping("/{id}")
    public R<QscPhoneAnalysisVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(iQscPhoneAnalysisService.queryById(id));
    }

    /**
     * 新增来电情况分析
     */
    @SaCheckPermission("screen:phoneAnalysis:add")
    @Log(title = "来电情况分析", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody QscPhoneAnalysisBo bo) {
        return toAjax(iQscPhoneAnalysisService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 修改来电情况分析
     */
    @SaCheckPermission("screen:phoneAnalysis:edit")
    @Log(title = "来电情况分析", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody QscPhoneAnalysisBo bo) {
        return toAjax(iQscPhoneAnalysisService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除来电情况分析
     *
     * @param ids 主键串
     */
    @SaCheckPermission("screen:phoneAnalysis:remove")
    @Log(title = "来电情况分析", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(iQscPhoneAnalysisService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }
}
