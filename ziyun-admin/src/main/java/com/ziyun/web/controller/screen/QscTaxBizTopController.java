package com.ziyun.web.controller.screen;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.ziyun.common.annotation.Log;
import com.ziyun.common.annotation.RepeatSubmit;
import com.ziyun.common.core.controller.BaseController;
import com.ziyun.common.core.domain.PageQuery;
import com.ziyun.common.core.domain.R;
import com.ziyun.common.core.page.TableDataInfo;
import com.ziyun.common.core.validate.AddGroup;
import com.ziyun.common.core.validate.EditGroup;
import com.ziyun.common.enums.BusinessType;
import com.ziyun.common.utils.poi.ExcelUtil;
import com.ziyun.screen.domain.bo.QscTaxBizTopBo;
import com.ziyun.screen.domain.vo.QscTaxBizTopVo;
import com.ziyun.screen.service.IQscTaxBizTopService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.*;
import java.util.Arrays;
import java.util.List;

/**
 * 月度业务量排名
 *
 * <AUTHOR>
 * @date 2022-12-04
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/screen/taxBizTop")
public class QscTaxBizTopController extends BaseController {

    private final IQscTaxBizTopService iQscTaxBizTopService;

    /**
     * 查询月度业务量排名列表
     */
    @SaCheckPermission("screen:taxBizTop:list")
    @GetMapping("/list")
    public TableDataInfo<QscTaxBizTopVo> list(QscTaxBizTopBo bo, PageQuery pageQuery) {
        return iQscTaxBizTopService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出月度业务量排名列表
     */
    @SaCheckPermission("screen:taxBizTop:export")
    @Log(title = "月度业务量排名", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(QscTaxBizTopBo bo, HttpServletResponse response) {
        List<QscTaxBizTopVo> list = iQscTaxBizTopService.queryList(bo);
        ExcelUtil.exportExcel(list, "月度业务量排名", QscTaxBizTopVo.class, response);
    }

    /**
     * 获取月度业务量排名详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("screen:taxBizTop:query")
    @GetMapping("/{id}")
    public R<QscTaxBizTopVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(iQscTaxBizTopService.queryById(id));
    }

    /**
     * 新增月度业务量排名
     */
    @SaCheckPermission("screen:taxBizTop:add")
    @Log(title = "月度业务量排名", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody QscTaxBizTopBo bo) {
        return toAjax(iQscTaxBizTopService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 修改月度业务量排名
     */
    @SaCheckPermission("screen:taxBizTop:edit")
    @Log(title = "月度业务量排名", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody QscTaxBizTopBo bo) {
        return toAjax(iQscTaxBizTopService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除月度业务量排名
     *
     * @param ids 主键串
     */
    @SaCheckPermission("screen:taxBizTop:remove")
    @Log(title = "月度业务量排名", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(iQscTaxBizTopService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }
}
