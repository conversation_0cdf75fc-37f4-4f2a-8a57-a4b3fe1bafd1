package com.ziyun.web.controller.screen;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.ziyun.common.annotation.Log;
import com.ziyun.common.annotation.RepeatSubmit;
import com.ziyun.common.core.controller.BaseController;
import com.ziyun.common.core.domain.PageQuery;
import com.ziyun.common.core.domain.R;
import com.ziyun.common.core.page.TableDataInfo;
import com.ziyun.common.core.validate.AddGroup;
import com.ziyun.common.core.validate.EditGroup;
import com.ziyun.common.enums.BusinessType;
import com.ziyun.common.utils.poi.ExcelUtil;
import com.ziyun.screen.domain.bo.QscCameraBo;
import com.ziyun.screen.domain.vo.QscCameraVo;
import com.ziyun.screen.service.IQscCameraService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.*;
import java.util.Arrays;
import java.util.List;

/**
 * 【请填写功能名称】
 *
 * <AUTHOR>
 * @date 2022-12-04
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/screen/camera")
public class QscCameraController extends BaseController {

    private final IQscCameraService iQscCameraService;

    /**
     * 查询【请填写功能名称】列表
     */
    @SaCheckPermission("screen:camera:list")
    @GetMapping("/list")
    public TableDataInfo<QscCameraVo> list(QscCameraBo bo, PageQuery pageQuery) {
        return iQscCameraService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出【请填写功能名称】列表
     */
    @SaCheckPermission("screen:camera:export")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(QscCameraBo bo, HttpServletResponse response) {
        List<QscCameraVo> list = iQscCameraService.queryList(bo);
        ExcelUtil.exportExcel(list, "【请填写功能名称】", QscCameraVo.class, response);
    }

    /**
     * 获取【请填写功能名称】详细信息
     *
     * @param cameraId 主键
     */
    @SaCheckPermission("screen:camera:query")
    @GetMapping("/{cameraId}")
    public R<QscCameraVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long cameraId) {
        return R.ok(iQscCameraService.queryById(cameraId));
    }

    /**
     * 新增【请填写功能名称】
     */
    @SaCheckPermission("screen:camera:add")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody QscCameraBo bo) {
        return toAjax(iQscCameraService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 修改【请填写功能名称】
     */
    @SaCheckPermission("screen:camera:edit")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody QscCameraBo bo) {
        return toAjax(iQscCameraService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除【请填写功能名称】
     *
     * @param cameraIds 主键串
     */
    @SaCheckPermission("screen:camera:remove")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.DELETE)
    @DeleteMapping("/{cameraIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] cameraIds) {
        return toAjax(iQscCameraService.deleteWithValidByIds(Arrays.asList(cameraIds), true) ? 1 : 0);
    }
}
