package com.ziyun.web.controller.screen;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.ziyun.common.annotation.Log;
import com.ziyun.common.annotation.RepeatSubmit;
import com.ziyun.common.core.controller.BaseController;
import com.ziyun.common.core.domain.PageQuery;
import com.ziyun.common.core.domain.R;
import com.ziyun.common.core.page.TableDataInfo;
import com.ziyun.common.core.validate.AddGroup;
import com.ziyun.common.core.validate.EditGroup;
import com.ziyun.common.enums.BusinessType;
import com.ziyun.common.utils.poi.ExcelUtil;
import com.ziyun.screen.domain.bo.QscPhoneTagAnalysisBo;
import com.ziyun.screen.domain.vo.QscPhoneTagAnalysisVo;
import com.ziyun.screen.service.IQscPhoneTagAnalysisService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.*;
import java.util.Arrays;
import java.util.List;

/**
 * 电话热点咨询
 *
 * <AUTHOR>
 * @date 2022-12-04
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/screen/phoneTagAnalysis")
public class QscPhoneTagAnalysisController extends BaseController {

    private final IQscPhoneTagAnalysisService iQscPhoneTagAnalysisService;

    /**
     * 查询电话热点咨询列表
     */
    @SaCheckPermission("screen:phoneTagAnalysis:list")
    @GetMapping("/list")
    public TableDataInfo<QscPhoneTagAnalysisVo> list(QscPhoneTagAnalysisBo bo, PageQuery pageQuery) {
        return iQscPhoneTagAnalysisService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出电话热点咨询列表
     */
    @SaCheckPermission("screen:phoneTagAnalysis:export")
    @Log(title = "电话热点咨询", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(QscPhoneTagAnalysisBo bo, HttpServletResponse response) {
        List<QscPhoneTagAnalysisVo> list = iQscPhoneTagAnalysisService.queryList(bo);
        ExcelUtil.exportExcel(list, "电话热点咨询", QscPhoneTagAnalysisVo.class, response);
    }

    /**
     * 获取电话热点咨询详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("screen:phoneTagAnalysis:query")
    @GetMapping("/{id}")
    public R<QscPhoneTagAnalysisVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(iQscPhoneTagAnalysisService.queryById(id));
    }

    /**
     * 新增电话热点咨询
     */
    @SaCheckPermission("screen:phoneTagAnalysis:add")
    @Log(title = "电话热点咨询", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody QscPhoneTagAnalysisBo bo) {
        return toAjax(iQscPhoneTagAnalysisService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 修改电话热点咨询
     */
    @SaCheckPermission("screen:phoneTagAnalysis:edit")
    @Log(title = "电话热点咨询", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody QscPhoneTagAnalysisBo bo) {
        return toAjax(iQscPhoneTagAnalysisService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除电话热点咨询
     *
     * @param ids 主键串
     */
    @SaCheckPermission("screen:phoneTagAnalysis:remove")
    @Log(title = "电话热点咨询", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(iQscPhoneTagAnalysisService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }
}
