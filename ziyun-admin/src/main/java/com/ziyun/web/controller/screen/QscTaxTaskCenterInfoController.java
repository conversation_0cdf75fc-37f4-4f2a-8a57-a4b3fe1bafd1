package com.ziyun.web.controller.screen;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.ziyun.common.annotation.Log;
import com.ziyun.common.annotation.RepeatSubmit;
import com.ziyun.common.core.controller.BaseController;
import com.ziyun.common.core.domain.PageQuery;
import com.ziyun.common.core.domain.R;
import com.ziyun.common.core.page.TableDataInfo;
import com.ziyun.common.core.validate.AddGroup;
import com.ziyun.common.core.validate.EditGroup;
import com.ziyun.common.enums.BusinessType;
import com.ziyun.common.utils.poi.ExcelUtil;
import com.ziyun.screen.domain.bo.QscTaxTaskCenterInfoBo;
import com.ziyun.screen.domain.vo.QscTaxTaskCenterInfoVo;
import com.ziyun.screen.service.IQscTaxTaskCenterInfoService;
import lombok.RequiredArgsConstructor;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.*;
import java.util.Arrays;
import java.util.List;

/**
 * 斗门任务中心信息
 *
 * <AUTHOR>
 * @date 2022-11-19
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/screen/taxTaskCenterInfo")
public class QscTaxTaskCenterInfoController extends BaseController {

    private final IQscTaxTaskCenterInfoService iTaxTaskCenterInfoService;

    /**
     * 查询斗门任务中心信息列表
     */
    @SaCheckPermission("screen:taxTaskCenterInfo:list")
    @GetMapping("/list")
    public TableDataInfo<QscTaxTaskCenterInfoVo> list(QscTaxTaskCenterInfoBo bo, PageQuery pageQuery) {
        return iTaxTaskCenterInfoService.customPageList(bo, pageQuery);
    }

    /**
     * 导入数据
     *
     * @param file          导入文件
     * @param updateSupport 是否更新已存在数据
     */
    @Log(title = "任务中心导入", businessType = BusinessType.IMPORT)
    @SaCheckPermission("screen:taxTaskCenterInfo:import")
    @PostMapping(value = "/importData", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public R<List<String>> importData(@RequestPart("file") MultipartFile file, boolean updateSupport) throws Exception {
        List<String> result = iTaxTaskCenterInfoService.imporData(file,updateSupport);
        return R.ok(result);
    }

    /**
     * 导出斗门任务中心信息列表
     */
    @SaCheckPermission("screen:taxTaskCenterInfo:export")
    @Log(title = "斗门任务中心信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(QscTaxTaskCenterInfoBo bo, HttpServletResponse response) {
        List<QscTaxTaskCenterInfoVo> list = iTaxTaskCenterInfoService.queryList(bo);
        ExcelUtil.exportExcel(list, "斗门任务中心信息", QscTaxTaskCenterInfoVo.class, response);
    }

    /**
     * 获取斗门任务中心信息详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("screen:taxTaskCenterInfo:query")
    @GetMapping("/{id}")
    public R<QscTaxTaskCenterInfoVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(iTaxTaskCenterInfoService.queryById(id));
    }

    /**
     * 新增斗门任务中心信息
     */
    @SaCheckPermission("screen:taxTaskCenterInfo:add")
    @Log(title = "斗门任务中心信息", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody QscTaxTaskCenterInfoBo bo) {
        return toAjax(iTaxTaskCenterInfoService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 修改斗门任务中心信息
     */
    @SaCheckPermission("screen:taxTaskCenterInfo:edit")
    @Log(title = "斗门任务中心信息", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody QscTaxTaskCenterInfoBo bo) {
        return toAjax(iTaxTaskCenterInfoService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除斗门任务中心信息
     *
     * @param ids 主键串
     */
    @SaCheckPermission("screen:taxTaskCenterInfo:remove")
    @Log(title = "斗门任务中心信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(iTaxTaskCenterInfoService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }
}
