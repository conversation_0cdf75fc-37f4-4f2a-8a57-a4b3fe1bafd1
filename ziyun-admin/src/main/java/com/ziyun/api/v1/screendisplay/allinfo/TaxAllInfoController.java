package com.ziyun.api.v1.screendisplay.allinfo;


import cn.hutool.http.HtmlUtil;
import com.ziyun.common.core.domain.R;
import com.ziyun.common.core.domain.entity.SysUser;
import com.ziyun.common.utils.StringUtils;
import com.ziyun.screen.domain.bo.QscTaxDealRateRankBo;
import com.ziyun.screen.domain.bo.QscTaxSmartServiceIndexBo;
import com.ziyun.screen.domain.chart.Exponent;
import com.ziyun.screen.domain.vo.*;
import com.ziyun.screen.domain.vo.halldata.RegionIndustryAnaylsisVo;
import com.ziyun.screen.domain.vo.halldata.RegionStockInOutVo;
import com.ziyun.screen.service.*;
import com.ziyun.system.domain.SysNotice;
import com.ziyun.system.service.ISysNoticeService;
import com.ziyun.system.service.ISysUserService;
import lombok.RequiredArgsConstructor;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


//@Api(value = "大屏api", tags = {"大屏api"})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@CrossOrigin
@RestController
@RequestMapping("/v1/screen-display/taxhall")
public class TaxAllInfoController {
    private final ISysNoticeService noticeService;

    private final ISysUserService userService;

    /**
     * 热点
     */
    private final IQscTaxSeekAdviceService seekAdviceService;

    /**
     * 收入
     */
    private final IQscTaxDealRateRankService dealRateRankService;
    /**
     * 智慧服务指数
     */
    private final IQscTaxSmartServiceIndexService smartService;

    private final IQscTaxStockInOutService stockInOutService;

    private final IQscTaxRegionIndustryTagInfoService tagInfoService;

    private final TaxHallScreenService hallScreenService;
    /**
     * 查询税局信息总览列表
     */
//    @ApiOperation("查询税局信息总览")
    @GetMapping("/info-overview")
    @ResponseBody
    public R<QscTaxHallOverviewVo> infoOverview(@RequestParam(value="name",required = false) String name) {
        return R.ok(hallScreenService.infoOverview(name));
    }

    /**
     * 税局地图点位信息
     */
//    @ApiOperation("税局地图点位信息")
    @GetMapping("/map-unit-point")
    @ResponseBody
    public R<List<QscMapUnitPointVo>> mapUnitPOint() {
        return R.ok(hallScreenService.mapUnitPOint());
    }


    /**
     * 税局服务方式总览
     */
//    @ApiOperation("税局服务方式总览")
    @GetMapping("/srv-type")
    @ResponseBody
    public R<Map<String,Object>> srvType(@RequestParam(value="name",required = false) String name) {
        return R.ok(hallScreenService.srvType(name));
    }

    /**
     * 查询税局服务方式总览列表
     */
//    @ApiOperation("查询税局业务总览")
    @GetMapping("/biz-vol-top")
    @ResponseBody
    public R<Map<String,Object>> bizVolTop(@RequestParam(value="name",required = false) String name) {
        return R.ok(hallScreenService.bizVolTop(name));
    }

    /**
     * 查询税局业务总览列表
     */
//    @ApiOperation("收入总览")
    @GetMapping("/income")
    @ResponseBody
    public R<Map<String,Object>> income(@RequestParam(value="name",required = false) String name) {
        return R.ok(hallScreenService.income(name));
    }

    /**
     * 查询税局热点总览列表
     */
//    @ApiOperation("查询税局热点总览")
    @GetMapping("/seek-advice")
    @ResponseBody
    public R<QscTaxSeekAdviceVo> seekAdvice() {
        return R.ok(seekAdviceService.queryById(1L));
    }

    /**
     * 税局最新通知
     */
//    @ApiOperation("税局最新通知")
    @GetMapping("/notice")
    @ResponseBody
    public R<List<SysNotice>> notice() {
        SysNotice sysn = new SysNotice();
        List<SysNotice> list = noticeService.selectNoticeList(sysn);
        for (SysNotice notice : list) {
            notice.setNoticeContent(HtmlUtil.cleanHtmlTag(notice.getNoticeContent()));
        }
        return R.ok(list);
    }

    /**
     * 税局最新通知
     */
    @GetMapping("/deal-rate-rank")
    @ResponseBody
    public R<List<QscTaxDealRateRankVo>> ranklist(@RequestParam(value="name",required = false) String name) {
        SysUser user;
        if(StringUtils.isNotBlank(name)&&StringUtils.equals(name,"doumen")){
            user = userService.selectUserByUserName(name);
        }else {
            user = userService.selectUserByUserName("ycsd");
        }
        QscTaxDealRateRankBo bo = new QscTaxDealRateRankBo();
        bo.setUserId(user.getUserId());
        List<QscTaxDealRateRankVo> list = dealRateRankService.queryList(bo);
        return R.ok(list);
    }

//    @ApiOperation("智慧服务指数")
    @GetMapping("/smart-service-index")
    @ResponseBody
    public R<Exponent> smartServiceIndex() {
        Exponent exponent = new Exponent();
        QscTaxSmartServiceIndexBo bo = new QscTaxSmartServiceIndexBo();
        List<QscTaxSmartServiceIndexVo> smartServiceIndexVos = smartService.queryList(bo);
        if(null!=smartServiceIndexVos){
            List<Map<String,Object>> smartServiceList = new ArrayList<>();
            for (QscTaxSmartServiceIndexVo sm : smartServiceIndexVos) {
                Map<String,Object> m = new HashMap<>();
                m.put("name",sm.getIndexName());
                m.put("value",sm.getIndexValue());
                m.put("left",sm.getIndexLeft());
                m.put("top",sm.getIdnexTop());
                smartServiceList.add(m);
            }
            exponent.setOutsideData(smartServiceList);
        }
        return R.ok(exponent);
    }


//    @ApiOperation("入库退库")
    @GetMapping("/stock-in-out")
    @ResponseBody
    public R<Map<String,Object>> StockInOut(){
        Map<String,Object> map = new HashMap<>();
        String regionPid = "330602";
        List<RegionStockInOutVo> rsioVos = stockInOutService.queryListByRegionId(regionPid);
        map.put("rsioVos",rsioVos);
        return R.ok(map);
    }



//    @ApiOperation("各街道行业分类")
    @GetMapping("/industry-anaylsis-info")
    @ResponseBody
    public R<Map<String,Object>> RegionIndustryAnaylsisInfo(@Param("id") String id){
        Map<String,Object> map = new HashMap<>();
        List<RegionIndustryAnaylsisVo> anaylsisVos = tagInfoService.queryListByRegionId(id);
        map.put("AnaylsisVo",anaylsisVos);
        return R.ok(map);
    }

    /**
     * 斗门taskcenter
     * @param name
     * @return
     */
    @GetMapping("/dm-center-task")
    @ResponseBody
    public R<Map<String,Object>> dmTask(@RequestParam(value="name",required = false) String name) {
        return R.ok(hallScreenService.dmCenterTask());
    }

}
