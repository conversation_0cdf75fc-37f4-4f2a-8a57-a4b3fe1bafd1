package org.ziyun.hall.service.impl;

import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.ziyun.hall.domain.bo.WindowBo;
import org.ziyun.hall.domain.vo.WindowVo;
import org.ziyun.hall.domain.Window;
import org.ziyun.hall.mapper.WindowMapper;
import org.ziyun.hall.service.IWindowService;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 窗口信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-02-28
 */
@RequiredArgsConstructor
@Service
public class WindowServiceImpl implements IWindowService {

    private final WindowMapper baseMapper;

    /**
     * 查询窗口信息
     */
    @Override
    public WindowVo queryById(Long UID){
        return baseMapper.selectVoById(UID);
    }

    /**
     * 查询窗口信息列表
     */
    @Override
    public TableDataInfo<WindowVo> queryPageList(WindowBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<Window> lqw = buildQueryWrapper(bo);
        Page<WindowVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询窗口信息列表
     */
    @Override
    public List<WindowVo> queryList(WindowBo bo) {
        LambdaQueryWrapper<Window> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<Window> buildQueryWrapper(WindowBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<Window> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(bo.getNAME()), Window::getNAME, bo.getNAME());
        lqw.eq(StringUtils.isNotBlank(bo.getLedAddress()), Window::getLedAddress, bo.getLedAddress());
        lqw.eq(StringUtils.isNotBlank(bo.getLedText()), Window::getLedText, bo.getLedText());
        lqw.eq(bo.getENABLED() != null, Window::getENABLED, bo.getENABLED());
        lqw.eq(StringUtils.isNotBlank(bo.getSID()), Window::getSID, bo.getSID());
        lqw.eq(StringUtils.isNotBlank(bo.getRankMode()), Window::getRankMode, bo.getRankMode());
        lqw.eq(StringUtils.isNotBlank(bo.getRankAddress()), Window::getRankAddress, bo.getRankAddress());
        return lqw;
    }

    /**
     * 新增窗口信息
     */
    @Override
    public Boolean insertByBo(WindowBo bo) {
        Window add = MapstructUtils.convert(bo, Window.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setUID(add.getUID());
        }
        return flag;
    }

    /**
     * 修改窗口信息
     */
    @Override
    public Boolean updateByBo(WindowBo bo) {
        Window update = MapstructUtils.convert(bo, Window.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(Window entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除窗口信息
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }
}
