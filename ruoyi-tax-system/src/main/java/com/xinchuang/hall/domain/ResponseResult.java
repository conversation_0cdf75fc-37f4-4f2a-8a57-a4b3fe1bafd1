package com.xinchuang.hall.domain;

import lombok.Data;

/**
 * 统一响应结果类
 */
@Data
public class ResponseResult {
    
    /**
     * 状态码
     */
    private Integer code;
    
    /**
     * 返回信息
     */
    private String message;
    
    /**
     * 返回数据
     */
    private Object data;
    
    /**
     * 成功返回结果
     *
     * @param data 返回数据
     * @return 响应结果
     */
    public static ResponseResult success(Object data) {
        ResponseResult result = new ResponseResult();
        result.setCode(200);
        result.setMessage("操作成功");
        result.setData(data);
        return result;
    }
    
    /**
     * 失败返回结果
     *
     * @param message 错误信息
     * @return 响应结果
     */
    public static ResponseResult error(String message) {
        ResponseResult result = new ResponseResult();
        result.setCode(500);
        result.setMessage(message);
        return result;
    }
}