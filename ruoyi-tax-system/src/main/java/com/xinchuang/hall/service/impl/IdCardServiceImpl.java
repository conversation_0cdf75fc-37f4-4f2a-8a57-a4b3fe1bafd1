package com.xinchuang.hall.service.impl;

import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import com.xinchuang.hall.service.IdCardService;
import com.xinchuang.hall.domain.IdCardInfo;
import com.xinchuang.hall.domain.ResponseResult;

/**
 * 身份证服务实现类
 */
@Service
public class IdCardServiceImpl implements IdCardService {

    @Override
    public ResponseResult processIdCardInfo(IdCardInfo idCardInfo) {
        // 验证身份证信息
        if (idCardInfo == null) {
            return ResponseResult.error("身份证信息不能为空");
        }
        if (!StringUtils.hasText(idCardInfo.getIdNumber())) {
            return ResponseResult.error("身份证号码不能为空");
        }
        if (!StringUtils.hasText(idCardInfo.getName())) {
            return ResponseResult.error("姓名不能为空");
        }

        try {
            // TODO: 调用第三方接口获取详细信息
            // 这里需要根据实际的第三方接口实现调用逻辑
            // 可以使用RestTemplate或者Feign进行接口调用
            
            // 模拟调用成功返回结果
            return ResponseResult.success(idCardInfo);
        } catch (Exception e) {
            return ResponseResult.error("调用第三方接口失败：" + e.getMessage());
        }
    }
}