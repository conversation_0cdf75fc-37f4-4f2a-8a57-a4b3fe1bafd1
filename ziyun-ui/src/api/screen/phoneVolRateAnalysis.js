import request from '@/utils/request'

// 查询月电话接听量和接听率列表
export function listPhoneVolRateAnalysis(query) {
  return request({
    url: '/screen/phoneVolRateAnalysis/list',
    method: 'get',
    params: query
  })
}

// 查询月电话接听量和接听率详细
export function getPhoneVolRateAnalysis(id) {
  return request({
    url: '/screen/phoneVolRateAnalysis/' + id,
    method: 'get'
  })
}

// 新增月电话接听量和接听率
export function addPhoneVolRateAnalysis(data) {
  return request({
    url: '/screen/phoneVolRateAnalysis',
    method: 'post',
    data: data
  })
}

// 修改月电话接听量和接听率
export function updatePhoneVolRateAnalysis(data) {
  return request({
    url: '/screen/phoneVolRateAnalysis',
    method: 'put',
    data: data
  })
}

// 删除月电话接听量和接听率
export function delPhoneVolRateAnalysis(id) {
  return request({
    url: '/screen/phoneVolRateAnalysis/' + id,
    method: 'delete'
  })
}
