import request from '@/utils/request'

// 查询【请填写功能名称】列表
export function listCamera(query) {
  return request({
    url: '/screen/camera/list',
    method: 'get',
    params: query
  })
}

// 查询【请填写功能名称】详细
export function getCamera(cameraId) {
  return request({
    url: '/screen/camera/' + cameraId,
    method: 'get'
  })
}

// 新增【请填写功能名称】
export function addCamera(data) {
  return request({
    url: '/screen/camera',
    method: 'post',
    data: data
  })
}

// 修改【请填写功能名称】
export function updateCamera(data) {
  return request({
    url: '/screen/camera',
    method: 'put',
    data: data
  })
}

// 删除【请填写功能名称】
export function delCamera(cameraId) {
  return request({
    url: '/screen/camera/' + cameraId,
    method: 'delete'
  })
}
