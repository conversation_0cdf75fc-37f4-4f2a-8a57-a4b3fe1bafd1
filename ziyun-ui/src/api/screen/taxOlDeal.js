import request from '@/utils/request'

// 查询网上办列表
export function listTaxOlDeal(query) {
  return request({
    url: '/screen/taxOlDeal/list',
    method: 'get',
    params: query
  })
}

// 查询网上办详细
export function getTaxOlDeal(id) {
  return request({
    url: '/screen/taxOlDeal/' + id,
    method: 'get'
  })
}

// 新增网上办
export function addTaxOlDeal(data) {
  return request({
    url: '/screen/taxOlDeal',
    method: 'post',
    data: data
  })
}

// 修改网上办
export function updateTaxOlDeal(data) {
  return request({
    url: '/screen/taxOlDeal',
    method: 'put',
    data: data
  })
}

// 删除网上办
export function delTaxOlDeal(id) {
  return request({
    url: '/screen/taxOlDeal/' + id,
    method: 'delete'
  })
}
