import request from '@/utils/request'

// 查询大厅信息总览列表
export function listUnitOverView(query) {
  return request({
    url: '/screen/unitOverView/list',
    method: 'get',
    params: query
  })
}

// 查询大厅信息总览详细
export function getUnitOverView(id) {
  return request({
    url: '/screen/unitOverView/' + id,
    method: 'get'
  })
}

// 新增大厅信息总览
export function addUnitOverView(data) {
  return request({
    url: '/screen/unitOverView',
    method: 'post',
    data: data
  })
}

// 修改大厅信息总览
export function updateUnitOverView(data) {
  return request({
    url: '/screen/unitOverView',
    method: 'put',
    data: data
  })
}

// 删除大厅信息总览
export function delUnitOverView(id) {
  return request({
    url: '/screen/unitOverView/' + id,
    method: 'delete'
  })
}
