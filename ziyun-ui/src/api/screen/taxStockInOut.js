import request from '@/utils/request'

// 查询入库出库列表
export function listTaxStockInOut(query) {
  return request({
    url: '/screen/taxStockInOut/list',
    method: 'get',
    params: query
  })
}

// 查询入库出库详细
export function getTaxStockInOut(stockId) {
  return request({
    url: '/screen/taxStockInOut/' + stockId,
    method: 'get'
  })
}

// 新增入库出库
export function addTaxStockInOut(data) {
  return request({
    url: '/screen/taxStockInOut',
    method: 'post',
    data: data
  })
}

// 修改入库出库
export function updateTaxStockInOut(data) {
  return request({
    url: '/screen/taxStockInOut',
    method: 'put',
    data: data
  })
}

// 删除入库出库
export function delTaxStockInOut(stockId) {
  return request({
    url: '/screen/taxStockInOut/' + stockId,
    method: 'delete'
  })
}
