import request from '@/utils/request'

// 查询征纳沟通平台数据列表
export function listTaxCommData(query) {
  return request({
    url: '/screen/taxCommData/list',
    method: 'get',
    params: query
  })
}

// 查询征纳沟通平台数据详细
export function getTaxCommData(id) {
  return request({
    url: '/screen/taxCommData/' + id,
    method: 'get'
  })
}

// 新增征纳沟通平台数据
export function addTaxCommData(data) {
  return request({
    url: '/screen/taxCommData',
    method: 'post',
    data: data
  })
}

// 修改征纳沟通平台数据
export function updateTaxCommData(data) {
  return request({
    url: '/screen/taxCommData',
    method: 'put',
    data: data
  })
}

// 删除征纳沟通平台数据
export function delTaxCommData(id) {
  return request({
    url: '/screen/taxCommData/' + id,
    method: 'delete'
  })
}
