import request from '@/utils/request'

// 查询来电情况分析列表
export function listPhoneAnalysis(query) {
  return request({
    url: '/screen/phoneAnalysis/list',
    method: 'get',
    params: query
  })
}

// 查询来电情况分析详细
export function getPhoneAnalysis(id) {
  return request({
    url: '/screen/phoneAnalysis/' + id,
    method: 'get'
  })
}

// 新增来电情况分析
export function addPhoneAnalysis(data) {
  return request({
    url: '/screen/phoneAnalysis',
    method: 'post',
    data: data
  })
}

// 修改来电情况分析
export function updatePhoneAnalysis(data) {
  return request({
    url: '/screen/phoneAnalysis',
    method: 'put',
    data: data
  })
}

// 删除来电情况分析
export function delPhoneAnalysis(id) {
  return request({
    url: '/screen/phoneAnalysis/' + id,
    method: 'delete'
  })
}
