<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="任务日期" prop="taskCreateTime">
        <el-date-picker clearable
          v-model="queryParams.taskCreateTime"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择任务日期">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="名称" prop="taskName">
        <el-input
          v-model="queryParams.taskName"
          placeholder="请输入名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="中心分类" prop="taskCenterType">
        <el-select v-model="queryParams.taskCenterType" placeholder="请选类型" clearable size="small">
        <el-option
          v-for="dict in dict.type.dm_task_center_type"
          :key="dict.value"
          :label="dict.label"
          :value="dict.value"
        />
        </el-select>
      </el-form-item>
      <el-form-item label="完结日期" prop="taskFinishTime">
        <el-date-picker clearable
          v-model="queryParams.taskFinishTime"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择完结日期">
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['screen:taxTaskCenterInfo:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['screen:taxTaskCenterInfo:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['screen:taxTaskCenterInfo:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="el-icon-upload2"
          size="mini"
          @click="handleImport"
          v-hasPermi="['screen:taxTaskCenterInfo:import']"
        >导入</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['screen:taxTaskCenterInfo:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="taxTaskCenterInfoList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="id" align="center" prop="id" v-if="false"/>
      <el-table-column label="用户ID" align="center" prop="userId" v-if="false" />
      <el-table-column label="部门ID" align="center" prop="deptId" v-if="false" />
<!--      <el-table-column label="导入序号" align="center" prop="serialNumber" v-if="false" />-->
      <el-table-column label="任务中心分类" align="center" prop="taskCenterType" />
      <el-table-column label="任务日期" align="center" prop="taskCreateTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.taskCreateTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="名称" align="center" prop="taskName" />
      <el-table-column label="姓名" align="center" prop="name" />
      <el-table-column label="联系电话" align="center" prop="phoneNumber" />
      <el-table-column label="任务来源" align="center" prop="taskSource" />
      <el-table-column label="业务编号" align="center" prop="taskBizNum" />
      <el-table-column label="类别" align="center" prop="taskType" />
      <el-table-column label="反映内容" align="center" prop="taskReactionContent" >
        <template slot-scope="scope">

        </template>
      </el-table-column>>
      <el-table-column label="任务详情" align="center" prop="taskDetails" />
      <el-table-column label="答复内容" align="center" prop="taskReplyContent" />
      <el-table-column label="处理(答复)意见及结论" align="center" prop="taskProcessResults" />
      <el-table-column label="辅导情况" align="center" prop="taskCounselingSituation" />
      <el-table-column label="受理人" align="center" prop="acceptor" />
      <el-table-column label="完结日期" align="center" prop="taskFinishTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.taskFinishTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="任务状态" align="center" prop="taskStatus" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['screen:taxTaskCenterInfo:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['screen:taxTaskCenterInfo:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改斗门任务中心信息对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item prop="userId" v-model="form.userId" v-if="false"/>
        <el-form-item prop="deptId" v-model="form.deptId" v-if="false"/>
<!--        <el-form-item prop="serialNumber" v-model="form.serialNumber" v-if="false"/>-->
        <el-form-item label="任务日期" prop="taskCreateTime">
          <el-date-picker clearable
            v-model="form.taskCreateTime"
            type="datetime"
            value-format="yyyy-MM-dd HH:mm:ss"
            placeholder="请选择任务日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="名称" prop="taskName">
          <el-input v-model="form.taskName" placeholder="请输入名称" />
        </el-form-item>
        <el-form-item label="姓名" prop="name">
          <el-input v-model="form.name" placeholder="请输入姓名" />
        </el-form-item>
        <el-form-item label="联系电话" prop="phoneNumber">
          <el-input v-model="form.phoneNumber" placeholder="请输入联系电话" />
        </el-form-item>
        <el-form-item label="任务来源" prop="taskSource">
          <el-input v-model="form.taskSource" placeholder="请输入任务来源" />
        </el-form-item>
        <el-form-item label="业务编号" prop="taskBizNum">
          <el-input v-model="form.taskBizNum" placeholder="请输入业务编号" />
        </el-form-item>
        <el-form-item label="反映内容">
          <editor v-model="form.taskReactionContent" :min-height="192"/>
        </el-form-item>
        <el-form-item label="任务详情" prop="taskDetails">
          <el-input v-model="form.taskDetails" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="答复内容">
          <editor v-model="form.taskReplyContent" :min-height="192"/>
        </el-form-item>
        <el-form-item label="处理(答复)意见及结论" prop="taskProcessResults">
          <el-input v-model="form.taskProcessResults" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="辅导情况" prop="taskCounselingSituation">
          <el-input v-model="form.taskCounselingSituation" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="受理人" prop="acceptor">
          <el-input v-model="form.acceptor" placeholder="请输入受理人" />
        </el-form-item>
        <el-form-item label="完结日期" prop="taskFinishTime">
          <el-date-picker clearable
            v-model="form.taskFinishTime"
            type="datetime"
            value-format="yyyy-MM-dd HH:mm:ss"
            placeholder="请选择完结日期">
          </el-date-picker>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
    <!-- 导入对话框 -->
    <el-dialog :title="upload.title" :visible.sync="upload.open" width="400px" append-to-body>
      <el-upload
        ref="upload"
        :limit="1"
        accept=".xlsx, .xls"
        :headers="upload.headers"
        :action="upload.url + '?updateSupport=' + upload.updateSupport"
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
        drag
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div class="el-upload__tip text-center" slot="tip">
          <div class="el-upload__tip" slot="tip">
            <el-checkbox v-model="upload.updateSupport" /> 是否更新已经存在的用户数据
          </div>
          <span>仅允许导入xls、xlsx格式文件。</span>
        </div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
        <el-button @click="upload.open = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listTaxTaskCenterInfo, getTaxTaskCenterInfo, delTaxTaskCenterInfo, addTaxTaskCenterInfo, updateTaxTaskCenterInfo } from "@/api/screen/taxTaskCenterInfo";
import {getToken} from "@/utils/auth";
import {getInfo} from "@/api/login";

export default {
  name: "TaxTaskCenterInfo",
  dicts: ['dm_task_center_type'],
  data() {
    return {
      // 按钮loading
      buttonLoading: false,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 斗门任务中心信息表格数据
      taxTaskCenterInfoList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      upload: {
        // 是否显示弹出层（用户导入）
        open: false,
        // 弹出层标题（用户导入）
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 是否更新已经存在的用户数据
        updateSupport: 0,
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/screen/taxTaskCenterInfo/importData"
      },
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        userId: undefined,
        deptId: undefined,
        // serialNumber:undefined,
        taskCenterType: undefined,
        taskCreateTime: undefined,
        taskName: undefined,
        name: undefined,
        phoneNumber: undefined,
        taskSource: undefined,
        taskBizNum: undefined,
        taskType: undefined,
        taskReactionContent: undefined,
        taskDetails: undefined,
        taskReplyContent: undefined,
        taskProcessResults: undefined,
        taskCounselingSituation: undefined,
        acceptor: undefined,
        taskFinishTime: undefined,
        taskStatus: undefined,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        id: [
          { required: true, message: "id不能为空", trigger: "blur" }
        ],
        userId: [
          { required: true, message: "用户id不能为空", trigger: "blur" }
        ],
        deptId: [
          { required: true, message: "部门id不能为空", trigger: "blur" }
        ],
        // serialNumber: [
        //   { required: true, message: "导入序号不能为空", trigger: "blur" }
        // ],
        taskCenterType: [
          { required: true, message: "任务中心分类不能为空", trigger: "change" }
        ],
        taskCreateTime: [
          { required: true, message: "任务日期不能为空", trigger: "blur" }
        ],
        taskName: [
          { required: true, message: "名称不能为空", trigger: "blur" }
        ],
        name: [
          { required: true, message: "姓名不能为空", trigger: "blur" }
        ],
        phoneNumber: [
          { required: true, message: "联系电话不能为空", trigger: "blur" }
        ],
        taskSource: [
          { required: true, message: "任务来源不能为空", trigger: "blur" }
        ],
        taskBizNum: [
          { required: true, message: "业务编号不能为空", trigger: "blur" }
        ],
        taskType: [
          { required: true, message: "类别不能为空", trigger: "change" }
        ],
        taskReactionContent: [
          { required: true, message: "反映内容不能为空", trigger: "blur" }
        ],
        taskDetails: [
          { required: true, message: "任务详情不能为空", trigger: "blur" }
        ],
        taskReplyContent: [
          { required: true, message: "答复内容不能为空", trigger: "blur" }
        ],
        taskProcessResults: [
          { required: true, message: "处理(答复)意见及结论不能为空", trigger: "blur" }
        ],
        taskCounselingSituation: [
          { required: true, message: "辅导情况不能为空", trigger: "blur" }
        ],
        acceptor: [
          { required: true, message: "受理人不能为空", trigger: "blur" }
        ],
        taskFinishTime: [
          { required: true, message: "完结日期不能为空", trigger: "blur" }
        ],
        taskStatus: [
          { required: true, message: "任务状态不能为空", trigger: "blur" }
        ],
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询斗门任务中心信息列表 */
    getList() {
      this.loading = true;
      listTaxTaskCenterInfo(this.queryParams).then(response => {
        this.taxTaskCenterInfoList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: undefined,
        userId: undefined,
        deptId: undefined,
        // serialNumber:undefined,
        taskCenterType: undefined,
        taskCreateTime: undefined,
        taskName: undefined,
        name: undefined,
        phoneNumber: undefined,
        taskSource: undefined,
        taskBizNum: undefined,
        taskType: undefined,
        taskReactionContent: undefined,
        taskDetails: undefined,
        taskReplyContent: undefined,
        taskProcessResults: undefined,
        taskCounselingSituation: undefined,
        acceptor: undefined,
        taskFinishTime: undefined,
        taskStatus: "0",
        createBy: undefined,
        createTime: undefined,
        updateBy: undefined,
        updateTime: undefined
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      getInfo().then(response => {
        this.form.userId = response.data.user.userId;
        this.form.deptId = response.data.user.deptId;
      });
      this.title = "添加斗门任务中心信息";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.loading = true;
      this.reset();
      const id = row.id || this.ids
      getTaxTaskCenterInfo(id).then(response => {
        this.loading = false;
        this.form = response.data;
        this.open = true;
        this.title = "修改斗门任务中心信息";
      });
      getInfo().then(response => {
        this.form.userId = response.data.user.userId;
        this.form.deptId = response.data.user.deptId;
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          this.buttonLoading = true;
          if (this.form.id != null) {
            updateTaxTaskCenterInfo(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            }).finally(() => {
              this.buttonLoading = false;
            });
          } else {
            addTaxTaskCenterInfo(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            }).finally(() => {
              this.buttonLoading = false;
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除斗门任务中心信息编号为"' + ids + '"的数据项？').then(() => {
        this.loading = true;
        return delTaxTaskCenterInfo(ids);
      }).then(() => {
        this.loading = false;
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {
      }).finally(() => {
        this.loading = false;
      });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('screen/taxTaskCenterInfo/export', {
        ...this.queryParams
      }, `taxTaskCenterInfo_${new Date().getTime()}.xlsx`)
    },
    /** 导入按钮操作 */
    handleImport() {
      this.upload.title = "数据导入";
      this.upload.open = true;
    },
    /** 下载模板操作 */
    importTemplate() {
      this.download('screen/taxTaskCenterInfo/importTemplate', {
      }, `TaskCenterInfo_template_${new Date().getTime()}.xlsx`)
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false;
      this.upload.isUploading = false;
      this.$refs.upload.clearFiles();
      this.$alert("<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" + response.msg + "</div>", "导入结果", { dangerouslyUseHTMLString: true });
      this.getList();
    },
    // 提交上传文件
    submitFileForm() {
      this.$refs.upload.submit();
    }
  }
};
</script>
