<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="大厅登记序号" prop="unitCode">
        <el-input
          v-model="queryParams.unitCode"
          placeholder="请输入大厅登记序号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['screen:unitOverView:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['screen:unitOverView:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['screen:unitOverView:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['screen:unitOverView:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="unitOverViewList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="id" align="center" prop="id" v-if="false"/>
      <el-table-column label="大厅登记序号" align="center" prop="unitCode" />
      <template v-if="dict && dict.type && dict.type.tax_hall_name">
        <dict-tag :options="dict.type.tax_hall_name" :value="row.unitCode"/>
      </template>
      <!--      <el-table-column label="渠道名称" align="center" prop="rankChannelName" >-->
      <!--        <template slot-scope="scope">-->
      <!--          <dict-tag :options="dict.type.tax_rank_channel" :value="scope.row.rankChannelName"/>-->
      <!--        </template>-->
      <!--      </el-table-column>-->
      <el-table-column label="窗口数量" align="center" prop="winNum" />
      <el-table-column label="人员数量" align="center" prop="empNum" />
      <el-table-column label="同比增长" align="center" prop="rate" />
      <el-table-column label="自助机数量" align="center" prop="diyNum" />
      <el-table-column label="用户ID" align="center" prop="userId" v-if="false" />
      <el-table-column label="部门ID" align="center" prop="deptId" v-if="false" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['screen:unitOverView:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['screen:unitOverView:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改大厅信息总览对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="大厅登记序号" prop="unitCode">
          <el-selectcd v-model="form.unitCode" placeholder="请输入大厅登记序号" />
          <el-option
            v-for="dict in dict.type.tax_hall_name"
            :key="dict.value"
            :label="dict.label"
            :value="parseInt(dict.value)"
          ></el-option>
        </el-form-item>
        <el-form-item label="渠道名称" prop="rankChannelName">
          <el-select v-model="form.unitCode" placeholder="请选择渠道名称">
            <el-option
              v-for="dict in dict.type.tax_hall_name"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="窗口数量" prop="winNum">
          <el-input v-model="form.winNum" placeholder="请输入窗口数量" />
        </el-form-item>
        <el-form-item label="人员数量" prop="empNum">
          <el-input v-model="form.empNum" placeholder="请输入人员数量" />
        </el-form-item>
        <el-form-item label="同比增长" prop="rate">
          <el-input v-model="form.rate" placeholder="请输入同比增长" />
        </el-form-item>
        <el-form-item label="自助机数量" prop="diyNum">
          <el-input v-model="form.diyNum" placeholder="请输入自助机数量" />
        </el-form-item>
        <el-form-item prop="userId" v-model="form.userId" v-if="false"/>
        <el-form-item prop="deptId" v-model="form.deptId" v-if="false"/>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listUnitOverView, getUnitOverView, delUnitOverView, addUnitOverView, updateUnitOverView } from "@/api/screen/unitOverView";

import {getInfo} from "@/api/login";
import row from "element-ui/packages/row";

export default {
  name: "UnitOverView",
  dicts: ['tax_hall_name'],
  computed: {
    row() {
      return row
    }
  },
  data() {
    return {
      // 按钮loading
      buttonLoading: false,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 大厅信息总览表格数据
      unitOverViewList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        unitCode: undefined,
        winNum: undefined,
        empNum: undefined,
        rate: undefined,
        diyNum: undefined,
        userId: undefined,
        deptId: undefined
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        id: [
          { required: true, message: "id不能为空", trigger: "blur" }
        ],
        unitCode: [
          { required: true, message: "大厅登记序号不能为空", trigger: "blur" }
        ],
        winNum: [
          { required: true, message: "窗口数量不能为空", trigger: "blur" }
        ],
        empNum: [
          { required: true, message: "人员数量不能为空", trigger: "blur" }
        ],
        rate: [
          { required: true, message: "同比增长不能为空", trigger: "blur" }
        ],
        diyNum: [
          { required: true, message: "自助机数量不能为空", trigger: "blur" }
        ],
        userId: [
          { required: true, message: "用户ID不能为空", trigger: "blur" }
        ],
        deptId: [
          { required: true, message: "部门ID不能为空", trigger: "blur" }
        ]
      }
    };
  },
  mounted() {
    this.getList();
  },
  methods: {
    /** 查询大厅信息总览列表 */
    getList() {
      this.loading = true;
      listUnitOverView(this.queryParams).then(response => {
        this.unitOverViewList.splice(0, this.unitOverViewList.length, ...response.rows)
        this.total = response.total || 0;
        this.loading = false;
      }).catch(error => {
        console.error('API Error:', error);
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: undefined,
        unitCode: undefined,
        winNum: undefined,
        empNum: undefined,
        rate: undefined,
        diyNum: undefined,
        createBy: undefined,
        createTime: undefined,
        updateBy: undefined,
        updateTime: undefined,
        userId: undefined,
        deptId: undefined
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      getInfo().then(response => {
        this.form.userId = response.data.user.userId;
        this.form.deptId = response.data.user.deptId;
      });
      this.title = "添加大厅信息总览";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.loading = true;
      this.reset();
      const id = row.id || this.ids
      getUnitOverView(id).then(response => {
        this.loading = false;
        this.form = response.data;
        this.open = true;
        this.title = "修改大厅信息总览";
      });
      getInfo().then(response => {
        this.form.userId = response.data.user.userId;
        this.form.deptId = response.data.user.deptId;
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          this.buttonLoading = true;
          if (this.form.id != null) {
            updateUnitOverView(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            }).finally(() => {
              this.buttonLoading = false;
            });
          } else {
            addUnitOverView(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            }).finally(() => {
              this.buttonLoading = false;
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除大厅信息总览编号为"' + ids + '"的数据项？').then(() => {
        this.loading = true;
        return delUnitOverView(ids);
      }).then(() => {
        this.loading = false;
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {
      }).finally(() => {
        this.loading = false;
      });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('screen/unitOverView/export', {
        ...this.queryParams
      }, `unitOverView_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
