<template>
  <div class="app-container">
    <div class="search-box">
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="80px">
        <el-form-item label="报告日期" prop="reportDate">
          <el-date-picker clearable
                        v-model="queryParams.reportDate"
                        type="month"
                        value-format="yyyy-MM"
                        placeholder="请选择报告日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['screen:taxSentimentMonthlyReport:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['screen:taxSentimentMonthlyReport:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['screen:taxSentimentMonthlyReport:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['screen:taxSentimentMonthlyReport:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="taxSentimentMonthlyReportList"
      @selection-change="handleSelectionChange"
      :header-cell-style="{background:'#f5f7fa',color:'#606266'}"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="" align="center" prop="id" v-if="false"/>
      <el-table-column label="月报时间" align="center" prop="reportDate" width="120">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.reportDate, '{y}-{m}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="月报图片" align="center" prop="imageUrls" width="280">
        <template slot-scope="scope">
          <div v-if="scope.row.imageUrls" class="image-preview">
            <image-preview-list :value="scope.row.imageUrls"></image-preview-list>
          </div>
          <el-empty v-else description="暂无图片"></el-empty>
        </template>
      </el-table-column>
      <el-table-column label="月报内容" align="center" prop="textContent">
        <template slot-scope="scope">
          <div v-if="scope.row.textContent" class="text-content" v-html="scope.row.textContent"></div>
          <el-empty v-else description="暂无内容"></el-empty>
        </template>
      </el-table-column>
      <el-table-column label="备注" align="center" prop="remarks" width="120">
        <template slot-scope="scope">
          <el-tooltip v-if="scope.row.remarks" :content="scope.row.remarks" placement="top">
            <span>{{ scope.row.remarks.length > 20 ? scope.row.remarks.substr(0, 20) + '...' : scope.row.remarks }}</span>
          </el-tooltip>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="120">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['screen:taxSentimentMonthlyReport:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['screen:taxSentimentMonthlyReport:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改税情感知月报对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="700px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="月报时间" prop="reportDate">
          <el-date-picker clearable
                          v-model="form.reportDate"
                          type="month"
                          value-format="yyyy-MM"
                          placeholder="请选择月报时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="月报图片">
          <image-upload
            v-model="form.imageUrls"
            :limit="5"
            :fileSize="5"
            :fileType="['png', 'jpg', 'jpeg']"
          ></image-upload>
        </el-form-item>
        <el-form-item label="月报内容">
          <editor v-model="form.textContent" :min-height="192"/>
        </el-form-item>
        <el-form-item label="备注" prop="remarks">
          <el-input v-model="form.remarks" type="textarea" :rows="2" placeholder="请输入备注"/>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listTaxSentimentMonthlyReport, getTaxSentimentMonthlyReport, delTaxSentimentMonthlyReport, addTaxSentimentMonthlyReport, updateTaxSentimentMonthlyReport } from "@/api/screen/taxSentimentMonthlyReport";
import { parseTime } from "@/utils/index";
import {getInfo} from "@/api/login";
import { listByIds } from "@/api/system/oss";
import ImageUpload from '@/components/ImageUpload/index.vue'

// 新增组件：用于预览图片列表
const ImagePreviewList = {
  props: {
    value: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      imageList: [],
      loading: false
    }
  },
  watch: {
    value: {
      immediate: true,
      async handler(val) {
        if (!val) {
          this.imageList = [];
          return;
        }
        this.loading = true;
        try {
          const res = await listByIds(val);
          if (res.code === 200 && res.data) {
            this.imageList = res.data.map(item => item.url);
          }
        } catch (error) {
          console.error('获取图片列表失败:', error);
        }
        this.loading = false;
      }
    }
  },
  render(h) {
    if (this.loading) {
      return h('div', { 
        class: 'image-loading',
        style: {
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          height: '100px'
        }
      }, [
        h('i', { 
          class: 'el-icon-loading',
          style: {
            fontSize: '24px',
            marginRight: '8px'
          }
        }),
        h('span', '加载中...')
      ]);
    }
    return h('div', { class: 'image-list' }, this.imageList.map(url =>
      h('el-image', {
        props: {
          src: url,
          previewSrcList: [url],
          fit: 'cover'
        },
        style: {
          width: '100px',
          height: '100px',
          margin: '2px'
        }
      })
    ));
  }
};

export default {
  name: "TaxSentimentMonthlyReport",
  components: {
    ImageUpload,
    ImagePreviewList
  },
  data() {
    return {
      // 按钮loading
      buttonLoading: false,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 税情感知月报表格数据
      taxSentimentMonthlyReportList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        reportDate: undefined,
        imageUrls: undefined,
        textContent: undefined,
        remarks: undefined,
        userId: undefined,
        deptId: undefined
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        reportDate: [
          { required: true, message: "报告日期不能为空", trigger: "blur" }
        ],
        textContent: [
          { required: true, message: "月报文本内容不能为空", trigger: "blur" }
        ],
        remarks: [
          { required: true, message: "备注不能为空", trigger: "blur" }
        ]
      },
      // 文件列表
      fileList: [],
      dialogImageUrl: '',
      dialogVisible: false,
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询税情感知月报列表 */
    getList() {
      this.loading = true;
      listTaxSentimentMonthlyReport(this.queryParams).then(response => {
        this.taxSentimentMonthlyReportList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      if (this.queryParams.reportDate) {
        // 确保日期格式为 yyyy-MM-01
        const formattedDate = `${this.queryParams.reportDate}-01`;
        this.queryParams.reportDate = formattedDate;
      }
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        reportDate: undefined,
        imageUrls: undefined,
        textContent: undefined,
        remarks: undefined,
        userId: undefined,
        deptId: undefined
      };
      this.getList();
    },
    /** 多选框选中数据 */
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      getInfo().then(response => {
        this.form.userId = response.data.user.userId;
        this.form.deptId = response.data.user.deptId;
      });
      this.title = "新增税情感知月报";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      const id = row.id || this.ids[0];
      getTaxSentimentMonthlyReport(id).then(response => {
        console.log('修改按钮点击后获取的数据：', response.data);
        this.form = response.data;
        this.open = true;
        this.title = "修改税情感知月报";
      });
    },
    /** 提交按钮操作 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (!this.form.imageUrls) {
            this.$message.error("请上传图片");
            return;
          }

          // 如果是URL格式，需要转换回ossId
          if (this.form.imageUrls.includes('http')) {
            const urls = this.form.imageUrls.split(',');
            const ossIds = this.fileList.map(item => item.ossId);
            this.form.imageUrls = ossIds.join(',');
          }

          this.buttonLoading = true;
          const formattedDate = `${this.form.reportDate}-01 00:00:00`;
          const form = {...this.form, reportDate: formattedDate};
          const action = this.form.id ? updateTaxSentimentMonthlyReport : addTaxSentimentMonthlyReport;

          action(form).then(() => {
            this.buttonLoading = false;
            this.open = false;
            this.getList();
            this.$message.success(this.form.id ? "修改成功" : "新增成功");
          });
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row ? [row.id] : this.ids;
      if (!ids || ids.length === 0) {
        this.$message.warning("请选择要删除的项目");
        return;
      }
      this.$confirm("是否确认删除税情感知月报编号为" + ids.join(", ") + "的数据项?", "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        return delTaxSentimentMonthlyReport(ids);
      }).then(() => {
        this.getList();
        this.$message.success("删除成功");
      });
    },
    /** 导出按钮操作 */
    handleExport() {
      // 导出功能具体实现逻辑
    },
    /** 取消按钮操作 */
    cancel() {
      this.open = false;
    },
    /** 重置表单 */
    reset() {
      this.form = {
        reportDate: undefined,
        imageUrls: undefined,
        textContent: undefined,
        remarks: undefined,
        userId: undefined,
        deptId: undefined
      };
      this.fileList = [];
    }
  }
};
</script>

<style scoped>
.app-container {
  padding: 20px;
}

.search-box {
  background: #fff;
  padding: 15px;
  border-radius: 4px;
  box-shadow: 0 1px 4px rgba(0,0,0,0.05);
  margin-bottom: 15px;
}

.image-preview {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.image-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.image-list .el-image {
  width: 100px;
  height: 100px;
  border-radius: 4px;
  object-fit: cover;
  cursor: pointer;
  transition: all 0.3s;
  border: 1px solid #ebeef5;
}

.image-list .el-image:hover {
  transform: scale(1.05);
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.el-table {
  margin-top: 15px;
  border-radius: 4px;
  overflow: hidden;
}

.el-table th {
  background-color: #f5f7fa !important;
}

.el-form-item {
  margin-bottom: 22px;
}

.dialog-footer {
  text-align: right;
  padding-top: 20px;
  border-top: 1px solid #e6e6e6;
}

.text-content {
  max-height: 150px;
  overflow-y: auto;
  padding: 10px;
  background: #f8f9fa;
  border-radius: 4px;
  line-height: 1.5;
}

.mb8 {
  margin-bottom: 15px;
}
</style>
