<template>
  <div class="app-container">
<!--    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">-->
<!--      <el-form-item label="办理方式名称" prop="dealName">-->
<!--        <el-input-->
<!--          v-model="queryParams.dealName"-->
<!--          placeholder="请输入办理方式名称"-->
<!--          clearable-->
<!--          @keyup.enter.native="handleQuery"-->
<!--        />-->
<!--      </el-form-item>-->
<!--      <el-form-item label="办税率" prop="dealRate">-->
<!--        <el-input-->
<!--          v-model="queryParams.dealRate"-->
<!--          placeholder="请输入办税率"-->
<!--          clearable-->
<!--          @keyup.enter.native="handleQuery"-->
<!--        />-->
<!--      </el-form-item>-->
<!--      <el-form-item label="区办税率" prop="sectionRate">-->
<!--        <el-input-->
<!--          v-model="queryParams.sectionRate"-->
<!--          placeholder="请输入区办税率"-->
<!--          clearable-->
<!--          @keyup.enter.native="handleQuery"-->
<!--        />-->
<!--      </el-form-item>-->
<!--      <el-form-item label="全市平均水平" prop="cityAvg">-->
<!--        <el-input-->
<!--          v-model="queryParams.cityAvg"-->
<!--          placeholder="请输入全市平均水平"-->
<!--          clearable-->
<!--          @keyup.enter.native="handleQuery"-->
<!--        />-->
<!--      </el-form-item>-->
<!--      <el-form-item label="全市排名" prop="listRank">-->
<!--        <el-input-->
<!--          v-model="queryParams.listRank"-->
<!--          placeholder="请输入全市排名"-->
<!--          clearable-->
<!--          @keyup.enter.native="handleQuery"-->
<!--        />-->
<!--      </el-form-item>-->
<!--      <el-form-item label="用户ID" prop="userId">-->
<!--        <el-input-->
<!--          v-model="queryParams.userId"-->
<!--          placeholder="请输入用户ID"-->
<!--          clearable-->
<!--          @keyup.enter.native="handleQuery"-->
<!--        />-->
<!--      </el-form-item>-->
<!--      <el-form-item label="部门ID" prop="deptId">-->
<!--        <el-input-->
<!--          v-model="queryParams.deptId"-->
<!--          placeholder="请输入部门ID"-->
<!--          clearable-->
<!--          @keyup.enter.native="handleQuery"-->
<!--        />-->
<!--      </el-form-item>-->
<!--      <el-form-item>-->
<!--        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>-->
<!--        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>-->
<!--      </el-form-item>-->
<!--    </el-form>-->

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['screen:taxDealRateRank:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['screen:taxDealRateRank:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['screen:taxDealRateRank:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['screen:taxDealRateRank:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="taxDealRateRankList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="id" align="center" prop="id" v-if="false"/>
      <el-table-column label="办理方式名称" align="center" prop="dealName" />
      <el-table-column label="办税率" align="center" prop="dealRate" v-if="false"/>
      <el-table-column label="区办税率" align="center" prop="sectionRate" />
      <el-table-column label="全市平均水平" align="center" prop="cityAvg" v-if="false"/>
      <el-table-column label="全市排名" align="center" prop="listRank" v-if="false"/>
      <el-table-column label="用户ID" align="center" prop="userId" v-if="false" />
      <el-table-column label="部门ID" align="center" prop="deptId" v-if="false" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['screen:taxDealRateRank:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['screen:taxDealRateRank:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改办税率排名对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="办理方式名称" prop="dealName">
          <el-input v-model="form.dealName" placeholder="请输入办理方式名称" />
        </el-form-item>
<!--        <el-form-item label="办税率" prop="dealRate">-->
<!--          <el-input v-model="form.dealRate" placeholder="请输入办税率" />-->
<!--        </el-form-item>-->
        <el-form-item label="区办税率" prop="sectionRate">
          <el-input v-model="form.sectionRate" placeholder="请输入区办税率" />
        </el-form-item>
<!--        <el-form-item label="全市平均水平" prop="cityAvg">-->
<!--          <el-input v-model="form.cityAvg" placeholder="请输入全市平均水平" />-->
<!--        </el-form-item>-->
<!--        <el-form-item label="全市排名" prop="listRank">-->
<!--          <el-input v-model="form.listRank" placeholder="请输入全市排名" />-->
<!--        </el-form-item>-->
        <el-form-item prop="userId" v-model="form.userId" v-if="false"/>
        <el-form-item prop="deptId" v-model="form.deptId" v-if="false"/>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listTaxDealRateRank, getTaxDealRateRank, delTaxDealRateRank, addTaxDealRateRank, updateTaxDealRateRank } from "@/api/screen/taxDealRateRank";

import {getInfo} from "@/api/login";

export default {
  name: "TaxDealRateRank",
  data() {
    return {
      // 按钮loading
      buttonLoading: false,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 办税率排名表格数据
      taxDealRateRankList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        dealName: undefined,
        dealRate: undefined,
        sectionRate: undefined,
        cityAvg: undefined,
        listRank: undefined,
        userId: undefined,
        deptId: undefined
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        id: [
          { required: true, message: "id不能为空", trigger: "blur" }
        ],
        dealName: [
          { required: true, message: "办理方式名称不能为空", trigger: "blur" }
        ],
        dealRate: [
          { required: false, message: "办税率不能为空", trigger: "blur" }
        ],
        sectionRate: [
          { required: true, message: "区办税率不能为空", trigger: "blur" }
        ],
        cityAvg: [
          { required: false, message: "全市平均水平不能为空", trigger: "blur" }
        ],
        listRank: [
          { required: false, message: "全市排名不能为空", trigger: "blur" }
        ],
        userId: [
          { required: true, message: "用户ID不能为空", trigger: "blur" }
        ],
        deptId: [
          { required: true, message: "部门ID不能为空", trigger: "blur" }
        ]
      }
    };
  },

  watch: {
    // 监听“办税率”（dealRate）的变化
    'form.sectionRate'(newVal) {
      if (newVal) {
        // 当“办税率”发生变化时，自动更新“区办税率”和“全市平均水平”
        this.form.dealRate = newVal; // 例如，将办税率乘以0.8来计算区办税率
        this.form.cityAvg = newVal;
        this.form.listRank = newVal;// 例如，将办税率乘以1.1来计算全市平均水平
      }
    }
  },

  created() {
    this.getList();
  },
  methods: {
    /** 查询办税率排名列表 */
    getList() {
      this.loading = true;
      listTaxDealRateRank(this.queryParams).then(response => {
        this.taxDealRateRankList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: undefined,
        dealName: undefined,
        dealRate: undefined,
        sectionRate: undefined,
        cityAvg: undefined,
        listRank: undefined,
        createBy: undefined,
        createTime: undefined,
        updateBy: undefined,
        updateTime: undefined,
        userId: undefined,
        deptId: undefined
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      getInfo().then(response => {
        this.form.userId = response.data.user.userId;
        this.form.deptId = response.data.user.deptId;
      });
      this.title = "添加办税率排名";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.loading = true;
      this.reset();
      const id = row.id || this.ids
      getTaxDealRateRank(id).then(response => {
        this.loading = false;
        this.form = response.data;
        this.open = true;
        this.title = "修改办税率排名";
      });
      getInfo().then(response => {
        this.form.userId = response.data.user.userId;
        this.form.deptId = response.data.user.deptId;
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          this.buttonLoading = true;
          if (this.form.id != null) {
            updateTaxDealRateRank(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            }).finally(() => {
              this.buttonLoading = false;
            });
          } else {
            addTaxDealRateRank(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            }).finally(() => {
              this.buttonLoading = false;
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除办税率排名编号为"' + ids + '"的数据项？').then(() => {
        this.loading = true;
        return delTaxDealRateRank(ids);
      }).then(() => {
        this.loading = false;
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {
      }).finally(() => {
        this.loading = false;
      });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('screen/taxDealRateRank/export', {
        ...this.queryParams
      }, `taxDealRateRank_${new Date().getTime()}.xlsx`)
    }


  }
};
</script>
