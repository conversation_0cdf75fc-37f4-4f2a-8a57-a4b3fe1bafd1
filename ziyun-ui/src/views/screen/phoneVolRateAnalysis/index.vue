<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
<!--      <el-form-item label="时间维度(单位:月)" prop="axisX">-->
<!--        <el-date-picker clearable-->
<!--          v-model="queryParams.axisX"-->
<!--          type="month"-->
<!--          value-format="yyyy-MM"-->
<!--          placeholder="请选择时间维度(单位:月)">-->
<!--        </el-date-picker>-->
<!--      </el-form-item>-->
      <el-form-item label="电话接听量" prop="barY">
        <el-input
          v-model="queryParams.barY"
          placeholder="请输入电话接听量"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="电话接听率(单位:%)" prop="lineY">
        <el-input
          v-model="queryParams.lineY"
          placeholder="请输入电话接听率(单位:%)"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['screen:phoneVolRateAnalysis:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['screen:phoneVolRateAnalysis:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['screen:phoneVolRateAnalysis:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['screen:phoneVolRateAnalysis:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="phoneVolRateAnalysisList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="ID" align="center" prop="id" v-if="false"/>
      <el-table-column label="时间维度(单位:月)" align="center" prop="axisX" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.axisX, '{y}-{m}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="电话接听量" align="center" prop="barY" />
      <el-table-column label="电话接听率(单位:%)" align="center" prop="lineY" />
      <el-table-column label="用户ID" align="center" prop="userId" v-if="false" />
      <el-table-column label="部门ID" align="center" prop="deptId" v-if="false" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['screen:phoneVolRateAnalysis:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['screen:phoneVolRateAnalysis:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改月电话接听量和接听率对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="时间维度(单位:月)" prop="axisX">
          <el-date-picker clearable
            v-model="form.axisX"
            type="month"
            value-format="yyyy-MM"
            placeholder="请选择时间维度(单位:月)">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="电话接听量" prop="barY">
          <el-input v-model="form.barY" placeholder="请输入电话接听量" />
        </el-form-item>
        <el-form-item label="电话接听率(单位:%)" prop="lineY">
          <el-input v-model="form.lineY" placeholder="请输入电话接听率(单位:%)" />
        </el-form-item>
        <el-form-item prop="userId" v-model="form.userId" v-if="false"/>
        <el-form-item prop="deptId" v-model="form.deptId" v-if="false"/>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listPhoneVolRateAnalysis, getPhoneVolRateAnalysis, delPhoneVolRateAnalysis, addPhoneVolRateAnalysis, updatePhoneVolRateAnalysis } from "@/api/screen/phoneVolRateAnalysis";

import {getInfo} from "@/api/login";

export default {
  name: "PhoneVolRateAnalysis",
  data() {
    return {
      // 按钮loading
      buttonLoading: false,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 月电话接听量和接听率表格数据
      phoneVolRateAnalysisList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        axisX: undefined,
        barY: undefined,
        lineY: undefined,
        userId: undefined,
        deptId: undefined
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        id: [
          { required: true, message: "ID不能为空", trigger: "blur" }
        ],
        axisX: [
          { required: true, message: "时间维度(单位:月)不能为空", trigger: "blur" }
        ],
        barY: [
          { required: true, message: "电话接听量不能为空", trigger: "blur" }
        ],
        lineY: [
          { required: true, message: "电话接听率(单位:%)不能为空", trigger: "blur" }
        ],
        userId: [
          { required: true, message: "用户ID不能为空", trigger: "blur" }
        ],
        deptId: [
          { required: true, message: "部门ID不能为空", trigger: "blur" }
        ]
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询月电话接听量和接听率列表 */
    getList() {
      this.loading = true;
      listPhoneVolRateAnalysis(this.queryParams).then(response => {
        this.phoneVolRateAnalysisList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: undefined,
        axisX: undefined,
        barY: undefined,
        lineY: undefined,
        createBy: undefined,
        createTime: undefined,
        updateBy: undefined,
        updateTime: undefined,
        userId: undefined,
        deptId: undefined
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      // 格式化日期为 yyyy-MM
      if (this.queryParams.axisX) {
        const formattedDate = `${this.queryParams.axisX}-01`; // 格式为 yyyy-MM-01，后台可能需要这样处理
        this.queryParams.axisX = formattedDate;
      }

      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      getInfo().then(response => {
        this.form.userId = response.data.user.userId;
        this.form.deptId = response.data.user.deptId;
      });
      this.title = "添加月电话接听量和接听率";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.loading = true;
      this.reset();
      const id = row.id || this.ids
      getPhoneVolRateAnalysis(id).then(response => {
        this.loading = false;
        this.form = response.data;
        this.open = true;
        this.title = "修改月电话接听量和接听率";
      });
      getInfo().then(response => {
        this.form.userId = response.data.user.userId;
        this.form.deptId = response.data.user.deptId;
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          this.buttonLoading = true;

          // 将日期格式转换为 yyyy-MM-01 00:00:00
          const formattedDate = `${this.form.axisX}-01 00:00:00`;
          const form = { ...this.form, axisX: formattedDate };

          if (this.form.id != null) {
            updatePhoneVolRateAnalysis(form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            }).finally(() => {
              this.buttonLoading = false;
            });
          } else {
            addPhoneVolRateAnalysis(form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            }).finally(() => {
              this.buttonLoading = false;
            });
          }
        }
      });
    },

    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除月电话接听量和接听率编号为"' + ids + '"的数据项？').then(() => {
        this.loading = true;
        return delPhoneVolRateAnalysis(ids);
      }).then(() => {
        this.loading = false;
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {
      }).finally(() => {
        this.loading = false;
      });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('screen/phoneVolRateAnalysis/export', {
        ...this.queryParams
      }, `phoneVolRateAnalysis_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
