<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="摄像头厂商" prop="cameraProduce">
        <el-select
          v-model="queryParams.cameraProduce"
          placeholder="请选择摄像头厂商"
          clearable
          size="small"
        >
          <el-option
            v-for="dict in dict.type.qsc_camera_produce"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="税务机关序号" prop="unitCode">
<!--        <el-input-->
<!--          v-model="queryParams.unitCode"-->
<!--          placeholder="请输入税务机关序号"-->
<!--          clearable-->
<!--          size="small"-->
<!--          @keyup.enter.native="handleQuery"-->
<!--        />-->
        <el-select
          v-model="queryParams.unitCode"
          placeholder="请选择大厅"
          clearable
          size="small"
        >
          <el-option
            v-for="dict in dict.type.tax_hall_name"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="设备类型" prop="deviceType">
        <el-select
          v-model="queryParams.deviceType"
          placeholder="请选择设备类型"
          clearable
          size="small"
        >
          <el-option
            v-for="dict in dict.type.qsc_device_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="NVR厂商" prop="nvrProduce">
        <el-select
          v-model="queryParams.nvrProduce"
          placeholder="请选择NVR厂商"
          clearable
          size="small"
        >
          <el-option
            v-for="dict in dict.type.qsc_nvr_produce"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['screen:camera:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['screen:camera:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['screen:camera:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['screen:camera:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="cameraList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="" align="center" prop="cameraId" v-if="true"/>
      <el-table-column label="" align="center" prop="ip" />
      <el-table-column label="" align="center" prop="port" />
      <el-table-column label="" align="center" prop="username" />
      <el-table-column label="" align="center" prop="password" />
      <el-table-column label="" align="center" prop="url" />
      <el-table-column label="" align="center" prop="cameraProduce" />
      <el-table-column label="" align="center" prop="cameraName" />
      <el-table-column label="" align="center" prop="deviceType" />
      <el-table-column label="" align="center" prop="unitCode" />
      <el-table-column label="" align="center" prop="nvrProduce" />
      <el-table-column label="" align="center" prop="nvrPath" />
      <el-table-column label="" align="center" prop="playBack" />
      <el-table-column label="用户ID" align="center" prop="userId" v-if="false" />
      <el-table-column label="部门ID" align="center" prop="deptId" v-if="false" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['screen:camera:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['screen:camera:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改【请填写功能名称】对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="" prop="ip">
          <el-input v-model="form.ip" placeholder="请输入" />
        </el-form-item>
        <el-form-item label="" prop="port">
          <el-input v-model="form.port" placeholder="请输入" />
        </el-form-item>
        <el-form-item label="" prop="username">
          <el-input v-model="form.username" placeholder="请输入" />
        </el-form-item>
        <el-form-item label="" prop="password">
          <el-input v-model="form.password" placeholder="请输入" />
        </el-form-item>
        <el-form-item label="" prop="url">
          <el-input v-model="form.url" placeholder="请输入" />
        </el-form-item>
        <el-form-item label="" prop="cameraProduce">
          <el-input v-model="form.cameraProduce" placeholder="请输入" />
        </el-form-item>
        <el-form-item label="" prop="cameraName">
          <el-input v-model="form.cameraName" placeholder="请输入" />
        </el-form-item>
        <el-form-item label="" prop="unitCode">
          <el-input v-model="form.unitCode" placeholder="请输入" />
        </el-form-item>
        <el-form-item label="" prop="nvrProduce">
          <el-input v-model="form.nvrProduce" placeholder="请输入" />
        </el-form-item>
        <el-form-item label="" prop="nvrPath">
          <el-input v-model="form.nvrPath" placeholder="请输入" />
        </el-form-item>
        <el-form-item label="" prop="playBack">
          <el-input v-model="form.playBack" placeholder="请输入" />
        </el-form-item>
        <el-form-item prop="userId" v-model="form.userId" v-if="false"/>
        <el-form-item prop="deptId" v-model="form.deptId" v-if="false"/>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listCamera, getCamera, delCamera, addCamera, updateCamera } from "@/api/screen/camera";

import {getInfo} from "@/api/login";

export default {
  name: "Camera",
  dicts: ['qsc_device_type','qsc_nvr_produce','tax_hall_name','qsc_camera_produce'],
  data() {
    return {
      // 按钮loading
      buttonLoading: false,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 【请填写功能名称】表格数据
      cameraList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        ip: undefined,
        port: undefined,
        username: undefined,
        password: undefined,
        url: undefined,
        cameraProduce: undefined,
        cameraName: undefined,
        deviceType: undefined,
        unitCode: undefined,
        nvrProduce: undefined,
        nvrPath: undefined,
        playBack: undefined,
        userId: undefined,
        deptId: undefined
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        cameraId: [
          { required: true, message: "不能为空", trigger: "blur" }
        ],
        ip: [
          { required: true, message: "不能为空", trigger: "blur" }
        ],
        port: [
          { required: true, message: "不能为空", trigger: "blur" }
        ],
        username: [
          { required: true, message: "不能为空", trigger: "blur" }
        ],
        password: [
          { required: true, message: "不能为空", trigger: "blur" }
        ],
        url: [
          { required: true, message: "不能为空", trigger: "blur" }
        ],
        cameraProduce: [
          { required: true, message: "不能为空", trigger: "blur" }
        ],
        cameraName: [
          { required: true, message: "不能为空", trigger: "blur" }
        ],
        deviceType: [
          { required: true, message: "不能为空", trigger: "change" }
        ],
        unitCode: [
          { required: true, message: "不能为空", trigger: "blur" }
        ],
        nvrProduce: [
          { required: true, message: "不能为空", trigger: "blur" }
        ],
        nvrPath: [
          { required: true, message: "不能为空", trigger: "blur" }
        ],
        playBack: [
          { required: true, message: "不能为空", trigger: "blur" }
        ],
        userId: [
          { required: true, message: "用户ID不能为空", trigger: "blur" }
        ],
        deptId: [
          { required: true, message: "部门ID不能为空", trigger: "blur" }
        ]
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询【请填写功能名称】列表 */
    getList() {
      this.loading = true;
      listCamera(this.queryParams).then(response => {
        this.cameraList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        cameraId: undefined,
        ip: undefined,
        port: undefined,
        username: undefined,
        password: undefined,
        url: undefined,
        cameraProduce: undefined,
        cameraName: undefined,
        deviceType: undefined,
        unitCode: undefined,
        nvrProduce: undefined,
        nvrPath: undefined,
        playBack: undefined,
        delFlag: undefined,
        createBy: undefined,
        createTime: undefined,
        updateBy: undefined,
        updateTime: undefined,
        userId: undefined,
        deptId: undefined
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.cameraId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      getInfo().then(response => {
        this.form.userId = response.data.user.userId;
        this.form.deptId = response.data.user.deptId;
      });
      this.title = "添加【请填写功能名称】";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.loading = true;
      this.reset();
      const cameraId = row.cameraId || this.ids
      getCamera(cameraId).then(response => {
        this.loading = false;
        this.form = response.data;
        this.open = true;
        this.title = "修改【请填写功能名称】";
      });
      getInfo().then(response => {
        this.form.userId = response.data.user.userId;
        this.form.deptId = response.data.user.deptId;
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          this.buttonLoading = true;
          if (this.form.cameraId != null) {
            updateCamera(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            }).finally(() => {
              this.buttonLoading = false;
            });
          } else {
            addCamera(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            }).finally(() => {
              this.buttonLoading = false;
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const cameraIds = row.cameraId || this.ids;
      this.$modal.confirm('是否确认删除【请填写功能名称】编号为"' + cameraIds + '"的数据项？').then(() => {
        this.loading = true;
        return delCamera(cameraIds);
      }).then(() => {
        this.loading = false;
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {
      }).finally(() => {
        this.loading = false;
      });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('screen/camera/export', {
        ...this.queryParams
      }, `camera_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
