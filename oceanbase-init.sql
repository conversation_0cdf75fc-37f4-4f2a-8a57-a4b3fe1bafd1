-- OceanBase数据库初始化脚本
-- 创建system表

-- 如果表存在则删除
DROP TABLE IF EXISTS `system`;

-- 创建system表
CREATE TABLE `system` (
  `KEY` varchar(50) NOT NULL COMMENT '系统配置键',
  `VALUE` text COMMENT '系统配置值',
  `MEMO` varchar(255) DEFAULT NULL COMMENT '备注说明',
  PRIMARY KEY (`KEY`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统配置表';

-- 插入初始数据
INSERT INTO `system` (`KEY`, `VALUE`, `MEMO`) VALUES 
('BS_API_APPKEY', '', '佰税科技API APPKEY'),
('BS_API_TOKEN', '', '佰税科技API TOKEN');

-- 验证数据
SELECT * FROM `system`;
